# V3 Process Flow - Full Architecture (Updated May 2025)

## Core Process Flow

```mermaid
flowchart TD
    subgraph GUI[GUI Layer]
        A[v3_gui_core.py\nMainWindowV3] --> B[v3_parameter_widgets.py\nParameter Widgets]
        A --> C[v3_gui_actions.py\nGUI Actions]
        B --> D[v3_engine/gui_parameter_manager.py\nParameter Manager]
    end

    subgraph Engine[Backtest Engine]
        E[v3_engine/parameter_registry.py\nParameter Registry]
        F[Strategy Classes\n e.g., EMA]
        G[Backtest Engine]
        H[Execution Engine]
        DataLoader[data/data_loader.py\nDataLoader] -->|Raw Data| DataValidator[v3_engine/data_validator.py]
        DataValidator -->|Validated Data| F
        F -->|Errors| ExceptionHandler[v3_engine/exception_handler.py]
        ExceptionHandler -->|Logged Errors| TradeLog[utils/trade_log.py\nTrade Log]
    end

    subgraph Reporting[V3 Reporting]
        I[v3_reporting/v3_performance_report.py\nPerformance Reporter]
        J[v3_reporting/v3_allocation_report.py\nAllocation Reporter]
        K[v3_reporting/v3_visualization.py\nVisualization]
        L[v3_reporting/v3_performance_charts.py\nPerformance Charts]
        M[v3_reporting/v3_trade_log.py\nTrade Log Formatter]
    end

    %% Main Flow
    D -->|Sync Parameters| E
    E -->|Apply Parameters| F
    F -->|Execute Strategy| G
    G -->|Execute Trades| H
    G -->|Generate Results| I
    G -->|Generate Allocations| J
    I -->|Create Charts| K
    J -->|Create Charts| K

    %% Styling
    classDef gui fill:#BBDEFB,stroke:#1976D2,color:#0D47A1;
    classDef engine fill:#C8E6C9,stroke:#388E3C,color:#1B5E20;
    classDef report fill:#F3E5F5,stroke:#8E24AA,color:#4A148C;
    classDef validation fill:#FFF9C4,stroke:#FBC02D,color:#5D4037;
    classDef error fill:#FFCDD2,stroke:#D32F2F,color:#B71C1C;
    class A,B,C,D gui;
    class E,F,G,H engine;
    class I,J,K,L,M report;
    class DataValidator validation;
    class ExceptionHandler error;
```

## Detailed Component Interactions

```mermaid
flowchart LR
    %% Data Flow
    DataLoader[data/data_loader.py\nDataLoader] -->|OHLCV Data| Strategy
    DataLoader -->|Benchmark Data| Backtest

    %% Parameter Flow
    ParameterRegistry <-->|register_parameter_list| StrategyParameterSet[v3_engine/strategy_parameter_set.py\nStrategyParameterSet]
    ParameterRegistry <-->|Type Definitions| Parameters[v3_engine/parameters.py\nParameter Classes]
    Parameters -->|Optimize| ParameterOptimizer[v3_engine/parameter_optimizer.py\nOptimizer]
    ParameterRegistry -->|get_core_parameters| Backtest
    ParameterRegistry -->|get_strategy_parameters| Strategy
    ParameterRegistry -->|get_all_parameters| PerformanceReporterAdapter

    %% Execution Flow
    Strategy -->|Generate Signals| Backtest
    Backtest -->|Portfolio Updates| Execution[execution/engine.py\nExecution Engine]

    %% Reporting Flow
    Backtest -->|Results| PerformanceReporter
    Backtest -->|Allocations| AllocationReporter
    PerformanceReporter -->|Formatted Data| PerformanceReporterAdapter[v3_engine/performance_reporter_adapter.py\nAdapter]

    %% Utilities
    Backtest -->|Date Handling| DateUtils[utils/date_utils.py\nDate Utilities]
    Backtest -->|Log Trades| TradeLog[utils/trade_log.py\nTrade Log]

    %% Styling
    classDef gui fill:#BBDEFB,stroke:#1976D2,color:#0D47A1;
    classDef param fill:#C8E6C9,stroke:#388E3C,color:#1B5E20;
    classDef strategy fill:#FFECB3,stroke:#F57F17,color:#E65100;
    classDef report fill:#F3E5F5,stroke:#8E24AA,color:#4A148C;
    classDef util fill:#EFEBE9,stroke:#5D4037,color:#3E2723;
    class ParameterRegistry,Parameters,StrategyParameterSet,ParameterOptimizer param;
    class Strategy,Backtest strategy;
    class PerformanceReporter,AllocationReporter,PerformanceReporterAdapter report;
    class DateUtils,TradeLog util;
```

## Key Improvements Over V2

- **Parameter Handling**: Unified registry with type safety and optimization support
- **Reporting**: Standardized interfaces through adapter pattern
- **Visualization**: Integrated chart generation with V3 parameters
- **Execution**: Clear separation of signal generation and trade execution
- **Documentation**: Comprehensive process flow documentation
- **Data Validation**: Added data validation and error handling to process flow
