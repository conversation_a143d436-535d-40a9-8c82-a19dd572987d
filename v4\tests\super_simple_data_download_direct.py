#!/usr/bin/env python
# super_simple_data_download_direct.py
"""
Minimal data download test: loads CPS v4 settings, fetches price data via market_data, prints and saves sample.
"""
import sys
from pathlib import Path
import pandas as pd

# 1) Add project root to PYTHONPATH
test_dir = Path(__file__).resolve().parent
project_root = test_dir.parent.parent
sys.path.insert(0, str(project_root))

# 1a) Setup logging to both console and log file
log_path = test_dir / 'terminal_log.txt'
log_file = open(log_path, 'w')
class Tee:
    def __init__(self, *files): self.files = files
    def write(self, data):
        for f in self.files: f.write(data)
    def flush(self):
        for f in self.files: f.flush()
tee = Tee(sys.stdout, log_file)
sys.stdout = tee
sys.stderr = tee

# 2) Configure environment paths (adds custom function library to PYTHONPATH)
import v4.config.paths_v4

# 3) Imports
from v4.settings.settings_CPS_v4 import load_settings
from market_data import data_fetch_stock_data
from utils.date_utils import standardize_date_range, standardize_dataframe_index, filter_dataframe_by_dates

# 3) Load settings
settings = load_settings()  # load CPS v4 settings

# 4) Print all loaded settings
import json
print("Loaded settings:")
print(json.dumps(settings, indent=2))
core = settings.get('core', {})
tickers = core.get('tickers', {}).get('default', [])
start_date = core.get('start_date')
end_date = core.get('end_date')

# 4) Download
print(f"Downloading {tickers} from {start_date} to {end_date}")
start_ts, end_ts = standardize_date_range(start_date, end_date)
data = {}
for ticker in tickers:
    df = data_fetch_stock_data(ticker=ticker, period='max', interval='1d')
    if hasattr(df.index, 'tz') and df.index.tz is not None:
        df.index = df.index.tz_localize(None)
    df = standardize_dataframe_index(df)
    df = filter_dataframe_by_dates(df, start_ts, end_ts)
    series = df['Adj Close'] if 'Adj Close' in df.columns else df['Close']
    data[ticker] = series
price_data = pd.DataFrame(data)

# 5) Print and verify
print(f"Data shape: {price_data.shape}")
print(price_data.head())

# 6) Save sample
out_csv = test_dir / 'price_data_head_direct.csv'
price_data.head().to_csv(out_csv)
print(f"Wrote sample to {out_csv}")
