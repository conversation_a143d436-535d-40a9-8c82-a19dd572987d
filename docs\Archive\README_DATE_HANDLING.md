# Date Handling Standardization

## Overview
This document outlines the standardized date handling approach implemented across the Backtesting Framework and Custom Function Library integration. The goal is to ensure consistent date formatting and handling throughout the codebase to reduce errors and simplify debugging.

## Key Principles

### 1. Date Format Standardization
- All dates should be handled as timezone-naive pandas Timestamp objects
- Date-only values should be represented as YYYY-MM-DD format
- Time components should only be used when necessary for calculations
- All date displays should suppress time components

### 2. Date Utilities Module
The `utils/date_utils.py` module provides standardized functions for all date operations:

#### Core Functions
- `standardize_date(date_input)`: Converts various date formats to timezone-naive pandas Timestamp
- `standardize_date_range(start_date, end_date)`: Returns standardized start and end dates as tuple
- `standardize_dataframe_index(df)`: Ensures DataFrame index is timezone-naive Timestamp
- `filter_dataframe_by_dates(df, start_date, end_date)`: Filters DataFrame by date range
- `display_date(date_obj)`: Formats date for display as YYYY-MM-DD
- `format_dataframe_index_for_display(df)`: Converts DataFrame index to date strings for display

#### Usage Examples
```python
# Standardizing dates
start_date = standardize_date('2023-01-01')
end_date = standardize_date('2023-12-31')

# Standardizing DataFrame
price_data = standardize_dataframe_index(price_data)

# Displaying dates
print(display_date(pd.Timestamp.now()))  # Output: YYYY-MM-DD format

# Formatting for reports
report_df = format_dataframe_index_for_display(df)
```

### 3. Implementation Guidelines

#### Data Loading
- Use `standardize_date` for all input dates
- Use `standardize_dataframe_index` for all DataFrame inputs
- Use `filter_dataframe_by_dates` for date range filtering

#### Backtesting
- Use standardized dates for all calculations
- Use `display_date` for any date display in logs or reports
- Use `format_dataframe_index_for_display` before exporting to Excel

#### Reporting
- Always use `display_date` for date formatting in reports
- Use `format_dataframe_index_for_display` for DataFrame output formatting
- Never show time components in reports unless explicitly requested

#### Visualization
- Use standardized dates for all chart axes
- Format dates consistently across all visualizations
- Use `display_date` for chart titles and labels

## Best Practices

1. **Never use timezone-aware timestamps** unless explicitly required
2. **Always standardize dates before calculations**
3. **Use display functions for all output**
4. **Keep time components out of reports**
5. **Document any date-related assumptions**

## Common Pitfalls to Avoid

1. Using datetime objects directly without standardization
2. Mixing timezone-aware and naive timestamps
3. Displaying time components in reports
4. Using string dates for calculations
5. Not formatting dates consistently across the codebase

## Error Handling

When encountering date-related errors:
1. Check if dates are properly standardized
2. Verify timezone handling
3. Ensure consistent date formats
4. Use the debug logging to track date transformations

## Debugging Tips

1. Use `display_date` for consistent date visualization in logs
2. Check DataFrame indices with `df.index.dtype`
3. Verify date ranges with `df.index.min()` and `df.index.max()`
4. Use `df.info()` to check for mixed date types

## Future Considerations

1. Adding timezone conversion utilities if needed
2. Implementing date validation functions
3. Adding more date formatting options for reports
4. Extending date utilities for international date formats
5. Adding date range validation functions
