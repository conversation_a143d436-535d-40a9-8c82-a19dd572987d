echo off
echo Starting phase 3 - Final cleanup of project reorganization...
echo.

REM Run as administrator check
NET SESSION >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo This script requires administrator privileges.
    echo Please right-click and select "Run as administrator"
    pause
    exit /b 1
)

echo Creating additional directories...
mkdir archive\legacy_files\app 2>nul
mkdir archive\legacy_files\config 2>nul
mkdir archive\legacy_files\engine 2>nul
mkdir archive\legacy_files\models 2>nul
mkdir archive\legacy_files\reporting 2>nul
mkdir archive\legacy_files\tools 2>nul
mkdir archive\legacy_files\utils 2>nul
mkdir archive\legacy_files\visualization 2>nul
mkdir archive\legacy_files\CPS_v4 2>nul
mkdir archive\legacy_files\optimization 2>nul
mkdir archive\legacy_files\v3_engine 2>nul
mkdir archive\legacy_files\v3_reporting 2>nul
mkdir utils\setup\memory_tool 2>nul
mkdir tests\scripts\backtest 2>nul

echo.
echo Moving remaining directories to archive...
echo - Moving app folder...
if exist app\NUL move /Y app\*.* archive\legacy_files\app\ >nul 2>&1

echo - Moving config folder...
if exist config\NUL move /Y config\*.* archive\legacy_files\config\ >nul 2>&1

echo - Moving CPS_v4 folder...
if exist CPS_v4\NUL move /Y CPS_v4\*.* archive\legacy_files\CPS_v4\ >nul 2>&1

echo - Moving engine folder...
if exist engine\NUL move /Y engine\*.* archive\legacy_files\engine\ >nul 2>&1

echo - Moving models folder...
if exist models\NUL move /Y models\*.* archive\legacy_files\models\ >nul 2>&1

echo - Moving optimization folder...
if exist optimization\NUL move /Y optimization\*.* archive\legacy_files\optimization\ >nul 2>&1

echo - Moving reporting folder...
if exist reporting\NUL move /Y reporting\*.* archive\legacy_files\reporting\ >nul 2>&1

echo - Moving tools folder...
if exist tools\NUL move /Y tools\*.* archive\legacy_files\tools\ >nul 2>&1

echo - Moving utils folder...
if exist utils\NUL move /Y utils\*.* archive\legacy_files\utils\ >nul 2>&1

echo - Moving v3_engine folder...
if exist v3_engine\NUL move /Y v3_engine\*.* archive\legacy_files\v3_engine\ >nul 2>&1

echo - Moving v3_reporting folder...
if exist v3_reporting\NUL move /Y v3_reporting\*.* archive\legacy_files\v3_reporting\ >nul 2>&1

echo - Moving visualization folder...
if exist visualization\NUL move /Y visualization\*.* archive\legacy_files\visualization\ >nul 2>&1

echo - Moving Memory tool setup folder...
if exist "Memory tool setup"\NUL move /Y "Memory tool setup"\*.* utils\setup\memory_tool\ >nul 2>&1

echo.
echo Moving root level Python files...

echo - Moving backtest scripts...
move /Y auto_report_fix.py tests\scripts\ >nul 2>&1
move /Y backtest_original.py tests\scripts\backtest\ >nul 2>&1
move /Y capture_v3_gui_output.py tests\scripts\ >nul 2>&1
move /Y check_env.py utils\setup\ >nul 2>&1
move /Y check_report_content.py tests\scripts\ >nul 2>&1
move /Y debug_allocation_report.py tests\scripts\ >nul 2>&1
move /Y debug_v3_reporting.py tests\scripts\ >nul 2>&1
move /Y run_backtest.py tests\scripts\backtest\ >nul 2>&1
move /Y run_backtest_v2_with_metrics.py tests\scripts\backtest\ >nul 2>&1
move /Y run_backtest_v3.py tests\scripts\backtest\ >nul 2>&1
move /Y run_backtest_v3_BU.py tests\scripts\backtest\ >nul 2>&1
move /Y test_backtest.py tests\scripts\backtest\ >nul 2>&1
move /Y test_ema_v3.py tests\scripts\ >nul 2>&1
move /Y test_v3_engine_reporting.py tests\scripts\ >nul 2>&1
move /Y test_v3_reporting.py tests\scripts\ >nul 2>&1
move /Y verify_v3_report_standards.py tests\verification\ >nul 2>&1

echo.
echo Moving log files to logs directory...
move /Y *.log logs\ >nul 2>&1
move /Y test_output.txt logs\ >nul 2>&1

echo.
echo Cleaning up remaining empty directories...
rmdir /S /Q app 2>nul
rmdir /S /Q config 2>nul
rmdir /S /Q CPS_v4 2>nul
rmdir /S /Q engine 2>nul
rmdir /S /Q models 2>nul
rmdir /S /Q optimization 2>nul
rmdir /S /Q reporting 2>nul
rmdir /S /Q tools 2>nul
rmdir /S /Q utils 2>nul
rmdir /S /Q v3_engine 2>nul
rmdir /S /Q v3_reporting 2>nul
rmdir /S /Q visualization 2>nul
rmdir /S /Q special_test 2>nul
rmdir /S /Q combined_docs 2>nul
rmdir /S /Q batch_backup 2>nul
rmdir /S /Q temp_backups 2>nul
rmdir /S /Q "Memory tool setup" 2>nul

echo.
echo Phase 3 - Final cleanup complete!
echo.
echo Remaining folders at root level are:
echo  - Special folders (.*): Configuration and system folders
echo  - memory-bank: Project memory for AI assistance
echo  - logs: System and application logs
echo  - data: Data files (with v3/v4 subfolders)
echo  - output: Output files (with v3/v4 subfolders)
echo  - docs: Documentation (with v3/v4 and CPS_v4 subfolders)
echo  - tests: Test scripts and verification
echo  - v3: Legacy V3 system components
echo  - v4: Active V4 development components
echo  - archive: Archived legacy files and directories
echo.
pause
