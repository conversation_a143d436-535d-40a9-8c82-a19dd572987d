#!/usr/bin/env python
# -*- coding: utf-8 -*-
# minimal_print_test.py
"""
Absolute minimal test script that only prints to console.
No imports other than sys for stdout.
"""

import sys

# Print directly to stdout and flush
sys.stdout.write("=" * 50 + "\n")
sys.stdout.write("MINIMAL PRINT TEST STARTED\n")
sys.stdout.write("=" * 50 + "\n")
sys.stdout.flush()

# Try different print methods
print("Standard print statement")
sys.stdout.write("Direct sys.stdout.write statement\n")
sys.stdout.flush()

# Print Python version
import platform
sys.stdout.write(f"Python version: {platform.python_version()}\n")
sys.stdout.flush()

# Print current directory
import os
sys.stdout.write(f"Current directory: {os.getcwd()}\n")
sys.stdout.flush()

# Print sys.path
sys.stdout.write("Python sys.path:\n")
for path in sys.path:
    sys.stdout.write(f"  - {path}\n")
sys.stdout.flush()

# Final message
sys.stdout.write("\n" + "=" * 50 + "\n")
sys.stdout.write("MINIMAL PRINT TEST COMPLETED\n")
sys.stdout.write("=" * 50 + "\n")
sys.stdout.flush()
