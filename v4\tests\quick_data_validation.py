#!/usr/bin/env python
"""
quick_data_validation.py
---------------------------------
A minimal sanity-check script to verify that the CPS v4 data-loading pipeline
can (1) download or read cached price data and (2) return a DataFrame.
The script intentionally keeps its surface area tiny so it runs fast and
isolates just the early data-flow stage.

Steps:
1. Load CPS v4 settings (single source of truth).
2. Call v4.data.load_price_data(), which internally honours data_params ->
   data_storage_mode (Save / Read / New).
3. Print basic diagnostics (shape, columns, head).
4. Persist a tiny CSV sample for manual inspection.
"""
from pathlib import Path
import sys
import json

# ---------------------------------------------------------------------------
# 1) Ensure project root is on PYTHONPATH so relative imports resolve cleanly
# ---------------------------------------------------------------------------
THIS_DIR = Path(__file__).resolve().parent
PROJECT_ROOT = THIS_DIR.parent.parent
sys.path.insert(0, str(PROJECT_ROOT))

# ---------------------------------------------------------------------------
# 2) Imports after path setup
# ---------------------------------------------------------------------------
from v4.settings.settings_CPS_v4 import load_settings
from v4.data.data_loader import load_price_data

# ---------------------------------------------------------------------------
# 3) Load settings & price data
# ---------------------------------------------------------------------------
settings = load_settings()
print("Loaded settings sections:", list(settings.keys()))

print("\nInvoking load_price_data() …")
price_df = load_price_data()

# ---------------------------------------------------------------------------
# 4) Basic diagnostics
# ---------------------------------------------------------------------------
if price_df is None:
    print("ERROR: load_price_data() returned None – data-flow failed!")
    sys.exit(1)

print(f"DataFrame shape: {price_df.shape}")
print("Columns:", list(price_df.columns))
print("\nHead:\n", price_df.head())

# ---------------------------------------------------------------------------
# 5) Save sample CSV for manual eyeballing
# ---------------------------------------------------------------------------
OUT_CSV = THIS_DIR / "quick_data_validation_head.csv"
price_df.head().to_csv(OUT_CSV, index=True)
print(f"\nSaved sample head to {OUT_CSV}")

print("\nquick_data_validation.py completed successfully")


