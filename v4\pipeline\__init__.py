#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
v4/pipeline/__init__.py

Pipeline Module Package for CPS v4 Backtest System

This package contains the refactored pipeline components from run_unified_pipeline.py:
- config.py: Configuration, logging setup, and CLI argument parsing
- modes.py: Pipeline execution modes (optimization and single run)
- trading.py: Trading operations and signal handling

Author: AI Assistant  
Date: 2025-07-26
"""

# Import all pipeline functions for easy access
from .config import setup_logger, parse_cli_arguments, determine_pipeline_mode
from .modes import run_optimization_pipeline, run_single_pipeline, run_baseline_optimization_iteration
from .trading import modify_run_trading_to_accept_dataframe

__all__ = [
    'setup_logger',
    'parse_cli_arguments', 
    'determine_pipeline_mode',
    'run_optimization_pipeline',
    'run_single_pipeline',
    'run_baseline_optimization_iteration',
    'modify_run_trading_to_accept_dataframe'
]
