#!/usr/bin/env python
# -*- coding: utf-8 -*-
# direct_file_test.py
"""
Test script that writes directly to a log file, completely bypassing the console.
"""

import os
import sys
import traceback
from pathlib import Path
from datetime import datetime

# Create log file in the same directory as this script
script_dir = os.path.dirname(os.path.abspath(__file__))
log_file_path = os.path.join(script_dir, "direct_file_test.log")

# Open log file directly
with open(log_file_path, 'w') as log_file:
    log_file.write(f"==== DIRECT FILE TEST LOG ====\n")
    log_file.write(f"Started at: {datetime.now()}\n")
    log_file.write(f"Script path: {__file__}\n")
    log_file.write(f"Working directory: {os.getcwd()}\n\n")
    
    try:
        # Step 1: Log basic environment info
        log_file.write("STEP 1: Logging environment info\n")
        log_file.write(f"Python version: {sys.version}\n")
        log_file.write(f"Python executable: {sys.executable}\n")
        log_file.write(f"Platform: {sys.platform}\n")
        
        # Step 2: Log sys.path
        log_file.write("\nSTEP 2: sys.path contents\n")
        for i, path in enumerate(sys.path):
            log_file.write(f"  {i}: {path}\n")
        
        # Step 3: Try to add project root to path
        log_file.write("\nSTEP 3: Adding project root to path\n")
        try:
            project_root = Path(__file__).parent.parent.parent
            log_file.write(f"Project root identified as: {project_root}\n")
            
            if str(project_root) not in sys.path:
                sys.path.append(str(project_root))
                log_file.write(f"Added {project_root} to sys.path\n")
            else:
                log_file.write(f"{project_root} already in sys.path\n")
        except Exception as e:
            log_file.write(f"ERROR setting up path: {e}\n")
            log_file.write(traceback.format_exc())

        # Step 4: Try to import v4 modules
        log_file.write("\nSTEP 4: Importing key modules\n")
        try:
            log_file.write("Attempting to import settings_CPS_v4...\n")
            from v4.settings.settings_CPS_v4 import load_settings
            log_file.write("✓ Successfully imported load_settings\n")
            
            log_file.write("Attempting to load settings...\n")
            settings = load_settings()
            log_file.write("✓ Successfully loaded settings\n")
            
            # Log settings structure
            log_file.write("\nSettings Structure:\n")
            for key in settings.keys():
                log_file.write(f"  - {key}\n")
                if isinstance(settings[key], dict):
                    for subkey in settings[key]:
                        log_file.write(f"    - {subkey}: {settings[key][subkey]}\n")
            
            # Log specific important settings
            if 'backtest' in settings:
                log_file.write("\nBacktest Settings (Detailed):\n")
                backtest_settings = settings['backtest']
                log_file.write(f"  - Strategy: {backtest_settings.get('strategy', 'N/A')}\n")
                log_file.write(f"  - Rebalance Frequency: {backtest_settings.get('rebalance_freq', 'N/A')}\n")
                log_file.write(f"  - Execution Delay: {backtest_settings.get('execution_delay', 'N/A')}\n")
            
            if 'data' in settings:
                log_file.write("\nData Settings (Detailed):\n")
                data_settings = settings['data']
                log_file.write(f"  - Data Source: {data_settings.get('data_source', 'N/A')}\n")
                log_file.write(f"  - Tickers: {data_settings.get('tickers', 'N/A')}\n")
                log_file.write(f"  - Date Range: {data_settings.get('start_date', 'N/A')} to {data_settings.get('end_date', 'N/A')}\n")
            
        except ImportError as e:
            log_file.write(f"ERROR importing modules: {e}\n")
            log_file.write(traceback.format_exc())
        
        # Step 5: Try to load price data
        log_file.write("\nSTEP 5: Loading price data\n")
        try:
            log_file.write("Attempting to import data_loader...\n")
            from v4.data.data_loader import load_price_data
            log_file.write("✓ Successfully imported load_price_data\n")
            
            # Get data settings
            data_settings = settings.get('data', {})
            tickers = data_settings.get('tickers', [])
            start_date = data_settings.get('start_date', '2020-01-01')
            end_date = data_settings.get('end_date', '2022-12-31')
            data_source = data_settings.get('data_source', 'yahoo')
            
            log_file.write(f"Loading price data with params:\n")
            log_file.write(f"  - tickers: {tickers}\n")
            log_file.write(f"  - start_date: {start_date}\n")
            log_file.write(f"  - end_date: {end_date}\n")
            log_file.write(f"  - data_source: {data_source}\n")
            
            price_data = load_price_data(
                tickers=tickers,
                start_date=start_date,
                end_date=end_date,
                data_source=data_source
            )
            
            log_file.write("✓ Successfully loaded price data\n")
            log_file.write(f"Price data shape: {price_data.shape}\n")
            log_file.write(f"Price data columns: {price_data.columns.tolist()}\n")
            log_file.write(f"Price data index: {price_data.index[0]} to {price_data.index[-1]}\n")
            
        except Exception as e:
            log_file.write(f"ERROR loading price data: {e}\n")
            log_file.write(traceback.format_exc())
        
        # Step 6: Try importing BacktestEngine
        log_file.write("\nSTEP 6: Importing BacktestEngine\n")
        try:
            log_file.write("Attempting to import BacktestEngine...\n")
            from v4.engine.backtest_v4 import BacktestEngine
            log_file.write("✓ Successfully imported BacktestEngine\n")
            
            engine = BacktestEngine()
            log_file.write(f"✓ Successfully created BacktestEngine instance\n")
        except Exception as e:
            log_file.write(f"ERROR importing/instantiating BacktestEngine: {e}\n")
            log_file.write(traceback.format_exc())
        
        # Step 7: Try signal generation
        log_file.write("\nSTEP 7: Testing signal generation\n")
        try:
            log_file.write("Attempting to import generate_signals...\n")
            from v4.engine.signal_generator_v4 import generate_signals
            log_file.write("✓ Successfully imported generate_signals\n")
            
            # Get strategy from backtest settings
            strategy = settings.get('backtest', {}).get('strategy', 'equal_weight')
            log_file.write(f"Using strategy: {strategy}\n")
            
            signals = generate_signals(price_data, strategy=strategy)
            log_file.write(f"✓ Successfully generated signals\n")
            log_file.write(f"Signals shape: {signals.shape}\n")
        except Exception as e:
            log_file.write(f"ERROR generating signals: {e}\n")
            log_file.write(traceback.format_exc())
        
        # Step 8: Run backtest
        log_file.write("\nSTEP 8: Running backtest\n")
        try:
            # Get backtest parameters
            backtest_settings = settings.get('backtest', {})
            strategy = backtest_settings.get('strategy', 'equal_weight')
            rebalance_freq = backtest_settings.get('rebalance_freq', 'M')
            execution_delay = backtest_settings.get('execution_delay', 1)
            initial_capital = backtest_settings.get('initial_capital', 10000)
            
            log_file.write(f"Backtest parameters:\n")
            log_file.write(f"  - strategy: {strategy}\n")
            log_file.write(f"  - rebalance_freq: {rebalance_freq}\n")
            log_file.write(f"  - execution_delay: {execution_delay}\n")
            log_file.write(f"  - initial_capital: {initial_capital}\n")
            
            results = engine.run_backtest(
                price_data=price_data,
                signal_generator=generate_signals,
                strategy=strategy,
                rebalance_freq=rebalance_freq,
                execution_delay=execution_delay,
                initial_capital=initial_capital
            )
            
            log_file.write("✓ Successfully ran backtest\n")
            
            # Check results structure
            log_file.write("\nResults Structure:\n")
            if isinstance(results, dict):
                log_file.write(f"Results is a dictionary with {len(results)} keys\n")
                for key in results:
                    log_file.write(f"  - {key}: {type(results[key])}\n")
                
                # Check for weights_history and signal_history
                weights_history = results.get('weights_history')
                signal_history = results.get('signal_history')
                
                if weights_history is None:
                    log_file.write("✗ weights_history is None!\n")
                elif signal_history is None:
                    log_file.write("✗ signal_history is None!\n")
                else:
                    log_file.write(f"weights_history shape: {weights_history.shape}\n")
                    log_file.write(f"signal_history shape: {signal_history.shape}\n")
                    
                    # Check if they're equal
                    if weights_history.equals(signal_history):
                        log_file.write("✗ weights_history EQUALS signal_history - allocation history is NOT preserved!\n")
                        
                        # Sample date comparison
                        if not weights_history.empty:
                            sample_date = weights_history.index[0]
                            log_file.write(f"Sample comparison for date {sample_date}:\n")
                            log_file.write(f"  weights: {weights_history.loc[sample_date].to_dict()}\n")
                            log_file.write(f"  signals: {signal_history.loc[sample_date].to_dict()}\n")
                    else:
                        log_file.write("✓ weights_history DIFFERS from signal_history - allocation history is preserved!\n")
                        
                        # Sample date comparison
                        if not weights_history.empty and not signal_history.empty:
                            sample_date = weights_history.index[0]
                            log_file.write(f"Sample comparison for date {sample_date}:\n")
                            log_file.write(f"  weights: {weights_history.loc[sample_date].to_dict()}\n")
                            log_file.write(f"  signals: {signal_history.loc[sample_date].to_dict()}\n")
            else:
                log_file.write(f"✗ Results is not a dictionary, but {type(results)}\n")
        except Exception as e:
            log_file.write(f"ERROR running backtest: {e}\n")
            log_file.write(traceback.format_exc())
    
    except Exception as e:
        log_file.write(f"\nFATAL ERROR: {e}\n")
        log_file.write(traceback.format_exc())
    
    # End log
    log_file.write("\n==== TEST COMPLETE ====\n")
    log_file.write(f"Finished at: {datetime.now()}\n")
    
# Create a small marker file to indicate the test has completed
with open(os.path.join(script_dir, "test_completed.marker"), 'w') as marker:
    marker.write(f"Test completed at {datetime.now()}")
