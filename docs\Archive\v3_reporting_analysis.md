# V3 Reporting System Analysis and Task Tracker

## Purpose

This document serves as the central tracking tool for systematically fixing and enhancing the V3 reporting system. It maps required report components to their implementation status, data flow requirements, and specific tasks needed to achieve compliance with the standards document.

## Standard Requirements vs. Implementation Status

### 1. Performance Table Report (XLSX)

- **Contains tabs:**
  - Signal History
  - Allocation History 
  - Trade Log
  - Performance tab (metrics)
- **Requirements:**
  - Must include all parameter values in specific format
  - All metrics must match actual values from backtest engine

### 2. Monthly & Annual Returns Graphic (PNG)

- Heatmap with years (rows) and months (columns)
- Color-coded returns from red (negative) to green (positive)
- Must include color bar/legend
- High DPI (300-600) for print quality

### 3. Cumulative Returns & Drawdown Graphic (PNG)

- Top panel: cumulative returns for strategy/benchmark
- Bottom panel: drawdowns over same period
- Must show parameters at bottom of image
- Arrows for remix/change in allocation if possible

## Report Components and Their Data Requirements

### Tab: Signal History

- **Data needed:** SignalHistoryData from backtest engine
- **Format:** 0.00%
- **Structure:** Dates (rows) × Tickers+Cash (columns)
- **Content:** % allocation signals (end-of-day)
- **Current issue:** SignalHistoryData not properly populated/preserved

### Tab: Allocation History

- **Data needed:** AllocationHistoryData derived from actual trades
- **Format:** 0.00%
- **Structure:** Matches Signal History structure
- **Content:** ACTUAL capital allocation after trades
- **Current issue:** Needs to account for execution_delay parameter

### Tab: Trade Log

- **Data needed:** TradeLogData with all executed trades
- **Format:** Various (dates, prices, etc. per standards)
- **Structure:** One row per trade with specific columns
- **Content:** All trades with details (symbol, quantity, price, etc.)

### Tab: Performance

- **Data needed:** ParameterData and calculated metrics
- **Format:** Per standards (percentages, ratios, currency)
- **Structure:** Parameters (left), Metrics (right)
- **Content:** Benchmark row + one row per parameter combo
- **Current issue:** Parameter optimization tuple handling

## Data Flow Pipeline

```
BacktestEngine → SignalHistoryData → V3AllocationReport → SignalHistoryTab + Charts
                                  → AllocationHistoryData
                                  → TradeLogData

ParameterData → PerformanceReporterAdapter → V3PerformanceReport → PerformanceTab + Charts
```

## Identified Issues and Fix Tasks

### HIGH Priority

1. **Fix Signal History Data Flow**
   
   - Problem: signal_history not properly populated or preserved
   - Target: engine/backtest.py needs to include signal_history in results
   - Impact: Blocks allocation reports and visualizations

2. **Fix Parameter Optimization Handling**
   
   - Problem: execution_delay parameter optimization not flowing correctly
   - Target: v3_engine/performance_reporter_adapter.py type handling
   - Impact: Critical for optimization functionality in reports

3. **Ensure Standards Compliance**
   
   - Problem: Outputs may not match exact format requirements
   - Target: All reporting modules need verification against standards
   - Impact: Essential for meeting product requirements

### MEDIUM Priority

4. **Fix Missing Imports**
   
   - Problem: Missing imports in various modules cause runtime errors
   - Target: v3_allocation_report.py, v3_performance_report.py, etc.
   - Impact: Prevents basic functionality from working

5. **Improve Allocation Report Formatting**
   
   - Problem: Dates include time, chart axes cluttered, etc.
   - Target: v3_allocation_report.py formatting
   - Impact: Improves report quality and readability

6. **Implement Missing Charts**
   
   - Problem: Monthly Returns and/or Cumulative Returns charts may be missing
   - Target: Chart generation modules
   - Impact: Required for standards compliance

## Task Checklist and Status Tracker

*Update the Status column after each test run. Current as of: 2025-05-14*

### Data Flow Components

| ID  | Component              | Required For       | Current Status | Issue Description                      | Fix Location                              | Priority |
| --- | ---------------------- | ------------------ | -------------- | -------------------------------------- | ----------------------------------------- | -------- |
| DF1 | Signal History Data    | Allocation reports | ❌ Not working  | "signal_history is None or empty"      | engine/backtest.py                        | HIGH     |
| DF2 | Parameter Optimization | Performance tab    | ❌ Not working  | Type errors with execution_delay tuple | v3_engine/performance_reporter_adapter.py | HIGH     |
| DF3 | Allocation History     | Allocation charts  | ❓ Unknown      | May need reconstruction from trades    | engine/backtest.py                        | MEDIUM   |
| DF4 | Trade Log Data         | Trade history tab  | ❓ Unknown      | May be working but not verified        | engine/execution.py                       | MEDIUM   |

### Report Component Implementation Status

| ID  | Component                | Standard Ref | Current Status | Issue Description           | Module                   | Priority |
| --- | ------------------------ | ------------ | -------------- | --------------------------- | ------------------------ | -------- |
| RC1 | Signal History Tab       | Tier 2       | ❌ Not working  | Missing signal_history data | v3_allocation_report.py  | HIGH     |
| RC2 | Allocation History Tab   | Tier 2       | ❌ Not working  | Not being generated         | v3_allocation_report.py  | HIGH     |
| RC3 | Allocation Weights Chart | Tier 2       | ❌ Not working  | Missing matplotlib import   | v3_allocation_report.py  | MEDIUM   |
| RC4 | Trade Log Tab            | Tier 2       | ❓ Unknown      | Not verified                | v3_performance_report.py | MEDIUM   |
| RC5 | Performance Tab          | Tier 2       | ❓ Unknown      | May have parameter issues   | v3_performance_report.py | HIGH     |
| RC6 | Monthly Returns Chart    | Tier 1       | ❓ Unknown      | Not verified                | v3_performance_report.py | MEDIUM   |
| RC7 | Cumulative Returns Chart | Tier 1       | ❓ Unknown      | Not verified                | v3_performance_report.py | MEDIUM   |

### Technical Infrastructure Issues

| ID  | Component                 | Required For      | Current Status | Issue Description          | Module                   | Priority |
| --- | ------------------------- | ----------------- | -------------- | -------------------------- | ------------------------ | -------- |
| TI1 | Matplotlib Import         | Charting          | ❌ Missing      | Import missing in module   | v3_allocation_report.py  | HIGH     |
| TI2 | Performance Report Import | Reporting         | ❌ Incorrect    | Wrong import path          | v3_performance_report.py | HIGH     |
| TI3 | Logger Definitions        | Error tracking    | ❌ Missing      | Missing in several modules | Multiple modules         | MEDIUM   |
| TI4 | Logging Level             | Verbosity control | ✅ Working      | BACKTEST_LOG_LEVEL env var | engine/backtest.py       | LOW      |

## Specific Fix Tasks

### HIGH Priority Fixes (Complete First)

1. **Fix Signal History Data Flow**  
   
   - Add logging to track signal_history throughout pipeline
   - Ensure BacktestEngine._calculate_results includes signal_history in return value
   - Fix any empty/None handling in v3_allocation_report.py
   - Status: 🔄 In Progress

2. **Fix Parameter Optimization Handling**
   
   - Add type checking for execution_delay parameter
   - Ensure proper conversion from optimization tuple to value
   - Fix parameter flow through performance reporter adapter
   - Status: 📝 Not Started

3. **Add Missing Imports**
   
   - Add matplotlib.pyplot import to v3_allocation_report.py
   - Fix import for generate_performance_report
   - Add logger definitions to missing modules
   - Status: 📝 Not Started

### MEDIUM Priority Fixes (Complete After High Priority)

4. **Improve Allocation Report Formatting**
   
   - Strip time from dates in Excel output
   - Show only years on chart x-axis
   - Enhance chart quality (DPI, colors, grid lines)
   - Status: 📝 Not Started

5. **Ensure Report Standards Compliance**
   
   - Verify all report formatting against standards
   - Check column ordering, number formats, headers
   - Validate tab names and structures
   - Status: 📝 Not Started

### LOW Priority Fixes (Complete Last)

6. **Improve Logging**
   - Change log level from INFO to DEBUG for trade messages
   - Update all logging calls in portfolio.py and execution.py
   - Status: 📝 Not Started

## Test Run Log

| Date       | Bat File | Issues Found | Fixes Applied | Status                |
| ---------- | -------- | ------------ | ------------- | --------------------- |
| 2025-05-14 |          |              |               | Initial documentation |

*This document will be updated after each test run to track progress.*

## Structured Verification Process

### Expected Output Files and Verification Checklist

This section defines all expected output files from a successful V3 reporting system run, their locations, minimum expected sizes, and verification criteria. This serves as a comprehensive checklist to validate system functionality.

#### 1. Log Files

| File                                       | Location | Min Size | Purpose                        | Verification Criteria                                                           |
| ------------------------------------------ | -------- | -------- | ------------------------------ | ------------------------------------------------------------------------------- |
| `v3_engine_reporting_test_{timestamp}.log` | `logs/`  | 1KB      | Main process log               | Contains "Starting V3 engine" and "Completed successfully"                      |
| `v3_debug_{timestamp}.txt`                 | `logs/`  | 5KB      | Detailed debug log             | Contains signal generation, parameter processing, and report generation entries |
| `v3_error_{timestamp}.log`                 | `logs/`  | 0KB      | Error log (empty if no errors) | Should be empty or contain specific error details                               |
| `v3_performance_{timestamp}.log`           | `logs/`  | 500B     | Performance metrics log        | Contains timing information for key processes                                   |

#### 2. Performance Report Files

| File                                             | Location  | Min Size | Purpose                  | Verification Criteria                                                                   |
| ------------------------------------------------ | --------- | -------- | ------------------------ | --------------------------------------------------------------------------------------- |
| `{strategy}_performance_tables_{timestamp}.xlsx` | `output/` | 50KB     | Main performance report  | Contains all required tabs (Signal History, Allocation History, Trade Log, Performance) |
| `{strategy}_monthly_returns_{timestamp}.png`     | `output/` | 100KB    | Monthly returns heatmap  | Image dimensions ≥ 1200x800, contains color legend                                      |
| `{strategy}_cumulative_returns_{timestamp}.png`  | `output/` | 100KB    | Cumulative returns chart | Image dimensions ≥ 1200x800, contains drawdown panel                                    |
| `{strategy}_drawdown_{timestamp}.png`            | `output/` | 100KB    | Drawdown chart           | Image dimensions ≥ 1200x800                                                             |
| `{strategy}_portfolio_weights_{timestamp}.png`   | `output/` | 100KB    | Portfolio weights chart  | Image dimensions ≥ 1200x800                                                             |

#### 3. Data Files

| File                                           | Location  | Min Size | Purpose                | Verification Criteria                                 |
| ---------------------------------------------- | --------- | -------- | ---------------------- | ----------------------------------------------------- |
| `{strategy}_signal_history_{timestamp}.csv`    | `output/` | 10KB     | Signal history data    | Contains dates and ticker columns, non-empty          |
| `{strategy}_weights_history_{timestamp}.csv`   | `output/` | 10KB     | Weights history data   | Contains dates and ticker columns, non-empty          |
| `{strategy}_returns_{timestamp}.csv`           | `output/` | 10KB     | Strategy returns data  | Contains dates and return values, non-empty           |
| `{strategy}_benchmark_returns_{timestamp}.csv` | `output/` | 10KB     | Benchmark returns data | Contains dates and benchmark return values, non-empty |
| `{strategy}_trades_{timestamp}.csv`            | `output/` | 1KB      | Trade log data         | Contains trade details, may be small if few trades    |

#### 4. Parameter Combination Files

| File                                                    | Location                  | Min Size | Purpose                    | Verification Criteria                                |
| ------------------------------------------------------- | ------------------------- | -------- | -------------------------- | ---------------------------------------------------- |
| `{strategy}_Combo_{n}_all_data_{timestamp}.xlsx`        | `output/param_combo_{n}/` | 50KB     | Parameter combination data | Contains all data for specific parameter combination |
| `{strategy}_Combo_{n}_signal_history_{timestamp}.xlsx`  | `output/param_combo_{n}/` | 10KB     | Signal history for combo   | Non-empty, matches parameter values                  |
| `{strategy}_Combo_{n}_weights_history_{timestamp}.xlsx` | `output/param_combo_{n}/` | 10KB     | Weights history for combo  | Non-empty, matches parameter values                  |
| `{strategy}_Combo_{n}_portfolio_values_{timestamp}.csv` | `output/param_combo_{n}/` | 5KB      | Portfolio values for combo | Contains dates and portfolio values                  |

#### 5. Allocation Reports

| File                                          | Location                     | Min Size | Purpose           | Verification Criteria                                    |
| --------------------------------------------- | ---------------------------- | -------- | ----------------- | -------------------------------------------------------- |
| `{strategy}_allocation_{timestamp}.xlsx`      | `output/allocation_reports/` | 20KB     | Allocation report | Contains allocation data with proper formatting          |
| `{strategy}_allocation_chart_{timestamp}.png` | `output/allocation_reports/` | 100KB    | Allocation chart  | Image dimensions ≥ 1200x800, shows allocations over time |

### Automated Verification Process

1. **Run Test with Logging**
   
   - Execute test with `BACKTEST_LOG_LEVEL=DEBUG`
   - Capture all console output to `logs/v3_console_{timestamp}.log`

2. **File Existence Check**
   
   - Verify all expected log files exist
   - Verify all expected report files exist
   - Verify all expected data files exist

3. **File Size Verification**
   
   - Check all files against minimum size requirements
   - Flag any files below minimum size as potential errors

4. **Content Validation**
   
   - Check log files for expected entries and completion messages
   - Verify Excel files contain expected sheets
   - Validate image dimensions and content
   - Check CSV files for proper structure and non-empty content

5. **Error Reporting**
   
   - Generate verification report with pass/fail status for each file
   - List any missing or undersized files
   - Provide error details from log files
   - Recommend specific fixes based on failure patterns

### Implementation Plan

1. **Create Verification Script**
   
   - Develop `verify_v3_reporting.py` to automate verification process
   - Script should take timestamp as input to check specific test run
   - Output verification results to `logs/verification_{timestamp}.log`

2. **Enhance Logging System**
   
   - Update all modules to use consistent logging
   - Add try/except blocks with detailed error logging
   - Create central logging configuration

3. **Add Status Tracking**
   
   - Implement process status tracking in main modules
   - Record start/end of each major process
   - Log success/failure status for each component

4. **Create Test Wrapper**
   
   - Develop `run_v3_test_with_verification.bat` to run test and verification
   - Automatically capture console output
   - Run verification script after test completion
   - Display summary of results

## Next Actions

1. Run the GUI test with enhanced logging to validate current status
2. Apply fixes for HIGH priority items (signal_history, imports)
3. Run another test to verify improvements
4. Update this document with findings
5. Implement verification script to automate testing process
