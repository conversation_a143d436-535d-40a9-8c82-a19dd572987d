#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
v4/py_reporting/report_validation.py

Validation Export Module for CPS V4 Performance Reporting

This module contains all validation file export functionality extracted from v4_performance_report.py:
- Export validation files (CSV/TXT) for testing from real backtest data
- Signal history validation export
- Allocation history validation export  
- Performance metrics validation export
- Trade log validation export
- Portfolio values validation export

Functions included:
- export_validation_files(): Main validation export coordinator
- _export_signal_history(): Export signal history to CSV/TXT
- _export_allocation_history(): Export allocation history to CSV/TXT
- _export_performance_metrics(): Export performance metrics to TXT
- _export_trade_log(): Export trade log to CSV
- _export_portfolio_values(): Export portfolio values to CSV

Author: AI Assistant
Date: 2025-07-26 (Refactored from v4_performance_report.py)
"""

import os
import logging
import pandas as pd
from pathlib import Path
from typing import Dict, List, Any, Optional

# Set up logging
logger = logging.getLogger(__name__)


def export_validation_files(backtest_results: Dict[str, Any], output_dir: str, prefix: str = '') -> str:
    """
    Export validation files (CSV/TXT) for testing from real backtest data.
    This function is specifically designed for testing and validation purposes.
    
    Args:
        backtest_results: Dictionary containing backtest results with real data
        output_dir: Directory to save validation files
        prefix: Optional prefix for filenames
        
    Returns:
        str: Path to the validation directory
    """
    try:
        logger.info("Exporting validation files using real backtest data")
        
        # Create validation directory
        validation_dir = Path(output_dir) / "validation_data"
        validation_dir.mkdir(parents=True, exist_ok=True)
        
        # Export each component if available
        exported_files = []
        
        # Export signal history
        if 'signal_history' in backtest_results:
            signal_file = _export_signal_history(
                backtest_results['signal_history'], 
                validation_dir, 
                prefix
            )
            if signal_file:
                exported_files.append(signal_file)
        
        # Export allocation history
        if 'allocation_history' in backtest_results:
            allocation_file = _export_allocation_history(
                backtest_results['allocation_history'], 
                validation_dir, 
                prefix
            )
            if allocation_file:
                exported_files.append(allocation_file)
        
        # Export performance metrics
        if 'performance' in backtest_results:
            performance_file = _export_performance_metrics(
                backtest_results['performance'], 
                validation_dir, 
                prefix
            )
            if performance_file:
                exported_files.append(performance_file)
        
        # Export trade log
        if 'trades' in backtest_results:
            trade_file = _export_trade_log(
                backtest_results['trades'], 
                validation_dir, 
                prefix
            )
            if trade_file:
                exported_files.append(trade_file)
        
        # Export portfolio values
        if 'portfolio_values' in backtest_results:
            portfolio_file = _export_portfolio_values(
                backtest_results['portfolio_values'].get('Date', []),
                backtest_results['portfolio_values'].get('portfolio_value', []),
                validation_dir, 
                prefix
            )
            if portfolio_file:
                exported_files.append(portfolio_file)
        
        # Create summary file
        summary_file = validation_dir / f"{prefix}validation_summary.txt"
        with open(summary_file, 'w') as f:
            f.write("Validation Files Export Summary\n")
            f.write("=" * 40 + "\n\n")
            f.write(f"Export Directory: {validation_dir}\n")
            f.write(f"Files Exported: {len(exported_files)}\n\n")
            
            for file_path in exported_files:
                f.write(f"- {file_path}\n")
        
        logger.info(f"Exported {len(exported_files)} validation files to {validation_dir}")
        return str(validation_dir)
        
    except Exception as e:
        logger.error(f"Error exporting validation files: {e}")
        raise


def _export_signal_history(signal_history: Dict[str, Dict[str, float]], output_dir: Path, prefix: str = '') -> str:
    """
    Export signal history to CSV and TXT formats for validation.
    
    Args:
        signal_history: Dictionary with dates as keys and asset signals as values
        output_dir: Directory to save files
        prefix: Optional filename prefix
        
    Returns:
        str: Path to the exported CSV file
    """
    try:
        if not signal_history:
            logger.warning("No signal history data to export")
            return ""
        
        # Convert to DataFrame
        df = pd.DataFrame.from_dict(signal_history, orient='index')
        df.index.name = 'Date'
        df = df.reset_index()
        
        # Export CSV
        csv_path = output_dir / f"{prefix}signal_history.csv"
        df.to_csv(csv_path, index=False)
        
        # Export TXT (tab-separated for easy reading)
        txt_path = output_dir / f"{prefix}signal_history.txt"
        df.to_csv(txt_path, sep='\t', index=False)
        
        logger.debug(f"Signal history exported to {csv_path} and {txt_path}")
        return str(csv_path)
        
    except Exception as e:
        logger.error(f"Error exporting signal history: {e}")
        return ""


def _export_allocation_history(allocation_history: pd.DataFrame, output_dir: Path, prefix: str = '') -> str:
    """
    Export actual (post-trade) allocation history to CSV and TXT for validation.

    Args:
        allocation_history: DataFrame with allocation data
        output_dir: Directory to save files
        prefix: Optional filename prefix
        
    Returns:
        str: Path to the exported CSV file
    """
    try:
        if allocation_history.empty:
            logger.warning("No allocation history data to export")
            return ""
        
        # Export CSV
        csv_path = output_dir / f"{prefix}allocation_history.csv"
        allocation_history.to_csv(csv_path, index=False)
        
        # Export TXT (tab-separated)
        txt_path = output_dir / f"{prefix}allocation_history.txt"
        allocation_history.to_csv(txt_path, sep='\t', index=False)
        
        # Export summary statistics
        summary_path = output_dir / f"{prefix}allocation_summary.txt"
        with open(summary_path, 'w') as f:
            f.write("Allocation History Summary\n")
            f.write("=" * 30 + "\n\n")
            f.write(f"Total Records: {len(allocation_history)}\n")
            f.write(f"Date Range: {allocation_history.index[0]} to {allocation_history.index[-1]}\n")
            f.write(f"Assets: {list(allocation_history.columns)}\n\n")
            
            # Basic statistics
            f.write("Allocation Statistics:\n")
            f.write(allocation_history.describe().to_string())
        
        logger.debug(f"Allocation history exported to {csv_path}, {txt_path}, and {summary_path}")
        return str(csv_path)
        
    except Exception as e:
        logger.error(f"Error exporting allocation history: {e}")
        return ""


def _export_performance_metrics(performance: Dict[str, float], output_dir: Path, prefix: str = '') -> str:
    """
    Export performance metrics to TXT format for validation.
    
    Args:
        performance: Dictionary of performance metrics
        output_dir: Directory to save files
        prefix: Optional filename prefix
        
    Returns:
        str: Path to the exported TXT file
    """
    try:
        if not performance:
            logger.warning("No performance metrics to export")
            return ""
        
        # Export as formatted text
        txt_path = output_dir / f"{prefix}performance_metrics.txt"
        with open(txt_path, 'w') as f:
            f.write("Performance Metrics\n")
            f.write("=" * 20 + "\n\n")
            
            for metric, value in performance.items():
                f.write(f"{metric}: {value}\n")
        
        # Export as CSV for easy parsing
        csv_path = output_dir / f"{prefix}performance_metrics.csv"
        df = pd.DataFrame(list(performance.items()), columns=['Metric', 'Value'])
        df.to_csv(csv_path, index=False)
        
        logger.debug(f"Performance metrics exported to {txt_path} and {csv_path}")
        return str(txt_path)
        
    except Exception as e:
        logger.error(f"Error exporting performance metrics: {e}")
        return ""


def _export_trade_log(trades: List[Dict[str, Any]], output_dir: Path, prefix: str = '') -> str:
    """
    Export trade log to CSV format for validation.
    
    Args:
        trades: List of trade dictionaries
        output_dir: Directory to save files
        prefix: Optional filename prefix
        
    Returns:
        str: Path to the exported CSV file
    """
    try:
        if not trades:
            logger.warning("No trades to export")
            return ""
        
        # Convert to DataFrame
        df = pd.DataFrame(trades)
        
        # Export CSV
        csv_path = output_dir / f"{prefix}trade_log.csv"
        df.to_csv(csv_path, index=False)
        
        # Export summary
        summary_path = output_dir / f"{prefix}trade_summary.txt"
        with open(summary_path, 'w') as f:
            f.write("Trade Log Summary\n")
            f.write("=" * 20 + "\n\n")
            f.write(f"Total Trades: {len(trades)}\n")
            
            if 'action' in df.columns:
                f.write(f"Buy Trades: {len(df[df['action'] == 'buy'])}\n")
                f.write(f"Sell Trades: {len(df[df['action'] == 'sell'])}\n")
            
            if 'amount' in df.columns:
                f.write(f"Total Volume: {df['amount'].sum():.2f}\n")
        
        logger.debug(f"Trade log exported to {csv_path} and {summary_path}")
        return str(csv_path)
        
    except Exception as e:
        logger.error(f"Error exporting trade log: {e}")
        return ""


def _export_portfolio_values(dates: List[str], values: List[float], output_dir: Path, prefix: str = '') -> str:
    """
    Export portfolio values to CSV format for validation.
    
    Args:
        dates: List of date strings
        values: List of portfolio values
        output_dir: Directory to save files
        prefix: Optional filename prefix
        
    Returns:
        str: Path to the exported CSV file
    """
    try:
        if not dates or not values:
            logger.warning("No portfolio values to export")
            return ""
        
        # Create DataFrame
        df = pd.DataFrame({
            'Date': dates,
            'Portfolio_Value': values
        })
        
        # Export CSV
        csv_path = output_dir / f"{prefix}portfolio_values.csv"
        df.to_csv(csv_path, index=False)
        
        # Export summary
        summary_path = output_dir / f"{prefix}portfolio_summary.txt"
        with open(summary_path, 'w') as f:
            f.write("Portfolio Values Summary\n")
            f.write("=" * 25 + "\n\n")
            f.write(f"Total Records: {len(df)}\n")
            f.write(f"Date Range: {dates[0]} to {dates[-1]}\n")
            f.write(f"Starting Value: {values[0]:,.2f}\n")
            f.write(f"Ending Value: {values[-1]:,.2f}\n")
            f.write(f"Total Return: {((values[-1] / values[0]) - 1) * 100:.2f}%\n")
        
        logger.debug(f"Portfolio values exported to {csv_path} and {summary_path}")
        return str(csv_path)
        
    except Exception as e:
        logger.error(f"Error exporting portfolio values: {e}")
        return ""
