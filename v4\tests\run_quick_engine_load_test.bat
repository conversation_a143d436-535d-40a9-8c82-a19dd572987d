@echo off
REM ============================================================================
REM run_quick_engine_load_test.bat - Quick engine load sanity check
REM Activates virtual environment and runs quick_engine_load_test.py
REM ============================================================================

REM Activate virtual environment
set VENV_PATH=F:\AI_Library\my_quant_env
call "%VENV_PATH%\Scripts\activate.bat"

REM Run the engine load test script with output capture
python "%~dp0quick_engine_load_test.py" > "%~dp0quick_engine_load_test_output.txt" 2>&1

type "%~dp0quick_engine_load_test_output.txt"
REM End of script
