# V3 Parameter Flow Documentation

This document explains the flow of parameters in the V3 system, ensuring clarity, extensibility, and type safety from definition to reporting. All references are linked to the most relevant and recently updated documentation files.

---

## Table of Contents

1. [Overview](#overview)
2. [Parameter Definition & Registration](#parameter-definition--registration)
3. [Parameter Flow: GUI → Engine → Reports](#parameter-flow-gui--engine--reports)
4. [Type Safety & Validation](#type-safety--validation)
5. [Pluggable Strategy Architecture](#pluggable-strategy-architecture)
6. [Extensibility & Best Practices](#extensibility--best-practices)
7. [Related Documentation](#related-documentation)

---

## Overview

The V3 system uses a unified parameter handling architecture to ensure that all parameters—core and strategy-specific—are consistently defined, managed, and validated across the GUI, backtest engine, and reporting modules.

---

## Parameter Definition & Registration

- **Core Parameters**: Always present (e.g., `initial_capital`, `commission_rate`).
- **Strategy-Specific Parameters**: Swapped in/out with each strategy (e.g., `st_lookback`, `mt_lookback`).
- All parameters are registered in a central registry (`ParameterRegistry`), enforcing type safety and discoverability.
- Strategies declare their parameters via a standardized interface and self-register on initialization.

See: [V3 Parameter Classification](./v3_parameter_classification.md)

---

## Parameter Flow: GUI → Engine → Reports

1. **GUI**:
   - Reads all registered parameters and generates controls (sliders, dropdowns, etc.) based on type and constraints.
   - User selections are validated and passed as a parameter set.
2. **Engine**:
   - Receives parameter sets, validates types, and injects them into the selected strategy and core engine modules.
   - During optimization, loops over valid parameter combinations as defined in the registry.
3. **Reports**:
   - Performance reporting modules receive both core and strategy parameters for full traceability.
   - Only validated, type-safe parameter values are included in outputs.

See: [V3 User Guide](./v3_user_guide.md)

---

## Type Safety & Validation

- All parameter values are type-checked at every boundary (GUI, engine, reporting).
- Numeric parameters enforce min/max/step; categorical parameters enforce valid choices.
- The registry prevents unregistered or incorrectly typed parameters from entering the system.

---

## Pluggable Strategy Architecture

- Each strategy is a modular class that declares its own parameters and exposes a standardized interface.
- Switching strategies swaps in the relevant parameters and updates the GUI/engine/reporting automatically.
- Core parameters remain persistent across all strategies.

See: [V3 Module Mapping](./v3_module_mapping.md)

---

## Extensibility & Best Practices

- To add a new strategy, define its parameters and register them using the provided interface.
- Always document new parameters in the classification table and user guide.
- Use the registry to enforce type safety and discoverability.
- For transition plans and architectural notes, see: [V3 Transition Plan](./v3_transition_plan.md)

---

## Related Documentation

- [V3 User Guide](./v3_user_guide.md)
- [V3 Parameter Classification](./v3_parameter_classification.md)
- [V3 Module Mapping](./v3_module_mapping.md)
- [V3 Transition Plan](./v3_transition_plan.md)

---

*Last updated: 2025-04-29*
