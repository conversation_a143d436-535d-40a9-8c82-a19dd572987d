History of Backtest Engine Issues

Backtest Allocation Report and Logging Issues
#backtest
#logging
#debug
#signal_history
#allocation_report

## Backtest Report Generation Issues

1. **Signal History Problem**: 
   - Signal history is not being properly populated or preserved in the backtest engine
   - Attempted fix: Added recovery logic and forward-fill, but issue persists
   - Error: "signal_history is None or empty, allocation report/graph may not be produced"

2. **Excessive Logging Issue**:
   - Trade/portfolio update messages still appearing at INFO level despite changing to DEBUG
   - Need to update all logging calls in portfolio.py and execution.py modules
   - Specific log messages like "[date] Updated portfolio from trade" and "New cash balance" need to be set to DEBUG level

3. **Next Steps**:
   - Check BacktestEngine class to ensure signal_history is properly returned in results
   - Update all logging calls in portfolio.py and execution.py to use DEBUG level
   - Consider implementing a global logging configuration that can be applied to all modules

V3 Parameter System and Reporting Issues
#v3_parameter_system
#reporting
#gui
#optimization
#backtest
Edit
The V3 parameter system has several key components:

1. StrategyOptimizeParameter - This is the correct class for parameters that should be optimizable and appear in reports. We should not replace these with NumericParameter or other parameter types.

2. Parameter Registration - Parameters need to be registered correctly to appear in the GUI. Core parameters should be registered directly to the 'core' group.

3. Reporting Issues - Several reporting modules have missing imports or undefined loggers:
   - v3_allocation_report.py: Missing matplotlib.pyplot import
   - V3_perf_repadapt_metrics.py: Missing logger definition
   - performance_reporter_adapter.py: Missing logger definition
   - v3_performance_report.py: Incorrect import for generate_performance_report

4. No Fallbacks - The system should not rely on fallback mechanisms for reporting. The primary reporting path should work correctly.

5. GUI Display - Parameters with show_in_gui=True should appear in the appropriate section of the GUI.

These issues need to be fixed systematically to ensure all reports are generated correctly.

plug-and-Play Strategy Parameter Architecture: Next Steps
#architecture
#parameter_handling
#strategy_plugin
#type_safety
#documentation

To achieve seamless plug-and-play strategies with variable parameters that flow through the GUI, backtest engine, and reporting, the following architecture improvements are required:

1. **Unified Parameter Handling System**: Develop a single source of truth for parameter definitions (types, defaults, optimization flags, etc.) with a consistent interface for all components.
2. **Strong Type Checking & Validation**: Enforce parameter types and constraints at every boundary (GUI, engine, reporting) to prevent silent errors and ensure reliability.
3. **Parameter Flow Documentation**: Clearly document how parameters are defined, passed, transformed, and consumed from GUI → engine → reports, including edge cases and extensibility guidelines.
4. **Pluggable Strategy Architecture**: Design strategies as modular classes/functions that declare their required parameters and expose a standardized interface for the system to discover and inject parameters automatically.

This is a critical next step for scaling the platform to support new strategies and robust optimization workflows.

Prompt: Ask user if they'd like help designing this improved architecture for parameter handling.