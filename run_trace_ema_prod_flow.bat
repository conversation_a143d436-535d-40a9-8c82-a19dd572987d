@echo off
REM run_trace_ema_prod_flow.bat - <PERSON>rip<PERSON> to run trace_ema_production_flow.py

REM Explicitly set current directory to where this batch script is (Project Root)
cd /D "%~dp0"

REM Activate virtual environment using absolute path
call F:\AI_Library\my_quant_env\Scripts\activate.bat

REM Run the Python script (now in Project Root)
REM Redirect Python's stdout and stderr to a log file in ProjectRoot/logs
REM Create logs directory if it doesn't exist (simple check)
if not exist ".\logs" mkdir ".\logs"
python trace_ema_production_flow.py > .\logs\trace_ema_PY_CONSOLE_OUTPUT.txt 2>&1

echo Batch script finished. Python console output (if any) is in .\logs\trace_ema_PY_CONSOLE_OUTPUT.txt
echo ERRORLEVEL is %ERRORLEVEL%
if %ERRORLEVEL% NEQ 0 (
    echo Python script reported an error. Check the log.
) else (
    echo Python script completed.
)
