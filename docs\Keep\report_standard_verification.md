# V3 Report Standard Verification Template

**Last Updated**: 2025-06-03  
**Status**: Initial Template  
**Related Documents**:
- [Report Standards PRD](../PRD%20-%20Fixing%20Report%20out%20standard%20053125.md)
- [Parameter System Refactoring](parameter_system_refactoring_v2.md)
- [Implementation Changelog](implementation_changelog.md)

## Purpose

This document provides a systematic framework for verifying V3 report compliance with standards defined in the PRD. It serves as both a verification checklist and a tracking tool for deviations that need to be addressed during the parameter system refactoring.

## Verification Process

1. Generate test reports using `run_ema_v3_gui_test.bat`
2. Locate output files in the designated output directory
3. Compare each report against the standards in this document
4. Document deviations in the "Current Status" column
5. Update "Resolution Plan" with specific code changes needed
6. Track progress in the "Status" column

## Performance Table Report (XLSX)

| Standard | Requirement | Current Status | Resolution Plan | Status |
|----------|-------------|----------------|-----------------|--------|
| **Filename** | EMA_V3_1_performance_tables_YYYYMMDD_HHMMSS.xlsx | | | |
| **Signal History Tab** | | | | |
| - Date Format | YYYY-MM-DD, no timestamps | | | |
| - Columns | Tickers + Cash | | | |
| - Allocation | Sum to 100% per row | | | |
| - Formatting | 0.00% | | | |
| **Allocation History Tab** | | | | |
| - Structure | Matches Signal History | | | |
| - Allocations | Actual capital with 1-day lag | | | |
| - Completeness | Each row sums to 100%, no gaps | | | |
| - Formatting | 0.00% | | | |
| **Trade Log Tab** | | | | |
| - Columns | trade_num, symbol, quantity, execution_date, execution_price, commission+slippage, amount, pnl | | | |
| - Sorting | By execution_date and trade_num | | | |
| - Completeness | All executed trades included | | | |
| **Header** | Cell A1 displays main parameters | | | |
| **Performance Tab** | | | | |
| - Structure | Parameters left, metrics right | | | |
| - Rows | One per parameter combination + benchmark | | | |
| - Formatting | Percentages (0.00%), Ratios (0.00), Currency ($#,##0.00) | | | |

## Monthly & Annual Returns Graphic (PNG)

| Standard | Requirement | Current Status | Resolution Plan | Status |
|----------|-------------|----------------|-----------------|--------|
| **Filename** | EMA_V3_1_monthly_returns_YYYYMMDD_HHMMSS.png | | | |
| **Content** | | | | |
| - Heatmap | Monthly returns (years as rows, months as columns) | | | |
| - Annual Returns | Single column next to heatmap | | | |
| - Color Legend | Return scale (e.g., -5% to +8%) | | | |
| **Formatting** | | | | |
| - Title | "Monthly Returns" | | | |
| - Axis Labels | Year (rows), Month (columns: Jan–Dec) | | | |
| - Values | Percentages (0.00%), no missing months/years | | | |
| - Resolution | High DPI (≥300, ideally 600) | | | |
| **Audit** | Matches Allocation History and Trade Log | | | |

## Combined Cumulative Returns & Drawdown Graphic (PNG)

| Standard | Requirement | Current Status | Resolution Plan | Status |
|----------|-------------|----------------|-----------------|--------|
| **Filename** | EMA_V3_1_cumulative_returns_drawdown_YYYYMMDD_HHMMSS.png | | | |
| **Content** | | | | |
| - Top Panel | Cumulative returns for strategy and benchmark | | | |
| - Bottom Panel | Drawdowns over the same period | | | |
| **Formatting** | | | | |
| - Title | "Cumulative Returns & Drawdown" | | | |
| - Axis Labels | Date (x), Percentage (y) | | | |
| - Legend | Strategy, Benchmark | | | |
| - Resolution | High DPI (≥300, ideally 600) | | | |

## Parameter Flow Verification

| Parameter | GUI → Registry | Registry → Adapter | Adapter → Reports | Reports Display |
|-----------|----------------|-------------------|-------------------|-----------------|
| st_lookback | | | | |
| mt_lookback | | | | |
| lt_lookback | | | | |
| execution_delay | | | | |
| top_n | | | | |
| create_excel | | | | |
| create_charts | | | | |
| [Additional parameters] | | | | |

## Signal History Verification

| Check Point | Expected | Current Status | Resolution Plan | Status |
|-------------|----------|----------------|-----------------|--------|
| Creation in backtest.py | Signal history created and populated | | | |
| Passing to adapter | Signal history preserved in adapter | | | |
| Usage in allocation_report.py | Signal history used for report generation | | | |
| Final report content | Signal history appears in Excel tab | | | |

## Verification Script Requirements

The automated verification script (`report_verification.py`) should check:

1. **File Existence and Size**
   - Excel file exists and is ≥50KB
   - PNG files exist and are ≥100KB

2. **Excel Structure**
   - All required tabs present
   - Correct column headers
   - Proper formatting applied

3. **Data Integrity**
   - Signal History rows sum to 100%
   - Allocation History rows sum to 100%
   - Trade Log is complete and sorted correctly

4. **Parameter Flow**
   - Parameters from registry appear in report headers
   - All required parameters are present

## Next Steps

1. Run initial report generation to establish baseline
2. Complete the "Current Status" column for all items
3. Prioritize deviations based on visibility and impact
4. Develop resolution plan for each deviation
5. Implement fixes according to the parameter system refactoring plan
6. Re-verify after each major change

## Progress Tracking

| Date | Changes Made | Deviations Fixed | Remaining Issues |
|------|--------------|------------------|------------------|
| 2025-06-03 | Initial template created | N/A | Full assessment needed |
|  |  |  |  |
