# V3 Parameter System Reference

This document provides a comprehensive reference for the V3 parameter system, including parameter classes, registry methods, parameter mappings, and implementation guidelines.

## Parameter System Core Components

### Parameter Registry Methods

| Method                                       | Description                                   | Usage                                                    |
| -------------------------------------------- | --------------------------------------------- | -------------------------------------------------------- |
| `register_parameter(group, parameter)`       | Register a single parameter with the registry | For registering individual parameters                    |
| `register_parameter_list(group, parameters)` | Register multiple parameters at once          | **Preferred method** for registering multiple parameters |
| `get_parameter(name, group=None)`            | Get a parameter by name                       | Retrieve a parameter for use                             |
| `get_parameters(group)`                      | Get all parameters in a group                 | Retrieve all parameters in a specific group              |
| `get_parameter_values(group)`                | Get parameter values as a dictionary          | Get parameter values for use in backtest                 |
| `get_core_parameters()`                      | Get all core parameters                       | Get engine parameters that are always present            |
| `get_strategy_parameters()`                  | Get all strategy parameters                   | Get parameters from groups starting with 'strategy_'     |
| `get_all_parameters()`                       | Get all parameters                            | Get all parameters from all groups                       |
| `clear_group(group)`                         | Clear all parameters in a group               | Used during re-registration of parameters                |

### Parameter Class Hierarchy

The V3 parameter system uses a class hierarchy to represent different types of parameters with varying behaviors:

```
BaseParameter
├── NumericParameter
├── CategoricalParameter
│   └── CategoricalListParameter
├── ConfigParameter
└── StrategyOptimizeParameter
```

### Parameter Classes and Their Uses

| Class                         | Description                                         | GUI Visible | Optimizable | Reportable | Use Cases                                              |
| ----------------------------- | --------------------------------------------------- |:-----------:|:-----------:|:----------:| ------------------------------------------------------ |
| **BaseParameter**             | Base class for all parameters                       | Yes         | No          | No         | Default for any non-defined variables                  |
| **NumericParameter**          | For numeric values with min/max/step                | Yes         | Yes         | No         | Lookback periods, thresholds, execution delay          |
| **CategoricalParameter**      | For selection from fixed options                    | Yes         | Yes         | No         | Rebalance frequency, strategy selection                |
| **CategoricalListParameter**  | For user-defined groups in config files             | Yes         | Yes         | No         | Ticker groups, asset classes                           |
| **ConfigParameter**           | For config-only values, not in GUI                  | No          | No          | No         | Initial capital, commission rate, slippage rate        |
| **StrategyOptimizeParameter** | Enhanced strategy parameters with reporting control | Yes         | Yes         | Yes        | Strategy-specific parameters needing reporting control |

## Parameter Flow

```mermaid
graph TD
  GUI -->|Parameters| Engine
  Engine -->|Results + Parameters| V3_Reporting
  V3_Reporting -->|Formatted Output| Reports
```

Parameters flow through the system in the following way:

1. **Definition**: Parameters are defined in their respective registration functions
2. **Registration**: Parameters are registered with the V3 registry
3. **GUI Integration**: Parameters marked with `show_in_gui=True` appear in the GUI
4. **Value Setting**: Parameter values are set via GUI or directly in code
5. **Engine Usage**: Parameters are passed to the backtest engine
6. **Reporting**: Parameter values are included in performance reports

## Parameter Matrix

### Data Parameters (`data` group)

| Parameter          | Class            | Description                       | Default Value                         | GUI Visible | Optimizable | Status       |
| ------------------ | ---------------- | --------------------------------- | ------------------------------------- |:-----------:|:-----------:| ------------ |
| `data_storage_mode`| ConfigParameter  | Data storage mode                 | `'Read'`                              | No          | No          | Implemented  |
| `start_date`       | ConfigParameter  | Start date for backtest           | `'2020-01-01'`                        | No          | No          | Implemented  |
| `end_date`         | ConfigParameter  | End date for backtest             | `'2025-04-23'`                        | No          | No          | Implemented  |
| `price_field`      | ConfigParameter  | Price field for calculations      | `'Close'`                             | No          | No          | Implemented  |
| `risk_free_ticker` | ConfigParameter  | Ticker for risk-free rate         | `'^IRX'`                              | No          | No          | Implemented  |

### Core Engine Parameters (`core` group)

| Parameter          | Class                     | Description                       | Default Value | GUI Visible | Optimizable | Status       |
| ------------------ | ------------------------- | --------------------------------- | ------------- |:-----------:|:-----------:| ------------ |
| `initial_capital`  | ConfigParameter           | Initial capital for portfolio     | `1000000`     | No          | No          | Implemented  |
| `commission_rate`  | ConfigParameter           | Commission rate for trades        | `0.001`       | No          | No          | Implemented  |
| `slippage_rate`    | ConfigParameter           | Slippage rate for trades          | `0.001`       | No          | No          | Implemented  |
| `execution_delay`  | StrategyOptimizeParameter | Trade execution delay in days     | `1`           | Yes         | Yes         | Implemented  |
| `rebalance_freq`   | CategoricalParameter      | Portfolio rebalancing frequency   | `'weekly'`    | Yes         | Yes         | Implemented  |

### EMA Strategy Parameters (`strategy_ema` group)

| Parameter      | Class                     | Description                   | Default Value     | GUI Visible | Optimizable | Status      |
| -------------- | ------------------------- | ----------------------------- | ----------------- |:-----------:|:-----------:| ----------- |
| `st_lookback`  | StrategyOptimizeParameter | Short-term EMA lookback       | `20`              | Yes         | Yes         | Implemented |
| `mt_lookback`  | StrategyOptimizeParameter | Medium-term EMA lookback      | `60`              | Yes         | Yes         | Implemented |
| `lt_lookback`  | StrategyOptimizeParameter | Long-term EMA lookback        | `120`             | Yes         | Yes         | Implemented |
| `top_n`        | StrategyOptimizeParameter | Number of top assets to hold  | `2`               | Yes         | Yes         | Implemented |
| `signal_algo`  | StrategyOptimizeParameter | Signal generation algorithm   | `'ema_crossover'` | Yes         | Yes         | Implemented |
| `tickers`      | StrategyOptimizeParameter | Ticker group for strategy     | Ticker list       | Yes         | Yes         | Implemented |

### Reporting Parameters (Future Implementation)

| Parameter         | Class           | Description                      | Default Value                                            | GUI Visible | Optimizable | Status      |
| ----------------- | --------------- | -------------------------------- | -------------------------------------------------------- |:-----------:|:-----------:| ----------- |
| `create_excel`    | ConfigParameter | Whether to create Excel reports  | `True`                                                   | No          | No          | Planned     |
| `save_trade_log`  | ConfigParameter | Whether to save trade logs       | `True`                                                   | No          | No          | Planned     |
| `chart_dpi`       | ConfigParameter | DPI for output charts            | `300`                                                    | No          | No          | Implemented |
| `trade_log_format`| ConfigParameter | Format for trade log output      | `'csv'`                                                  | No          | No          | Implemented |
| `report_benchmark`| ConfigParameter | Benchmark ticker for comparison  | `'SPY'`                                                  | No          | No          | Implemented |
| `metrics`         | ConfigParameter | Performance metrics to include   | `['total_return', 'annualized_return', ..., 'win_rate']` | No          | No          | Planned     |

## V3 Reporting System

### Core Modules

1. **Parameter Registration** (`v3_engine/reporting_parameters.py`)
   - Central registry for all reporting parameters
   - Handles parameter types, defaults and validation
   - Integrated with main parameter registry

2. **Reporting Adapter** (`v3_engine/performance_reporter_adapter.py`)
   - Bridges V3 parameters to legacy reporting code
   - Handles parameter conversion and validation
   - Maintains backward compatibility

3. **Reporting Modules** (`v3_reporting/`)
   - `v3_performance_report.py`: Performance reporting wrapper
   - `v3_allocation_report.py`: Allocation reporting wrapper
   - `v3_visualization.py`: Chart generation wrapper

4. **Validation Utilities** (`v3_engine/data_validator.py`)
   - Validates data and signal/allocation history

### Error Handling Features

- Automatic parameter type conversion
- Fallback logic for missing signal history
- Graceful degradation when legacy features are unavailable

### Optimization Support

- Proper handling of parameter optimization tuples
- Preservation of optimization context through reporting chain
- Clear labeling of optimized parameters in output

## Parameter Conversion Guidelines

When converting config variables to parameter classes:

1. Use **ConfigParameter** for variables that:
   - Should not appear in the GUI
   - Should not be optimized
   - Are loaded from config
   - Should appear in performance reports

2. Use **NumericParameter** for variables that:
   - Have numeric values with min/max bounds
   - May need optimization
   - Should appear in the GUI

3. Use **CategoricalParameter** for variables that:
   - Have a fixed set of options
   - May need optimization over those options
   - Should appear in the GUI

4. Use **BaseParameter** as a fallback for any other variables

5. Use **StrategyOptimizeParameter** for strategy-specific parameters that:
   - Need reporting control
   - Are always reportable
   - Are always optimizable
   - Are always GUI visible

## Implementation Status and Next Steps

### Current Status

- Core parameter system implemented
- Parameter registry implemented
- EMA strategy parameters converted to V3 format
- GUI integration for core and EMA parameters
- Basic reporting adapter implemented

### Next Steps

1. **Complete Conversion of Legacy Parameters**
   - Convert remaining visualization parameters
   - Convert remaining reporting parameters
   - Ensure all data parameters use appropriate classes

2. **Strategy Parameter Standardization**
   - Create standardized structure for strategy parameter sets
   - Implement plug-and-play capability for new strategies
   - Design strategy selector for GUI

3. **Reporting System Enhancement**
   - Improve performance reporter adapter for better parameter handling
   - Enhance chart generation with V3 parameters
   - Standardize allocation reporting
