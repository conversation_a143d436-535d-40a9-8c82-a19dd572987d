#!/usr/bin/env python
# -*- coding: utf-8 -*-
# report_compliance_utils.py
"""
Utility functions for report compliance verification and organization.

This module provides helper functions to verify that reports meet the
compliance standards outlined in reporting_output_requirements_v4.md.

Author: AI Assistant
Date: 2025-06-12
"""

import os
import sys
import logging
import datetime
import pandas as pd
import numpy as np
from pathlib import Path
import openpyxl
from typing import Dict, Any, List, Tuple, Optional

# Set up logging
logger = logging.getLogger(__name__)

# Add the project root to the path if needed
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# Constants for report compliance
REQUIRED_TABS = [
    'Signal History',
    'Allocation History',
    'Trade Log',
    'Performance',
]

REQUIRED_PERFORMANCE_METRICS = [
    'CAGR',
    'Volatility',
    'Sharpe Ratio',
    'Max Drawdown',
    'Win Rate',
    'Profit Factor',
]

def verify_report_structure(report_path: str) -> Tuple[bool, Dict[str, bool]]:
    """
    Verify the structure of a performance report against compliance standards.
    
    Args:
        report_path: Path to the Excel report file
        
    Returns:
        Tuple containing:
            - Boolean indicating overall compliance
            - Dictionary with detailed results for each check
    """
    if not os.path.exists(report_path):
        logger.error(f"Report file not found: {report_path}")
        return False, {"file_exists": False}
    
    results = {
        "file_exists": True,
        "tabs_present": False,
        "signal_history_format": False,
        "allocation_history_format": False,
        "trade_log_format": False,
        "performance_metrics": False
    }
    
    try:
        # Load workbook
        wb = openpyxl.load_workbook(report_path, read_only=True)
        
        # Check tabs
        missing_tabs = [tab for tab in REQUIRED_TABS if tab not in wb.sheetnames]
        results["tabs_present"] = len(missing_tabs) == 0
        if not results["tabs_present"]:
            logger.error(f"Missing tabs: {missing_tabs}")
        
        # Check Signal History format
        if 'Signal History' in wb.sheetnames:
            results["signal_history_format"] = verify_signal_history_tab(wb['Signal History'])
        
        # Check Allocation History format
        if 'Allocation History' in wb.sheetnames:
            results["allocation_history_format"] = verify_allocation_history_tab(wb['Allocation History'])
        
        # Check Trade Log format
        if 'Trade Log' in wb.sheetnames:
            results["trade_log_format"] = verify_trade_log_tab(wb['Trade Log'])
        
        # Check Performance metrics
        if 'Performance' in wb.sheetnames:
            results["performance_metrics"] = verify_performance_tab(wb['Performance'])
        
        # Overall compliance
        overall_compliance = all(results.values())
        
        return overall_compliance, results
    
    except Exception as e:
        logger.error(f"Error verifying report structure: {str(e)}")
        return False, results

def verify_signal_history_tab(worksheet) -> bool:
    """
    Verify the Signal History tab format and content.
    
    Args:
        worksheet: openpyxl worksheet object
        
    Returns:
        Boolean indicating compliance
    """
    try:
        # Check header row
        header_row = [cell.value for cell in next(worksheet.rows)]
        if not header_row or 'Date' not in header_row:
            logger.error("Signal History missing Date column")
            return False
        
        # Check for asset columns (at least one asset + cash)
        if len(header_row) < 3:  # Date + at least one asset + cash
            logger.error("Signal History missing asset columns")
            return False
        
        # Check data rows (at least some data)
        data_rows = list(worksheet.iter_rows(min_row=2, values_only=True))
        if len(data_rows) < 1:
            logger.error("Signal History has no data rows")
            return False
        
        # Check allocation sums (should be close to 1.0 or 100%)
        for i, row in enumerate(data_rows, start=2):
            # Skip rows with no date
            if not row[0]:
                continue
                
            # Get numeric values, skipping the date column
            values = [v for v in row[1:] if isinstance(v, (int, float))]
            if not values:
                continue
                
            # Check if values are percentages (>1) or decimals (<1)
            is_percentage = any(v > 1.0 for v in values)
            row_sum = sum(values)
            
            # Validate sum (100% or 1.0)
            if is_percentage and abs(row_sum - 100.0) > 1.0:
                logger.error(f"Signal History row {i} sum is {row_sum}%, not 100%")
                return False
            elif not is_percentage and abs(row_sum - 1.0) > 0.01:
                logger.error(f"Signal History row {i} sum is {row_sum}, not 1.0")
                return False
        
        return True
    
    except Exception as e:
        logger.error(f"Error verifying Signal History tab: {str(e)}")
        return False

def verify_allocation_history_tab(worksheet) -> bool:
    """
    Verify the Allocation History tab format and content.
    
    Args:
        worksheet: openpyxl worksheet object
        
    Returns:
        Boolean indicating compliance
    """
    try:
        # Check header row
        header_row = [cell.value for cell in next(worksheet.rows)]
        if not header_row or 'Date' not in header_row:
            logger.error("Allocation History missing Date column")
            return False
        
        # Check for asset columns (at least one asset + cash)
        if len(header_row) < 3:  # Date + at least one asset + cash
            logger.error("Allocation History missing asset columns")
            return False
        
        # Check data rows (at least some data)
        data_rows = list(worksheet.iter_rows(min_row=2, values_only=True))
        if len(data_rows) < 1:
            logger.error("Allocation History has no data rows")
            return False
        
        # Check allocation sums (should be close to 1.0 or 100%)
        for i, row in enumerate(data_rows, start=2):
            # Skip rows with no date
            if not row[0]:
                continue
                
            # Get numeric values, skipping the date column
            values = [v for v in row[1:] if isinstance(v, (int, float))]
            if not values:
                continue
                
            # Check if values are percentages (>1) or decimals (<1)
            is_percentage = any(v > 1.0 for v in values)
            row_sum = sum(values)
            
            # Validate sum (100% or 1.0)
            if is_percentage and abs(row_sum - 100.0) > 1.0:
                logger.error(f"Allocation History row {i} sum is {row_sum}%, not 100%")
                return False
            elif not is_percentage and abs(row_sum - 1.0) > 0.01:
                logger.error(f"Allocation History row {i} sum is {row_sum}, not 1.0")
                return False
        
        return True
    
    except Exception as e:
        logger.error(f"Error verifying Allocation History tab: {str(e)}")
        return False

def verify_trade_log_tab(worksheet) -> bool:
    """
    Verify the Trade Log tab format and content.
    
    Args:
        worksheet: openpyxl worksheet object
        
    Returns:
        Boolean indicating compliance
    """
    try:
        # Check header row
        header_row = [str(cell.value).lower() if cell.value else '' for cell in next(worksheet.rows)]
        
        # Required columns
        required_cols = ['date', 'ticker', 'action', 'quantity', 'price', 'total']
        missing_cols = [col for col in required_cols if not any(col in h for h in header_row)]
        
        if missing_cols:
            logger.error(f"Trade Log missing required columns: {missing_cols}")
            return False
        
        # Check data rows (at least some data)
        data_rows = list(worksheet.iter_rows(min_row=2, values_only=True))
        if len(data_rows) < 1:
            logger.warning("Trade Log has no trades (empty)")
            # Not a failure - strategy might not have trades
        
        return True
    
    except Exception as e:
        logger.error(f"Error verifying Trade Log tab: {str(e)}")
        return False

def verify_performance_tab(worksheet) -> bool:
    """
    Verify the Performance tab format and content.
    
    Args:
        worksheet: openpyxl worksheet object
        
    Returns:
        Boolean indicating compliance
    """
    try:
        # Extract all cell values to search for metrics
        all_values = [cell.value for row in worksheet.rows for cell in row]
        all_values = [str(v).lower() if v is not None else '' for v in all_values]
        
        # Check for required metrics
        missing_metrics = []
        for metric in REQUIRED_PERFORMANCE_METRICS:
            metric_lower = metric.lower()
            if not any(metric_lower in val for val in all_values):
                missing_metrics.append(metric)
        
        if missing_metrics:
            logger.error(f"Performance tab missing required metrics: {missing_metrics}")
            return False
        
        return True
    
    except Exception as e:
        logger.error(f"Error verifying Performance tab: {str(e)}")
        return False

def verify_graphics_output(output_dir: str) -> Tuple[bool, Dict[str, bool]]:
    """
    Verify the presence and basic properties of required graphics files.
    
    Args:
        output_dir: Directory containing the output files
        
    Returns:
        Tuple containing:
            - Boolean indicating overall compliance
            - Dictionary with detailed results for each check
    """
    results = {
        "monthly_returns_graphic": False,
        "cumulative_returns_graphic": False
    }
    
    # Check for monthly returns graphic
    monthly_returns_files = list(Path(output_dir).glob("*monthly*returns*.png"))
    results["monthly_returns_graphic"] = len(monthly_returns_files) > 0
    
    if not results["monthly_returns_graphic"]:
        logger.error("Monthly returns graphic not found")
    else:
        # Check file size (should be at least 10KB for a proper chart)
        file_size = os.path.getsize(monthly_returns_files[0])
        if file_size < 10 * 1024:
            logger.warning(f"Monthly returns graphic is suspiciously small: {file_size} bytes")
    
    # Check for cumulative returns graphic
    cumulative_files = list(Path(output_dir).glob("*cumulative*returns*.png"))
    results["cumulative_returns_graphic"] = len(cumulative_files) > 0
    
    if not results["cumulative_returns_graphic"]:
        logger.error("Cumulative returns graphic not found")
    else:
        # Check file size (should be at least 10KB for a proper chart)
        file_size = os.path.getsize(cumulative_files[0])
        if file_size < 10 * 1024:
            logger.warning(f"Cumulative returns graphic is suspiciously small: {file_size} bytes")
    
    # Overall compliance
    overall_compliance = all(results.values())
    
    return overall_compliance, results

def generate_compliance_report(report_path: str, output_dir: str) -> str:
    """
    Generate a comprehensive compliance report for a performance report.
    
    Args:
        report_path: Path to the Excel report file
        output_dir: Directory containing output files (for graphics)
        
    Returns:
        Path to the generated compliance report file
    """
    # Verify report structure
    structure_compliance, structure_results = verify_report_structure(report_path)
    
    # Verify graphics output
    graphics_compliance, graphics_results = verify_graphics_output(output_dir)
    
    # Overall compliance
    overall_compliance = structure_compliance and graphics_compliance
    
    # Create compliance report
    report_dir = os.path.dirname(report_path)
    compliance_report_path = os.path.join(report_dir, "compliance_report.txt")
    
    with open(compliance_report_path, "w") as f:
        f.write(f"Compliance Report for {os.path.basename(report_path)}\n")
        f.write(f"Date: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        f.write("1. Report Structure Compliance\n")
        f.write(f"   Overall: {'PASSED' if structure_compliance else 'FAILED'}\n")
        for check, result in structure_results.items():
            f.write(f"   - {check}: {'PASSED' if result else 'FAILED'}\n")
        
        f.write("\n2. Graphics Output Compliance\n")
        f.write(f"   Overall: {'PASSED' if graphics_compliance else 'FAILED'}\n")
        for check, result in graphics_results.items():
            f.write(f"   - {check}: {'PASSED' if result else 'FAILED'}\n")
        
        f.write("\nOverall Compliance: ")
        f.write("PASSED" if overall_compliance else "FAILED")
    
    return compliance_report_path

if __name__ == "__main__":
    # Simple test to verify module loads correctly
    print("Report Compliance Utils module loaded successfully")
