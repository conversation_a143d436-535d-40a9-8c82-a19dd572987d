#!/usr/bin/env python
# -*- coding: utf-8 -*-
# simple_console_test.py
"""
Extremely simple test script with verbose print statements at every step
to trace the execution flow of the backtest engine.
Uses REAL data and settings from configuration files.

Author: AI Assistant
Date: 2025-06-14
"""

# Force immediate output to console
import sys
import os
import pathlib
import builtins # For direct print before logging is configured

# --- AGGRESSIVE PATH PREPEND ---
# Get the absolute path of the current script
_current_script_path = pathlib.Path(__file__).resolve()
# Assuming the script is in 'v4/tests/', go up two levels to get the project root
_project_root = _current_script_path.parent.parent.parent 
# Prepend the project root to sys.path
if str(_project_root) not in sys.path:
    sys.path.insert(0, str(_project_root))
# Prepend the v4 directory as well, just in case
_v4_dir = _project_root / 'v4'
if str(_v4_dir) not in sys.path:
    sys.path.insert(0, str(_v4_dir)) # Insert v4 also, to be very sure

builtins.print(f"TOP_LEVEL_DEBUG: Project root = {str(_project_root)}")
builtins.print(f"TOP_LEVEL_DEBUG: v4 dir = {str(_v4_dir)}")
builtins.print(f"TOP_LEVEL_DEBUG: sys.path[0] = {sys.path[0]}")
builtins.print(f"TOP_LEVEL_DEBUG: sys.path[1] = {sys.path[1] if len(sys.path) > 1 else 'N/A'}")
builtins.print(f"TOP_LEVEL_DEBUG: Full sys.path = {sys.path}")
# --- END AGGRESSIVE PATH PREPEND ---

import logging
import io
import pandas as pd
import numpy as np
from datetime import datetime
from pathlib import Path # Ensure Path is imported from pathlib

class EncodedStream(io.TextIOWrapper):
    def __init__(self, buffer, encoding):
        super().__init__(buffer, encoding=encoding, 
                        errors='replace', line_buffering=True)
        
    def write(self, s):
        # Force ASCII for console, UTF-8 for files
        if self.encoding == 'utf-8':
            super().write(s)
        else:
            super().write(s.encode('ascii', errors='replace').decode('ascii'))

# Setup logging first            
log_path = Path(__file__).parent / 'terminal_log.txt'
logging.basicConfig(
    level=logging.INFO,
    format='%(message)s',
    handlers=[logging.FileHandler(log_path, mode='w', encoding='utf-8')]
)

# Replace standard streams
sys.stdout = EncodedStream(sys.stdout.buffer, 'utf-8')  
sys.stderr = EncodedStream(sys.stderr.buffer, 'utf-8')

# Override print at Python level
_original_print = builtins.print
def safe_print(*args, **kwargs):
    file = kwargs.get('file', sys.stdout)
    _original_print(*args, **kwargs)
    if file in (sys.stdout, sys.stderr):
        logging.info(' '.join(str(arg) for arg in args))
builtins.print = safe_print

import os
from functools import partial

# ASCII status markers
PASS = '[PASS]'
FAIL = '[FAIL]'
WARN = '[WARN]'
INFO = '[INFO]' # Define INFO status marker

# Custom print function that handles both console and file output
def flushed_print(*args, **kwargs):
    """Print function that forces immediate flush to console"""
    print(*args, **kwargs)
    sys.stdout.flush()

flushed_print("=" * 80)
print("SCRIPT STARTED: simple_console_test.py")
flushed_print("=" * 80)

flushed_print("\nSTEP 1: Importing basic modules - (Handled by top-level imports now)")
try:
    # Verification of imports already done by Python interpreter at script start
    flushed_print(f"{PASS} Basic modules (sys, os, pathlib) assumed loaded due to top-level imports.")
except Exception as e: # Should not happen if script loaded
    flushed_print(f"{FAIL} ERROR verifying basic modules: {e}")
    flushed_print('Continuing after error.')

flushed_print("\nSTEP 2: Path setup (Handled by AGGRESSIVE PATH PREPEND at top)")
try:
    flushed_print(f"{INFO} sys.path aggressively prepended at script start.")
    # The builtins.print calls at the top show the sys.path status
except Exception as e:
    flushed_print(f"{FAIL} ERROR verifying path setup: {e}") # Should not happen
    flushed_print('Continuing after error.')

flushed_print("\nSTEP 3: Importing data manipulation modules")
try:
    import pandas as pd
    import numpy as np
    from datetime import datetime
    flushed_print(f"{PASS} Data modules imported: pandas, numpy, datetime")
    flushed_print(f"  - pandas version: {pd.__version__}")
    flushed_print(f"  - numpy version: {np.__version__}")
except Exception as e:
    flushed_print(f"{FAIL} ERROR importing data modules: {e}")
    flushed_print('Continuing after error.')

flushed_print("\nSTEP 4: Loading settings from configuration files")
try:
    from v4.settings.settings_CPS_v4 import load_settings
    flushed_print("  - Successfully imported load_settings from settings_CPS_v4")
    
    settings = load_settings()
    flushed_print(f"{PASS} Settings loaded successfully")
    flushed_print("  - Settings keys:")
    for key in settings.keys():
        flushed_print(f"    - {key}")
    
    # Print some key settings
    if 'backtest' in settings:
        flushed_print("\n  - Backtest settings:")
        for key, value in settings['backtest'].items():
            flushed_print(f"    - {key}: {value}")
    
    if 'data' in settings:
        flushed_print("\n  - Data settings:")
        for key, value in settings['data'].items():
            flushed_print(f"    - {key}: {value}")
except Exception as e:
    flushed_print(f"{FAIL} ERROR loading settings: {e}")
    print("Traceback:")
    import traceback
    traceback.print_exc()
    flushed_print('Continuing after error.')

flushed_print("\nSTEP 5: Loading real price data from settings")
try:
    # Import data loader
    from v4.data.data_loader import load_price_data
    flushed_print("  - Successfully imported load_price_data")
    
    # Get data parameters from settings
    data_settings = settings.get('data', {})
    data_source = data_settings.get('data_source', 'yahoo')
    tickers = data_settings.get('tickers', [])
    start_date = data_settings.get('start_date', '2020-01-01')
    end_date = data_settings.get('end_date', '2022-12-31')
    
    flushed_print(f"  - Data source: {data_source}")
    flushed_print(f"  - Tickers: {tickers}")
    flushed_print(f"  - Date range: {start_date} to {end_date}")
    
    # Load the price data
    flushed_print("  - Loading price data...")
    price_data = load_price_data(tickers=tickers, start_date=start_date, end_date=end_date, data_source=data_source)
    
    flushed_print(f"{PASS} Price data loaded successfully")
    flushed_print("  - Price data sample:")
    print(price_data.head())
    # Save sample price_data to CSV
    out_csv = Path(__file__).parent / 'price_data_head_simple.csv'
    price_data.head().to_csv(out_csv)
    flushed_print(f"Wrote price_data sample to {out_csv}")
    flushed_print(f"  - Price data shape: {price_data.shape}")
except Exception as e:
    flushed_print(f"{FAIL} ERROR generating price data: {e}")
    print("Traceback:")
    import traceback
    traceback.print_exc()
    flushed_print('Continuing after error.')

flushed_print("\nSTEP 6: Importing backtest engine")
try:
    flushed_print("  Attempting to import BacktestEngine...")
    from v4.engine.backtest_v4 import BacktestEngine
    flushed_print(f"{PASS} Successfully imported BacktestEngine")
except Exception as e:
    flushed_print(f"{FAIL} ERROR importing BacktestEngine: {e}")
    print("Traceback:")
    import traceback
    traceback.print_exc()
    flushed_print('Continuing after error.')

flushed_print("\nSTEP 7: Importing signal generator")
try:
    flushed_print("  Attempting to import generate_signals...")
    from v4.engine.signal_generator_v4 import generate_signals
    flushed_print(f"{PASS} Successfully imported generate_signals")
except Exception as e:
    flushed_print(f"{FAIL} ERROR importing generate_signals: {e}")
    print("Traceback:")
    import traceback
    traceback.print_exc()
    flushed_print('Continuing after error.')

flushed_print("\nSTEP 8: Creating BacktestEngine instance")
try:
    flushed_print("  Instantiating BacktestEngine...")
    engine = BacktestEngine()
    flushed_print(f"{PASS} Successfully created BacktestEngine instance")
    flushed_print(f"  Engine object: {engine}")
except Exception as e:
    flushed_print(f"{FAIL} ERROR creating BacktestEngine instance: {e}")
    print("Traceback:")
    import traceback
    traceback.print_exc()
    flushed_print('Continuing after error.')

flushed_print("\nSTEP 9: Testing signal generation")
try:
    flushed_print("  Generating signals with strategy='equal_weight'...")
    signals = generate_signals(price_data, strategy='equal_weight')
    flushed_print(f"{PASS} Successfully generated signals")
    flushed_print(f"  Signals shape: {signals.shape}")
    flushed_print("  Signals sample:")
    print(signals.head())
except Exception as e:
    flushed_print(f"{FAIL} ERROR generating signals: {e}")
    print("Traceback:")
    import traceback
    traceback.print_exc()
    flushed_print('Continuing after error.')

flushed_print("\nSTEP 10: Running backtest")
try:
    # Get backtest parameters from settings
    backtest_settings = settings.get('backtest', {})
    strategy = backtest_settings.get('strategy', 'equal_weight')
    rebalance_freq = backtest_settings.get('rebalance_freq', 'M')
    execution_delay = backtest_settings.get('execution_delay', 1)
    initial_capital = backtest_settings.get('initial_capital', 10000)
    
    flushed_print("  Running backtest with parameters:")
    flushed_print(f"    - price_data shape: {price_data.shape}")
    flushed_print(f"    - signal_generator: {generate_signals.__name__}")
    flushed_print(f"    - strategy: {strategy}")
    flushed_print(f"    - rebalance_freq: {rebalance_freq}")
    flushed_print(f"    - execution_delay: {execution_delay}")
    flushed_print(f"    - initial_capital: {initial_capital}")
    
    flushed_print("  Calling engine.run_backtest()...")
    results = engine.run_backtest(
        price_data=price_data,
        signal_generator=generate_signals,
        strategy=strategy,
        rebalance_freq=rebalance_freq,
        execution_delay=execution_delay,
        initial_capital=initial_capital
    )
    flushed_print(f"{PASS} Successfully ran backtest")
except Exception as e:
    flushed_print(f"{FAIL} ERROR running backtest: {e}")
    print("Traceback:")
    import traceback
    traceback.print_exc()
    flushed_print('Continuing after error.')

flushed_print("\nSTEP 11: Checking results structure")
try:
    flushed_print("  Checking if results is a dictionary...")
    if isinstance(results, dict):
        flushed_print(f"{PASS} Results is a dictionary with {len(results)} keys")
        flushed_print("  Results keys:")
        for key in results:
            flushed_print(f"    - {key}: {type(results[key])}")
    else:
        flushed_print(f"{FAIL} Results is not a dictionary, but {type(results)}")
        flushed_print('Continuing after error.')
        
    # Check essential keys
    essential_keys = ['weights_history', 'signal_history']
    for key in essential_keys:
        if key in results:
            flushed_print(f"{PASS} '{key}' found in results")
            if isinstance(results[key], pd.DataFrame):
                flushed_print(f"    - Shape: {results[key].shape}")
                flushed_print(f"    - Sample:\n{results[key].head()}")
            else:
                flushed_print(f"    - Type: {type(results[key])}")
        else:
            flushed_print(f"{FAIL} '{key}' NOT FOUND in results")
except Exception as e:
    flushed_print(f"{FAIL} ERROR checking results: {e}")
    print("Traceback:")
    import traceback
    traceback.print_exc()
    flushed_print('Continuing after error.')

flushed_print("\nSTEP 12: Comparing weights_history and signal_history")
try:
    weights_history = results.get('weights_history')
    signal_history = results.get('signal_history')
    
    if weights_history is None:
        flushed_print(f"{FAIL} weights_history is None!")
    elif signal_history is None:
        flushed_print(f"{FAIL} signal_history is None!")
    else:
        flushed_print(f"  weights_history shape: {weights_history.shape}")
        flushed_print(f"  signal_history shape: {signal_history.shape}")
        
        if weights_history.equals(signal_history):
            flushed_print(f"{FAIL} weights_history EQUALS signal_history - allocation history is NOT preserved!")
            
            # Check a sample date to show the equality
            if not weights_history.empty:
                sample_date = weights_history.index[0]
                print(f"\n  Sample comparison for date {sample_date}:")
                flushed_print(f"  weights: {weights_history.loc[sample_date].to_dict()}")
                flushed_print(f"  signals: {signal_history.loc[sample_date].to_dict()}")
        else:
            flushed_print(f"{PASS} weights_history DIFFERS from signal_history - allocation history is preserved!")
            
            # Check a sample date to show the difference
            if not weights_history.empty and not signal_history.empty:
                sample_date = weights_history.index[0]
                print(f"\n  Sample comparison for date {sample_date}:")
                flushed_print(f"  weights: {weights_history.loc[sample_date].to_dict()}")
                flushed_print(f"  signals: {signal_history.loc[sample_date].to_dict()}")
except Exception as e:
    flushed_print(f"{FAIL} ERROR comparing histories: {e}")
    print("Traceback:")
    import traceback
    traceback.print_exc()

flushed_print("\n" + "=" * 80)
flushed_print("TEST COMPLETE")

# STEP 14: Saving sample CSVs
try:
    flushed_print("\nSTEP 14: Saving sample CSVs")
    import pandas as pd
    # Signal history
    sig_out = Path(__file__).parent / 'signal_history_head.csv'
    results['signal_history'].head().to_csv(sig_out)
    flushed_print(f"Saved signal_history sample to {sig_out}")
    # Weights history
    wgt_out = Path(__file__).parent / 'weights_history_head.csv'
    results['weights_history'].head().to_csv(wgt_out)
    flushed_print(f"Saved weights_history sample to {wgt_out}")
    # Portfolio history
    port_df = pd.DataFrame({
        'total_value': results.get('portfolio_values'),
        'cash_value': results.get('cash_values')
    })
    port_out = Path(__file__).parent / 'portfolio_history_head.csv'
    port_df.head().to_csv(port_out)
    flushed_print(f"Saved portfolio_history sample to {port_out}")
except Exception as e:
    flushed_print(f"{FAIL} ERROR saving sample CSVs: {e}")

# Step 13: Print performance metrics if available
try:
    flushed_print("\nSTEP 13: Printing performance metrics")
    perf = results.get('performance') or results.get('metrics') or results.get('performance_metrics')
    if perf is None:
        flushed_print("No performance metrics found in results.")
    else:
        flushed_print("Performance metrics:")
        print(perf)
except Exception as e:
    flushed_print(f"{FAIL} ERROR printing performance metrics: {e}")

flushed_print("=" * 80)
