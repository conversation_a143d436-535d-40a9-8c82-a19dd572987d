"""
Python Reporting Module for CPS v4 Financial Backtesting System

This module contains all Python reporting code for the CPS v4 system.
Separated from output report directories to avoid confusion.

Key Components:
- performance_table_generator.py: Main XLSX performance report generation
- allocation_report_v4.py: Allocation reporting functionality  
- equity_curves_manager.py: Equity curve management and processing

Created: 2025-07-26
Purpose: Centralized Python reporting code location
"""
