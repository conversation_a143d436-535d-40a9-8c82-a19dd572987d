@echo off
REM run_tests.bat - Test execution script for v4_reporting modules
REM Created: 2025-06-11

echo Starting v4_reporting test execution at %date% %time%
echo ===============================================

REM Activate the virtual environment
call F:\AI_Library\my_quant_env\Scripts\activate.bat

REM Create logs directory if it doesn't exist
if not exist "test_logs" mkdir "test_logs"

REM Set log file with timestamp
set timestamp=%date:~10,4%%date:~4,2%%date:~7,2%_%time:~0,2%%time:~3,2%%time:~6,2%
set timestamp=%timestamp: =0%
set log_file=test_logs\test_run_%timestamp%.log

echo Test execution started > %log_file%
echo Timestamp: %date% %time% >> %log_file%
echo =============================================== >> %log_file%

REM Run the test script and log output
echo Running v4_performance_report tests... >> %log_file%
python v4_reporting\test_v4_performance_report.py >> %log_file% 2>&1
echo. >> %log_file%

REM Run new parameter validation test
echo Running parameter validation tests... >> %log_file%
python tests_v4\test_all_parameters_loaded.py >> %log_file% 2>&1
echo. >> %log_file%

REM Check test results file and append to log
echo Test Results Summary: >> %log_file%
echo ---------------------- >> %log_file%
if exist "test_output\test_results.txt" (
    type test_output\test_results.txt >> %log_file%
) else (
    echo No test results file found! >> %log_file%
)

REM Check parameter validation results file and append to log
echo Parameter Validation Results: >> %log_file%
echo ----------------------------- >> %log_file%
if exist "test_output\test_all_params_results.txt" (
    type test_output\test_all_params_results.txt >> %log_file%
) else (
    echo No parameter validation results file found! >> %log_file%
)

REM Check error log file and append to log if it exists
echo. >> %log_file%
echo Error Details: >> %log_file%
echo -------------- >> %log_file%
if exist "test_output\error_log.txt" (
    type test_output\error_log.txt >> %log_file%
) else (
    echo No error log file found. >> %log_file%
)

echo. >> %log_file%
echo Test execution completed at %date% %time% >> %log_file%
echo =============================================== >> %log_file%

REM Display completion message
echo Test execution completed. Results saved to %log_file%
echo.

REM Keep virtual environment active for developer convenience

REM Display the log file
type %log_file%
