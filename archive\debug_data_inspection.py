"""
Data diagnostic tool to analyze the input data to the backtest and identify issues.
"""

import pandas as pd
import numpy as np
import os
import logging
import sys
from pathlib import Path
import matplotlib.pyplot as plt

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("debug_data.log"),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# Import configuration
from config.config import config
from config.paths import OUTPUT_PATH

# Import data loader
from data.data_loader import load_data_for_backtest

def inspect_series(series, name):
    """Inspect a pandas Series for potential issues"""
    logger.info(f"\n=== Inspection of {name} ===")
    
    # Basic information
    logger.info(f"Type: {type(series)}")
    logger.info(f"Length: {len(series)}")
    
    if len(series) == 0:
        logger.warning(f"{name} is empty!")
        return
    
    # Index information
    logger.info(f"Index type: {type(series.index)}")
    logger.info(f"Index range: {series.index[0]} to {series.index[-1]}")
    logger.info(f"Frequency: {pd.infer_freq(series.index)}")
    
    # Check for duplicates in index
    dup_count = series.index.duplicated().sum()
    if dup_count > 0:
        logger.warning(f"{name} has {dup_count} duplicate index values!")
    
    # Value information
    if isinstance(series, pd.Series):
        logger.info(f"Data type: {series.dtype}")
    elif isinstance(series, pd.DataFrame):
        logger.info(f"Data types:\n{series.dtypes}")
    else:
        logger.info(f"Data type: {type(series)}")
    
    # Check for NaN values
    if isinstance(series, pd.Series):
        has_nan = series.isna().any()
        nan_count = series.isna().sum()
    elif isinstance(series, pd.DataFrame):
        has_nan = series.isna().any().any()
        nan_count = series.isna().sum().sum()
    else:
        has_nan = False
        nan_count = 0
    
    logger.info(f"Contains NaN: {has_nan}")
    logger.info(f"NaN count: {nan_count}")
    
    # Statistics
    if isinstance(series, pd.Series):
        if pd.api.types.is_numeric_dtype(series):
            logger.info(f"Min: {series.min()}")
            logger.info(f"Max: {series.max()}")
            logger.info(f"Mean: {series.mean()}")
            
            # Check for constant values
            if series.nunique() == 1:
                logger.warning(f"{name} has only one unique value: {series.iloc[0]}")
            
            # Check for infinity
            if np.isinf(series).any():
                logger.warning(f"{name} contains infinity values!")
    elif isinstance(series, pd.DataFrame):
        numeric_cols = series.select_dtypes(include=[np.number]).columns
        if len(numeric_cols) > 0:
            logger.info(f"Min values:\n{series[numeric_cols].min()}")
            logger.info(f"Max values:\n{series[numeric_cols].max()}")
            logger.info(f"Mean values:\n{series[numeric_cols].mean()}")
            
            # Check for constant columns
            for col in numeric_cols:
                if series[col].nunique() == 1:
                    logger.warning(f"Column {col} in {name} has only one unique value: {series[col].iloc[0]}")
            
            # Check for infinity
            if np.isinf(series[numeric_cols]).any().any():
                logger.warning(f"{name} contains infinity values!")

def inspect_dataframe(df, name):
    """Inspect a pandas DataFrame for potential issues"""
    logger.info(f"\n=== Inspection of {name} ===")
    
    # Basic information
    logger.info(f"Type: {type(df)}")
    logger.info(f"Shape: {df.shape}")
    
    if len(df) == 0:
        logger.warning(f"{name} is empty!")
        return
    
    # Index information
    logger.info(f"Index type: {type(df.index)}")
    logger.info(f"Index range: {df.index[0]} to {df.index[-1]}")
    logger.info(f"Frequency: {pd.infer_freq(df.index)}")
    
    # Check for duplicates in index
    dup_count = df.index.duplicated().sum()
    if dup_count > 0:
        logger.warning(f"{name} has {dup_count} duplicate index values!")
    
    # Column information
    logger.info(f"Columns: {df.columns.tolist()}")
    
    # Data types
    logger.info(f"Data types:\n{df.dtypes}")
    
    # Missing values
    na_counts = df.isna().sum()
    if na_counts.sum() > 0:
        logger.warning(f"Missing values per column:\n{na_counts}")
    
    # Check for infinity
    inf_counts = np.isinf(df).sum()
    if inf_counts.sum() > 0:
        logger.warning(f"Infinity values per column:\n{inf_counts}")
    
    # Save first few rows to CSV
    sample_file = os.path.join(OUTPUT_PATH, f"{name}_sample.csv")
    df.head(20).to_csv(sample_file)
    logger.info(f"Saved sample of {name} to {sample_file}")
    
    # Save entire DataFrame to CSV for inspection
    full_file = os.path.join(OUTPUT_PATH, f"{name}_full.csv")
    df.to_csv(full_file)
    logger.info(f"Saved full {name} to {full_file}")
    
    # Generate plot for visualization
    if df.select_dtypes(include=[np.number]).shape[1] > 0:
        try:
            plt.figure(figsize=(12, 6))
            df.plot()
            plot_file = os.path.join(OUTPUT_PATH, f"{name}_plot.png")
            plt.savefig(plot_file)
            plt.close()
            logger.info(f"Saved {name} plot to {plot_file}")
        except Exception as e:
            logger.error(f"Error creating plot for {name}: {e}")
    
    # Try to identify potential truth value ambiguity issues
    potential_issues = []
    
    # Columns that contain True/False values (could cause ambiguity in conditions)
    bool_cols = df.select_dtypes(include=['bool']).columns.tolist()
    if bool_cols:
        potential_issues.append(f"Boolean columns that could cause ambiguity: {bool_cols}")
    
    # Columns with potentially problematic names
    for col in df.columns:
        if any(kw in col.lower() for kw in ['bool', 'flag', 'is_', 'has_']):
            potential_issues.append(f"Column with potential boolean name: {col}")
    
    if potential_issues:
        logger.warning("Potential truth value ambiguity issues detected:")
        for issue in potential_issues:
            logger.warning(f"  - {issue}")

def main():
    """Main function to analyze the data passed to the backtest"""
    logger.info("Starting data inspection...")
    
    # Create output directory if it doesn't exist
    os.makedirs(OUTPUT_PATH, exist_ok=True)
    
    # Load data using the same function as run_backtest.py
    data = load_data_for_backtest(config)
    
    # Inspect each component
    inspect_dataframe(data['price_data'], "price_data")
    inspect_dataframe(data['returns_data'], "returns_data")
    
    # Handle risk_free_rate which could be a Series or DataFrame
    if isinstance(data['risk_free_rate'], pd.Series):
        inspect_series(data['risk_free_rate'], "risk_free_rate")
    else:
        # If it's a DataFrame, convert to Series if it has only one column
        if isinstance(data['risk_free_rate'], pd.DataFrame) and data['risk_free_rate'].shape[1] == 1:
            logger.info("Converting risk_free_rate from DataFrame to Series")
            data['risk_free_rate'] = data['risk_free_rate'].iloc[:, 0]
            inspect_series(data['risk_free_rate'], "risk_free_rate")
        else:
            inspect_dataframe(data['risk_free_rate'], "risk_free_rate")

    # Generate and inspect signals
    logger.info("\n=== Generating and Inspecting Strategy Signals ===")
    
    # Import necessary functions
    try:
        # Add Custom Function Library to path
        from config.paths import CUSTOM_LIB_PATH
        sys.path.insert(0, str(Path(CUSTOM_LIB_PATH).resolve()))
        
        # Import backtesting functions
        from backtesting.backtest_framework import backtest_strategy
        
        # Get strategy function based on config
        strategy_name = config['backtest_params']['strategy']
        logger.info(f"Generating signals for strategy: {strategy_name}")
        
        # Create a strategy function closure that takes historical data and returns weights
        def strategy_func(hist_data, hist_returns, **params):
            logger.info(f"Strategy function called with data shape: {hist_data.shape}")
            
            # Save the input data to the strategy function for inspection
            hist_data.to_csv(os.path.join(OUTPUT_PATH, "strategy_input_data.csv"))
            if hist_returns is not None:
                hist_returns.to_csv(os.path.join(OUTPUT_PATH, "strategy_input_returns.csv"))
            
            if strategy_name == 'equal_weight':
                # Equal weight strategy
                weights = {}
                for ticker in hist_data.columns:
                    weights[ticker] = 1.0 / len(hist_data.columns)
                return weights
                
            elif strategy_name == 'momentum':
                # Simple momentum strategy
                lookback = config['strategy_params'].get('lookback', 60)
                momentum_period = min(lookback, len(hist_returns))
                
                if momentum_period < 20:
                    # Not enough data, use equal weight
                    weights = {}
                    for ticker in hist_data.columns:
                        weights[ticker] = 1.0 / len(hist_data.columns)
                    return weights
                
                # Calculate momentum
                momentum = hist_returns.iloc[-momentum_period:].mean()
                
                # Rank by momentum
                ranked = momentum.sort_values(ascending=False)
                
                # Select top N assets
                top_n = config['strategy_params'].get('top_n', 2)
                top_assets = ranked.index[:top_n]
                
                # Allocate equally to top assets
                weights = {}
                for ticker in hist_data.columns:
                    weights[ticker] = 1.0 / top_n if ticker in top_assets else 0.0
                
                return weights
                
            elif strategy_name == 'ema':
                # Exponential moving average crossover strategy
                fast_period = config['strategy_params'].get('fast_period', 50)
                slow_period = config['strategy_params'].get('slow_period', 200)
                
                # Calculate EMAs
                weights = {}
                for ticker in hist_data.columns:
                    # Skip if not enough data
                    if len(hist_data[ticker].dropna()) < slow_period:
                        weights[ticker] = 0.0
                        continue
                    
                    # Calculate EMAs
                    fast_ema = hist_data[ticker].ewm(span=fast_period).mean()
                    slow_ema = hist_data[ticker].ewm(span=slow_period).mean()
                    
                    # Save EMAs for inspection
                    emas = pd.DataFrame({
                        'price': hist_data[ticker],
                        'fast_ema': fast_ema,
                        'slow_ema': slow_ema
                    })
                    emas.to_csv(os.path.join(OUTPUT_PATH, f"{ticker}_emas.csv"))
                    
                    # Generate signal (1 if fast > slow, 0 otherwise)
                    # Use .iloc[-1] to get the last value of each Series
                    last_fast = fast_ema.iloc[-1]
                    last_slow = slow_ema.iloc[-1]
                    
                    # Log the comparison
                    logger.info(f"{ticker} - Fast EMA: {last_fast}, Slow EMA: {last_slow}")
                    logger.info(f"{ticker} - Comparison: {last_fast > last_slow}")
                    
                    if last_fast > last_slow:
                        weights[ticker] = 1.0
                    else:
                        weights[ticker] = 0.0
                
                # Normalize weights to sum to 1
                total_weight = sum(weights.values())
                if total_weight > 0:
                    for ticker in weights:
                        weights[ticker] = weights[ticker] / total_weight
                else:
                    # If no assets selected, use cash (all zeros)
                    pass
                
                # Save the weights for inspection
                pd.Series(weights).to_csv(os.path.join(OUTPUT_PATH, "strategy_weights.csv"))
                
                return weights
                
            else:
                logger.error(f"Unknown strategy: {strategy_name}")
                # Default to equal weight
                weights = {}
                for ticker in hist_data.columns:
                    weights[ticker] = 1.0 / len(hist_data.columns)
                return weights
        
        # Standardize dates for the backtest
        from utils.date_utils import standardize_date, date_to_str, map_rebalance_frequency
        start_date = standardize_date(config['data_params']['start_date'])
        end_date = standardize_date(config['data_params']['end_date'])
        
        # Convert Timestamps to strings as expected by the backtest_strategy function
        start_date_str = date_to_str(start_date) if start_date is not None else None
        end_date_str = date_to_str(end_date) if end_date is not None else None
        
        # Map rebalance frequency string to pandas frequency
        rebalance_freq = map_rebalance_frequency(config['backtest_params']['rebalance_freq'])
        
        try:
            # Run the backtest with a wrapper to catch and analyze errors
            logger.info("Running backtest to generate signals...")
            
            # First, try to run the strategy function directly on the data
            try:
                logger.info("Testing strategy function directly...")
                price_data = data['price_data']
                returns_data = data['returns_data']
                
                # Call strategy function with the latest data
                test_weights = strategy_func(price_data, returns_data)
                logger.info(f"Strategy function direct test successful. Weights: {test_weights}")
            except Exception as e:
                logger.error(f"Error in direct strategy function test: {e}", exc_info=True)
            
            # Now try the full backtest
            backtest_result = backtest_strategy(
                price_data=data['price_data'],
                strategy_func=strategy_func,
                strategy_params={},  # Parameters are already in the strategy_func closure
                rebalance_freq=rebalance_freq,
                execution_delay=config['backtest_params']['execution_delay'],
                start_date=start_date_str,
                end_date=end_date_str
            )
            
            # Unpack results
            strategy_returns, weights_history, signal_history = backtest_result
            
            # Save the signal history
            logger.info("Saving signal history...")
            signal_file = os.path.join(OUTPUT_PATH, "signal_history.csv")
            signal_history.to_csv(signal_file)
            logger.info(f"Saved signal history to {signal_file}")
            
            # Save the weights history
            weights_file = os.path.join(OUTPUT_PATH, "weights_history.csv")
            weights_history.to_csv(weights_file)
            logger.info(f"Saved weights history to {weights_file}")
            
            # Save the strategy returns
            returns_file = os.path.join(OUTPUT_PATH, "strategy_returns.csv")
            strategy_returns.to_csv(returns_file)
            logger.info(f"Saved strategy returns to {returns_file}")
            
            # Inspect the signal history
            inspect_dataframe(signal_history, "signal_history")
            
            # Inspect the weights history
            inspect_dataframe(weights_history, "weights_history")
            
        except Exception as e:
            logger.error(f"Error running backtest: {e}", exc_info=True)
            
            # Try to identify where the error occurs
            if "truth value of a Series is ambiguous" in str(e):
                logger.error("Detected truth value ambiguity error. This typically occurs when:")
                logger.error("1. A pandas Series is used in a boolean context (if series: ...)")
                logger.error("2. A comparison between Series objects doesn't specify how to combine results")
                logger.error("Potential locations in the code:")
                logger.error("- In the strategy function when comparing EMAs")
                logger.error("- In the backtest_strategy function when applying signals")
                logger.error("- In performance calculations")
                
                # Add specific debugging for the EMA strategy
                if strategy_name == 'ema':
                    logger.info("Debugging EMA strategy specifically...")
                    price_data = data['price_data']
                    
                    # Test each ticker individually
                    for ticker in price_data.columns:
                        try:
                            logger.info(f"Testing EMA calculations for {ticker}...")
                            
                            # Get parameters
                            fast_period = config['strategy_params'].get('fast_period', 50)
                            slow_period = config['strategy_params'].get('slow_period', 200)
                            
                            # Calculate EMAs
                            fast_ema = price_data[ticker].ewm(span=fast_period).mean()
                            slow_ema = price_data[ticker].ewm(span=slow_period).mean()
                            
                            # Test the comparison that might be causing the error
                            logger.info(f"Testing comparison for {ticker}...")
                            
                            # Get the last values
                            last_fast = fast_ema.iloc[-1]
                            last_slow = slow_ema.iloc[-1]
                            
                            # Test the comparison
                            is_greater = last_fast > last_slow
                            logger.info(f"{ticker} - Fast EMA > Slow EMA: {is_greater} (type: {type(is_greater)})")
                            
                        except Exception as e:
                            logger.error(f"Error in EMA calculation for {ticker}: {e}")
    
    except ImportError as e:
        logger.error(f"Failed to import necessary functions: {e}")
    
    # Check for data shape consistency
    logger.info("\n=== Data Consistency Checks ===")
    
    # Check that all data has matching indices
    price_idx = set(data['price_data'].index)
    returns_idx = set(data['returns_data'].index)
    rf_idx = set(data['risk_free_rate'].index)
    
    if price_idx != returns_idx:
        logger.warning("price_data and returns_data have different index values!")
        logger.warning(f"Indices in price_data but not in returns_data: {price_idx - returns_idx}")
        logger.warning(f"Indices in returns_data but not in price_data: {returns_idx - price_idx}")
    
    if price_idx != rf_idx:
        logger.warning("price_data and risk_free_rate have different index values!")
        logger.warning(f"Indices in price_data but not in risk_free_rate: {price_idx - rf_idx}")
        logger.warning(f"Indices in risk_free_rate but not in price_data: {rf_idx - price_idx}")
    
    # Test for potential Series truth value ambiguity
    logger.info("\n=== Testing for Series Truth Value Ambiguity ===")
    
    # Create a simple test function that will trigger the error if it exists
    def test_ambiguity(data_item, name):
        try:
            if isinstance(data_item, pd.Series):
                # This will raise an error if the Series has multiple values
                bool_value = bool(data_item)
                logger.info(f"{name} can be converted to a boolean without error.")
            elif isinstance(data_item, pd.DataFrame):
                # Test each column for potential issues
                for col in data_item.columns:
                    series = data_item[col]
                    bool_value = bool(series)
                    logger.info(f"Column {col} in {name} can be converted to a boolean without error.")
            else:
                logger.info(f"{name} is not a Series or DataFrame, no ambiguity test needed.")
        except ValueError as e:
            logger.warning(f"Ambiguity detected in {name}: {e}")
            
            # Try some common fixes
            if isinstance(data_item, pd.Series):
                logger.info(f"Testing alternatives for {name}:")
                try:
                    result = data_item.empty
                    logger.info(f"  - .empty works: {result}")
                except Exception as e:
                    logger.warning(f"  - .empty fails: {e}")
                
                try:
                    result = data_item.bool()
                    logger.info(f"  - .bool() works: {result}")
                except Exception as e:
                    logger.warning(f"  - .bool() fails: {e}")
                
                try:
                    result = data_item.any()
                    logger.info(f"  - .any() works: {result}")
                except Exception as e:
                    logger.warning(f"  - .any() fails: {e}")
                
                try:
                    result = data_item.all()
                    logger.info(f"  - .all() works: {result}")
                except Exception as e:
                    logger.warning(f"  - .all() fails: {e}")
    
    # Test each data component
    for key, value in data.items():
        test_ambiguity(value, key)
        if isinstance(value, pd.DataFrame):
            # Also test individual columns
            for col in value.columns:
                test_ambiguity(value[col], f"{key}['{col}']")
    
    logger.info("Data inspection complete. Check the output CSV files and log for details.")

if __name__ == "__main__":
    main()
