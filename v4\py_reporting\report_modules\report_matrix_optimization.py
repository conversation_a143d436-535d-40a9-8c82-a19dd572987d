"""
Matrix Optimization Module for CPS V4 Performance Reporting

This module contains the complex optimization functionality extracted from performance_table_generator.py
to complete the refactoring. It handles matrix optimization, validation framework, and EquityCurvesManager
integration while maintaining identical function signatures for seamless compatibility.

Author: AI Assistant
Date: 2025-07-28
"""

import pandas as pd
import numpy as np
from datetime import datetime
import configparser
import os
from pathlib import Path
import logging
import subprocess
import shutil
import tempfile
import json
import itertools
import time

# Import centralized path configuration
from v4.config.paths_v4 import (
    PROJECT_ROOT, OUTPUT_DIR, V4_TRACE_OUTPUTS_DIR, OPTIMIZATION_VALIDATION_DIR,
    V4_SETTINGS_FILE, DATA_DIR, get_validation_dir, get_reporting_file_path,
    get_v4_trace_file_path, EQUITY_CURVE_STRATEGY_LATEST, EQUITY_CURVE_BENCHMARK_LATEST
)

# Import combo ID tracking system
from v4.py_reporting.combo_id_tracker import ComboIDTracker

# Import EquityCurvesManager directly - no wrapper complexity
from v4.py_reporting.equity_curves_manager import EquityCurvesManager

# Import smart logging system
from v4.utils.smart_logging import create_smart_logger

# Set up smart logging
logger = create_smart_logger(__name__)

# Module-level variables for validation mode
validation_mode = False
validation_dir = OPTIMIZATION_VALIDATION_DIR
combination_file_mapping = {}


# Removed complex error analysis functions - keeping it simple for now


def get_optimization_combinations(config_path=None, validation_mode=False, validation_dir=None):
    """Generate all combinations of optimization parameters - PUBLIC METHOD."""

    # Use the updated section-agnostic optimization detector
    from v4.optimization_detector import get_optimization_combinations as get_combinations_agnostic

    # Load configuration path
    if config_path is None:
        config_path = str(V4_SETTINGS_FILE)

    logger.debug(f"Starting get_optimization_combinations using section-agnostic detector")

    # Delegate to the section-agnostic optimization detector
    return get_combinations_agnostic(config_path)




def _setup_validation_directories(validation_dir):
    """Set up directories for validation artifacts with timestamps according to stepmappingopt.md."""
    
    # If validation_dir is already set and exists, use it (from batch file)
    if validation_dir and isinstance(validation_dir, Path) and validation_dir.exists():
        # Initialize status file if it doesn't exist (flat structure)
        status_file = validation_dir / "status__current_step.txt"
        if not status_file.exists():
            with open(status_file, "w") as f:
                f.write("VALIDATION_INITIALIZED")

        logger.info(f"Validation mode enabled. Using existing validation directory: {validation_dir}")
        return validation_dir
        
    # Create main validation directory with timestamp subfolder
    validation_timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    validation_dir = get_validation_dir(validation_timestamp)

    # Create only the main directory (flat structure)
    validation_dir.mkdir(parents=True, exist_ok=True)

    # Initialize status file (flat structure with prefix)
    with open(validation_dir / "status__current_step.txt", "w") as f:
        f.write("VALIDATION_INITIALIZED")
        
    # Log validation setup (flat structure)
    validation_log_path = validation_dir / "validation__setup.log"
    with open(validation_log_path, "w") as f:
        f.write(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] Validation directories created\n")
        f.write(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] Timestamp: {validation_timestamp}\n")
        
    logger.info(f"Validation directories created: {validation_dir}")
    return validation_dir


def _log_validation_step(step_number, step_name, status="RUNNING", details=None, validation_dir=None):
    """Log validation step information and update status with flat file structure."""
    if not validation_dir:
        return

    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    # Update current step status file (flat structure)
    with open(validation_dir / "status__current_step.txt", "w") as f:
        f.write(f"STEP_{step_number}_OF_10_{status}")

    # Create step-specific checkpoint file (flat structure with prefix)
    step_checkpoint_file = validation_dir / f"step{step_number:02d}__checkpoint.txt"
    with open(step_checkpoint_file, "w") as f:
        f.write(f"STEP_{step_number}_OF_10_{status}\n")
        f.write(f"Step Name: {step_name}\n")
        f.write(f"Timestamp: {timestamp}\n")
        if details:
            f.write(f"Details: {details}\n")

    # Log to console and validation log
    log_message = f"[{timestamp}] [VALIDATION] STEP {step_number}: {step_name} - {status}"
    if details:
        log_message += f" | {details}"

    print(log_message)
    logger.info(log_message)

    # Append to step-specific log (flat structure with prefix)
    step_log_file = validation_dir / f"step{step_number:02d}__log.txt"
    with open(step_log_file, "a") as f:
        f.write(f"{log_message}\n")
        if details:
            f.write(f"[{timestamp}] [DETAILS] {details}\n")
    
    # Append to main validation log (flat structure)
    with open(validation_dir / "validation__main.log", "a") as f:
        f.write(f"{log_message}\n")
        
    # If step failed, stop immediately (no fallbacks)
    if status == "FAILED":
        error_msg = f"Validation STEP {step_number} FAILED: {step_name}"
        if details:
            error_msg += f" - {details}"
        logger.error(f"[{timestamp}] [VALIDATION] {error_msg}")
        raise RuntimeError(error_msg)


def generate_combo_id(combination_params):
    """Generate unique, readable combination identifier based on optimizable parameters only.

    Args:
        combination_params: Dictionary of parameter values for this combination

    Returns:
        str: Readable combination ID based on optimize=True parameters only
    """
    # Get optimizable parameters from settings
    optimizable_params = _get_optimizable_parameters_for_combo_id()

    # Build ComboID using only optimizable parameters
    combo_parts = []
    for param_name in sorted(optimizable_params.keys()):  # Sort for consistent ordering
        if param_name in combination_params:
            value = combination_params[param_name]
            # Create abbreviated parameter names
            if param_name == 'st_lookback':
                combo_parts.append(f"S{value}")
            elif param_name == 'mt_lookback':
                combo_parts.append(f"M{value}")
            elif param_name == 'lt_lookback':
                combo_parts.append(f"L{value}")
            elif param_name == 'execution_delay':
                combo_parts.append(f"E{value}")
            elif param_name == 'top_n' or param_name == 'system_top_n':
                combo_parts.append(f"T{value}")
            else:
                # Generic handling for other optimizable parameters
                combo_parts.append(f"{param_name[:2].upper()}{value}")

    combo_id = "_".join(combo_parts)
    logger.debug(f"[COMBO_ID] Generated ID: {combo_id} for params: {combination_params}")
    logger.debug(f"[COMBO_ID] Used optimizable params: {list(optimizable_params.keys())}")
    return combo_id


def _get_optimizable_parameters_for_combo_id():
    """Get parameters that have optimize=True from settings for ComboID generation."""
    try:
        from v4.settings.config_helper import ConfigHelper
        from v4.config.paths_v4 import V4_SETTINGS_FILE

        config = ConfigHelper(V4_SETTINGS_FILE)
        optimizable_params = {}

        # Check all sections for ComplexN parameters with optimize=True
        for section_name in config.sections():
            section = config._config[section_name]
            for param_name, param_value in section.items():
                if isinstance(param_value, str) and param_value.strip().startswith('(') and 'optimize=' in param_value:
                    try:
                        # Parse ComplexN format
                        param_str = param_value.strip()[1:-1]
                        param_dict = {}
                        for item in param_str.split(','):
                            key, value = item.split('=', 1)
                            key = key.strip()
                            value = value.strip()
                            param_dict[key] = value

                        # Check if optimize=True
                        if param_dict.get('optimize', 'False').lower() == 'true':
                            optimizable_params[param_name] = param_dict.get('default_value', 'N/A')
                            logger.debug(f"[COMBO_ID] Found optimizable param: {param_name} = {param_dict.get('default_value')}")
                    except Exception as e:
                        logger.warning(f"Error parsing parameter {param_name}: {e}")

        logger.info(f"[COMBO_ID] Found {len(optimizable_params)} optimizable parameters: {list(optimizable_params.keys())}")
        return optimizable_params

    except Exception as e:
        logger.error(f"Error getting optimizable parameters: {e}")
        # Fallback to current hardcoded parameters if parsing fails
        return {'st_lookback': 15, 'mt_lookback': 70}


def _cleanup_temp_settings(temp_settings_path):
    """Clean up temporary settings file with proper Windows file lock handling."""
    import gc
    
    if not temp_settings_path or not os.path.exists(temp_settings_path):
        return
        
    # Force garbage collection to release any file handles
    gc.collect()
    
    # Try multiple times with delay to handle Windows file locks
    max_attempts = 5
    for attempt in range(max_attempts):
        try:
            # Close any open file handles in this process
            if hasattr(os, 'sync'):
                os.sync()
                
            os.unlink(temp_settings_path)
            logger.info(f"Successfully cleaned up temporary settings file: {temp_settings_path}")
            return
        except (OSError, PermissionError, FileNotFoundError) as e:
            if attempt < max_attempts - 1:
                # Exponential backoff with jitter
                sleep_time = (0.1 * (2 ** attempt)) + (0.01 * attempt)
                time.sleep(sleep_time)
                continue
            else:
                # Final attempt failed - just log warning, don't break the flow
                logger.warning(f"Failed to clean up temp settings file: {temp_settings_path} after {max_attempts} attempts")
                logger.warning(f"Error: {e}")
                logger.warning("Temp file will be cleaned up automatically when Python exits")
                return


def _load_price_data(data_dir=None):
    """Load the most recent price data file, preferring main data directory."""
    # First try to load from main data directory (most current)
    main_data_dir = DATA_DIR
    if main_data_dir.exists():
        excel_files = list(main_data_dir.glob("tickerdata_SPY_SHV_EFA_TLT_PFF_*.xlsx"))
        if excel_files:
            # Get most recent Excel file by modification time
            latest_excel = max(excel_files, key=lambda f: f.stat().st_mtime)
            logger.info(f"Loading price data from main data directory: {latest_excel}")

            try:
                price_df = pd.read_excel(latest_excel, index_col=0, parse_dates=True)
                if len(price_df) > 0:  # Check if file has data
                    logger.info(f"Loaded price data: {price_df.shape}, {price_df.index[0]} to {price_df.index[-1]}")
                    return price_df
                else:
                    logger.warning(f"Excel file {latest_excel} is empty, falling back to CSV")
            except Exception as e:
                logger.warning(f"Failed to load Excel price data from {latest_excel}: {e}, falling back to CSV")

    # Use trace outputs directory if data_dir not found
    data_dir = data_dir or V4_TRACE_OUTPUTS_DIR

    # Find most recent price data file
    price_files = list(data_dir.glob("price_data_*.csv"))
    if not price_files:
        # Try initial price data
        price_files = list(data_dir.glob("00_initial_price_data.csv"))

    if not price_files:
        raise FileNotFoundError("No price data files found in v4_trace_outputs or v4/data")

    latest_price = max(price_files, key=lambda f: f.stat().st_mtime)
    logger.debug(f"Loading price data from trace outputs: {latest_price}")

    price_df = pd.read_csv(latest_price, index_col=0, parse_dates=True)
    logger.info(f"Loaded price data: {price_df.shape}, {price_df.index[0]} to {price_df.index[-1]}")
    return price_df


def _load_existing_equity_curve(combination):
    """Load equity curve from existing pipeline results (single mode)."""
    try:
        # In single mode, the pipeline already ran and created equity curve files
        # Look for the most recent equity curve file in the reporting directory
        reporting_dir = OUTPUT_DIR

        # Look for equity curve files (CSV format)
        equity_files = []
        if reporting_dir.exists():
            for file in reporting_dir.iterdir():
                if 'equity_curve' in file.name.lower() and file.suffix == '.csv':
                    equity_files.append(file)

        if not equity_files:
            # Try alternative location - v4_trace_outputs
            trace_dir = V4_TRACE_OUTPUTS_DIR
            if trace_dir.exists():
                for file in trace_dir.iterdir():
                    if 'allocation_history' in file.name.lower() and file.suffix == '.csv':
                        equity_files.append(file)

        if not equity_files:
            error_msg = f"No equity curve files found in {reporting_dir} or {V4_TRACE_OUTPUTS_DIR} for single mode"
            logger.critical(f"[TRACE] {error_msg}")
            raise ValueError(error_msg)

        # Use the most recent file
        latest_file = max(equity_files, key=lambda f: f.stat().st_mtime)
        logger.debug(f"Loading existing equity curve from: {latest_file}")

        # Load the equity curve
        df = pd.read_csv(latest_file)

        # Look for Portfolio_Value or Total column (different files have different column names)
        if 'Portfolio_Value' in df.columns:
            equity_curve = df['Portfolio_Value'].dropna()
            logger.debug(f"Loaded equity curve from Portfolio_Value column with {len(equity_curve)} data points, final value: {equity_curve.iloc[-1] if len(equity_curve) > 0 else 'EMPTY'}")
            return equity_curve
        elif 'Total' in df.columns:
            equity_curve = df['Total'].dropna()
            logger.debug(f"Loaded equity curve from Total column with {len(equity_curve)} data points, final value: {equity_curve.iloc[-1] if len(equity_curve) > 0 else 'EMPTY'}")
            return equity_curve
        else:
            error_msg = f"Neither Portfolio_Value nor Total column found in {latest_file}. Available columns: {list(df.columns)}"
            logger.critical(f"[TRACE] {error_msg}")
            raise ValueError(error_msg)

    except Exception as e:
        error_msg = f"Failed to load existing equity curve for combination {combination}: {str(e)}"
        logger.critical(f"[TRACE] {error_msg}")
        raise ValueError(error_msg)


def _create_temp_settings_for_combination(combination, validation_mode=False, validation_dir=None):
    """Create a temporary settings file for this optimization combination using ConfigParser (NO REGEX)."""

    # Create a temporary settings file with proper cleanup
    original_settings = V4_SETTINGS_FILE

    # Verify the original settings file exists
    if not original_settings.exists():
        error_msg = f"Original settings file not found: {original_settings}"
        logger.error(error_msg)
        raise FileNotFoundError(error_msg)

    # Create temp file but close the file descriptor immediately
    temp_fd, temp_path = tempfile.mkstemp(suffix='.ini', prefix='temp_settings_')
    os.close(temp_fd)  # Close immediately to avoid file locks

    try:
        # SIMPLE APPROACH: Use ConfigParser instead of regex (much more reliable)
        config = configparser.ConfigParser(interpolation=None)
        # CRITICAL FIX: Preserve case sensitivity for AlphaList parameter resolution
        config.optionxform = str  # Prevents lowercasing of option names
        config.read(original_settings)

        # Log for debugging
        logger.info(f"Creating temp settings for combination: {combination}")

        # SIMPLE FIX: Update parameters using ConfigParser (NO REGEX NEEDED)
        # This is much more reliable and debuggable than regex patterns

        # Map combination keys to INI parameter names and sections
        param_mapping = {
            'st_lookback': ('Strategy', 'st_lookback'),
            'mt_lookback': ('Strategy', 'mt_lookback'),
            'lt_lookback': ('Strategy', 'lt_lookback'),
            'execution_delay': ('Core', 'execution_delay'),
            'top_n': ('Core', 'system_top_n'),
            'system_lookback': ('Core', 'system_lookback')
        }

        # Update each parameter that exists in the combination
        for combo_key, value in combination.items():
            if combo_key in param_mapping:
                section, param_name = param_mapping[combo_key]

                # Ensure section exists
                if not config.has_section(section):
                    config.add_section(section)

                # Set the simple value (no ComplexN format)
                config.set(section, param_name, str(value))
                logger.info(f"✅ Set {section}.{param_name} = {value} (was ComplexN format)")

        # CSV FLAG CONTROL: Enable CSV for validation, disable for optimization
        if not config.has_section('System'):
            config.add_section('System')

        if validation_mode:
            # VALIDATION MODE: Enable CSV generation so we can load equity curves
            config.set('System', 'csv_flag_use', 'True')
            logger.info("✅ Set System.csv_flag_use = True for validation mode")
        else:
            # OPTIMIZATION MODE: Disable CSV generation for performance
            config.set('System', 'csv_flag_use', 'False')
            logger.info("✅ Set System.csv_flag_use = False for optimization mode")

        # Write the modified configuration to temp file
        with open(temp_path, 'w') as f:
            config.write(f)
            
        # Track updated parameters for validation
        updated_params = {k: combination.get(k) for k in combination.keys()}
        
        # STEP 2: Settings File Validation
        if validation_mode and validation_dir:
            _log_validation_step(2, "Settings File Creation", validation_dir=validation_dir)
            print(f"[VALIDATION] Created temporary settings file with {len(updated_params)} parameters")
            
            # Create validation artifacts
            validation_result = {
                "step": 2,
                "status": "SUCCESS",
                "timestamp": datetime.now().strftime("%Y-%m-%dT%H:%M:%SZ"),
                "settings_file": str(temp_path),
                "parameters_modified": updated_params,
                "error": None
            }
            
            # Save to JSON file (flat structure with prefix)
            results_file = validation_dir / "step02__settings.json"
            with open(results_file, "w") as f:
                json.dump(validation_result, f, indent=2)
                
            # Also save a copy of the settings file for validation (flat structure)
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            settings_copy = validation_dir / f"step02__settings_combination_{timestamp}.ini"
            shutil.copy(temp_path, settings_copy)
            
            # Log to file (flat structure)
            log_file = validation_dir / "step02__settings.log"
            with open(log_file, "w") as f:
                f.write(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] [VALIDATION] STEP 2: Settings File Creation\n")
                f.write(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] [SUCCESS] Created settings file at {temp_path}\n")
                f.write(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] [INFO] Modified parameters: {updated_params}\n")
                f.write(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] [INFO] Settings copy saved to: {settings_copy}\n")
                
            # Update status file (flat structure)
            with open(validation_dir / "status__current_step.txt", "w") as f:
                f.write("STEP_2_OF_10_COMPLETED")

        return temp_path

    except Exception as e:
        # Clean up on error
        try:
            if os.path.exists(temp_path):
                os.unlink(temp_path)
        except:
            pass
        raise e


def _load_unified_portfolio_for_combination(combo_id, timestamp):
    """Load unified portfolio file for specific optimization combination.

    Args:
        combo_id: Unique combination identifier
        timestamp: Timestamp for file matching

    Returns:
        pd.Series: Equity curve from Portfolio_Value column, or None if not found
    """
    try:
        # Construct expected filename pattern
        expected_pattern = f"unified_portfolio_combo_{combo_id}_*.csv"

        # Search for matching files in reporting directory
        reporting_dir = OUTPUT_DIR
        matching_files = list(reporting_dir.glob(expected_pattern))

        if not matching_files:
            # Try with exact timestamp
            exact_file = reporting_dir / f"unified_portfolio_combo_{combo_id}_{timestamp}.csv"
            if exact_file.exists():
                matching_files = [exact_file]
            else:
                logger.error(f"[MAPPING] No unified portfolio file found for combination {combo_id}")
                logger.error(f"[MAPPING] Searched pattern: {expected_pattern}")
                logger.error(f"[MAPPING] Searched directory: {reporting_dir}")
                return None

        # Use most recent file if multiple found
        target_file = max(matching_files, key=lambda f: f.stat().st_mtime)

        # Load and validate file
        df = pd.read_csv(target_file, index_col='Date', parse_dates=True)

        # Validate required columns
        required_cols = ['Portfolio_Value']
        missing_cols = [col for col in required_cols if col not in df.columns]
        if missing_cols:
            logger.error(f"[MAPPING] Missing required columns in {target_file}: {missing_cols}")
            logger.error(f"[MAPPING] Available columns: {list(df.columns)}")
            return None

        # Extract equity curve for validation
        equity_curve = df['Portfolio_Value'].copy()

        logger.info(f"[MAPPING] Loaded unified portfolio for combo {combo_id}")
        logger.info(f"[MAPPING] File: {target_file}")
        logger.info(f"[MAPPING] Data points: {len(equity_curve)}")
        logger.info(f"[MAPPING] Final portfolio value: ${equity_curve.iloc[-1]:,.2f}")

        return equity_curve

    except Exception as e:
        logger.error(f"[MAPPING] Error loading unified portfolio for combo {combo_id}: {e}")
        return None


def _run_pipeline_for_combination_BACKUP(combination, validation_mode=False, validation_dir=None):
    """BACKUP: Original subprocess implementation - keep for reference during testing."""
    # This function is kept as backup during the transition to direct function calls
    # It contains the original subprocess-based implementation that caused infinite recursion
    # TODO: Remove after successful validation of direct function approach
    pass


def _run_pipeline_for_combination_direct(combination, validation_mode=False, validation_dir=None):
    """Run pipeline directly in-process instead of subprocess - ELIMINATES INFINITE RECURSION."""
    try:
        print("*** DIRECT IMPLEMENTATION EXECUTING - NO SUBPROCESS! ***")
        print(f"*** Direct function called for combo: {combination}")

        logger.info(f"[DIRECT_IMPL] _run_pipeline_for_combination_direct ENTRY - NO SUBPROCESS!")

        # Create unique identifier for this combination
        combo_id = generate_combo_id(combination)

        # Store combination mapping for later file retrieval
        global combination_file_mapping
        combination_file_mapping[combo_id] = combination

        # Log execution start
        start_time = datetime.now()

        # Create execution log file (replaces subprocess log)
        if validation_mode and validation_dir:
            execution_log_file = validation_dir / f"step03__execution_{combo_id}.log"
        else:
            execution_log_file = Path("optimization_validation") / f"execution_{combo_id}_{start_time.strftime('%Y%m%d_%H%M%S')}.log"
            execution_log_file.parent.mkdir(exist_ok=True)

        # Log basic execution info
        with open(execution_log_file, 'w') as log_f:
            log_f.write(f"DIRECT EXECUTION LOG - {start_time.strftime('%Y-%m-%d %H:%M:%S')}\n")
            log_f.write(f"Combo ID: {combo_id}\n")
            log_f.write(f"Combination: {combination}\n")
            log_f.write(f"Validation Mode: {validation_mode}\n")
            log_f.write(f"Using direct optimization function (no temp settings)\n")
            log_f.write(f"\n--- DIRECT EXECUTION START ---\n")

        logger.info(f"[DIRECT] Starting pipeline for combo {combo_id} (validation_mode={validation_mode})")

        # DIRECT FUNCTION CALL - NO SUBPROCESS!
        # Import required functions
        from v4.settings.settings_CPS_v4 import load_settings
        from v4.pipeline.modes import run_optimization_combination

        # Load base settings (no temp file needed for direct function call)
        try:
            with open(execution_log_file, 'a') as log_f:
                log_f.write(f"Loading base settings for optimization combination\n")
            settings = load_settings()
            with open(execution_log_file, 'a') as log_f:
                log_f.write(f"Base settings loaded successfully\n")
        except Exception as e:
            with open(execution_log_file, 'a') as log_f:
                log_f.write(f"ERROR loading base settings: {str(e)}\n")
                log_f.write(f"Exception type: {type(e).__name__}\n")
            raise

        # Call run_optimization_combination directly (NO SUBPROCESS, NO TEMP FILES)
        try:
            with open(execution_log_file, 'a') as log_f:
                log_f.write(f"Calling run_optimization_combination...\n")
            results = run_optimization_combination(settings, combination, combo_id)
            with open(execution_log_file, 'a') as log_f:
                log_f.write(f"Optimization combination execution completed successfully\n")
        except Exception as e:
            with open(execution_log_file, 'a') as log_f:
                log_f.write(f"ERROR in optimization combination execution: {str(e)}\n")
                log_f.write(f"Exception type: {type(e).__name__}\n")
            raise

        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()

        # Log successful execution
        with open(execution_log_file, 'a') as log_f:
            log_f.write(f"--- DIRECT EXECUTION END ---\n")
            log_f.write(f"Duration: {duration:.1f} seconds\n")
            log_f.write(f"Results keys: {list(results.keys()) if results else 'None'}\n")

        logger.info(f"[DIRECT] Pipeline completed successfully for combo {combo_id} in {duration:.1f}s")

        # Extract equity curve from optimization results
        equity_curve = results.get('equity_curve') if results else None

        if equity_curve is not None and results.get('success', False):
            logger.info(f"[DIRECT] Successfully extracted equity curve for combo {combo_id} ({len(equity_curve)} data points)")

            # Log equity curve info
            with open(execution_log_file, 'a') as log_f:
                log_f.write(f"Equity curve extracted: {len(equity_curve)} data points\n")
                if len(equity_curve) > 0:
                    log_f.write(f"First value: {equity_curve.iloc[0]:.2f}\n")
                    log_f.write(f"Last value: {equity_curve.iloc[-1]:.2f}\n")

            return equity_curve
        else:
            error_msg = f"Failed to extract equity curve from optimization results for combo: {combination}"
            logger.error(f"[DIRECT] {error_msg}")

            with open(execution_log_file, 'a') as log_f:
                log_f.write(f"ERROR: No equity curve in optimization results\n")
                log_f.write(f"Available results: {results}\n")
                log_f.write(f"Success flag: {results.get('success', False) if results else 'No results'}\n")

            raise ValueError(error_msg)

    except Exception as e:
        logger.error(f"[DIRECT] Exception in direct pipeline execution: {e}")

        # Log error details
        if 'execution_log_file' in locals():
            with open(execution_log_file, 'a') as log_f:
                log_f.write(f"ERROR: {str(e)}\n")
                log_f.write(f"Exception type: {type(e).__name__}\n")

        raise RuntimeError(f"Direct pipeline execution failed for combination {combination}") from e


def _run_pipeline_for_combination(combination, validation_mode=False, validation_dir=None):
    """Run unified pipeline for a specific parameter combination and return equity curve.

    NEW IMPLEMENTATION: Uses direct function calls instead of subprocess to eliminate infinite recursion.
    """
    print("="*80)
    print("*** NEW IMPLEMENTATION CALLED - NO SUBPROCESS! ***")
    print(f"*** Function: _run_pipeline_for_combination")
    print(f"*** Combination: {combination}")
    print(f"*** Validation mode: {validation_mode}")
    print("="*80)

    logger.info(f"[DIRECT_ENTRY] _run_pipeline_for_combination called with combo: {combination}")
    logger.info(f"[DIRECT_ENTRY] validation_mode={validation_mode}, validation_dir={validation_dir}")
    return _run_pipeline_for_combination_direct(combination, validation_mode, validation_dir)


def _validate_single_combination(combination, config_path=None, validation_mode=False, validation_dir=None):
    """Minimal validation - run single real backtest for validation according to stepmappingopt.md."""
    if not validation_mode:
        return True

    try:
        # STEP 3: Single Combination Test
        _log_validation_step(3, "Single Combination Test", "RUNNING", validation_dir=validation_dir)

        # Load configuration to check optimization mode
        if config_path is None:
            config_path = str(V4_SETTINGS_FILE)
        
        config = configparser.ConfigParser(interpolation=None)
        config.read(config_path)
        
        # Check if optimization is active to determine validation approach
        optimization_active = config.getboolean('System', 'optimization_active', fallback=False)

        if optimization_active:
            # OPTIMIZATION MODE: Run direct function call for this specific combination (NO SUBPROCESS)
            equity_curve = _run_pipeline_for_combination(combination, validation_mode, validation_dir)
        else:
            # SINGLE MODE: Use existing pipeline results (no subprocess needed)
            logger.debug("Single mode validation - using existing pipeline results")
            equity_curve = _load_existing_equity_curve(combination)
        
        if equity_curve is not None and len(equity_curve) > 0:
            # Create validation artifacts
            validation_result = {
                "step": 3,
                "status": "SUCCESS",
                "timestamp": datetime.now().strftime("%Y-%m-%dT%H:%M:%SZ"),
                "combination": combination,
                "equity_curve_length": len(equity_curve),
                "first_value": float(equity_curve.iloc[0]) if len(equity_curve) > 0 else None,
                "last_value": float(equity_curve.iloc[-1]) if len(equity_curve) > 0 else None,
                "mean_value": float(equity_curve.mean()) if len(equity_curve) > 0 else None,
                "return_code": 0,
                "error": None
            }
            
            # Save to JSON file (flat structure)
            results_file = validation_dir / "step03__single_result.json"
            with open(results_file, "w") as f:
                json.dump(validation_result, f, indent=2)
            
            # Log to file (flat structure)
            log_file = validation_dir / "step03__single.log"
            with open(log_file, "w") as f:
                f.write(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] [VALIDATION] STEP 3: Single Combination Test\n")
                f.write(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] [SUCCESS] Single backtest completed successfully\n")
                f.write(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] [INFO] Equity curve length: {len(equity_curve)}\n")
                f.write(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] [INFO] First value: {validation_result['first_value']}\n")
                f.write(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] [INFO] Last value: {validation_result['last_value']}\n")
            
            # Update status
            _log_validation_step(3, "Single Combination Test", "COMPLETED", validation_dir=validation_dir)
            return True
        else:
            error_msg = f"Single backtest failed - no equity curve returned for combination: {combination}"
            _log_validation_step(3, "Single Combination Test", "FAILED", error_msg, validation_dir=validation_dir)
            return False
            
    except Exception as e:
        error_msg = f"Single backtest failed with exception: {str(e)}"
        _log_validation_step(3, "Single Combination Test", "FAILED", error_msg, validation_dir=validation_dir)
        return False


def _run_matrix_optimization(combinations, validation_mode=False, validation_dir=None, csv_flag_use=False):
    """Run matrix-based optimization using EquityCurvesManager and ComboIDTracker for comprehensive visibility."""
    
    start_time = time.time()

    # INSTRUMENTATION: Entry logging
    logger.info(f"Starting matrix optimization with {len(combinations)} combinations")
    logger.debug(f"Parameter grid size: {len(combinations)} combinations")
    for i, combo in enumerate(combinations[:3]):  # Log first 3 for verification
        logger.debug(f"Sample combo {i}: {combo}")
    
    # MILESTONE: Start optimization
    logger.info(f"[OPTIMIZATION] Starting {len(combinations)} combinations...")

    # Initialize ComboIDTracker for comprehensive visibility
    output_dir = validation_dir if validation_mode and validation_dir else OUTPUT_DIR
    combo_tracker = ComboIDTracker(combinations, output_dir)
    combo_tracker.display_startup_summary()

    # Initialize EquityCurvesManager
    equity_manager = EquityCurvesManager(output_dir=str(OUTPUT_DIR))

    # Get date range from price data for validation tests
    price_df = _load_price_data()
    date_index = price_df.index
    equity_manager.initialize_with_date_index(date_index)

    # Progress tracking
    completed = 0
    failed = 0
    progress_interval = max(1, len(combinations) // 10)  # Report every 10%

    # Process each combination
    for i, combo in enumerate(combinations):
        # Generate combo ID for tracking
        combo_id = generate_combo_id(combo)
        combo_start_time = time.time()
        
        try:
            # Start tracking this combo
            combo_tracker.start_processing(combo_id, combo)
            
            # Use smart logger for optimization progress
            logger.milestone(f"Processing combination {i+1}/{len(combinations)}: {combo}")
            combo_tracker.update_phase(combo_id, "strategy_calculation", "running", {"combination_index": i})
            
            # STEP 4: Equity Curve Validation
            if validation_mode and validation_dir:
                _log_validation_step(4, "Equity Curve Processing", validation_dir=validation_dir)
                combo_tracker.update_phase(combo_id, "validation_setup", "running")
            
            # Run pipeline for this combination (silent)
            combo_tracker.update_phase(combo_id, "pipeline_execution", "running")
            equity_curve = _run_pipeline_for_combination(combo)

            if equity_curve is not None:
                combo_tracker.update_phase(combo_id, "data_processing", "running")
                
                # Use smart logger for strategy calculation results
                logger.debug(f"Strategy calc result - equity curve length: {len(equity_curve)}")
                logger.debug(f"Strategy calc result - first value: {equity_curve.iloc[0] if len(equity_curve) > 0 else 'EMPTY'}")
                logger.debug(f"Strategy calc result - last value: {equity_curve.iloc[-1] if len(equity_curve) > 0 else 'EMPTY'}")
                logger.debug(f"Strategy calc result - mean value: {equity_curve.mean() if len(equity_curve) > 0 else 'EMPTY'}")
                logger.debug(f"Strategy calc result - any zeros?: {(equity_curve == 0).any() if len(equity_curve) > 0 else 'EMPTY'}")
                
                # Add to equity curves manager
                column_name = equity_manager.add_combination_result(combo, equity_curve)
                completed += 1
                
                logger.debug(f"Successfully stored combo {i} as {column_name}")
                
                # Track successful completion
                combo_duration = time.time() - combo_start_time
                combo_tracker.complete_processing(combo_id, True, combo_duration, {
                    "column_name": column_name,
                    "equity_curve_length": len(equity_curve),
                    "final_value": float(equity_curve.iloc[-1]) if len(equity_curve) > 0 else None
                })
                
                # STEP 4: Additional Equity Curve Validation
                if validation_mode and validation_dir:
                    # Save equity curve data for validation
                    validation_result = {
                        "step": 4,
                        "status": "SUCCESS",
                        "timestamp": datetime.now().strftime("%Y-%m-%dT%H:%M:%SZ"),
                        "combination_index": i,
                        "combination_params": combo,
                        "equity_curve_length": len(equity_curve),
                        "first_value": float(equity_curve.iloc[0]) if len(equity_curve) > 0 else None,
                        "last_value": float(equity_curve.iloc[-1]) if len(equity_curve) > 0 else None,
                        "mean_value": float(equity_curve.mean()) if len(equity_curve) > 0 else None,
                        "has_zeros": bool((equity_curve == 0).any()) if len(equity_curve) > 0 else False,
                        "error": None
                    }
                    
                    # Save to CSV file - HARD STOP if csv_flag_use is False
                    if csv_flag_use:
                        results_file = validation_dir / "step04__equity_curves.csv"
                        df_row = pd.DataFrame([{
                            "combination_index": i,
                            "equity_curve_length": len(equity_curve),
                            "first_value": validation_result["first_value"],
                            "last_value": validation_result["last_value"],
                            "mean_value": validation_result["mean_value"],
                            "has_zeros": validation_result["has_zeros"]
                        }])

                        # Append to existing file or create new
                        if results_file.exists():
                            df_row.to_csv(results_file, mode='a', header=False, index=False)
                        else:
                            df_row.to_csv(results_file, index=False)
                    else:
                        logger.info(f"Skipped validation CSV save for combo {i} (CSV generation disabled during optimization)")
                    
                    # Log to file (flat structure)
                    log_file = validation_dir / "step04__equity_curves.log"
                    with open(log_file, "a") as f:
                        f.write(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] [VALIDATION] Combination {i+1}/{len(combinations)} processed successfully\n")
                        f.write(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] [INFO] Equity curve length: {len(equity_curve)}\n")
                        f.write(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] [INFO] First value: {validation_result['first_value']}\n")
                        f.write(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] [INFO] Last value: {validation_result['last_value']}\n")
            else:
                # Track failed combination
                combo_duration = time.time() - combo_start_time
                error_msg = f"Strategy calculation returned None for combo {i}: {combo}"
                logger.error(f"[TRACE] {error_msg}")
                combo_tracker.complete_processing(combo_id, False, combo_duration, {"error": error_msg})
                failed += 1
                
                if validation_mode and validation_dir:
                    combo_tracker.update_phase(combo_id, "validation_error", "failed", {"error": error_msg})

        except Exception as e:
            # Track exception in combination processing
            combo_duration = time.time() - combo_start_time
            error_msg = f"Exception in strategy calculation for combo {i}: {e}"
            logger.error(f"[TRACE] {error_msg}")
            combo_tracker.complete_processing(combo_id, False, combo_duration, {"error": str(e), "exception_type": type(e).__name__})
            failed += 1

        # MILESTONE: Progress reporting and periodic saves
        if (i + 1) % progress_interval == 0 or (i + 1) == len(combinations):
            elapsed = time.time() - start_time
            pct = ((i + 1) / len(combinations)) * 100

            if i + 1 < len(combinations):
                eta_seconds = (elapsed / (i + 1)) * (len(combinations) - (i + 1))
                eta = f"{eta_seconds/60:.0f}min" if eta_seconds > 60 else f"{eta_seconds:.0f}s"
                logger.info(f"[PROGRESS] {i+1}/{len(combinations)} ({pct:.0f}%) | Success: {completed} | Failed: {failed} | ETA: {eta}")
            else:
                duration = f"{elapsed/60:.0f}min" if elapsed > 60 else f"{elapsed:.0f}s"
                logger.info(f"[PROGRESS] {i+1}/{len(combinations)} (100%) | Success: {completed} | Failed: {failed} | Duration: {duration}")
            
            # Save progress
            equity_manager.save_to_disk()
            logger.debug(f"Progress saved after {i+1} combinations")

    # Display completion summary with tracking data
    combo_tracker.display_completion_summary()
    
    # Save tracking data
    tracking_file = combo_tracker.save_tracking_data()
    
    # MILESTONE: Final save and create summary
    equity_file, metadata_file = equity_manager.save_to_disk()
    
    # Create performance summary
    summary_df = equity_manager.create_performance_summary()
    if not summary_df.empty:
        summary_file = get_reporting_file_path(f"optimization_performance_summary_{equity_manager.timestamp}.csv")
        summary_df.to_csv(summary_file, index=False)
        logger.info(f"Performance summary saved: {summary_file}")
    
    # Convert back to legacy format for compatibility (optional)
    equity_matrix = equity_manager.equity_curves_df.copy()
    combination_metadata = {}
    
    # Create legacy metadata format
    for column_name, metadata in equity_manager.combination_metadata.items():
        if 'parameters' in metadata:
            combination_metadata[column_name] = metadata['parameters']

    # MILESTONE: Complete
    matrix_size = f"{len(combinations)} combinations × {len(date_index)} days"
    logger.info(f"[MATRIX] Saved equity matrix: {matrix_size} → {equity_file}")
    logger.info(f"[COMBO_TRACK] Tracking data saved: {tracking_file}")
    
    # INSTRUMENTATION: Exit logging
    logger.info(f"Matrix optimization completed - processed: {completed}, failed: {failed}")
    logger.info(f"Final matrix shape: {equity_matrix.shape}")
    logger.info(f"Equity file saved to: {equity_file}")
    logger.info(f"Metadata file saved to: {metadata_file}")

    return equity_matrix, combination_metadata