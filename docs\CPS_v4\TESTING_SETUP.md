# CPS V4 Testing Setup

## Current Issue
Step 3 of the validation pipeline hangs for 6+ minutes then fails. We need to debug what's happening in the subprocess.

## Test Files Overview

### 1. Root Level - Integration Testing (USE THIS)
- **Batch file**: `test_optimization_fix_simple.bat`
- **Python script**: `test_optimization_validation.py`
- **Purpose**: Full 10-step validation pipeline testing
- **Current problem**: Step 3 hangs during subprocess execution

### 2. V4 Directory - Unit Testing (Different purpose)
- **Batch file**: `v4/test_optimization_fix_simple.bat`
- **Python script**: `v4/tests/v4/test_optimization_fix.py`
- **Purpose**: Unit test for EMA allocation model parameter override fix
- **Status**: This is working fine

## For Debugging Step 3 Issue

**Run**: `test_optimization_fix_simple.bat` (root level)

**When it fails, check these logs**:
1. **Main log**: `optimization_validation/validation_run_YYYYMMDD_HHMMSS.log`
2. **Validation directory**: `optimization_validation/YYYYMMDD_HHMMSS/`
   - `step03__subprocess_*.log` - **KEY**: Shows what subprocess is doing during hang
   - `step03__log.txt` - Basic step status
3. **Pipeline mode log**: `optimization_validation/pipeline_mode_debug.log` - Verify combo_id detection

## Expected Flow
1. Step 1: Parameter extraction ✅ (working)
2. Step 2: Settings file creation ✅ (working)  
3. Step 3: Single combination test ❌ (hangs for 6+ minutes)
   - Creates temp settings file
   - Sets CPS_V4_COMBO_ID environment variable
   - Calls subprocess: `python v4/run_unified_pipeline.py --settings temp_file`
   - **Problem**: Subprocess hangs instead of completing

## What We Added for Debugging
- **Subprocess logging**: Captures complete stdout/stderr from the hanging subprocess
- **Pipeline mode logging**: Verifies if combo_id detection is working
- **Simple batch file**: Points to the right logs when failure occurs

## Next Steps
1. Run `test_optimization_fix_simple.bat`
2. When Step 3 hangs, check the subprocess log to see what it's actually doing
3. Check pipeline mode log to verify our fix is working
4. Fix the actual issue once we can see what's causing the hang