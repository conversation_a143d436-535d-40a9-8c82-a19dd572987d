# Plan

## Notes

### New Staged Architecture (2025-06-20)
- Data → Algo Engine (EMA → Ranking → Allocation) → Trading Engine
- **Algo Engine**: compute EMAs, asset rankings, and target allocations over the full price history; output `signal_history`
- **Trading Engine**: ingest pre-computed `signal_history` in backtest, removing inline signal generation
- **CPS v4 Parameter Constraint**: CPS v4 settings loading/process must remain untouched and drive all algorithm behavior

## Notes
- All validation and output verification must use ONLY the full production code flow (real backtest engine and reporting pipeline).
- Synthetic/test module validation is discontinued and must NOT be used for any output validation or debugging.
- All fixes, diagnostics, and verification must be performed in the production pipeline only.
- When converting any module to CPS v4, always preserve all production logic and business rules in the original file.
- Only inject the new parameter system (CPS v4) for configuration and parameter flow.
- Do NOT replace, minimize, or reimplement modules as stubs or simplified versions.
- For all required parameters, do NOT use `.get(..., fallback)`. All required parameters must be present in CPS v4 settings. If missing, raise an error and halt execution.
- Only optional/reporting parameters may use `.get(..., fallback)` only if the user reviews and approves on case by case basis.
- Key Ref:
	-- \memory-bank\v4_module+functions_list_AI.md
	

## Task List

### Phase 1: Current Active
- [ ] **Validate Fix of`data_loader_v4.py`:**
	-- validate redundant risk_free_rate removed from settings ini
	-- validate we have new data downloaded, and switch to read mode
1.1 [ ] **Validation : End-to-End**
  - [ ] Run `run_real_data_validation.py` to validate the full data pipeline: data loader -> backtest engine -> output generation.
	-- validate broader sequence of data into backtest engine into output
  - [ ] Create a checklist of all expected output elements (e.g., Excel report, trade log, charts).
  - [ ] Verify all checklist items are generated correctly against a full run.

### Phase 2: V4 Reporting System Implementation & Engine-Reporting Integration Testing (READY TO START)
- [ ] [Task 2.1] Implement V4 Reporting Modules
- [ ] Create `v4_reporting` directory structure
- [ ] Begin implementation of `v4_performance_report.py`
- [ ] Implement `v4_allocation_report.py`
- Ensure Reporting matches exactly standard ref md: report_standard_verification.md


### Phase 3: [ ] **Future Steps**
	- Full GUI Integration & End-to-End Testing (NOT STARTED)
  - [ ] Implement dynamic TNX data download for `risk_free_rate`.
  - [ ] Address potential indentation error in `settings_CPS_v4.py` (lower priority).


