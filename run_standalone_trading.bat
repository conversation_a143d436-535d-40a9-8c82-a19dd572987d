@echo off
REM ============================================
REM Script: run_standalone_trading.bat
REM Description: Runs only the trading phase using existing signal files
REM ============================================

SETLOCAL

REM --- Paths ---
SET "PYTHON_EXE=F:\AI_Library\my_quant_env\Scripts\python.exe"
SET "SCRIPT_DIR=%~dp0"
SET "TRADING_SCRIPT=%SCRIPT_DIR%run_standalone_trading.py"
SET "OUTPUT_DIR=%SCRIPT_DIR%v4_trace_outputs"

REM --- Ensure output directory exists ---
IF NOT EXIST "%OUTPUT_DIR%" (
    mkdir "%OUTPUT_DIR%"
)

REM --- Timestamp (YYYYMMDD_HHMMSS) ---
SET "TIMESTAMP=%DATE:~10,4%%DATE:~4,2%%DATE:~7,2%_%TIME:~0,2%%TIME:~3,2%%TIME:~6,2%"
SET "TIMESTAMP=%TIMESTAMP: =0%"

REM --- Log files ---
SET "FULL_LOG=%OUTPUT_DIR%\trading_log_%TIMESTAMP%.txt"
SET "FILTERED_LOG=%OUTPUT_DIR%\trading_filtered_%TIMESTAMP%.txt"

echo [%TIME%] Running standalone trading phase (log: "%FULL_LOG%")

REM --- Get signal file argument if provided ---
SET "SIGNAL_FILE=%~1"
IF "%SIGNAL_FILE%"=="" (
    echo No signal file specified. Using default signals_output.parquet
) ELSE (
    echo Using signal file: %SIGNAL_FILE%
)

REM --- Run Trading Phase ---
echo ===== V4 STANDALONE TRADING PHASE ===== > "%FULL_LOG%"
echo [%TIME%] Starting Trading Phase... >> "%FULL_LOG%"

REM Run trading phase and capture output
echo [%TIME%] Starting Trading Phase...
"%PYTHON_EXE%" "%TRADING_SCRIPT%" %SIGNAL_FILE% > "%FULL_LOG%" 2>&1
SET "TRADING_EXIT_CODE=%ERRORLEVEL%"

REM Display trading phase results
type "%FULL_LOG%"

echo Trading phase completed with exit code %TRADING_EXIT_CODE%
echo Trading phase completed with exit code %TRADING_EXIT_CODE% >> "%FULL_LOG%"

REM --- Create filtered log ---
echo ===== FILTERED OUTPUT ===== > "%FILTERED_LOG%"

REM Search for key markers in log file
echo Extracting key information to filtered log...
findstr /C:"[MILESTONE]" /C:"[ERROR]" /C:"[WARNING]" "%FULL_LOG%" >> "%FILTERED_LOG%"

echo [%TIME%] V4 standalone trading phase completed. Filtered log saved to: "%FILTERED_LOG%"

exit /b %TRADING_EXIT_CODE%
