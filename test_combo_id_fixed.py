#!/usr/bin/env python
"""
Fixed Test for Combo ID Tracking System

This test addresses the Windows file locking issue by using proper cleanup
and context manager support.

Author: AI Assistant
Date: 2025-07-28
"""

import sys
import os
from pathlib import Path
import tempfile

# Add the project root to Python path
sys.path.insert(0, str(Path(__file__).parent))

# Import the tracking system
from v4.py_reporting.combo_id_tracker import ComboID<PERSON>racker

def test_combo_id_tracker_with_context_manager():
    """Test ComboIDTracker using context manager to avoid file locking issues."""
    print("=" * 60)
    print("TESTING: ComboIDTracker with Context Manager")
    print("=" * 60)
    
    # Create test combinations
    combinations = [
        {
            'st_lookback': 5,
            'mt_lookback': 30,
            'lt_lookback': 100,
            'execution_delay': 1,
            'system_lookback': 60,
            'top_n': 2
        },
        {
            'st_lookback': 15,
            'mt_lookback': 70,
            'lt_lookback': 100,
            'execution_delay': 1,
            'system_lookback': 60,
            'top_n': 2
        }
    ]
    
    try:
        # Create temporary directory for testing
        with tempfile.TemporaryDirectory() as temp_dir:
            output_dir = Path(temp_dir)
            
            print("1. Testing with context manager and file logging...")
            # Use context manager for proper cleanup
            with ComboIDTracker(combinations, output_dir, enable_file_logging=True) as tracker:
                
                # Test startup summary
                print("\n2. Testing startup summary...")
                tracker.display_startup_summary()
                
                # Test combo processing simulation
                print("\n3. Testing combo processing simulation...")
                
                # Process first combo successfully
                combo_id_1 = "S5_M30_L100_E1_T2"
                tracker.start_processing(combo_id_1, combinations[0])
                tracker.update_phase(combo_id_1, "settings_creation", "running")
                tracker.register_file_creation(combo_id_1, "settings", Path("temp_settings_S5_M30_L100_E1_T2.ini"))
                tracker.complete_processing(combo_id_1, True, 45.2, {"final_value": 125000.0})
                
                # Process second combo with failure
                combo_id_2 = "S15_M70_L100_E1_T2"
                tracker.start_processing(combo_id_2, combinations[1])
                tracker.update_phase(combo_id_2, "pipeline_execution", "failed")
                tracker.complete_processing(combo_id_2, False, 600.0, {"error": "Pipeline timeout"})
                
                # Test status retrieval
                print("\n4. Testing status retrieval...")
                progress = tracker.get_progress_summary()
                print(f"Progress: {progress.completed_combos} completed, {progress.failed_combos} failed")
                
                # Test completion summary
                print("\n5. Testing completion summary...")
                tracker.display_completion_summary()
                
                # Test data saving
                print("\n6. Testing data saving...")
                tracking_file = tracker.save_tracking_data()
                print(f"Tracking data saved to: {tracking_file}")
                
                # Verify files exist
                files_created = list(output_dir.glob("*"))
                print(f"\nFiles created: {len(files_created)}")
                for file in files_created:
                    print(f"  - {file.name}")
            
            # Context manager should have cleaned up automatically
            print("\n✅ Context manager cleanup completed successfully!")
            
            # Verify we can still access the temp directory
            remaining_files = list(output_dir.glob("*"))
            print(f"Files remaining after cleanup: {len(remaining_files)}")
            
            return True
            
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_combo_id_tracker_no_file_logging():
    """Test ComboIDTracker with file logging disabled."""
    print("\n" + "=" * 60)
    print("TESTING: ComboIDTracker without File Logging")
    print("=" * 60)
    
    combinations = [
        {
            'st_lookback': 25,
            'mt_lookback': 90,
            'lt_lookback': 100,
            'execution_delay': 1,
            'system_lookback': 60,
            'top_n': 2
        }
    ]
    
    try:
        with tempfile.TemporaryDirectory() as temp_dir:
            output_dir = Path(temp_dir)
            
            print("1. Testing with file logging disabled...")
            # Disable file logging to avoid Windows file locking
            with ComboIDTracker(combinations, output_dir, enable_file_logging=False) as tracker:
                
                # Test basic functionality
                combo_id = "S25_M90_L100_E1_T2"
                tracker.start_processing(combo_id, combinations[0])
                tracker.update_phase(combo_id, "test_phase", "running")
                tracker.complete_processing(combo_id, True, 30.0)
                
                # Test progress
                progress = tracker.get_progress_summary()
                print(f"Progress: {progress.completed_combos} completed")
                
                # Test validation
                is_valid = tracker.validate_combo_id_format(combo_id)
                print(f"Combo ID validation: {'✅ VALID' if is_valid else '❌ INVALID'}")
                
                # Save data (should work without file logging)
                tracking_file = tracker.save_tracking_data()
                print(f"Tracking data saved to: {tracking_file}")
            
            print("\n✅ No file logging test completed successfully!")
            return True
            
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_manual_cleanup():
    """Test manual cleanup without context manager."""
    print("\n" + "=" * 60)
    print("TESTING: Manual Cleanup")
    print("=" * 60)
    
    combinations = [
        {
            'st_lookback': 10,
            'mt_lookback': 50,
            'lt_lookback': 100,
            'execution_delay': 1,
            'system_lookback': 60,
            'top_n': 2
        }
    ]
    
    try:
        with tempfile.TemporaryDirectory() as temp_dir:
            output_dir = Path(temp_dir)
            
            print("1. Testing manual cleanup...")
            # Create tracker without context manager
            tracker = ComboIDTracker(combinations, output_dir, enable_file_logging=True)
            
            try:
                # Test basic functionality
                combo_id = "S10_M50_L100_E1_T2"
                tracker.start_processing(combo_id, combinations[0])
                tracker.complete_processing(combo_id, True, 25.0)
                
                print("2. Performing manual cleanup...")
                # Manual cleanup
                tracker.cleanup()
                
                print("✅ Manual cleanup completed successfully!")
                return True
                
            finally:
                # Ensure cleanup happens even if test fails
                try:
                    tracker.cleanup()
                except:
                    pass
            
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all fixed combo ID tracking tests."""
    print("🚀 COMBO ID TRACKING - FIXED TESTS")
    print("Testing combo ID tracking with proper cleanup...")
    
    results = []
    
    # Test 1: Context manager with file logging
    results.append(test_combo_id_tracker_with_context_manager())
    
    # Test 2: No file logging
    results.append(test_combo_id_tracker_no_file_logging())
    
    # Test 3: Manual cleanup
    results.append(test_manual_cleanup())
    
    # Summary
    print("\n" + "=" * 60)
    print("FIXED TESTS SUMMARY")
    print("=" * 60)
    
    test_names = [
        "Context Manager with File Logging",
        "No File Logging Mode",
        "Manual Cleanup"
    ]
    
    for i, (name, result) in enumerate(zip(test_names, results)):
        status = '✅ PASS' if result else '❌ FAIL'
        print(f"{i+1}. {name}: {status}")
    
    all_passed = all(results)
    overall_status = '✅ ALL TESTS PASSED' if all_passed else '❌ SOME TESTS FAILED'
    
    print(f"\nOverall Result: {overall_status}")
    
    if all_passed:
        print("\n🎉 Windows file locking issue is FIXED!")
        print("✅ Context manager cleanup works")
        print("✅ File logging can be disabled for testing")
        print("✅ Manual cleanup works properly")
        print("✅ No more Windows file locking errors")
        print("\nThe combo ID tracking system is now robust and Windows-compatible!")
    else:
        print("\n⚠️  Some tests still failed. Please check the implementation.")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)