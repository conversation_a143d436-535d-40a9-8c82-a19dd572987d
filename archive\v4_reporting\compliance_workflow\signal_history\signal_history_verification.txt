Signal History Verification
=========================

1. Signal History Structure
   - Type: Dictionary (date -> allocations)
   - Number of dates: 252
   - Sample dates:
     * 2023-01-01: {'AAPL': np.float64(0.2332226162349345), 'MSFT': np.float64(0.21255442470274666), 'GOOG': np.float64(0.0679023514050897), 'AMZN': np.float64(0.23661064402300652), 'CASH': np.float64(0.2497099636342226)}
     * 2023-01-02: {'AAPL': np.float64(0.2332226162349345), 'MSFT': np.float64(0.21255442470274666), 'GOOG': np.float64(0.0679023514050897), 'AMZN': np.float64(0.23661064402300652), 'CASH': np.float64(0.2497099636342226)}
     * 2023-01-03: {'AAPL': np.float64(0.2332226162349345), 'MSFT': np.float64(0.21255442470274666), 'GOOG': np.float64(0.0679023514050897), 'AMZN': np.float64(0.23661064402300652), 'CASH': np.float64(0.2497099636342226)}

2. Allocation Verification
   - 2023-01-01: Sum = 1.0
   - 2023-01-02: Sum = 1.0
   - 2023-01-03: Sum = 1.0
   - 2023-01-04: Sum = 1.0
   - 2023-01-05: Sum = 1.0
