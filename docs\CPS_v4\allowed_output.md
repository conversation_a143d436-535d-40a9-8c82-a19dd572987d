# Allowed Output Specification for Optimization Mode

## Purpose
This document defines exactly which files are allowed to be created during optimization validation and matrix optimization runs. This specification prevents file collisions and ensures clean, efficient optimization execution.

## Current Status: Debugging Phase
During the current debugging phase, we allow minimal output to identify and fix validation issues. Once validation is working, additional user controls will be implemented.

## Allowed Outputs During Optimization

### ✅ LOGS AND STATUS FILES (Debugging Phase Only)
**Status:** ALLOWED (temporary during debugging)
**Future:** Will be user-controllable via ini setting

- `validation__main.log` - Main validation process log
- `step01__parameters.log` - Parameter extraction log  
- `step02__settings.log` - Settings file creation log
- `step03__single.log` - Single combination test log
- `step04__equity_curves.log` - Equity curve processing log
- `status__current_step.txt` - Current validation step status
- `step01__parameters.json` - Extracted parameter combinations
- `step02__settings_combination_*.ini` - Settings files per combination
- `step03__single_result.json` - Single combination test results

### ✅ UNIFIED ALLOCATION FILE
**Status:** REQUIRED
**Purpose:** Single file per optimization run with unique naming

- `unified_allocation_history_YYYYMMDD_HHMMSS.csv`
- Contains: Date, Asset positions, Dollar allocations, Portfolio values
- Naming: One file per optimization run with timestamp
- Location: `v4_trace_outputs/`

### ✅ COMBINED EQUITY CURVE MATRIX
**Status:** REQUIRED  
**Purpose:** All parameter combinations in single matrix file

- `optimization_equity_curves_matrix_YYYYMMDD_HHMMSS.csv`
- Contains: Date column + one column per parameter combination
- Naming: One file per optimization run with timestamp
- Location: `optimization_validation/YYYYMMDD_HHMMSS/`

## Prohibited Outputs During Optimization

### ❌ INDIVIDUAL EQUITY CURVE CSV FILES
**Status:** PROHIBITED
**Reason:** Causes file collisions during optimization

- `reporting/equity_curve_strategy_latest.csv` ← **MAIN COLLISION SOURCE**
- Any individual equity curve files per combination

### ❌ TRADE LOG FILES  
**Status:** PROHIBITED
**Reason:** Not needed for optimization validation, causes file bloat

- `trade_log_YYYYMMDD.csv`
- `unified_trade_log_*.csv`
- Any trade-level detail files

### ❌ SIGNAL HISTORY FILES
**Status:** PROHIBITED  
**Reason:** Not needed for optimization validation, causes file bloat

- `signal_history_YYYYMMDD.csv`
- `04_raw_signal_history_*.csv`
- Any signal-level detail files

### ❌ ALLOCATION HISTORY FILES (Individual)
**Status:** PROHIBITED
**Reason:** Replaced by unified allocation file

- `allocation_history_YYYYMMDD.csv`
- Individual allocation files per combination

## Implementation Requirements

### Flag Configuration
The following ini flags must enforce this specification:

```ini
# Core CSV Control
csv_flag_use = True                    # Enable minimal CSV output
csv_valid_det = False                  # Disable extra validation CSVs
optimization_active = True             # Enable optimization mode
retain_csv_signal_output = False       # Disable signal history CSVs

# Future: Add user control for logs (TASK)
debug_logs_enabled = True              # Control validation logs (TO BE IMPLEMENTED)
```

### Code Enforcement Points
1. **modes.py**: Must NOT create `equity_curve_strategy_latest.csv` during optimization
2. **modes.py**: Must respect `csv_valid_det = False` for trade logs and allocation history
3. **performance_table_generator.py**: Must create only matrix equity curve file
4. **All modules**: Must check optimization_active flag before creating individual files

## Future Tasks

### TASK: User-Controllable Debug Logs
**Priority:** Medium (after validation is working)
**Description:** Add ini setting to control validation log output during optimization
**Implementation:** 
- Add `debug_logs_enabled = True/False` to settings
- Modify validation framework to respect this flag
- Default to True during debugging phase

## Validation Criteria

### Success Criteria for Optimization Run
1. ✅ Only allowed files are created
2. ✅ No collision files exist in reporting directory
3. ✅ Matrix equity curve file contains all combinations
4. ✅ Unified allocation file has unique timestamp naming
5. ✅ No prohibited CSV files are generated

### Failure Indicators
1. ❌ `equity_curve_strategy_latest.csv` exists after optimization
2. ❌ Multiple files with same timestamp (collisions)
3. ❌ Trade log or signal history files created
4. ❌ Individual allocation history files created

## Notes
- This specification is designed to eliminate file collisions during optimization
- Focus is on minimal, essential output for validation purposes
- Additional output can be enabled for single-run mode when `optimization_active = False`
- All file naming must include timestamps to ensure uniqueness
