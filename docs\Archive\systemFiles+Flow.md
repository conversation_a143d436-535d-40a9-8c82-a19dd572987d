# System Files + Flow Documentation

```mermaid
flowchart TD
    PB[projectbrief.md] --> PC[productContext.md]
    PB --> SP[systemPatterns.md]
    PB --> TC[techContext.md]
    PB --> PM[parameters.md]
    PB --> PG[parameterGroups.md]
    PB --> EM[Engine Modules]
    PB --> PF[Process Flow]

    PC --> AC[activeContext.md]
    SP --> AC
    TC --> AC
    PM --> AC
    PG --> AC
    EM --> AC
    PF --> AC

    AC --> P[progress.md]
```

## Engine Modules and Functions List

```mermaid
graph TD
    A[Parameter Classification] --> B[Parameter Flow]
    B --> C[Module Mapping]
    C --> D[Reporting Standards]
    D --> E[User Guide]
```

```mermaid
flowchart LR
    %% Data Flow
    DataLoader[data/data_loader.py\nDataLoader] -->|OHLCV Data| Strategy
    DataLoader -->|Benchmark Data| Backtest

    %% Parameter Flow
    ParameterRegistry <-->|register_parameter_list| StrategyParameterSet[v3_engine/strategy_parameter_set.py\nStrategyParameterSet]
    ParameterRegistry <-->|Type Definitions| Parameters[v3_engine/parameters.py\nParameter Classes]
    Parameters -->|Optimize| ParameterOptimizer[v3_engine/parameter_optimizer.py\nOptimizer]
    ParameterRegistry -->|get_core_parameters| Backtest
    ParameterRegistry -->|get_strategy_parameters| Strategy
    ParameterRegistry -->|get_all_parameters| PerformanceReporterAdapter

    %% Execution Flow
    Strategy -->|Generate Signals| Backtest
    Backtest -->|Portfolio Updates| Execution[execution/engine.py\nExecution Engine]

    %% Reporting Flow
    Backtest -->|Results| PerformanceReporter
    Backtest -->|Allocations| AllocationReporter
    PerformanceReporter -->|Formatted Data| PerformanceReporterAdapter[v3_engine/performance_reporter_adapter.py\nAdapter]

    %% Utilities
    Backtest -->|Date Handling| DateUtils[utils/date_utils.py\nDate Utilities]
    Backtest -->|Log Trades| TradeLog[utils/trade_log.py\nTrade Log]

    %% Styling
    classDef gui fill:#BBDEFB,stroke:#1976D2,color:#0D47A1;
    classDef param fill:#C8E6C9,stroke:#388E3C,color:#1B5E20;
    classDef strategy fill:#FFECB3,stroke:#F57F17,color:#E65100;
    classDef report fill:#F3E5F5,stroke:#8E24AA,color:#4A148C;
    classDef util fill:#EFEBE9,stroke:#5D4037,color:#3E2723;
    class ParameterRegistry,Parameters,StrategyParameterSet,ParameterOptimizer param;
    class Strategy,Backtest strategy;
    class PerformanceReporter,AllocationReporter,PerformanceReporterAdapter report;
    class DateUtils,TradeLog util;
```

### Core Engine Components

1. **Backtest Engine** (`engine/backtest.py`)
   
   - Main backtest execution logic
   - Handles parameter optimization
   - Coordinates all engine components

2. **Portfolio Management** (`engine/portfolio.py`)
   
   - Tracks portfolio state and positions
   - Calculates performance metrics
   - Handles mark-to-market valuation

3. **Order Execution** (`engine/execution.py`)
   
   - Processes trades with slippage/commissions
   - Implements execution delay logic
   - Maintains trade history

4. **Signal Generation** (`v3_engine/ema_v3_adapter.py`)
   
   - EMA strategy signal calculations
   - Handles signal generation logic
   - Interfaces with parameter registry

## Key Parameter Registry Methods

| Method                                       | Description                                   | Usage                                                    |
| -------------------------------------------- | --------------------------------------------- | -------------------------------------------------------- |
| `register_parameter(group, parameter)`       | Register a single parameter with the registry | For registering individual parameters                    |
| `register_parameter_list(group, parameters)` | Register multiple parameters at once          | **Preferred method** for registering multiple parameters |
| `get_parameter(name, group=None)`            | Get a parameter by name                       | Retrieve a parameter for use                             |
| `get_parameters(group)`                      | Get all parameters in a group                 | Retrieve all parameters in a specific group              |
| `get_parameter_values(group)`                | Get parameter values as a dictionary          | Get parameter values for use in backtest                 |
| `get_core_parameters()`                      | Get all core parameters                       | Get engine parameters that are always present            |
| `get_strategy_parameters()`                  | Get all strategy parameters                   | Get parameters from groups starting with 'strategy_'     |
| `get_all_parameters()`                       | Get all parameters                            | Get all parameters from all groups                       |

## Parameter Class Hierarchy

```markdown
BaseParameter
├── NumericParameter
├── CategoricalParameter
│└── CategoricalListParameter
├── ConfigParameter
└── StrategyOptimizeParameter
```

## Key System Flows

1. **Parameter Flow**
   
   - GUI → Backtest Engine → Reports
   - Type-safe parameter handling
   - Optimization loop integration

2. **Data Flow**
   
   - Market Data → Signal Engine → Portfolio
   - Position comparison → Order generation
   - Trade execution → Portfolio updates

3. **Reporting Flow**
   
   - Portfolio state → Performance metrics
   - Signal history → Allocation reports
   - Trade history → Execution analysis

## Additional Information

- All path references must use `config/paths.py`
- Parameter optimization requires proper type checking
- Signal history must be preserved for allocation reports
- Backward compatibility maintained for existing configs

# V3 Parameter System Documentation

## Key Parameter Registry Methods

| Method                                       | Description                                   | Usage                                                    |
| -------------------------------------------- | --------------------------------------------- | -------------------------------------------------------- |
| `register_parameter(group, parameter)`       | Register a single parameter with the registry | For registering individual parameters                    |
| `register_parameter_list(group, parameters)` | Register multiple parameters at once          | **Preferred method** for registering multiple parameters |
| `get_parameter(name, group=None)`            | Get a parameter by name                       | Retrieve a parameter for use                             |
| `get_parameters(group)`                      | Get all parameters in a group                 | Retrieve all parameters in a specific group              |
| `get_parameter_values(group)`                | Get parameter values as a dictionary          | Get parameter values for use in backtest                 |
| `get_core_parameters()`                      | Get all core parameters                       | Get engine parameters that are always present            |
| `get_strategy_parameters()`                  | Get all strategy parameters                   | Get parameters from groups starting with 'strategy_'     |
| `get_all_parameters()`                       | Get all parameters                            | Get all parameters from all groups                       |

## Parameter Class Hierarchy

The V3 parameter system uses a class hierarchy to represent different types of parameters with varying behaviors:

```markdown
BaseParameter
├── NumericParameter
├── CategoricalParameter
│└── CategoricalListParameter
├── ConfigParameter
└── StrategyOptimizeParameter
```

## Parameter Classes and Their Uses

| Class                         | Description                                         | GUI Visible | Optimizable | Reportable | Use Cases                                              |
| ----------------------------- | --------------------------------------------------- |:-----------:|:-----------:|:----------:| ------------------------------------------------------ |
| **BaseParameter**             | Base class for all parameters                       | Yes         | No          | No         | Default for any non-defined variables                  |
| **NumericParameter**          | For numeric values with min/max/step                | Yes         | Yes         | No         | Lookback periods, thresholds, execution delay          |
| **CategoricalParameter**      | For selection from fixed options                    | Yes         | Yes         | No         | Rebalance frequency, strategy selection                |
| **CategoricalListParameter**  | For user-defined groups in config files             | Yes         | Yes         | No         | Ticker groups, asset classes                           |
| **ConfigParameter**           | For config-only values, not in GUI                  | No          | No          | No         | Initial capital, commission rate, slippage rate        |
| **StrategyOptimizeParameter** | Enhanced strategy parameters with reporting control | Yes         | Yes         | Yes        | Strategy-specific parameters needing reporting control |

## Parameter Matrix

Below is a comprehensive list of all parameters in the system, their types, and uses:

### Core Engine Parameters

| Parameter             | Class                     | Group | Description                     | Default  | GUI Visible | Optimizable |
| --------------------- | ------------------------- | ----- | ------------------------------- | -------- |:-----------:|:-----------:|
| `initial_capital`     | ConfigParameter           | core  | Initial capital for portfolio   | 1000000  | No          | No          |
| `commission_rate`     | ConfigParameter           | core  | Commission rate for trades      | 0.001    | No          | No          |
| `slippage_rate`       | ConfigParameter           | core  | Slippage rate for trades        | 0.001    | No          | No          |
| `execution_delay`     | StrategyOptimizeParameter | core  | Trade execution delay in days   | 1        | Yes         | Yes         |
| `rebalance_frequency` | StrategyOptimizeParameter | core  | Portfolio rebalancing frequency | 'weekly' | Yes         | Yes         |

### Data Parameters

| Parameter           | Class         | Group | Description                        | Default      | GUI Visible | Optimizable |
| ------------------- | ------------- | ----- | ---------------------------------- | ------------ |:-----------:|:-----------:|
| `start_date`        | BaseParameter | data  | Start date for backtest            | '2020-01-01' | No*         | No          |
| `end_date`          | BaseParameter | data  | End date for backtest              | '2025-04-23' | No*         | No          |
| `data_storage_mode` | BaseParameter | data  | How to handle data (Save/Read/New) | 'Read'       | No*         | No          |
| `price_field`       | BaseParameter | data  | Price field for calculations       | 'Close'      | No*         | No          |
| `risk_free_ticker`  | BaseParameter | data  | Ticker for risk-free rate          | '^IRX'       | No*         | No          |

*These parameters are currently defined in config but not yet converted to parameter classes

### Benchmark Parameters

| Parameter                  | Class         | Group     | Description                      | Default  | GUI Visible | Optimizable |
| -------------------------- | ------------- | --------- | -------------------------------- | -------- |:-----------:|:-----------:|
| `benchmark_ticker`         | BaseParameter | benchmark | Ticker for benchmark comparison  | 'SPY'    | No*         | No          |
| `benchmark_rebalance_freq` | BaseParameter | benchmark | Benchmark rebalancing frequency  | 'yearly' | No*         | No          |
| `debug_benchmark`          | BaseParameter | benchmark | Whether to log benchmark details | True     | No*         | No          |

*These parameters are currently defined in config but not yet converted to parameter classes

### EMA Strategy Parameters

| Parameter     | Class                     | Group        | Description                     | Default     | GUI Visible | Optimizable |
| ------------- | ------------------------- | ------------ | ------------------------------- | ----------- |:-----------:|:-----------:|
| `st_lookback` | StrategyOptimizeParameter | strategy_ema | Short-term EMA lookback period  | 15          | Yes         | Yes         |
| `mt_lookback` | StrategyOptimizeParameter | strategy_ema | Medium-term EMA lookback period | 70          | Yes         | Yes         |
| `lt_lookback` | StrategyOptimizeParameter | strategy_ema | Long-term EMA lookback period   | 100         | Yes         | Yes         |
| `top_n`       | StrategyOptimizeParameter | strategy_ema | Number of top assets to hold    | 2           | Yes         | Yes         |
| `signal_algo` | StrategyOptimizeParameter | strategy_ema | Signal generation algorithm     | 'ema_cross' | Yes         | Yes         |
| `tickers`     | StrategyOptimizeParameter | strategy_ema | Ticker groups to use            | 'sp500'     | Yes         | Yes         |

### Reporting Parameters

| Parameter        | Class         | Group     | Description                     | Default               | GUI Visible | Optimizable |
| ---------------- | ------------- | --------- | ------------------------------- | --------------------- |:-----------:|:-----------:|
| `create_excel`   | BaseParameter | reporting | Whether to create Excel reports | True                  | No*         | No          |
| `save_trade_log` | BaseParameter | reporting | Whether to save trade logs      | True                  | No*         | No          |
| `metrics`        | BaseParameter | reporting | Performance metrics to include  | ['total_return', ...] | No*         | No          |

### Visualization Parameters

| Parameter       | Class         | Group         | Description                 | Default                     | GUI Visible | Optimizable |
| --------------- | ------------- | ------------- | --------------------------- | --------------------------- |:-----------:|:-----------:|
| `create_charts` | BaseParameter | visualization | Whether to create charts    | True                        | No*         | No          |
| `chart_types`   | BaseParameter | visualization | Types of charts to create   | ['cumulative_returns', ...] | No*         | No          |
| `chart_format`  | BaseParameter | visualization | Format for chart output     | 'png'                       | No*         | No          |
| `chart_dpi`     | BaseParameter | visualization | Resolution for chart output | 300                         | No*         | No          |

*These parameters are currently defined in config but not yet converted to parameter classes

## V3 Reporting System Components (Updated May 2025)

### Core Modules

1. **Parameter Registration** (`v3_engine/reporting_parameters.py`)
   
   - Central registry for all reporting parameters
   - Handles parameter types, defaults and validation
   - Integrated with main parameter registry

2. **Reporting Adapter** (`v3_engine/performance_reporter_adapter.py`)
   
   - Bridges V3 parameters to legacy reporting code
   - Handles parameter conversion and validation
   - Maintains backward compatibility
   - **Function updated:** `_convert_parameters_to_config` (improved version for robust V3-to-reporting config conversion)

3. **Reporting Modules** (`v3_reporting/`)
   
   - `v3_performance_report.py`: Performance reporting wrapper
   - `v3_allocation_report.py`: Allocation reporting wrapper
   - `v3_visualization.py`: Chart generation wrapper
   - **Function added:** `generate_allocation_chart` (weekly area plot PNG, high-DPI, for allocation reporting)

4. **Validation Utilities** (`v3_engine/data_validator.py`)
   
   - **Function added:** `validate_signal_allocation` (checks for presence of signal and allocation history in results)

### Key Features

- **Parameter Flow**:

```mermaid
graph TD
  GUIWidgets[app/gui/v3_parameter_widgets.py] -->|Update Parameters| Registry[v3_engine/parameter_registry.py]
  Registry -->|Parameter Values| EMAAdapter[v3_engine/ema_v3_adapter.py]
  Registry -->|Parameter Values| BacktestEngine[engine/backtest.py]
  BacktestEngine -->|Results + Parameters| ReporterAdapter[v3_engine/performance_reporter_adapter.py]
  ReporterAdapter -->|Formatted Parameters| V3Reporting[v3_reporting/*]
  V3Reporting -->|Formatted Output| Reports[Excel/PNG Reports]
```

- **Error Handling**:
  
  - Automatic parameter type conversion
  - Fallback logic for missing signal history
  - Graceful degradation when legacy features are unavailable

- **Optimization Support**:
  
  - Proper handling of parameter optimization tuples
  - Preservation of optimization context through reporting chain
  - Clear labeling of optimized parameters in output

## V3 Reporting Modules

### Performance Charts (v3_performance_charts.py)

#### Functions

- `generate_monthly_returns_chart`: Creates monthly returns bar chart
- `generate_annual_returns_chart`: Creates annual returns bar chart
- `generate_cumulative_returns_chart`: Creates cumulative returns line chart
- `generate_drawdown_chart`: Creates drawdown visualization

### Trade Log (v3_trade_log.py)

#### Functions

- `format_trade_log`: Formats trade log data for Excel output
- `write_trade_log_header`: Writes parameter settings to Excel header
- `generate_trade_log`: Main function to create complete trade log output

## Parameter Flow

Parameters flow through the system in the following way:

1. **Definition**: Parameters are defined in their respective registration functions
2. **Registration**: Parameters are registered with the V3 registry
3. **GUI Integration**: Parameters marked with `show_in_gui=True` appear in the GUI
4. **Value Setting**: Parameter values are set via GUI or directly in code
5. **Engine Usage**: Parameters are passed to the backtest engine
6. **Reporting**: Parameter values are included in performance reports

## Parameter Conversion Guidelines

When converting config variables to parameter classes:

1. Use **ConfigParameter** for variables that:
   
   - Should not appear in the GUI
   - Should not be optimized
   - Are loaded from config
   - Should appear in performance reports

2. Use **NumericParameter** for variables that:
   
   - Have numeric values with min/max bounds
   - May need optimization

## Full Process Flow Architecture (Complete Content)

# V3 Process Flow - Full Architecture (Updated May 2025)

## Core Process Flow

```mermaid
flowchart TD
    subgraph GUI[GUI Layer]
        A[app/gui/v3_gui_core.py\nMainWindowV3] --> B[app/gui/v3_parameter_widgets.py\nParameter Widgets]
        A --> C[app/gui/v3_gui_actions.py\nGUI Actions]
        B --> D[v3_engine/gui_parameter_manager.py\nParameter Manager]
    end

    subgraph Engine[Backtest Engine]
        E[v3_engine/parameter_registry.py\nParameter Registry]
        F[v3_engine/ema_v3_adapter.py\nEMA Adapter]
        G[engine/backtest.py\nBacktest Engine]
        DataLoader[data/data_loader.py\nDataLoader] -->|Raw Data| F
        DataValidator[v3_engine/data_validator.py\nData Validator]
    end

    subgraph Reporting[V3 Reporting]
        I[v3_reporting/v3_performance_report.py\nPerformance Reporter]
        J[v3_reporting/v3_allocation_report.py\nAllocation Reporter]
        K[v3_reporting/v3_visualization.py\nVisualization]
        L[v3_reporting/v3_performance_charts.py\nPerformance Charts]
        M[v3_reporting/v3_trade_log.py\nTrade Log Formatter]
        N[v3_engine/performance_reporter_adapter.py\nReporter Adapter]
    end

    %% Main Flow
    D -->|Sync Parameters| E
    E -->|Apply Parameters| F
    F -->|Generate Signals| G
    G -->|Results| I
    G -->|Allocations| J
    I -->|Create Charts| K
    I -->|Create Charts| L
    J -->|Create Charts| K
    N -->|Bridge Parameters| I

    %% Styling
    classDef gui fill:#BBDEFB,stroke:#1976D2,color:#0D47A1;
    classDef engine fill:#C8E6C9,stroke:#388E3C,color:#1B5E20;
    classDef report fill:#F3E5F5,stroke:#8E24AA,color:#4A148C;
    classDef validation fill:#FFF9C4,stroke:#FBC02D,color:#5D4037;
    class A,B,C,D gui;
    class E,F,G,DataLoader,DataValidator engine;
    class I,J,K,L,M,N report;
```

## Detailed Component Interactions

```mermaid
flowchart LR
    %% Data Flow
    DataLoader[data/data_loader.py\nDataLoader] -->|OHLCV Data| EMAAdapter[v3_engine/ema_v3_adapter.py\nEMA Adapter]
    DataLoader -->|Benchmark Data| BacktestEngine[engine/backtest.py\nBacktest Engine]

    %% Parameter Flow
    ParameterRegistry[v3_engine/parameter_registry.py\nRegistry] -->|Parameter Values| EMAAdapter
    ParameterRegistry -->|Parameter Values| BacktestEngine
    ParameterOptimizer[v3_engine/parameter_optimizer.py\nOptimizer] -->|Optimization Combinations| ParameterRegistry
    Parameters[v3_engine/parameters.py\nParameter Classes] -->|Define Types| ParameterRegistry
    StrategyParameterSet[v3_engine/strategy_parameter_set.py\nParameter Set] -->|Register| ParameterRegistry

    %% Execution Flow
    EMAAdapter -->|Generate Signals| BacktestEngine
    BacktestEngine -->|Execute Trades| BacktestEngine

    %% Reporting Flow
    BacktestEngine -->|Results| PerformanceReporter[v3_reporting/v3_performance_report.py\nPerformance Reporter]
    BacktestEngine -->|Allocations| AllocationReporter[v3_reporting/v3_allocation_report.py\nAllocation Reporter]
    PerformanceReporter -->|Charts| PerformanceCharts[v3_reporting/v3_performance_charts.py\nCharts]
    AllocationReporter -->|Charts| Visualization[v3_reporting/v3_visualization.py\nVisualization]
    PerformanceReporterAdapter[v3_engine/performance_reporter_adapter.py\nAdapter] -->|Bridge| PerformanceReporter

    %% Utilities
    BacktestEngine -->|Date Handling| DateUtils[utils/date_utils.py\nDate Utilities]
    BacktestEngine -->|Log Trades| TradeLog[utils/trade_log.py\nTrade Log]

    %% Styling
    classDef gui fill:#BBDEFB,stroke:#1976D2,color:#0D47A1;
    classDef param fill:#C8E6C9,stroke:#388E3C,color:#1B5E20;
    classDef strategy fill:#FFECB3,stroke:#F57F17,color:#E65100;
    classDef report fill:#F3E5F5,stroke:#8E24AA,color:#4A148C;
    classDef util fill:#EFEBE9,stroke:#5D4037,color:#3E2723;
    class ParameterRegistry,ParameterOptimizer,Parameters,StrategyParameterSet param;
    class EMAAdapter,BacktestEngine,DataLoader strategy;
    class PerformanceReporter,AllocationReporter,PerformanceCharts,Visualization,PerformanceReporterAdapter report;
    class DateUtils,TradeLog util;
```

## Key Improvements Over V2

- **Parameter Handling**: Unified registry with type safety and optimization support
- **Reporting**: Standardized interfaces through adapter pattern
- **Visualization**: Integrated chart generation with V3 parameters
- **Execution**: Clear separation of signal generation and trade execution
- **Documentation**: Comprehensive process flow documentation
- **Data Validation**: Added data validation and error handling to process flow
