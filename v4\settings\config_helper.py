"""
Configuration Helper - Section-Agnostic Parameter Lookup
Eliminates the need to know which section a parameter is in.

This solves the recurring bug where code reads from wrong sections.
"""

import configparser
from pathlib import Path

class ConfigHelper:
    """Helper class that finds parameters by name regardless of section."""
    
    def __init__(self, config_file_path):
        """Initialize with config file path."""
        self.config = configparser.ConfigParser()
        self.config.read(config_file_path)
        
        # Build a flat lookup table: parameter_name -> (section, value)
        self._param_lookup = {}
        for section_name in self.config.sections():
            for param_name in self.config[section_name]:
                if param_name in self._param_lookup:
                    print(f"WARNING: Duplicate parameter '{param_name}' found in sections '{self._param_lookup[param_name][0]}' and '{section_name}'")
                self._param_lookup[param_name] = (section_name, self.config[section_name][param_name])
    
    def get(self, param_name, fallback=None):
        """Get parameter value by name (any section)."""
        if param_name in self._param_lookup:
            return self._param_lookup[param_name][1]
        return fallback
    
    def getboolean(self, param_name, fallback=False):
        """Get boolean parameter by name (any section)."""
        if param_name in self._param_lookup:
            section_name, value = self._param_lookup[param_name]
            return self.config.getboolean(section_name, param_name, fallback=fallback)
        return fallback
    
    def getint(self, param_name, fallback=0):
        """Get integer parameter by name (any section)."""
        if param_name in self._param_lookup:
            section_name, value = self._param_lookup[param_name]
            return self.config.getint(section_name, param_name, fallback=fallback)
        return fallback
    
    def getfloat(self, param_name, fallback=0.0):
        """Get float parameter by name (any section)."""
        if param_name in self._param_lookup:
            section_name, value = self._param_lookup[param_name]
            return self.config.getfloat(section_name, param_name, fallback=fallback)
        return fallback
    
    def list_all_params(self):
        """List all parameters and their sections (for debugging)."""
        for param_name, (section_name, value) in self._param_lookup.items():
            print(f"{param_name} = {value} (in [{section_name}])")
    
    def find_param_section(self, param_name):
        """Find which section a parameter is in."""
        if param_name in self._param_lookup:
            return self._param_lookup[param_name][0]
        return None

# Convenience function for quick usage
def get_param(param_name, config_file_path=None, fallback=None):
    """Quick function to get any parameter by name."""
    if config_file_path is None:
        from v4.config.paths_v4 import V4_SETTINGS_FILE
        config_file_path = V4_SETTINGS_FILE
    
    helper = ConfigHelper(config_file_path)
    return helper.get(param_name, fallback)

def get_param_boolean(param_name, config_file_path=None, fallback=False):
    """Quick function to get any boolean parameter by name."""
    if config_file_path is None:
        from v4.config.paths_v4 import V4_SETTINGS_FILE
        config_file_path = V4_SETTINGS_FILE
    
    helper = ConfigHelper(config_file_path)
    return helper.getboolean(param_name, fallback)
