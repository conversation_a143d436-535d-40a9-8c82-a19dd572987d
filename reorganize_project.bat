@echo off
echo Starting project reorganization...
echo.

REM Run as administrator check
NET SESSION >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo This script requires administrator privileges.
    echo Please right-click and select "Run as administrator"
    pause
    exit /b 1
)

echo Creating directory structure...
mkdir v4\settings 2>nul
mkdir v4\engine 2>nul
mkdir v4\models 2>nul
mkdir v4\reporting 2>nul
mkdir v4\scripts 2>nul
mkdir v4\app\gui 2>nul
mkdir v4\utils 2>nul
mkdir v4\config 2>nul

mkdir v3\engine 2>nul
mkdir v3\reporting 2>nul
mkdir v3\app\gui 2>nul
mkdir v3\models 2>nul
mkdir v3\utils 2>nul
mkdir v3\visualization 2>nul
mkdir v3\scripts 2>nul
mkdir v3\config 2>nul

mkdir tests\v3 2>nul
mkdir tests\v4 2>nul
mkdir tests\verification 2>nul
mkdir tests\scripts 2>nul
mkdir tests\scripts\bat 2>nul

mkdir v3\scripts\bat 2>nul
mkdir v4\scripts\bat 2>nul
mkdir utils\setup 2>nul

mkdir archive\special_tests 2>nul
mkdir archive\combined_docs 2>nul
mkdir archive\legacy_files\batch_backup 2>nul
mkdir archive\legacy_files\temp_backups 2>nul

mkdir data\v3 2>nul
mkdir data\v4 2>nul
mkdir output\v3 2>nul
mkdir output\v4 2>nul

mkdir docs\v3 2>nul
mkdir docs\v4 2>nul

mkdir v3\optimization 2>nul
mkdir v4\optimization 2>nul
mkdir v3\tools 2>nul
mkdir v4\tools 2>nul

echo.
echo Moving CPS_v4 files to v4/settings...
move /Y CPS_v4\*.* v4\settings\ >nul 2>&1

echo Moving engine files...
move /Y engine\*_v4.py v4\engine\ >nul 2>&1
move /Y engine\*.* v3\engine\ >nul 2>&1

echo Moving model files...
move /Y models\*_v4.py v4\models\ >nul 2>&1
move /Y models\*.* v3\models\ >nul 2>&1

echo Moving v3_engine files...
move /Y v3_engine\*.* v3\engine\ >nul 2>&1

echo Moving v3_reporting files...
move /Y v3_reporting\*.* v3\reporting\ >nul 2>&1

echo Moving app/gui files...
move /Y app\gui\*_v4.py v4\app\gui\ >nul 2>&1
move /Y app\gui\*.* v3\app\gui\ >nul 2>&1

echo Moving utils files...
move /Y utils\*_v4.py v4\utils\ >nul 2>&1
move /Y utils\*.* v3\utils\ >nul 2>&1

echo Moving visualization files...
move /Y visualization\*.* v3\visualization\ >nul 2>&1

echo Moving config files...
move /Y config\*_v4.py v4\config\ >nul 2>&1
move /Y config\*.* v3\config\ >nul 2>&1

echo Moving reporting files...
move /Y reporting\*_v4.py v4\reporting\ >nul 2>&1
move /Y reporting\*.* v3\reporting\ >nul 2>&1

echo Moving test files...
move /Y tests\verify_v3_reporting.py tests\v3\ >nul 2>&1
move /Y tests\test_v3_reporting_parameters.py tests\v3\ >nul 2>&1
move /Y tests\verify_critical_issues.py tests\verification\ >nul 2>&1
move /Y tests\verify_report_output.py tests\verification\ >nul 2>&1
move /Y tests\verify_signal_history_reporting.py tests\verification\ >nul 2>&1
move /Y tests\verify_execution_delay_fix.py tests\verification\ >nul 2>&1
move /Y tests\verification_helpers.py tests\verification\ >nul 2>&1
move /Y tests\report_validators.py tests\verification\ >nul 2>&1
move /Y tests\debug_execution_delay.py tests\scripts\ >nul 2>&1
move /Y tests\debug_execution_delay_csv.py tests\scripts\ >nul 2>&1
move /Y tests\debug_execution_delay_simple.py tests\scripts\ >nul 2>&1
move /Y tests\find_execution_delay_error.py tests\scripts\ >nul 2>&1
move /Y tests\fix_execution_delay.py tests\scripts\ >nul 2>&1
move /Y tests\run_report_verification.py tests\scripts\ >nul 2>&1
move /Y tests\simple_critical_verify.py tests\scripts\ >nul 2>&1
move /Y tests\simple_verify.py tests\scripts\ >nul 2>&1
move /Y tests\simulate_gui_optimization.py tests\scripts\ >nul 2>&1
move /Y tests\test_report_output.py tests\v3\ >nul 2>&1
move /Y tests\trace_trade_none.py tests\scripts\ >nul 2>&1
move /Y tests\__init__.py tests\v3\ >nul 2>&1

echo Moving batch files...
move /Y run_backtest_v3.bat v3\scripts\bat\ >nul 2>&1
move /Y run_ema_v3_gui_test.bat v3\scripts\bat\ >nul 2>&1
move /Y run_ema_v3_test.bat v3\scripts\bat\ >nul 2>&1
move /Y run_v3_reporting_debug.bat v3\scripts\bat\ >nul 2>&1
move /Y run_v3_reporting_parameters_test.bat v3\scripts\bat\ >nul 2>&1
move /Y run_v3_reporting_test.bat v3\scripts\bat\ >nul 2>&1
move /Y run_v3_test.bat v3\scripts\bat\ >nul 2>&1
move /Y run_v3_test_with_verification.bat v3\scripts\bat\ >nul 2>&1
move /Y run_v3_verification.bat v3\scripts\bat\ >nul 2>&1
move /Y run_v3_with_reports.bat v3\scripts\bat\ >nul 2>&1
move /Y run_critical_verification.bat tests\scripts\bat\ >nul 2>&1
move /Y run_report_verification.bat tests\scripts\bat\ >nul 2>&1
move /Y run_simple_verify.bat tests\scripts\bat\ >nul 2>&1
move /Y run_trace_trade_none.bat tests\scripts\bat\ >nul 2>&1
move /Y run_verification.bat tests\scripts\bat\ >nul 2>&1
move /Y run_verify_CPS_v4.bat tests\scripts\bat\ >nul 2>&1
move /Y run_verify_signal_history.bat tests\scripts\bat\ >nul 2>&1
move /Y check_python_env.bat utils\setup\ >nul 2>&1
move /Y debug_v3.bat v3\scripts\bat\ >nul 2>&1
move /Y debug_verification.bat tests\scripts\bat\ >nul 2>&1
move /Y install_pywin32.bat utils\setup\ >nul 2>&1
move /Y run_env_check.bat utils\setup\ >nul 2>&1
move /Y run_gui_simple.bat v3\scripts\bat\ >nul 2>&1
move /Y setup_ai_sdlc.bat utils\setup\ >nul 2>&1
move /Y setup_ai_sdlc_fixed.bat utils\setup\ >nul 2>&1
move /Y verify_mem_tool_install.bat utils\setup\ >nul 2>&1

echo Moving special_test files...
move /Y special_test\*.* archive\special_tests\ >nul 2>&1

echo Moving combined_docs files...
move /Y combined_docs\*.* archive\combined_docs\ >nul 2>&1

echo Moving batch_backup files...
move /Y batch_backup\*.* archive\legacy_files\batch_backup\ >nul 2>&1

echo Moving temp_backups files...
move /Y temp_backups\*.* archive\legacy_files\temp_backups\ >nul 2>&1

echo Moving data files...
move /Y data\*.v4.* data\v4\ >nul 2>&1
for %%f in (data\*.*) do (
    if not "%%~xf"==".v4" move /Y "%%f" data\v3\ >nul 2>&1
)

echo Moving output files...
move /Y output\*.v4.* output\v4\ >nul 2>&1
for %%f in (output\*.*) do (
    if not "%%~xf"==".v4" move /Y "%%f" output\v3\ >nul 2>&1
)

echo Moving documentation files...
move /Y docs\v3_*.* docs\v3\ >nul 2>&1
move /Y docs\v4_*.* docs\v4\ >nul 2>&1

echo Moving optimization files...
move /Y optimization\*.v4.* v4\optimization\ >nul 2>&1
for %%f in (optimization\*.*) do (
    if not "%%~xf"==".v4" move /Y "%%f" v3\optimization\ >nul 2>&1
)

echo Moving tools files...
move /Y tools\*.v4.* v4\tools\ >nul 2>&1
for %%f in (tools\*.*) do (
    if not "%%~xf"==".v4" move /Y "%%f" v3\tools\ >nul 2>&1
)

echo Cleaning up empty directories...
rmdir /S /Q combined_docs 2>nul
rmdir /S /Q batch_backup 2>nul
rmdir /S /Q temp_backups 2>nul
rmdir /S /Q special_test 2>nul
rmdir /S /Q v3_engine 2>nul
rmdir /S /Q v3_reporting 2>nul
rmdir /S /Q app 2>nul
rmdir /S /Q engine 2>nul
rmdir /S /Q models 2>nul
rmdir /S /Q reporting 2>nul
rmdir /S /Q visualization 2>nul
rmdir /S /Q utils 2>nul
rmdir /S /Q config 2>nul
rmdir /S /Q CPS_v4 2>nul

echo.
echo Project reorganization complete!
echo.
pause
