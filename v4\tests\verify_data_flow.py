#!/usr/bin/env python
# -*- coding: utf-8 -*-
# verify_data_flow.py
"""
Simple test script to verify basic data flow in the backtest engine.
This script checks if data is properly generated and passed through each stage.

Author: AI Assistant
Date: 2025-06-14
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime
import logging
import json
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Add parent directory to path to import modules
project_root = Path(__file__).parent.parent.parent
if str(project_root) not in sys.path:
    sys.path.append(str(project_root))

# Output directory
OUTPUT_DIR = Path(__file__).parent / "debug_output"
OUTPUT_DIR.mkdir(exist_ok=True)

def save_to_json(data, filename):
    """Save data to JSON file."""
    filepath = OUTPUT_DIR / filename
    
    # Convert data to serializable format if needed
    if isinstance(data, pd.DataFrame):
        data_dict = data.to_dict(orient='index')
        # Convert index to strings if they're dates
        if isinstance(data.index, pd.DatetimeIndex):
            data_dict = {str(k): v for k, v in data_dict.items()}
    elif isinstance(data, pd.Series):
        data_dict = data.to_dict()
        if isinstance(data.index, pd.DatetimeIndex):
            data_dict = {str(k): v for k, v in data_dict.items()}
    else:
        data_dict = data
    
    with open(filepath, 'w') as f:
        json.dump(data_dict, f, indent=2, default=str)
    
    logger.info(f"Saved {filename} to {filepath}")
    return filepath

def generate_test_data(start_date='2022-01-01', end_date='2022-12-31', symbols=None):
    """Generate synthetic price data for testing."""
    if symbols is None:
        symbols = ['AAPL', 'MSFT', 'AMZN', 'GOOGL']
    
    # Create date range
    dates = pd.date_range(start=start_date, end=end_date, freq='B')
    
    # Create price data with random walks
    np.random.seed(42)  # For reproducibility
    price_data = pd.DataFrame(index=dates)
    
    for symbol in symbols:
        # Start with 100
        prices = [100]
        # Generate random daily returns
        for _ in range(1, len(dates)):
            daily_return = np.random.normal(0.0005, 0.015)  # Mean and std dev
            prices.append(prices[-1] * (1 + daily_return))
        
        price_data[symbol] = prices
    
    return price_data

def verify_data_flow():
    """Verify basic data flow in the backtest engine."""
    print("\n=== VERIFYING BASIC DATA FLOW IN BACKTEST ENGINE ===\n")
    
    # Step 1: Generate test data
    print("Step 1: Generating test data...")
    price_data = generate_test_data()
    print(f"Generated price data with shape: {price_data.shape}")
    print(f"Price data sample:\n{price_data.head()}")
    save_to_json(price_data, "01_price_data.json")
    
    # Step 2: Import and check backtest engine
    print("\nStep 2: Importing backtest engine...")
    try:
        from v4.engine.backtest_v4 import BacktestEngine
        print("✅ Successfully imported BacktestEngine")
    except Exception as e:
        print(f"❌ Failed to import BacktestEngine: {e}")
        return
    
    # Step 3: Import and check signal generator
    print("\nStep 3: Importing signal generator...")
    try:
        from v4.engine.signal_generator_v4 import generate_signals
        print("✅ Successfully imported generate_signals")
    except Exception as e:
        print(f"❌ Failed to import generate_signals: {e}")
        return
    
    # Step 4: Generate signals
    print("\nStep 4: Generating signals...")
    try:
        signals = generate_signals(price_data, strategy='equal_weight')
        print(f"Generated signals with shape: {signals.shape}")
        print(f"Signals sample:\n{signals.head()}")
        save_to_json(signals, "02_generated_signals.json")
        print("✅ Successfully generated signals")
    except Exception as e:
        print(f"❌ Failed to generate signals: {e}")
        return
    
    # Step 5: Create backtest engine instance
    print("\nStep 5: Creating backtest engine instance...")
    try:
        engine = BacktestEngine()
        print("✅ Successfully created BacktestEngine instance")
    except Exception as e:
        print(f"❌ Failed to create BacktestEngine instance: {e}")
        return
    
    # Step 6: Override settings for testing
    print("\nStep 6: Loading and overriding settings...")
    try:
        from v4.settings.settings_CPS_v4 import load_settings, save_settings
        settings = load_settings()
        settings['backtest']['rebalance_freq'] = 'M'  # Monthly rebalancing
        settings['backtest']['execution_delay'] = 1    # 1-day execution delay
        save_settings(settings)
        print("✅ Successfully loaded and overrode settings")
        save_to_json(settings, "03_settings.json")
    except Exception as e:
        print(f"❌ Failed to load or override settings: {e}")
        return
    
    # Step 7: Run backtest
    print("\nStep 7: Running backtest...")
    try:
        results = engine.run_backtest(
            price_data=price_data,
            signal_generator=generate_signals,
            strategy='equal_weight'
        )
        print("✅ Successfully ran backtest")
    except Exception as e:
        print(f"❌ Failed to run backtest: {e}")
        return
    
    # Step 8: Check results structure
    print("\nStep 8: Checking results structure...")
    expected_keys = ['initial_capital', 'final_value', 'total_return', 'strategy_returns', 
                    'benchmark_returns', 'weights_history', 'position_history', 
                    'signal_history', 'trade_log', 'performance']
    
    missing_keys = [key for key in expected_keys if key not in results]
    if missing_keys:
        print(f"❌ Missing expected keys in results: {missing_keys}")
    else:
        print("✅ All expected keys present in results")
    
    # Step 9: Check signal_history
    print("\nStep 9: Checking signal_history...")
    signal_history = results.get('signal_history')
    if signal_history is not None and not signal_history.empty:
        print(f"✅ signal_history exists with shape: {signal_history.shape}")
        print(f"signal_history sample:\n{signal_history.head()}")
        save_to_json(signal_history, "04_signal_history.json")
    else:
        print("❌ signal_history is None or empty")
    
    # Step 10: Check weights_history
    print("\nStep 10: Checking weights_history...")
    weights_history = results.get('weights_history')
    if weights_history is not None and not weights_history.empty:
        print(f"✅ weights_history exists with shape: {weights_history.shape}")
        print(f"weights_history sample:\n{weights_history.head()}")
        save_to_json(weights_history, "05_weights_history.json")
    else:
        print("❌ weights_history is None or empty")
    
    # Step 11: Check position_history
    print("\nStep 11: Checking position_history...")
    position_history = results.get('position_history')
    if position_history is not None and not position_history.empty:
        print(f"✅ position_history exists with shape: {position_history.shape}")
        print(f"position_history sample:\n{position_history.head()}")
        save_to_json(position_history, "06_position_history.json")
    else:
        print("❌ position_history is None or empty")
    
    # Step 12: Check trade_log
    print("\nStep 12: Checking trade_log...")
    trade_log = results.get('trade_log')
    if trade_log is not None and not trade_log.empty:
        print(f"✅ trade_log exists with shape: {trade_log.shape}")
        print(f"trade_log sample:\n{trade_log.head()}")
        save_to_json(trade_log, "07_trade_log.json")
    else:
        print("❌ trade_log is None or empty")
    
    # Step 13: Check if weights_history equals signal_history
    print("\nStep 13: Comparing weights_history and signal_history...")
    if weights_history is not None and signal_history is not None:
        if weights_history.equals(signal_history):
            print("❌ weights_history equals signal_history - allocation history is not preserved!")
        else:
            print("✅ weights_history differs from signal_history - allocation history is preserved!")
            
            # Save a comparison of a few dates
            sample_dates = weights_history.index[::20]  # Every 20th date
            comparison = []
            
            for date in sample_dates:
                weights = weights_history.loc[date] if date in weights_history.index else pd.Series()
                signals = signal_history.loc[date] if date in signal_history.index else pd.Series()
                
                comparison.append({
                    'date': str(date),
                    'weights': weights.to_dict() if not weights.empty else {},
                    'signals': signals.to_dict() if not signals.empty else {},
                    'match': weights.equals(signals) if not weights.empty and not signals.empty else False
                })
            
            save_to_json(comparison, "08_weights_vs_signals_comparison.json")
    
    # Step 14: Save full results (excluding large objects)
    print("\nStep 14: Saving performance metrics...")
    performance = results.get('performance', {})
    save_to_json(performance, "09_performance_metrics.json")
    
    # Step 15: Summary
    print("\n=== DATA FLOW VERIFICATION SUMMARY ===")
    if all([signal_history is not None and not signal_history.empty,
            weights_history is not None and not weights_history.empty,
            position_history is not None and not position_history.empty,
            trade_log is not None and not trade_log.empty]):
        print("✅ Basic data flow is working - all key components are present")
        
        if weights_history is not None and signal_history is not None and not weights_history.equals(signal_history):
            print("✅ Allocation history is preserved correctly")
        else:
            print("❌ Allocation history is not preserved correctly")
    else:
        print("❌ Basic data flow is broken - some key components are missing")
    
    print(f"\nAll verification data saved to: {OUTPUT_DIR}")

if __name__ == "__main__":
    print("=====================================================")
    print("BACKTEST ENGINE DATA FLOW VERIFICATION")
    print(f"Run Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=====================================================")
    
    verify_data_flow()
    
    print("\n=====================================================")
    print("VERIFICATION COMPLETE")
    print("=====================================================")
