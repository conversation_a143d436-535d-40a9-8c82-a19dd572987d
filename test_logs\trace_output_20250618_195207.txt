2025-06-18 19:52:08,140 - INFO - Successfully loaded settings for ema_allocation_model_v4.
Initial PYTHONPATH: None
Initial sys.path: ['S:\\Dropbox\\Scott Only Internal\\Quant_Python_24\\Backtest_FinAsset_Alloc_Template', 'C:\\Python313\\python313.zip', 'C:\\Python313\\DLLs', 'C:\\Python313\\Lib', 'C:\\Python313', 'F:\\AI_Library\\my_quant_env', 'F:\\AI_Library\\my_quant_env\\Lib\\site-packages', 'F:\\GitHub_Clone\\ai-sdlc', 'F:\\AI_Library\\my_quant_env\\Lib\\site-packages\\win32', 'F:\\AI_Library\\my_quant_env\\Lib\\site-packages\\win32\\lib', 'F:\\AI_Library\\my_quant_env\\Lib\\site-packages\\Pythonwin']
sys.path after ensuring PROJECT_ROOT: ['S:\\Dropbox\\Scott Only Internal\\Quant_Python_24\\Backtest_FinAsset_Alloc_Template', 'C:\\Python313\\python313.zip', 'C:\\Python313\\DLLs', 'C:\\Python313\\Lib', 'C:\\Python313', 'F:\\AI_Library\\my_quant_env', 'F:\\AI_Library\\my_quant_env\\Lib\\site-packages', 'F:\\GitHub_Clone\\ai-sdlc', 'F:\\AI_Library\\my_quant_env\\Lib\\site-packages\\win32', 'F:\\AI_Library\\my_quant_env\\Lib\\site-packages\\win32\\lib', 'F:\\AI_Library\\my_quant_env\\Lib\\site-packages\\Pythonwin']
Successfully imported config.paths. PROJECT_ROOT according to paths.py: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template
Loaded settings from: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\settings\settings_parameters_v4.ini
Found 11 parameter sections
Found 55 named lists for parameter references
Successfully loaded settings.
Python version: 3.13.1 (tags/v3.13.1:0671451, Dec  3 2024, 19:06:28) [MSC v.1942 64 bit (AMD64)]
Python paths:
  S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4
  S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template
  S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\CPS_V4
  S:\Dropbox\Scott Only Internal\Quant_Python_24\Custom Function Library
  C:\Python313\python313.zip
  C:\Python313\DLLs
  C:\Python313\Lib
  C:\Python313
  F:\AI_Library\my_quant_env
  F:\AI_Library\my_quant_env\Lib\site-packages
Loaded settings from: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\settings\settings_parameters_v4.ini
Found 11 parameter sections
Found 55 named lists for parameter references
Loaded settings from: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\settings\settings_parameters_v4.ini
Found 11 parameter sections
Found 55 named lists for parameter references
Loaded settings from: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\settings\settings_parameters_v4.ini
Found 11 parameter sections
Found 55 named lists for parameter references
Loaded settings from: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\settings\settings_parameters_v4.ini
Found 11 parameter sections
Found 55 named lists for parameter references
Loaded settings from: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\settings\settings_parameters_v4.ini
Found 11 parameter sections
Found 55 named lists for parameter references
Loaded settings from: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\settings\settings_parameters_v4.ini
Found 11 parameter sections
Found 55 named lists for parameter references
2025-06-18 19:52:08,168 - INFO - Script setup complete. Settings loaded and loggers configured.
2025-06-18 19:52:08,168 - INFO - Script setup complete. Settings loaded and loggers configured.
2025-06-18 19:52:08,172 - INFO - Script setup complete. Settings loaded and loggers configured.
2025-06-18 19:52:08,172 - INFO - Starting EMA Signal Breakdown trace.
2025-06-18 19:52:08,172 - INFO - Starting EMA Signal Breakdown trace.
2025-06-18 19:52:08,172 - INFO - EMA Signal Breakdown Trace Log - 20250618_195208
2025-06-18 19:52:08,172 - INFO - Period: 2021-01-20 to 2021-02-28
2025-06-18 19:52:08,172 - INFO - Initializing Backtest Engine components...
2025-06-18 19:52:08,172 - INFO - Initializing Backtest Engine components...
2025-06-18 19:52:08,172 - INFO - Initializing Backtest Engine components...
2025-06-18 19:52:08,173 - INFO - Initialized backtest engine with $1,000,000.00 capital
2025-06-18 19:52:08,173 - INFO - Commission rate: 0.10%
2025-06-18 19:52:08,173 - INFO - Slippage rate: 0.05%
2025-06-18 19:52:08,173 - INFO - Backtest Engine initialized with initial capital: $1,000,000.00 and execution delay: 0 days.
2025-06-18 19:52:08,173 - INFO - Backtest Engine initialized with initial capital: $1,000,000.00 and execution delay: 0 days.
2025-06-18 19:52:08,173 - INFO - Backtest Engine initialized with initial capital: $1,000,000.00 and execution delay: 0 days.
2025-06-18 19:52:08,173 - INFO - Loading price data...
2025-06-18 19:52:08,173 - INFO - Loading price data...
2025-06-18 19:52:08,174 - INFO - [TickerData] Mode=Read, file=S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\data\tickerdata_SPY_SHV_EFA_TLT_PFF_20200101_20250615.xlsx
2025-06-18 19:52:08,580 - INFO - Price data loaded: 1370 rows, 5 assets.
2025-06-18 19:52:08,580 - INFO - Price data loaded: 1370 rows, 5 assets.
2025-06-18 19:52:08,581 - INFO - Price data loaded. Shape: (1370, 5)
2025-06-18 19:52:08,581 - INFO - Loading CPS V4 settings...
2025-06-18 19:52:08,581 - INFO - Loading CPS V4 settings...
2025-06-18 19:52:08,586 - INFO - --- Effective Settings for Trace ---
2025-06-18 19:52:08,586 - INFO - EMA Lookbacks: ST=15, MT=70, LT=100
2025-06-18 19:52:08,607 - INFO - EMA Weights: Min=0.0, Max=1.0
2025-06-18 19:52:08,607 - INFO - EMA Strategy Top N: 3
2025-06-18 19:52:08,607 - INFO - System Top N (for allocation rules): 2
2025-06-18 19:52:08,607 - INFO - Signal Algorithm for Rules: ema
2025-06-18 19:52:08,607 - INFO - -----------------------------------

Loaded settings from: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\settings\settings_parameters_v4.ini
Found 11 parameter sections
Found 55 named lists for parameter references
2025-06-18 19:52:08,610 - INFO - Tracing 27 business days from 2021-01-20 to 2021-02-28.
2025-06-18 19:52:08,610 - INFO - Tracing 27 business days from 2021-01-20 to 2021-02-28.
2025-06-18 19:52:08,611 - INFO - 
========== Tracing for Date: 2021-01-20 ==========
2025-06-18 19:52:08,611 - INFO - --- Calling Production Model with trace_mode=True ---
2025-06-18 19:52:08,620 - INFO - Applied ema allocation weights
2025-06-18 19:52:08,623 - INFO - --- Step 1: Ratios (EMAXAvg) ---
2025-06-18 19:52:08,624 - INFO - Ratios received:
SPY    1.031513
SHV    1.000027
EFA    1.039898
TLT    0.985320
PFF    1.014675
2025-06-18 19:52:08,624 - INFO - --- Step 2: Ranks ---
2025-06-18 19:52:08,627 - INFO - Ranks received:
  Asset  EMAXAvg_Value  Rank_Ordinal
0   EFA       1.039898             1
1   SPY       1.031513             2
2   PFF       1.014675             3
3   SHV       1.000027             4
4   TLT       0.985320             5
2025-06-18 19:52:08,627 - INFO - --- Step 3: Signal (Allocation) ---
2025-06-18 19:52:08,628 - INFO - Signal received:
SPY    0.4
SHV    0.0
EFA    0.6
TLT    0.0
PFF    0.0
2025-06-18 19:52:08,628 - INFO - --- Processing Backtest Engine for date: 2021-01-20 ---
2025-06-18 19:52:08,628 - INFO - No orders from previous signals to execute for 2021-01-20
2025-06-18 19:52:08,629 - INFO - Generated 2 new orders based on today's signal for 2021-01-20
2025-06-18 19:52:08,629 - INFO - T+0 Execution: Processing 2 orders generated from today's signal.
2025-06-18 19:52:08,629 - ERROR - An unexpected error occurred: ExecutionEngine.execute_orders() missing 1 required positional argument: 'prices'
Traceback (most recent call last):
  File "S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\trace_ema_signal_breakdown.py", line 311, in main
    executed_trades_t0 = backtest_engine.execution_engine.execute_orders(new_orders_from_today_signal, current_prices_for_engine)
TypeError: ExecutionEngine.execute_orders() missing 1 required positional argument: 'prices'
2025-06-18 19:52:08,629 - ERROR - An unexpected error occurred: ExecutionEngine.execute_orders() missing 1 required positional argument: 'prices'
Traceback (most recent call last):
  File "S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\trace_ema_signal_breakdown.py", line 311, in main
    executed_trades_t0 = backtest_engine.execution_engine.execute_orders(new_orders_from_today_signal, current_prices_for_engine)
TypeError: ExecutionEngine.execute_orders() missing 1 required positional argument: 'prices'
2025-06-18 19:52:08,631 - ERROR - An unexpected error occurred: ExecutionEngine.execute_orders() missing 1 required positional argument: 'prices'
Traceback (most recent call last):
  File "S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\trace_ema_signal_breakdown.py", line 311, in main
    executed_trades_t0 = backtest_engine.execution_engine.execute_orders(new_orders_from_today_signal, current_prices_for_engine)
TypeError: ExecutionEngine.execute_orders() missing 1 required positional argument: 'prices'
2025-06-18 19:52:08,631 - INFO - EMA signal breakdown trace finished.
2025-06-18 19:52:08,631 - INFO - EMA signal breakdown trace finished.
2025-06-18 19:52:08,632 - INFO - EMA signal breakdown trace finished.
