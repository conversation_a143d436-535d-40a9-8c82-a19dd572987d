#!/usr/bin/env python
# verification_helpers.py
"""
Helper functions for V3 reporting parameter integration verification.
Provides mock data generation and utility functions to support the main verification script.
"""

import os
import pandas as pd
import datetime
import logging

logger = logging.getLogger('v3_verification.helpers')

def generate_mock_backtest_results(output_dir):
    """
    Generate mock backtest results for testing reporting components.
    
    This function creates realistic test data without running an actual backtest,
    ensuring verification can proceed without affecting production data.
    
    Args:
        output_dir (str): Directory to save the mock results
        
    Returns:
        dict: Mock backtest results dictionary
    """
    # Create date range for 2024
    dates = pd.date_range(start='2024-01-01', end='2024-12-31', freq='B')
    
    # Create mock portfolio values with realistic growth pattern
    portfolio_values = pd.Series(
        index=dates,
        data=[100000 * (1 + 0.0005 * i + 0.002 * (i % 20 == 0) - 0.001 * (i % 15 == 0)) 
              for i in range(len(dates))]
    )
    
    # Create mock signal history (allocation weights)
    tickers = ['SPY', 'AGG', 'GLD', 'QQQ']
    signal_history = {}
    
    # Create quarterly rebalance dates
    rebalance_dates = pd.date_range(start='2024-01-01', end='2024-12-31', freq='Q')
    
    for date in rebalance_dates:
        # Simple rotation strategy for mock data
        if date.month in [1, 2, 3]:
            signal_history[date] = {'SPY': 0.6, 'AGG': 0.4, 'GLD': 0.0, 'QQQ': 0.0}
        elif date.month in [4, 5, 6]:
            signal_history[date] = {'SPY': 0.4, 'AGG': 0.2, 'GLD': 0.4, 'QQQ': 0.0}
        elif date.month in [7, 8, 9]:
            signal_history[date] = {'SPY': 0.3, 'AGG': 0.3, 'GLD': 0.2, 'QQQ': 0.2}
        else:
            signal_history[date] = {'SPY': 0.25, 'AGG': 0.25, 'GLD': 0.25, 'QQQ': 0.25}
    
    # Create mock price data with realistic market behavior
    price_data = pd.DataFrame(index=dates)
    for ticker in tickers:
        # Different growth rates and volatility for different assets
        if ticker == 'SPY':
            # Equity with moderate growth and volatility
            price_data[ticker] = [100 * (1 + 0.0007 * i + 0.02 * (i % 30 == 0) - 0.015 * (i % 45 == 0)) 
                                 for i in range(len(dates))]
        elif ticker == 'AGG':
            # Bond with low growth and low volatility
            price_data[ticker] = [100 * (1 + 0.0002 * i + 0.003 * (i % 60 == 0) - 0.002 * (i % 75 == 0)) 
                                 for i in range(len(dates))]
        elif ticker == 'GLD':
            # Gold with moderate growth and high volatility
            price_data[ticker] = [100 * (1 + 0.0004 * i + 0.025 * (i % 20 == 0) - 0.02 * (i % 25 == 0)) 
                                 for i in range(len(dates))]
        else:  # QQQ
            # Tech with high growth and high volatility
            price_data[ticker] = [100 * (1 + 0.001 * i + 0.03 * (i % 25 == 0) - 0.025 * (i % 35 == 0)) 
                                 for i in range(len(dates))]
    
    # Create mock trade log with realistic trades
    trade_log = []
    for date in rebalance_dates:
        for ticker in tickers:
            # Calculate realistic quantities based on portfolio value and allocation
            portfolio_value = portfolio_values[portfolio_values.index <= date].iloc[-1]
            target_allocation = signal_history[date][ticker]
            price = price_data.loc[date, ticker]
            
            # Calculate quantity based on target allocation
            target_value = portfolio_value * target_allocation
            quantity = int(target_value / price)
            
            if quantity > 0:
                trade_log.append({
                    'date': date,
                    'ticker': ticker,
                    'action': 'BUY',
                    'quantity': quantity,
                    'price': price,
                    'value': quantity * price,
                    'commission': quantity * price * 0.001  # 0.1% commission
                })
    
    # Package into results dictionary with all required fields
    mock_results = {
        'portfolio_values': portfolio_values,
        'signal_history': signal_history,
        'price_data': price_data,
        'trade_log': trade_log,
        'initial_capital': 100000,
        'parameters': {
            'strategy': 'mock_strategy',
            'rebalance_frequency': 'quarterly',
            'execution_delay': 1,
            'commission_rate': 0.001,
            'slippage_rate': 0.0005,
            'lookback_short': 20,
            'lookback_medium': 50,
            'lookback_long': 200,
            'top_n': 3
        }
    }
    
    # Save to test directory for use by other tests
    mock_results_path = os.path.join(output_dir, 'mock_results.pkl')
    pd.to_pickle(mock_results, mock_results_path)
    
    logger.info(f"Created mock backtest results at {mock_results_path}")
    return mock_results

def check_module_size_compliance(project_root, max_lines=450):
    """
    Check if all modules comply with the line limit.
    
    Args:
        project_root (str): Project root directory
        max_lines (int): Maximum allowed lines per module
        
    Returns:
        tuple: (compliance_status, list of violations)
    """
    violations = []
    
    # Define module directories to check
    module_dirs = [
        os.path.join(project_root, 'v3_reporting'),
        os.path.join(project_root, 'v3_engine'),
        os.path.join(project_root, 'config'),
        os.path.join(project_root, 'models')
    ]
    
    for module_dir in module_dirs:
        if not os.path.exists(module_dir):
            logger.warning(f"Module directory not found: {module_dir}")
            continue
            
        for root, _, files in os.walk(module_dir):
            for file in files:
                if file.endswith('.py'):
                    file_path = os.path.join(root, file)
                    with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                        try:
                            line_count = sum(1 for _ in f)
                            
                            if line_count > max_lines:
                                violations.append((file_path, line_count))
                        except Exception as e:
                            logger.error(f"Error reading {file_path}: {e}")
    
    return len(violations) == 0, violations

def generate_verification_summary(results, timestamp, output_dir):
    """
    Generate a detailed verification summary report.
    
    Args:
        results (dict): Test results dictionary
        timestamp (str): Timestamp for the report
        output_dir (str): Directory to save the summary
        
    Returns:
        str: Path to the summary file
    """
    summary_path = os.path.join(output_dir, f"verification_summary_{timestamp}.txt")
    
    with open(summary_path, 'w') as f:
        f.write("V3 Reporting Parameter Integration Verification Summary\n")
        f.write("="*60 + "\n")
        f.write(f"Timestamp: {timestamp}\n")
        f.write(f"Date: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        # Overall status
        all_passed = all(results.values())
        overall_status = "PASSED" if all_passed else "FAILED"
        f.write(f"Overall Status: {overall_status}\n")
        f.write("-"*60 + "\n\n")
        
        # Individual test results
        f.write("Test Results:\n")
        for test, result in results.items():
            status = "PASSED" if result else "FAILED"
            f.write(f"  - {test.replace('_', ' ').title()}: {status}\n")
        
        f.write("\n" + "-"*60 + "\n")
        f.write("Verification Notes:\n")
        f.write("  - This verification was run in an isolated environment\n")
        f.write("  - No production files or data were modified\n")
        f.write("  - Test artifacts are stored in the verification_output directory\n")
        f.write("  - See verification.log for detailed test information\n")
    
    logger.info(f"Verification summary written to {summary_path}")
    return summary_path

def verify_excel_report(file_path, expected_sheets=None):
    """
    Verify an Excel report has the expected structure.
    
    Args:
        file_path (str): Path to the Excel file
        expected_sheets (list): List of expected sheet names
        
    Returns:
        bool: True if verification passes
    """
    if not os.path.exists(file_path):
        logger.error(f"Excel file not found: {file_path}")
        return False
    
    try:
        # Load the Excel file
        excel = pd.ExcelFile(file_path)
        
        # Check if expected sheets are present
        if expected_sheets:
            for sheet in expected_sheets:
                if sheet not in excel.sheet_names:
                    logger.error(f"Expected sheet '{sheet}' not found in {file_path}")
                    return False
        
        # Check if file has content
        for sheet_name in excel.sheet_names:
            df = pd.read_excel(file_path, sheet_name=sheet_name)
            if df.empty:
                logger.error(f"Sheet '{sheet_name}' is empty in {file_path}")
                return False
        
        return True
    except Exception as e:
        logger.error(f"Error verifying Excel file {file_path}: {e}")
        return False
