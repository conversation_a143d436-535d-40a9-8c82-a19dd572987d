"""
v4/Algo_signal_phase.py
Signal generation phase script for the decoupled backtest architecture.
Part of the CPS v4 compliant backtest system.

# Note: Uses CSV format only (<PERSON><PERSON><PERSON> eliminated for performance)
This script handles the signal generation process and saves the output to standardized file formats.
It loads price data using the existing data loader and generates signals using the configured
signal generator, then saves the signals in CSV format for optimal loading performance.

Based on the validated logic from main_v4_production_run.py.
"""

import sys
import os
from pathlib import Path
from datetime import datetime
import pandas as pd
import numpy as np

# Import centralized path configuration
from v4.config.paths_v4 import PROJECT_ROOT, V4_TRACE_OUTPUTS_DIR

# Import core V4 components
from v4.settings.settings_CPS_v4 import load_settings
from v4.engine.data_loader_v4 import load_data_for_backtest
from v4.models.ema_signal_bridge import run_ema_model_with_tracing_single

# --- Logging Function (from main_v4_production_run.py) ---
def log_message(message, level="INFO"):
    """Centralized logging function to standardize output format.
    Levels: INFO, WARNING, ERROR, CRIT<PERSON>AL, MILESTONE
    """
    timestamp = datetime.now().strftime("%H:%M:%S")
    prefix = f"[{level}] {timestamp} -"
    print(f"{prefix} {message}")
    sys.stdout.flush()  # Ensure output is immediately written

def run_signal_phase(custom_settings_file: str = None):
    """Run signal generation phase and save output.
    
    Args:
        custom_settings_file: Optional path to custom settings file for optimization
    
    Based on the validated logic from main_v4_production_run.py.
    """
    log_message("--- Starting Signal Generation Phase ---", "MILESTONE")

    # 1. Load Settings
    log_message("\nStep 1: Loading settings...", "MILESTONE")
    try:
        if custom_settings_file:
            log_message(f"Using custom settings file: {custom_settings_file}")
            settings = load_settings(custom_file=custom_settings_file)
        else:
            log_message("Using default settings file: settings_CPS_v4.ini")
            settings = load_settings()
        log_message("Settings loaded successfully.")
    except Exception as e:
        log_message(f"Could not load settings. Error: {e}", "CRITICAL")
        return None

    # 2. Load Data
    log_message("\nStep 2: Loading market data...", "MILESTONE")
    try:
        all_data = load_data_for_backtest(current_settings=settings)
        price_data = all_data['price_data']
        # benchmark_data is not directly returned; it's calculated inside the engine
        log_message(f"Price data loaded for {len(price_data.columns)} assets.")
    except Exception as e:
        log_message(f"Could not load data. Error: {e}", "CRITICAL")
        sys.exit(1)

    # 3. Generate Signals
    log_message("\nStep 3: Generating signals...", "MILESTONE")
    try:
        # Get strategy parameters from settings
        raw_strategy_params = settings.get('strategy', {})
        strategy_params = raw_strategy_params.copy()
        # The signal factory `generate_signals` expects a 'strategy' argument.
        # We get this from the 'strategy_name' field in the settings.
        strategy_to_run = strategy_params.pop('strategy_name', 'equal_weight')

        log_message(f"   - Using strategy: '{strategy_to_run}' with params: {strategy_params}")
        
        # Generate signals using run_ema_model_with_tracing to get all CSV outputs
        signals_df = run_ema_model_with_tracing_single(
            price_data=price_data,
            **strategy_params
        )
        
        # Save signals to CSV - HARD STOP if csv_flag_use is False
        csv_flag_use = os.environ.get('CPS_V4_CSV_FLAG_USE', '').lower() == 'true'

        if csv_flag_use:
            # Save CSV for user inspection
            trace_csv_dir = V4_TRACE_OUTPUTS_DIR
            os.makedirs(trace_csv_dir, exist_ok=True)
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            csv_filename = f"signals_output_{timestamp}.csv"
            csv_filepath = trace_csv_dir / csv_filename
            signals_df.to_csv(csv_filepath)  # Keep index=True to preserve dates as first column
            log_message(f"Signals saved to timestamped CSV: {csv_filepath}")
        else:
            # CSV disabled: Skip CSV generation, return in-memory path
            csv_filepath = "IN_MEMORY_SIGNALS"  # Signal that data is in-memory only
        
        log_message(f"Signals saved to: {csv_filepath}", "MILESTONE")
        
        # Signal generation is complete
        log_message("Signal generation complete.", "MILESTONE")
        return csv_filepath
    except Exception as e:
        log_message(f"Signal generation failed. Error: {e}", "ERROR")
        # Log the full traceback for debugging
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    run_signal_phase()
