# ultra_minimal_file_write.py
import os
import sys
import datetime
import traceback

# Generate a unique filename with a timestamp
timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S_%f")
test_file_name = f"ultra_minimal_write_test_{timestamp}.txt"
error_file_name = f"ultra_minimal_write_ERROR_{timestamp}.txt"

# Determine the script's directory to ensure we're writing locally
script_dir = ""
try:
    script_dir = os.path.dirname(os.path.abspath(__file__))
except NameError:
    # __file__ might not be defined if script is run in some embedded contexts
    script_dir = os.getcwd() # Fallback to current working directory

file_path = os.path.join(script_dir, test_file_name)
error_file_path = os.path.join(script_dir, error_file_name)

# Attempt to print to console (might not be visible, but try)
print(f"ULTRA_MINIMAL_FILE_WRITE: Attempting to write to: {file_path}")
sys.stdout.flush()

try:
    with open(file_path, "w") as f:
        f.write(f"File write successful at {datetime.datetime.now()}\n")
        f.write(f"Script path (__file__): {__file__ if '__file__' in locals() else 'Not Defined'}\n")
        f.write(f"Absolute script path (os.path.abspath(__file__)): {os.path.abspath(__file__) if '__file__' in locals() else 'Not Defined'}\n")
        f.write(f"Script directory used: {script_dir}\n")
        f.write(f"Current working directory (os.getcwd()): {os.getcwd()}\n")

    print(f"ULTRA_MINIMAL_FILE_WRITE: Successfully wrote to: {file_path}")
    sys.stdout.flush()
    sys.exit(0) # Explicitly exit with 0 for success

except Exception as e:
    # Primary operation failed, try to log the error to a separate file
    print(f"ULTRA_MINIMAL_FILE_WRITE: FAILED to write to {file_path}. Error: {str(e)}")
    sys.stdout.flush()
    try:
        with open(error_file_path, "w") as ef:
            ef.write(f"ERROR in ultra_minimal_file_write.py at {datetime.datetime.now()}\n")
            ef.write(f"Attempted to write to: {file_path}\n")
            ef.write(f"Error details: {str(e)}\n")
            ef.write("Traceback:\n")
            traceback.print_exc(file=ef)
        print(f"ULTRA_MINIMAL_FILE_WRITE: Error details logged to: {error_file_path}")
        sys.stdout.flush()
    except Exception as e_log_err:
        # If logging the error also fails, print that (again, might not be visible)
        print(f"ULTRA_MINIMAL_FILE_WRITE: CRITICAL - FAILED to write error log. Error logging error: {str(e_log_err)}")
        sys.stdout.flush()
    
    sys.exit(1) # Exit with non-zero on error
