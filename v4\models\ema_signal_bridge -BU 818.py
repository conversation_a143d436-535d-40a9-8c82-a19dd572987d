"""
v4/models/ema_signal_bridge.py
Bridge module to connect EMASignalGenerator with ema_allocation_model for tracing.

This module provides a function to run the EMA allocation model in trace mode
and save the detailed signal history data to CSV files.
"""

import pandas as pd
import logging
from pathlib import Path
import os

from v4.models.ema_allocation_model_v4 import ema_allocation_model_updated
from v4.utils.tracing_utils import save_df_to_trace_dir

logger = logging.getLogger(__name__)

def run_ema_model_with_tracing(price_data, **params):
    """
    Run the EMA allocation model in trace mode and save the detailed signal history data.
    
    Args:
        price_data (DataFrame): Historical price data
        **params: Additional parameters for the EMA model
        
    Returns:
        dict: Asset allocations with timestamp keys {pd.Timestamp: {symbol: weight}}
    """
    logger.info("Running EMA model with tracing enabled")
    
    # Run the EMA model in trace mode
    trace_results = ema_allocation_model_updated(
        price_data=price_data,
        trace_mode=True,
        **params
    )
    
    # Unpack the results
    (dict_weights, ratios_output, ranks_df, signal_output, 
     short_ema_df, med_ema_df, long_ema_df,
     stmtemax_hist_df, mtltemax_hist_df, emaxavg_hist_df) = trace_results
    
    # Save detailed trace data to CSV files
    try:
        # Save EMA averages with proper date matrix format
        ema_averages = pd.concat([short_ema_df, med_ema_df, long_ema_df], axis=1)
        ema_averages.columns = [f"{col}_ST" for col in short_ema_df.columns] + \
                              [f"{col}_MT" for col in med_ema_df.columns] + \
                              [f"{col}_LT" for col in long_ema_df.columns]
        ema_averages.index.name = 'Date'  # Set index name
        save_df_to_trace_dir(ema_averages, f"02_ema_average_history_{timestamp}.csv", 
                           step_description="EMA Averages History")
        
        # Save ranks with proper date matrix format
        ranks_df.index.name = 'Date'
        save_df_to_trace_dir(ranks_df, f"03_ranks_history_{timestamp}.csv", 
                           step_description="Asset Ranks History")
        
        # Save raw signal history (EMA ratios) with proper date matrix format
        raw_signals = pd.concat([stmtemax_hist_df, mtltemax_hist_df, emaxavg_hist_df], axis=1)
        raw_signals.columns = [f"{col}_STMTEMAX" for col in stmtemax_hist_df.columns] + \
                             [f"{col}_MTLTEMAX" for col in mtltemax_hist_df.columns] + \
                             [f"{col}_EMAXAvg" for col in emaxavg_hist_df.columns]
        raw_signals.index.name = 'Date'
        save_df_to_trace_dir(raw_signals, f"04_raw_signal_history_{timestamp}.csv", 
                           step_description="Raw Signal History")
        
        logger.info("Successfully saved detailed signal history data to CSV files")
    except Exception as e:
        logger.error(f"Failed to save detailed signal history data: {e}")
    
    # Create a proper date matrix DataFrame for the full signal history
    # Use the original dict_weights to construct a historical DataFrame
    # Start with a DataFrame with the same dates as the price data
    signal_history = pd.DataFrame(index=price_data.index, columns=price_data.columns)
    signal_history.fillna(0.0, inplace=True)  # Initialize with zeros
    
    # For each date in the ranks DataFrame
    for date in ranks_df.index:
        # Get the ranks for this date
        try:
            date_ranks = ranks_df.loc[date]
            
            # Find the top 2 ranked assets (lowest rank values)
            top_assets = date_ranks.nsmallest(2).index.tolist()
            
            if len(top_assets) >= 2:
                # Assign 60% to top asset, 40% to second
                signal_history.loc[date, top_assets[0]] = 0.6
                signal_history.loc[date, top_assets[1]] = 0.4
                
                # Ensure all other assets have 0% allocation
                for ticker in signal_history.columns:
                    if ticker not in top_assets:
                        signal_history.loc[date, ticker] = 0.0
        except Exception as e:
            logger.error(f"Error calculating allocations for date {date}: {e}")
    
    return signal_history

# ---------------------------------------------------------------------------
# Attach class-like interface so backtest_v4.py can treat this function as an
# object with generate_signals/validate_signals methods (backward compatibility)
# ---------------------------------------------------------------------------

def _bridge_generate_signals(price_data, **params):
    """Alias for run_ema_model_with_tracing so backtest expects .generate_signals."""
    return run_ema_model_with_tracing(price_data, **params)

def _bridge_validate_signals(self, signals):
    """Placeholder validator – passthrough for now."""
    return signals

# Monkey-patch the function to have expected attributes
run_ema_model_with_tracing.generate_signals = _bridge_generate_signals  # type: ignore
run_ema_model_with_tracing.validate_signals = _bridge_validate_signals  # type: ignore

