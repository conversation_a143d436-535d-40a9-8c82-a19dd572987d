@echo off
REM Activate virtual environment
call F:\AI_Library\my_quant_env\Scripts\activate.bat

REM Set Python command with path
set PYCMD=python -c "import sys; sys.path.insert(0, r's:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template'); import memory_tool; print('SUCCESS: memory_tool found at:', memory_tool.__file__)"

REM Run the check
%PYCMD%
if errorlevel 1 (
    echo ERROR: memory_tool import failed
    echo Check: 
    echo 1. Virtual environment activation
    echo 2. Project path exists
    echo 3. memory_tool/__init__.py exists
)
pause
