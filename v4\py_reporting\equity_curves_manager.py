"""
Enhanced Equity Curves Manager for Parameter Optimization

This module provides a robust solution for managing equity curves across multiple
parameter optimization combinations. Instead of overwriting the same file repeatedly,
it maintains a consolidated CSV with:

- Date column (index)
- One column per parameter combination with descriptive naming
- Incremental updates as each combination completes
- Efficient storage and retrieval for performance analysis
- Support for 5000+ parameter combinations

Author: AI Assistant  
Date: 2025-01-24
"""

import pandas as pd
import numpy as np
from pathlib import Path
from datetime import datetime
import logging
import json
from typing import Dict, List, Tuple, Optional, Any
import os

logger = logging.getLogger(__name__)


class EquityCurvesManager:
    """Manages consolidated equity curves for optimization workflows."""
    
    def __init__(self, output_dir: str = "reporting", filename_prefix: str = "optimization_equity_curves"):
        """Initialize the equity curves manager.
        
        Args:
            output_dir: Directory to save equity curves file
            filename_prefix: Prefix for the equity curves filename
        """
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.equity_file = self.output_dir / f"{filename_prefix}_{self.timestamp}.csv"
        self.metadata_file = self.output_dir / f"{filename_prefix}_metadata_{self.timestamp}.json"
        
        # Initialize empty DataFrame for equity curves
        self.equity_curves_df = None
        self.combination_metadata = {}
        
        logger.info(f"EquityCurvesManager initialized:")
        logger.info(f"  Equity file: {self.equity_file}")
        logger.info(f"  Metadata file: {self.metadata_file}")
    
    def _create_column_name(self, combination: Dict[str, Any]) -> str:
        """Create a unique, descriptive column name using combo ID system.
        
        Args:
            combination: Dictionary of parameter values
            
        Returns:
            Combo ID string in format S5_M30_L100_E1_T2
        """
        # Use the combo ID system for consistent naming
        combo_id = self._generate_combo_id(combination)
        
        # Ensure uniqueness by adding counter if needed (shouldn't happen with proper combo IDs)
        base_name = combo_id
        counter = 1
        while combo_id in self.combination_metadata:
            counter += 1
            combo_id = f"{base_name}_v{counter}"
        
        return combo_id
    
    def _generate_combo_id(self, combination: Dict[str, Any]) -> str:
        """Generate readable combo ID from parameter combination.
        
        Args:
            combination: Parameter combination dictionary
            
        Returns:
            str: Combo ID in format S5_M30_L100_E1_T2
        """
        return f"S{combination['st_lookback']}_M{combination['mt_lookback']}_L{combination['lt_lookback']}_E{combination['execution_delay']}_T{combination['top_n']}"
    
    def initialize_with_date_index(self, date_index: pd.DatetimeIndex) -> None:
        """Initialize the equity curves DataFrame with date index.
        
        Args:
            date_index: DatetimeIndex for the equity curves
        """
        self.equity_curves_df = pd.DataFrame(index=date_index)
        logger.info(f"Initialized equity curves DataFrame with {len(date_index)} dates")
        logger.info(f"Date range: {date_index[0]} to {date_index[-1]}")
    
    def add_combination_result(self, combination: Dict[str, Any], equity_curve: pd.Series) -> str:
        """Add equity curve for a parameter combination.
        
        Args:
            combination: Parameter combination dictionary
            equity_curve: Equity curve as pandas Series
            
        Returns:
            Column name used for this combination
        """
        if self.equity_curves_df is None:
            # Initialize with the equity curve's index if not already done
            self.initialize_with_date_index(equity_curve.index)
        
        # Create unique column name
        column_name = self._create_column_name(combination)
        
        # Align equity curve with our date index (in case of minor differences)
        aligned_curve = equity_curve.reindex(self.equity_curves_df.index, method='ffill')
        
        # Add to DataFrame
        self.equity_curves_df[column_name] = aligned_curve
        
        # Store metadata
        self.combination_metadata[column_name] = {
            'parameters': combination.copy(),
            'added_timestamp': datetime.now().isoformat(),
            'curve_start_value': float(aligned_curve.iloc[0]) if len(aligned_curve) > 0 else np.nan,
            'curve_end_value': float(aligned_curve.iloc[-1]) if len(aligned_curve) > 0 else np.nan,
            'curve_length': len(aligned_curve)
        }
        
        logger.info(f"Added combination result: {column_name}")
        logger.info(f"  Parameters: {combination}")
        logger.info(f"  Curve: ${aligned_curve.iloc[0]:,.0f} → ${aligned_curve.iloc[-1]:,.0f}")
        
        return column_name
    
    def add_benchmark_curve(self, benchmark_curve: pd.Series, name: str = "BENCHMARK_EW") -> str:
        """Add benchmark equity curve.
        
        Args:
            benchmark_curve: Benchmark equity curve
            name: Name for benchmark column
            
        Returns:
            Column name used for benchmark
        """
        if self.equity_curves_df is None:
            self.initialize_with_date_index(benchmark_curve.index)
        
        # Align benchmark with our date index
        aligned_curve = benchmark_curve.reindex(self.equity_curves_df.index, method='ffill')
        
        # Add to DataFrame
        self.equity_curves_df[name] = aligned_curve
        
        # Store metadata
        self.combination_metadata[name] = {
            'type': 'benchmark',
            'name': name,
            'added_timestamp': datetime.now().isoformat(),
            'curve_start_value': float(aligned_curve.iloc[0]) if len(aligned_curve) > 0 else np.nan,
            'curve_end_value': float(aligned_curve.iloc[-1]) if len(aligned_curve) > 0 else np.nan,
            'curve_length': len(aligned_curve)
        }
        
        logger.info(f"Added benchmark curve: {name}")
        logger.info(f"  Curve: ${aligned_curve.iloc[0]:,.0f} → ${aligned_curve.iloc[-1]:,.0f}")
        
        return name
    
    def save_to_disk(self) -> Tuple[Path, Path]:
        """Save equity curves and metadata to disk.
        
        Returns:
            Tuple of (equity_file_path, metadata_file_path)
        """
        if self.equity_curves_df is None or self.equity_curves_df.empty:
            logger.warning("No equity curves to save")
            return None, None
        
        # Save equity curves CSV
        self.equity_curves_df.to_csv(self.equity_file)
        
        # Save metadata JSON
        with open(self.metadata_file, 'w') as f:
            json.dump(self.combination_metadata, f, indent=2, default=str)
        
        logger.info(f"Saved equity curves: {self.equity_file}")
        logger.info(f"  Shape: {self.equity_curves_df.shape}")
        logger.info(f"  Columns: {len(self.equity_curves_df.columns)} combinations")
        logger.info(f"Saved metadata: {self.metadata_file}")
        
        return self.equity_file, self.metadata_file
    
    def get_equity_curve(self, column_name: str) -> Optional[pd.Series]:
        """Get equity curve for a specific combination.
        
        Args:
            column_name: Name of the column/combination
            
        Returns:
            Equity curve Series or None if not found
        """
        if self.equity_curves_df is None or column_name not in self.equity_curves_df.columns:
            return None
        
        return self.equity_curves_df[column_name]
    
    def get_combination_parameters(self, column_name: str) -> Optional[Dict[str, Any]]:
        """Get parameter combination for a specific column.
        
        Args:
            column_name: Name of the column/combination
            
        Returns:
            Parameters dictionary or None if not found
        """
        if column_name not in self.combination_metadata:
            return None
        
        return self.combination_metadata[column_name].get('parameters', {})
    
    def get_all_combinations(self) -> List[Tuple[str, Dict[str, Any]]]:
        """Get all combinations with their parameters.
        
        Returns:
            List of (column_name, parameters) tuples
        """
        combinations = []
        for column_name, metadata in self.combination_metadata.items():
            if 'parameters' in metadata:  # Skip benchmarks
                combinations.append((column_name, metadata['parameters']))
        
        return combinations
    
    def calculate_performance_metrics(self, column_name: str) -> Dict[str, float]:
        """Calculate performance metrics for a specific combination.
        
        Args:
            column_name: Name of the column/combination
            
        Returns:
            Dictionary of performance metrics
        """
        equity_curve = self.get_equity_curve(column_name)
        if equity_curve is None or len(equity_curve) < 2:
            return {}
        
        # Calculate returns
        returns = equity_curve.pct_change().dropna()
        
        # Basic metrics
        total_return = (equity_curve.iloc[-1] / equity_curve.iloc[0]) - 1
        
        # Annualized metrics (assuming daily data)
        trading_days = 252
        years = len(equity_curve) / trading_days
        cagr = (equity_curve.iloc[-1] / equity_curve.iloc[0]) ** (1/years) - 1 if years > 0 else 0
        
        volatility = returns.std() * np.sqrt(trading_days) if len(returns) > 0 else 0
        sharpe = (returns.mean() * trading_days) / volatility if volatility > 0 else 0
        
        # Drawdown calculation
        rolling_max = equity_curve.expanding().max()
        drawdown = (equity_curve - rolling_max) / rolling_max
        max_drawdown = drawdown.min()
        
        metrics = {
            'Total_Return': total_return,
            'CAGR': cagr,
            'Volatility': volatility,
            'Sharpe_Ratio': sharpe,
            'Max_Drawdown': max_drawdown,
            'Final_Value': equity_curve.iloc[-1],
            'Start_Value': equity_curve.iloc[0],
            'Trading_Days': len(equity_curve)
        }
        
        return metrics
    
    def create_performance_summary(self) -> pd.DataFrame:
        """Create performance summary DataFrame for all combinations.
        
        Returns:
            DataFrame with performance metrics for all combinations
        """
        if self.equity_curves_df is None or self.equity_curves_df.empty:
            return pd.DataFrame()
        
        summary_data = []
        
        for column_name in self.equity_curves_df.columns:
            # Get parameters
            params = self.get_combination_parameters(column_name)
            if params is None:
                # Handle benchmarks or other non-parameter columns
                params = {'Type': column_name}
            
            # Calculate metrics
            metrics = self.calculate_performance_metrics(column_name)
            
            # Combine parameters and metrics
            row_data = {'Column_Name': column_name}
            row_data.update(params)
            row_data.update(metrics)
            
            summary_data.append(row_data)
        
        summary_df = pd.DataFrame(summary_data)
        
        # Sort by Sharpe Ratio descending (best first)
        if 'Sharpe_Ratio' in summary_df.columns:
            summary_df = summary_df.sort_values('Sharpe_Ratio', ascending=False)
        
        logger.info(f"Created performance summary with {len(summary_df)} combinations")
        
        return summary_df
    
    @classmethod
    def load_from_disk(cls, equity_file: Path, metadata_file: Path = None) -> 'EquityCurvesManager':
        """Load equity curves manager from saved files.
        
        Args:
            equity_file: Path to equity curves CSV
            metadata_file: Path to metadata JSON (optional)
            
        Returns:
            EquityCurvesManager instance
        """
        manager = cls(output_dir=equity_file.parent, filename_prefix="loaded")
        
        # Load equity curves
        manager.equity_curves_df = pd.read_csv(equity_file, index_col=0, parse_dates=True)
        
        # Load metadata if available
        if metadata_file and metadata_file.exists():
            with open(metadata_file, 'r') as f:
                manager.combination_metadata = json.load(f)
        
        logger.info(f"Loaded EquityCurvesManager from {equity_file}")
        logger.info(f"  Shape: {manager.equity_curves_df.shape}")
        
        return manager


def create_optimization_workflow(combinations: List[Dict[str, Any]], 
                               optimization_runner_func: callable,
                               output_dir: str = "reporting") -> EquityCurvesManager:
    """Create complete optimization workflow with equity curves manager.
    
    Args:
        combinations: List of parameter combinations
        optimization_runner_func: Function that takes a combination and returns equity curve
        output_dir: Directory for output files
        
    Returns:
        EquityCurvesManager with all results
    """
    logger.info(f"Starting optimization workflow with {len(combinations)} combinations")
    
    # Initialize manager
    manager = EquityCurvesManager(output_dir=output_dir)
    
    # Process each combination
    for i, combination in enumerate(combinations):
        logger.info(f"Processing combination {i+1}/{len(combinations)}: {combination}")
        
        try:
            # Run optimization for this combination
            equity_curve = optimization_runner_func(combination)
            
            if equity_curve is not None:
                # Add to manager
                column_name = manager.add_combination_result(combination, equity_curve)
                logger.info(f"✓ Added combination {i+1} as {column_name}")
            else:
                logger.warning(f"✗ Combination {i+1} returned None")
                
        except Exception as e:
            logger.error(f"✗ Error processing combination {i+1}: {e}")
            continue
        
        # Save progress periodically (every 10 combinations or at end)
        if (i + 1) % 10 == 0 or (i + 1) == len(combinations):
            manager.save_to_disk()
            logger.info(f"Progress saved: {i+1}/{len(combinations)} combinations complete")
    
    # Final save
    equity_file, metadata_file = manager.save_to_disk()
    
    # Create performance summary
    summary_df = manager.create_performance_summary()
    if not summary_df.empty:
        summary_file = manager.output_dir / f"optimization_performance_summary_{manager.timestamp}.csv"
        summary_df.to_csv(summary_file, index=False)
        logger.info(f"Performance summary saved: {summary_file}")
    
    logger.info("Optimization workflow complete!")
    return manager
