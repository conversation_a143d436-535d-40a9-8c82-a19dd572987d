@echo off
SETLOCAL EnableDelayedExpansion

:: =============================================
:: Memory Tool Setup Script
:: Version: 2.0 (2025-05-17)
:: Sets up environment and dependencies for AI Memory Tool
:: =============================================

:: --------------------------
:: Configuration Section
:: --------------------------
SET PYTHON_EXE=F:\AI_Library\my_quant_env\Scripts\python.exe
SET PROJECT_ROOT=%~dp0
SET LOGS_DIR=%PROJECT_ROOT%logs
SET LOG_FILE=%LOGS_DIR%\memory_tool_setup_%DATE:~-4,4%%DATE:~-10,2%%DATE:~-7,2%.log
SET PACKAGES=pyan3 pylint graphviz

:: --------------------------
:: Initialization
:: --------------------------
title Memory Tool Setup v2.0
color 0A
mode con: cols=90 lines=25

:: Create logs directory if it doesn't exist
if not exist "%LOGS_DIR%" mkdir "%LOGS_DIR%"

echo [%DATE% %TIME%] Starting Memory Tool Setup > "%LOG_FILE%"
echo ============================================= >> "%LOG_FILE%"
echo === MEMORY TOOL SETUP v2.0 ===
echo Project Root: %PROJECT_ROOT%
echo Python Executable: %PYTHON_EXE%
echo Log File: %LOG_FILE%
echo =============================================
echo.

:: --------------------------
:: Dependency Checks
:: --------------------------
echo [1/3] Checking system dependencies...
echo [%DATE% %TIME%] Checking dependencies >> "%LOG_FILE%"

:: Verify Python executable exists
if not exist "%PYTHON_EXE%" (
    echo ERROR: Python executable not found at %PYTHON_EXE% >> "%LOG_FILE%"
    echo [X] ERROR: Python executable not found at %PYTHON_EXE%
    goto :end
) else (
    echo [✓] Python found at %PYTHON_EXE% >> "%LOG_FILE%"
    echo [✓] Python executable found
)

:: Verify Graphviz installation
where dot >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo WARNING: Graphviz not found in PATH. Diagrams will not be generated. >> "%LOG_FILE%"
    echo [!] WARNING: Graphviz not found (diagrams disabled)
) else (
    echo [✓] Graphviz detected >> "%LOG_FILE%"
    echo [✓] Graphviz detected
)
echo.

:: --------------------------
:: Package Installation
:: --------------------------
echo [2/3] Installing Python packages...
echo [%DATE% %TIME%] Installing packages: %PACKAGES% >> "%LOG_FILE%"
echo Installing: %PACKAGES%
echo.

"%PYTHON_EXE%" -m pip install --upgrade pip >> "%LOG_FILE%" 2>&1
if %ERRORLEVEL% neq 0 (
    echo ERROR: Failed to upgrade pip >> "%LOG_FILE%"
    echo [X] ERROR: Failed to upgrade pip
    goto :end
)

"%PYTHON_EXE%" -m pip install %PACKAGES% >> "%LOG_FILE%" 2>&1
if %ERRORLEVEL% neq 0 (
    echo ERROR: Package installation failed >> "%LOG_FILE%"
    echo [X] ERROR: Package installation failed
    goto :end
)

:: --------------------------
:: Verification
:: --------------------------
echo [3/3] Verifying installations...
echo [%DATE% %TIME%] Verifying package installations >> "%LOG_FILE%"
echo.

"%PYTHON_EXE%" -c "import pyan3, pylint, graphviz; print('All packages imported successfully')" >> "%LOG_FILE%" 2>&1
if %ERRORLEVEL% neq 0 (
    echo ERROR: Package verification failed >> "%LOG_FILE%"
    echo [X] ERROR: Package verification failed
    goto :end
) else (
    echo [✓] All packages verified >> "%LOG_FILE%"
    echo [✓] All packages verified
)

:: --------------------------
:: Completion
:: --------------------------
echo [%DATE% %TIME%] Setup completed successfully >> "%LOG_FILE%"
echo ============================================= >> "%LOG_FILE%"
echo.
echo =============================================
echo [✓] SETUP COMPLETED SUCCESSFULLY
echo Log file created at:
echo %LOG_FILE%
echo =============================================
echo.

:end
echo.
echo Press any key to exit...
pause >nul
ENDLOCAL
