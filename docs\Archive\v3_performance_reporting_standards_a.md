# V3 Performance Reporting Standards (Version A)

---

# Tier 1: Major Output Reports

## Performance Table Report (XLSX)

### Tabs within Performance Table Report (Tier 2)

#### Signal History

- **Tab Name**: "Signal History"
- **Structure**:
  - Rows: Daily dates (no timestamps)
  - Columns: Tickers + Cash
- **Content**:
  - % allocation signals (end-of-day)
  - Each row sums to 100%
- **Format**: `0.00%`

---

#### Allocation History

- **Tab Name**: "Allocation History"
- **Structure**: Matches Signal History
- **Content**:
  - Each row reflects the engine's ACTUAL capital allocation for that day, based on all trades executed up to and including that date.
  - Once an allocation is made, it is held constant and shown on all subsequent days until the next trade or allocation event. There are no gaps—every day in the backtest period must be fully allocated across all assets and cash.
  - Actual % allocations (from trade logs)
  - Typically 1-day lag from signals
  - Each row sums to 100%
- **Format**: `0.00%`

##### Sample Table

| Date       | SPY | SHV | EFA | TLT | PFF | Cash |
| ---------- | --- | --- | --- | --- | --- | ---- |
| 2020-01-02 | 0   | 0   | 0   | 0.6 | 0.4 | 0    |
| 2020-01-03 | 0   | 0   | 0   | 0.6 | 0.4 | 0    |
| 2020-01-06 | 0   | 0   | 0   | 0.6 | 0.4 | 0    |
| 2020-01-07 | 0   | 0   | 0   | 0.6 | 0.4 | 0    |
| 2020-01-08 | 0   | 0   | 0   | 0.6 | 0.4 | 0    |
| 2020-01-09 | 0   | 0   | 0   | 0.6 | 0.4 | 0    |
| ...        | ... | ... | ... | ... | ... | ...  |

##### Allocation Weights Over Time Graphic (PNG)

- **Requirement:**
  - Produce a PNG image visualizing portfolio allocation weights over time, for all assets in the Allocation History tab.
  - The graphic should show the evolution of allocations as a stacked area or bar chart, with the x-axis as Date and y-axis as Allocation (0 to 1).
  - The legend must clearly indicate each asset.
  - Store the image as `docs/allocation_history_example.png`.
  - The PNG may be a separate output file or, if easy, an additional tab in the XLSX (image embedded).
- **Example:**
  - ![Example Allocation History Graphic](../docs/allocation_history_example.png)

---

#### Trade Log

- **Tab Name**: "Trade Log"
- **Header**:
  - Cell A1 must display all main/default parameter settings in text format  
    Example: "st_lookback=30, mt_lookback=90, lt_lookback=180, execution_delay=1, top_n=5"
- **Structure:**
  - Columns (in order):
    1. `trade_num` (sequential integer)
    2. `symbol` (ticker)
    3. `quantity` (signed integer, + for buy, - for sell)
    4. `execution_date` (YYYY-MM-DD, no time)
    5. `execution_price` (float, 3 decimals)
    6. `commission+slippage` (float, 2 decimals) — *Total of commission and slippage for the trade*
    7. `amount` (float, 2 decimals)
    8. `pnl` (float, 2 decimals)
- **Rows:**
  - One per executed trade (buy/sell), sorted by execution_date and trade_num.
  - Final date: All open positions forcibly closed, with closing trades shown.
- **Content:**
  - Every executed trade from the backtest engine, not intended but actual.
  - All tickers, all dates in range.
  - No missing trades; partial fills included if engine supports.
- **Formatting:**
  - Dates: `YYYY-MM-DD`
  - `execution_price`: 3 decimals (e.g., 97.123)
  - `commission+slippage`, `amount`, `pnl`: 2 decimals (e.g., 4019.66, -7.85)
  - All values as floats (no currency formatting required)
  - No blank rows or extra summary/statistics.
- **Audit Rule:**
  - Must be a direct, reproducible output from the engine for given parameters.

##### Sample Table

| trade_num | symbol | quantity | execution_date | execution_price | commission+slippage | amount   | pnl   |
| --------- | ------ | -------- | -------------- | --------------- | ------------------- | -------- | ----- |
| 1         | SHV    | 41       | 2020-04-03     | 97.942          | 4.02                | 4019.66  | 0.00  |
| 2         | TLT    | 40       | 2020-04-03     | 147.598         | 6.05                | 5909.90  | 0.00  |
| 3         | SHV    | -41      | 2020-06-03     | 97.749          | 4.01                | -4007.62 | -7.85 |
| ...       | ...    | ...      | ...            | ...             | ...                 | ...      | ...   |

---

### performance_tables File Requirements

- **Filename**: `EMA_V3_1_performance_tables_YYYYMMDD_HHMMSS.xlsx`, Tabsheet= Performance
- **Header**: 
  - Cell A1 must display all main/default parameter settings in text format  
    Example: "st_lookback=30, mt_lookback=90, lt_lookback=180, execution_delay=1, top_n=5"

### Structure Requirements

1. **Parameter Columns** (left side):
   
   - Strategy
   - st_lookback
   - mt_lookback
   - lt_lookback
   - execution_delay
   - top_n
   - [Other parameters]

2. **Strategy Rows**:
   
   - One row per parameter combination during optimization
     - That means a new row for everytime a new parameter combination is generation (with all columns updated for those parameter outcomes)
   - All parameter values must be explicitly shown
   - 

3. **Metric Columns** (right side):
   
   - CAGR
   - Sharpe
   - Sortino
   - Max Drawdown
   - Win Rate
   - Final Value
   - YTD returns
   - Annual returns

### Formatting Rules

- **Percentages**: `0.00%`
- **Ratios**: `0.00`
- **Currency**: `$#,##0.00`
- **Benchmark Row**:
  - Appears exactly once at top
  - Full metrics, empty parameter columns

---

## Monthly & Annual Returns Graphic (PNG)

- **Report Name:** Monthly and Annual Actual Returns (PNG)
- **Output Format:** PNG image, stored in the report output folder alongside the Excel file.
- **Purpose:** Visual summary of the ACTUAL realized strategy returns for the main/default variable parameter settings, for the entire backtest period.
- **Content:**
  - **Monthly Returns:**
    - Displayed as a heatmap/matrix, with years as rows and months as columns.
    - Each cell shows the monthly return as a percentage (e.g., "2.40%"), color-coded from red (negative) to green (positive).
    - All values are actual, realized returns from the engine (not signals or intended allocations).
  - **Annual Returns:**
    - Displayed as a single column next to the monthly matrix, showing the total annual return for each year.
    - Same color-coding as monthly.
  - **Color Bar:**
    - Include a color bar/legend indicating the return scale (e.g., -5% to +8%).
- **Formatting:**
  - Title: "Monthly Returns" (centered above matrix)
  - Axis labels: Year (rows), Month (columns: Jan–Dec)
  - All values as percentages, with 2 decimals (e.g., "-5.81%")
  - No missing months/years; blank or gray out if no data
  - High DPI (at least 300, ideally 600) for print-quality output
- **File Naming:**
  - `EMA_V3_1_monthly_returns_YYYYMMDD_HHMMSS.png` (match Excel naming convention)
- **Audit Rule:**
  - Must match the actual realized returns in the Allocation History and Trade Log tabs for the main/default parameter settings
- **Example:**
  - ![Example Monthly/Annual Returns Heatmap](../docs/example_monthly_returns.png)

---

## Combined Cumulative Returns & Drawdown Graphic (PNG)

- **Report Name:** Combined Cumulative Returns and Drawdowns (PNG)
- **Output Format:** PNG image, stored in the report output folder alongside the Excel file.
- **Purpose:** Visual summary of the ACTUAL realized strategy performance for the main/default variable parameter settings, showing both cumulative returns and drawdowns in a single graphic for the entire backtest period.
- **Layout Requirements:**
  - The top panel displays cumulative returns for the strategy and benchmark (if present), using actual post-engine capital values.
  - The bottom panel displays drawdowns over the same period.
  - Both plots must share the same x-axis (Date), covering the full backtest period.
- **Parameter Display:**
  - The sub-title must show the start and end date of the backtest period (e.g., "2020-01-01 to 2025-05-01").
  - All main/default parameter settings (e.g., st_lookback=30, mt_lookback=90, etc.) must be displayed in a smaller but readable font at the bottom of the image.
- **Overlay Requirement:**
  - If possible, overlay arrows or markers on the cumulative returns plot to indicate each date when a remix/change in % allocation (from Allocation History) occurs.
  - Arrows/markers should be clear but not obscure the main trend lines.
- **File Naming:**
  - `EMA_V3_1_cumulative_returns_drawdown_YYYYMMDD_HHMMSS.png` (match Excel naming convention)
- **Audit Rule:**
  - Must match the actual realized returns and drawdowns in the Allocation History, Trade Log, and other reports for the main/default parameter settings.
- **Example:**
  - to be continued

---

## Implementation Rules

1. Never remove existing functionality
2. Never change column ordering
3. Never modify core formatting
4. All Reporting = Protected sections and require explicit approval before changing!
