#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
V3 Reporting Debug Script

This script performs a simplified test of the V3 reporting system components
to identify which specific module or data flow is failing.

FileName: debug_v3_reporting.py
"""

import os
import sys
import pandas as pd
import numpy as np
from pathlib import Path
import datetime
import json
import traceback
import logging

# Add project root to path
project_root = Path(__file__).parent
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

# Configure logging with more detailed format
timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')

# Windows-specific fix for Unicode characters in console
if os.name == 'nt':
    import sys
    import codecs
    # This is the more reliable way to set UTF-8 output in Windows console
    sys.stdout = codecs.getwriter('utf-8')(sys.stdout.buffer)
    sys.stderr = codecs.getwriter('utf-8')(sys.stderr.buffer)

logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f"reporting/logs/debug_v3_reporting_{timestamp}.log", encoding='utf-8'),
        logging.StreamHandler()
    ]
)

# Create module logger
logger = logging.getLogger("debug_v3_reporting")
logger.info("Starting V3 reporting debug script")

def test_imports():
    """Test all critical imports for the V3 reporting system"""
    logger.info("Testing critical imports...")
    import_tests = []
    
    # Test v3_engine imports
    try:
        from v3_engine.parameter_registry import get_registry
        import_tests.append(("v3_engine.parameter_registry", True, None))
    except Exception as e:
        import_tests.append(("v3_engine.parameter_registry", False, str(e)))
        logger.error(f"Failed to import v3_engine.parameter_registry: {e}")
    
    try:
        from v3_engine.performance_reporter_adapter import PerformanceReporterAdapter
        import_tests.append(("v3_engine.performance_reporter_adapter", True, None))
    except Exception as e:
        import_tests.append(("v3_engine.performance_reporter_adapter", False, str(e)))
        logger.error(f"Failed to import v3_engine.performance_reporter_adapter: {e}")
    
    # Test v3_reporting imports
    try:
        from v3_reporting.v3_allocation_report import generate_v3_allocation_report
        import_tests.append(("v3_reporting.v3_allocation_report", True, None))
    except Exception as e:
        import_tests.append(("v3_reporting.v3_allocation_report", False, str(e)))
        logger.error(f"Failed to import v3_reporting.v3_allocation_report: {e}")
    
    try:
        from v3_reporting.v3_performance_report import generate_v3_performance_report
        import_tests.append(("v3_reporting.v3_performance_report", True, None))
    except Exception as e:
        import_tests.append(("v3_reporting.v3_performance_report", False, str(e)))
        logger.error(f"Failed to import v3_reporting.v3_performance_report: {e}")
    
    # Test adapter module imports
    try:
        from v3_engine.V3_perf_repadapt_legacybridge import generate_performance_report
        import_tests.append(("v3_engine.V3_perf_repadapt_legacybridge", True, None))
    except Exception as e:
        import_tests.append(("v3_engine.V3_perf_repadapt_legacybridge", False, str(e)))
        logger.error(f"Failed to import v3_engine.V3_perf_repadapt_legacybridge: {e}")
    
    try:
        from v3_engine.V3_perf_repadapt_metrics import extract_metrics_from_results
        import_tests.append(("v3_engine.V3_perf_repadapt_metrics", True, None))
    except Exception as e:
        import_tests.append(("v3_engine.V3_perf_repadapt_metrics", False, str(e)))
        logger.error(f"Failed to import v3_engine.V3_perf_repadapt_metrics: {e}")
        
    # Report results
    logger.info("Import test results:")
    for module, status, error in import_tests:
        status_str = "✅ OK" if status else f"❌ FAILED: {error}"
        logger.info(f"  {module}: {status_str}")
    
    return import_tests

def create_test_data():
    """Create test data for reporting components"""
    logger.info("Creating test data for reporting components...")
    
    # Create date range
    start_date = datetime.date(2024, 1, 1)
    end_date = datetime.date(2024, 4, 1)
    dates = pd.date_range(start=start_date, end=end_date, freq='B')
    
    # Create test tickers
    tickers = ['AAPL', 'MSFT', 'AMZN', 'CASH']
    
    # Create signal history DataFrame
    np.random.seed(42)  # For reproducibility
    signal_history = pd.DataFrame(index=dates)
    for ticker in tickers:
        if ticker == 'CASH':
            # Cash should be the remainder to make sum = 1
            signal_history[ticker] = 0.1
        else:
            signal_history[ticker] = np.random.random(len(dates)) * 0.3
    
    # Normalize rows to sum to 1
    signal_history = signal_history.div(signal_history.sum(axis=1), axis=0)
    
    # Create price data
    price_data = pd.DataFrame(index=dates)
    for ticker in tickers:
        if ticker == 'CASH':
            # Cash price doesn't change
            price_data[ticker] = 1.0
        else:
            # Start at 100 and apply random daily returns
            daily_returns = np.random.normal(0.0005, 0.01, len(dates))
            price = 100
            prices = [price]
            for ret in daily_returns[1:]:
                price *= (1 + ret)
                prices.append(price)
            price_data[ticker] = prices
    
    # Create portfolio values
    portfolio_values = pd.Series(index=dates)
    portfolio_values[0] = 100000  # Initial value
    for i in range(1, len(dates)):
        # Simple approximation of portfolio growth based on allocation
        allocation = signal_history.iloc[i-1]
        price_change = price_data.iloc[i] / price_data.iloc[i-1] - 1
        weighted_return = (allocation * price_change).sum()
        portfolio_values[i] = portfolio_values[i-1] * (1 + weighted_return)
    
    # Create a backtest results structure to match what the engine produces
    backtest_results = {
        'signal_history': signal_history,
        'weights_history': signal_history.copy(),  # For testing, same as signal history
        'price_data': price_data,
        'portfolio_values': portfolio_values,
        'initial_capital': 100000.0,
        'final_value': portfolio_values.iloc[-1],
        'total_return': portfolio_values.iloc[-1] / portfolio_values.iloc[0] - 1,
        'strategy_returns': portfolio_values.pct_change().dropna(),
        'benchmark_returns': pd.Series(np.random.normal(0.0005, 0.01, len(dates)-1), index=dates[1:]),
        'trades': [],  # Empty trades list for simplicity
        'performance': {
            'sharpe': 1.2,
            'sortino': 1.8,
            'max_drawdown': 0.05,
            'volatility': 0.08,
            'win_rate': 0.55,
            'cagr': 0.12
        }
    }
    
    logger.info(f"Created test data with {len(dates)} dates and {len(tickers)} tickers")
    return backtest_results

def test_report_components(backtest_results):
    """Test each reporting component individually"""
    logger.info("Testing reporting components individually...")
    output_dir = Path("output/debug_v3_reporting")
    output_dir.mkdir(parents=True, exist_ok=True)
    
    results = {
        "v3_allocation_report": {"status": "not_tested"},
        "v3_performance_report": {"status": "not_tested"},
        "performance_reporter_adapter": {"status": "not_tested"}
    }
    
    # Create a timestamp for output files
    timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
    
    # Test allocation report component
    try:
        logger.info("Testing v3_allocation_report...")
        from v3_reporting.v3_allocation_report import generate_v3_allocation_report
        
        signal_df = backtest_results['signal_history']
        allocation_df = backtest_results['weights_history']
        portfolio_values = backtest_results['portfolio_values']
        
        logger.debug(f"Signal history: {signal_df.shape}, first few rows:\n{signal_df.head()}")
        
        output_files = generate_v3_allocation_report(
            signal_df=signal_df,
            allocation_df=allocation_df,
            output_dir=str(output_dir),
            strategy_name="debug",
            portfolio_values=portfolio_values
        )
        
        logger.info(f"Allocation report generation successful: {output_files}")
        results["v3_allocation_report"] = {
            "status": "success",
            "output_files": output_files
        }
    except Exception as e:
        logger.error(f"Failed to generate allocation report: {e}")
        logger.error(traceback.format_exc())
        results["v3_allocation_report"] = {
            "status": "failed",
            "error": str(e),
            "traceback": traceback.format_exc()
        }
    
    # Test performance report adapter
    try:
        logger.info("Testing performance_reporter_adapter...")
        from v3_engine.performance_reporter_adapter import PerformanceReporterAdapter
        
        # Get a registry first
        from v3_engine.parameter_registry import ParameterRegistry, get_registry
        registry = get_registry()
        
        adapter = PerformanceReporterAdapter(registry=registry)
        output_path = str(output_dir / f"debug_performance_{timestamp}.xlsx")
        
        logger.debug(f"Backtest results keys: {list(backtest_results.keys())}")
        
        report_path = adapter.generate_performance_report(
            backtest_results=backtest_results,
            output_path=output_path,
            strategy_name="debug",
            parameters=None  # Use defaults for testing
        )
        
        logger.info(f"Performance reporter adapter successful: {report_path}")
        results["performance_reporter_adapter"] = {
            "status": "success",
            "output_path": report_path
        }
    except Exception as e:
        logger.error(f"Failed to use performance reporter adapter: {e}")
        logger.error(traceback.format_exc())
        results["performance_reporter_adapter"] = {
            "status": "failed",
            "error": str(e),
            "traceback": traceback.format_exc()
        }
    
    # Test direct performance report
    try:
        logger.info("Testing v3_performance_report...")
        from v3_reporting.v3_performance_report import generate_v3_performance_report
        
        output_path = str(output_dir / f"debug_direct_performance_{timestamp}.xlsx")
        
        report_path = generate_v3_performance_report(
            backtest_results=backtest_results,
            output_path=output_path,
            strategy_name="debug"
        )
        
        logger.info(f"Direct performance report successful: {report_path}")
        results["v3_performance_report"] = {
            "status": "success",
            "output_path": report_path
        }
    except Exception as e:
        logger.error(f"Failed to generate direct performance report: {e}")
        logger.error(traceback.format_exc())
        results["v3_performance_report"] = {
            "status": "failed",
            "error": str(e),
            "traceback": traceback.format_exc()
        }
    
    # Save results
    output_json = output_dir / f"debug_results_{timestamp}.json"
    with open(output_json, 'w') as f:
        json.dump(results, f, indent=4, default=str)
    
    logger.info(f"Component tests completed. Results saved to {output_json}")
    return results

def run_debug():
    """Run the debug process"""
    results = {}
    try:
        logger.info("========== STARTING V3 REPORTING DEBUG ==========")
        
        # Test imports
        import_results = test_imports()
        results["imports"] = import_results
        
        # Check if critical imports failed - if so, stop early
        critical_failures = [item for item in import_results if not item[1]]
        if critical_failures:
            logger.critical(f"Critical import failures detected: {len(critical_failures)}")
            for module, _, error in critical_failures:
                logger.critical(f"Failed to import {module}: {error}")
            results["status"] = "FAILED_IMPORTS"
            return results
        
        # Create test data
        backtest_results = create_test_data()
        results["test_data_created"] = True
        
        # Test components
        component_results = test_report_components(backtest_results)
        results["components"] = component_results
        
        # Overall status
        failures = [comp for comp, res in component_results.items() if res.get("status") != "success"]
        if failures:
            logger.warning(f"Component failures detected: {failures}")
            results["status"] = "PARTIAL_SUCCESS"
        else:
            logger.info("All components tested successfully")
            results["status"] = "SUCCESS"
            
    except Exception as e:
        logger.critical(f"Unhandled exception in debug process: {e}")
        logger.critical(traceback.format_exc())
        results["status"] = "ERROR"
        results["error"] = str(e)
        results["traceback"] = traceback.format_exc()
    
    logger.info("========== COMPLETED V3 REPORTING DEBUG ==========")
    return results

if __name__ == "__main__":
    try:
        # Run debug
        results = run_debug()
        
        # Print summary
        print("\n" + "="*80)
        print(" V3 REPORTING DEBUG SUMMARY ")
        print("="*80)
        print(f"Status: {results.get('status', 'UNKNOWN')}")
        
        # Import tests
        print("\nImport Tests:")
        imports = results.get("imports", [])
        for module, status, error in imports:
            status_text = "✅ OK" if status else f"❌ FAILED: {error}"
            print(f"  {module}: {status_text}")
        
        # Component tests
        print("\nComponent Tests:")
        components = results.get("components", {})
        for component, result in components.items():
            status = result.get("status", "unknown")
            status_text = "✅ SUCCESS" if status == "success" else f"❌ {status.upper()}"
            print(f"  {component}: {status_text}")
            if status != "success":
                print(f"    Error: {result.get('error', 'Unknown error')}")
        
        print("\nCheck reporting/logs/debug_v3_reporting_*.log for detailed information")
        print("="*80)
        
    except Exception as e:
        print(f"Error running debug: {e}")
        traceback.print_exc()
