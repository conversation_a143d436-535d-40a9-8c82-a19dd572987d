# Backtest Engine V2 Documentation

## Overview

The v2 backtest engine is an enhanced version of the original backtest framework with improved separation of concerns, better auditability, and more detailed performance metrics. The engine follows a modular design that separates signal generation, position comparison, order generation, execution, and portfolio management.

## Enhanced Architecture & Implementation Details

### Modular Separation of Concerns
- The engine is composed of distinct modules for signal generation, allocation (position comparison and order generation), execution (with slippage/commission), and portfolio management. Each module is implemented in a dedicated Python file for clarity and maintainability.
- All modules interact through well-defined interfaces, ensuring that the portfolio state remains the single source of truth throughout the backtest.

### Parameter Definition and Docstring Standards
- All configurable parameters (e.g., initial capital, commission, slippage, signal parameters) are explicitly defined in constructors or functions and documented with comprehensive docstrings.
- Parameter optimization is implemented using `define_parameter` and `optimize_parameters` per Custom Function Library guidelines.

### Cost Basis Tracking & Auditability
- The portfolio module tracks cost basis for each position, ensuring accurate P&L and tax calculations.
- The `TradeLog` maintains a complete, auditable history of all trade executions.

### Daily Mark-to-Market & Period-End Calculations
- Portfolio values are updated daily using the `mark_to_market` method.
- Monthly and yearly returns are calculated using the last available market day of each period, ensuring realistic performance reporting.

### Execution Delay & Pending Orders
- The backtest engine supports execution delays by maintaining a queue of pending orders, which are executed after the specified delay period.

### Circular Import Fixes & Path Handling
- All module imports are explicit and use relative paths. Any previously conflicting modules have been renamed (e.g., `local_parameter_optimization.py`) to avoid circular references.

### File Presence & Roles
- All documented modules are present and their roles match the descriptions in this documentation.

## Architecture

### Module Mapping

| Path/Module | Lines | Role | Key Functions |
|------------|-------|------|--------------|
| `engine/backtest.py` | ~350 | Main engine coordinator | `run_backtest()`: Execute full simulation<br>`_calculate_results()`: Generate performance metrics |
| `engine/portfolio.py` | ~382 | Manages portfolio state and position history | `update_positions()`: Apply trades to portfolio<br>`mark_to_market()`: Daily valuation<br>`get_returns_series()`: Compute period returns |
| `engine/orders.py` | ~172 | Defines order and trade classes | `Order`: Buy/sell instructions<br>`Trade`: Executed order details<br>`TradeLog`: Complete history storage |
| `engine/execution.py` | ~197 | Handles order execution with market impact | `execute_orders()`: Apply slippage/commissions<br>`PercentSlippageModel`: Price impact model |
| `engine/allocation.py` | ~125 | Generates rebalance orders | `compare_positions()`: Compare current vs target<br>`generate_orders()`: Create Order objects |
| `models/ema_allocation_model.py` | ~254 | Signal generation model | `calculate_ema_metrics()`: Compute moving averages<br>`ema_allocation_model()`: Generate target weights |
| `data/data_loader.py` | ~245 | Data retrieval and preparation | `get_adjusted_close_data()`: Fetch price data<br>`load_data_for_backtest()`: Prepare all data |
| `run_backtest_v2.py` | ~281 | Main entry point script | `run_backtest_v2()`: Orchestrate full backtest<br>`adapter_ema_allocation()`: Model adapter |
| `config/config_v2.py` | ~100 | Configuration parameters | Default parameters for backtest engine |
| `config/local_parameter_optimization.py` | ~80 | Parameter definition | `define_parameter()`: Parameter specification |
| `config/paths.py` | ~50 | Path management | Centralized path definitions |
| `utils/date_utils.py` | ~120 | Date handling utilities | `standardize_date_range()`: Date normalization |

### Execution Flow

```mermaid
graph TD
    A[run_backtest_v2.py] --> B[data_loader.py]
    B --> C[ema_allocation_model.py]
    A --> D[backtest.py]
    C --> D
    D --> E[allocation.py]
    E --> D
    D --> F[execution.py]
    F --> G[portfolio.py]
    F --> H[orders.py]
    
    subgraph "Data Preparation"
        B
    end
    
    subgraph "Signal Generation"
        C
    end
    
    subgraph "Order Generation"
        E
    end
    
    subgraph "Order Execution"
        F
        H
    end
    
    subgraph "Portfolio Management"
        G
    end
    
    subgraph "Engine Coordinator"
        D
    end
```

### Logic Flow & Feedback Loops

The backtest engine follows a clear separation of concerns:
- **BacktestEngine (backtest.py)**: Central coordinator. Manages simulation loop, calls signal generator on rebalance dates, coordinates allocation and execution, and handles execution delay logic.
- **Order Processing Flow**: `Signal Generator → BacktestEngine → Allocation → BacktestEngine → Execution → Portfolio`
- **Feedback Loops**:
  - `portfolio.py` is the single source of truth for positions and performance, feeding data back to allocation (for rebalancing), execution (for trade validation), and reporting (for performance metrics).
- **On Rebalance Days**: Calls signal generator → generates orders via `calculate_rebalance_orders` → executes or queues orders depending on delay.
- **On Each Market Day**: Checks for pending orders, updates portfolio mark-to-market, tracks performance metrics.

## Key Features

1. **Separation of Signal Generation and Execution**
   - Signal generators only produce allocation targets
   - Allocation module compares current positions to targets
   - Order generation is handled separately from execution

2. **Enhanced Portfolio Tracking**
   - Detailed position history with cost basis tracking
   - Complete trade history for auditability
   - Daily mark-to-market updates

3. **Realistic Order Execution**
   - Configurable execution delay
   - Slippage and commission models
   - Pending order queue for delayed execution

4. **Comprehensive Performance Metrics**

- **Parameter Optimization Table Generation:**

  The backtest engine supports generating comprehensive performance tables that display results for multiple parameter combinations (e.g., `st_lookback`, `mt_lookback`, `lt_lookback`, `top_n`, `execution_delay`). This is achieved by:

  - Running parameter optimization (grid search) via `local_parameter_optimization.py`, which iterates through parameter combinations.
  - For each combination, the engine runs a backtest, computes performance metrics (CAGR, Sharpe, Sortino, Max Drawdown, etc.), and logs results.
  - Results are aggregated into a DataFrame, then passed to `generate_performance_report_local` in `reporting/performance_reporting.py`.
  - The resulting Excel file (e.g., `ema_performance_tables_YYYY-MM-DD_HHMMSS.xlsx`) contains a table with:
    - Parameter values as columns on the left
    - Strategy and benchmark metrics as columns on the right
    - One row per parameter combination

  This design ensures clear comparison of strategy performance across different parameter settings and supports robust analysis for parameter optimization workflows.

   - Standard metrics (returns, volatility, Sharpe ratio, drawdown)
   - Advanced metrics (turnover, win rate)
   - Period-based returns (monthly, yearly)

## Usage

### Basic Usage

```python
from engine.backtest import BacktestEngine
from models.ema_allocation_model import ema_allocation_model

# Create backtest engine
engine = BacktestEngine(
    initial_capital=100000.0,
    commission_rate=0.001,
    slippage_rate=0.001
)

# Run backtest
results = engine.run_backtest(
    price_data=price_data,
    signal_generator=ema_allocation_model,
    rebalance_freq='weekly',
    execution_delay=1,
    st_lookback=10,
    mt_lookback=50,
    lt_lookback=150
)

# Access results
print(f"Final Value: ${results['final_value']:,.2f}")
print(f"Total Return: {results['total_return']:.2%}")
print(f"Sharpe Ratio: {results['performance']['sharpe']:.2f}")
```

### Creating a Signal Generator

Signal generators are functions that take price data and parameters, and return target allocations:

```python
def my_signal_generator(price_data, **params):
    """
    Generate allocation signals.
    
    Args:
        price_data (DataFrame): Historical price data
        **params: Additional parameters
        
    Returns:
        dict: Target allocations {symbol: weight}
    """
    # Calculate signals
    # ...
    
    # Return target allocations
    return allocations
```

## Parameter Optimization

The framework includes parameter optimization capabilities that follow the Custom Function Library guidelines:

```python
from config.local_parameter_optimization import define_parameter, optimize_parameters

# Define parameters
params = {
    'lookback': define_parameter(True, 20, 10, 50, 5),
    'threshold': define_parameter(True, 0.5, 0.1, 1.0, 0.1)
}

# Define metric function
def sharpe_ratio(results, data):
    return results['performance']['sharpe']

# Optimize parameters
best_params, best_metric, all_results = optimize_parameters(
    strategy_func=my_strategy,
    param_dict=params,
    data=price_data,
    metric_func=sharpe_ratio,
    optimization_method='grid'
)
```

## Testing

### Function Call Sequence
When running `run_ema_backtest_v2.bat`, the following occurs:
1. **Environment Activation**: Activates Python environment at `F:\AI_Library\my_quant_env`
2. **Script Execution**: Runs `run_backtest_v2.py` with EMA strategy parameters
3. **Parameter Processing**: `update_config_from_args()` processes CLI args and updates config
4. **Data Loading**: Loads/standardizes price and returns data
5. **Backtest Engine Initialization**: Sets up engine and models
6. **Backtest Execution**: Runs main loop, calls signal model, generates/executes orders, updates portfolio
7. **Results Calculation**: Computes returns, volatility, Sharpe, drawdown, monthly/yearly returns
8. **Reporting**: Displays and/or saves results

### Test Setup for Full Backtest Run
- Prepare small test dataset with known price patterns (at least 3 assets, 1 year daily)
- Component tests: signal generation, order generation, execution, portfolio update
- Integration tests: single asset, equal-weight, trend-following allocation
- Performance validation: compare to benchmarks, verify key metrics
- Edge cases: missing data, extreme markets, rebalance frequency, execution delay

## Implementation Notes

1. **Date Handling**
   - All dates are based on market days
   - Period-end calculations use the last market day in each period
   - No time elements are used in the core engine (dates only)

2. **Naming Conventions**
   - Local modules are prefixed with "local_" to avoid conflicts with the Custom Function Library
   - All functions and classes have descriptive names that indicate their purpose

3. **Error Handling**
   - The engine includes robust error handling and logging
   - Fallbacks are provided for missing data or failed calculations

4. **Performance Considerations**
   - The engine is optimized for backtesting performance
   - Large datasets can be handled efficiently
   - Detailed logging can be controlled via the logging level

## Future Enhancements

1. **Additional Slippage Models**
   - Volume-based slippage
   - Market impact models

2. **Advanced Order Types**
   - Limit orders
   - Stop orders
   - Trailing stops

3. **Risk Management**
   - Position sizing based on volatility
   - Drawdown controls
   - Sector/asset class exposure limits
