"""Validate AlphaList references and ComplexN metadata in CPS V4 settings.

Writes `test_named_lists_results.txt` into v4_reporting/test_output so the batch
script can absorb the summary.
"""
from __future__ import annotations

import sys
import traceback
from pathlib import Path
from typing import Any, Dict

# Ensure project root on path
PROJECT_ROOT = Path(__file__).resolve().parents[1]
if str(PROJECT_ROOT) not in sys.path:
    sys.path.insert(0, str(PROJECT_ROOT))

try:
    from v4.settings.settings_CPS_v4 import load_settings, PARAM_TYPES  # type: ignore
except Exception as exc:
    print(f"ERROR: cannot import settings loader: {exc}")
    traceback.print_exc()
    sys.exit(1)

OUTPUT_DIR = PROJECT_ROOT / "v4_reporting" / "test_output"
OUTPUT_DIR.mkdir(parents=True, exist_ok=True)
RESULTS_FILE = OUTPUT_DIR / "test_named_lists_results.txt"
ERROR_FILE = OUTPUT_DIR / "error_log.txt"


def validate() -> Dict[str, Any]:
    settings = load_settings()
    failures: list[str] = []

    named_lists = {k.lower(): v for k, v in settings.get("lists", {}).items()}

    for section, params in settings.items():
        if section.lower() == "lists":
            continue
        for key, value in params.items():
            # AlphaList dictionary structure detection
            if isinstance(value, dict) and "default" in value and "picklist" in value:
                pick_val = value["picklist"]
                # Case 1: loader resolved picklist to actual list/tuple → success
                if isinstance(pick_val, (list, tuple)):
                    pass  # OK
                else:
                    # Loader left string because list not found; validate against named_lists
                    pick_str = str(pick_val)
                    pick_lc = pick_str.lower()
                    if "," in pick_str:
                        # Composite picklist pointing to multiple named-lists
                        sub_names = [n.strip().lower() for n in pick_str.split(",")]
                        missing_sub = [n for n in sub_names if n not in named_lists]
                        if missing_sub:
                            failures.append(
                                f"AlphaList picklist '{pick_str}' (sub-lists {', '.join(missing_sub)}) referenced by {section}.{key} not found in [Lists] section"
                            )
                    else:
                        if pick_lc not in named_lists:
                            failures.append(
                                f"AlphaList picklist '{pick_str}' referenced by {section}.{key} not found in [Lists] section"
                            )
            # ComplexN validation
            if isinstance(value, dict) and any(k in value for k in ["optimize", "default_value", "min_value"]):
                required = {"optimize", "default_value", "min_value", "max_value", "increment"}
                missing = required - value.keys()
                if missing:
                    failures.append(
                        f"ComplexN parameter {section}.{key} missing keys: {', '.join(sorted(missing))}"
                    )
    return {"failures": failures}


def main() -> None:  # pragma: no cover
    result = validate()
    failures = result["failures"]

    with RESULTS_FILE.open("w", encoding="utf-8") as fh:
        fh.write("Named-Lists & ComplexN Validation Results\n")
        fh.write("========================================\n")
        if not failures:
            fh.write("All AlphaList references and ComplexN metadata are valid.\n")
            print("Named-list & ComplexN validation passed.")
        else:
            fh.write("Failures detected:\n")
            for item in failures:
                fh.write(f" - {item}\n")
            print(f"Named-list/ComplexN validation failed – {len(failures)} issue(s).")

    if failures:
        with ERROR_FILE.open("a", encoding="utf-8") as ef:
            ef.write("Named-list & ComplexN validation failures:\n")
            for item in failures:
                ef.write(f"{item}\n")
            ef.write("\n")
        sys.exit(1)


if __name__ == "__main__":
    main()
