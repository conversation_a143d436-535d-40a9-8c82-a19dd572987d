"""
v4/engine/order_executor.py
Module for order execution logic in the backtest engine.
Part of the backtest engine system (CPS v4 compliant).
"""

import pandas as pd
import logging
from datetime import datetime, timedelta
from collections import defaultdict

logger = logging.getLogger(__name__)

class OrderExecutor:
    """
    Handles order execution, scheduling, and tracking in the backtest engine.
    Separates execution logic from the main backtest engine.
    """
    
    def __init__(self, portfolio, commission_model, slippage_model):
        """
        Initialize the order executor.
        
        Args:
            portfolio: Portfolio object to execute orders against
            commission_model: Commission model to apply to orders
            slippage_model: Slippage model to apply to orders
        """
        self.portfolio = portfolio
        self.commission_model = commission_model
        self.slippage_model = slippage_model
        self.scheduled_orders = defaultdict(list)  # {date: [orders]}
        self.executed_orders = []
        self.trade_log = []
        
        logger.info("Initialized OrderExecutor")
    
    def schedule_order(self, order, execution_date=None):
        """
        Schedule an order for future execution.
        
        Args:
            order: Order object to schedule
            execution_date: Date to execute the order (if None, use order.execution_date)
        """
        if execution_date is None:
            execution_date = order.execution_date
            
        if execution_date is None:
            logger.warning("No execution date provided for order, executing immediately")
            return self.execute_order(order)
            
        # Standardize date format
        if isinstance(execution_date, pd.Timestamp):
            execution_date = execution_date.date()
            
        # Store order for future execution
        self.scheduled_orders[execution_date].append(order)
        logger.debug(f"Scheduled order for {order.symbol} on {execution_date}")
        
        return True
    
    def execute_scheduled_orders(self, current_date, prices):
        """
        Execute all orders scheduled for the current date.
        
        Args:
            current_date: Current date in the simulation
            prices: Current prices for all symbols
            
        Returns:
            list: Executed trades
        """
        # Standardize date format
        if isinstance(current_date, pd.Timestamp):
            current_date = current_date.date()
            
        # Check if we have orders for this date
        if current_date not in self.scheduled_orders:
            return []
            
        # Get orders for this date
        orders = self.scheduled_orders[current_date]
        if not orders:
            return []
            
        logger.debug(f"Executing {len(orders)} scheduled orders for {current_date}")
        
        # Execute orders
        trades = []
        for order in orders:
            trade = self.execute_order(order, prices)
            if trade:
                trades.append(trade)
                
        # Remove executed orders
        del self.scheduled_orders[current_date]
        
        return trades
    
    def execute_order(self, order, prices=None):
        """
        Execute a single order immediately.
        
        Args:
            order: Order object to execute
            prices: Current prices (optional, will use order.price if not provided)
            
        Returns:
            Trade: Executed trade or None if execution failed
        """
        # Get price for the symbol
        price = None
        if prices is not None:
            if isinstance(prices, dict):
                price = prices.get(order.symbol)
            elif isinstance(prices, pd.Series):
                price = prices.get(order.symbol)
            elif isinstance(prices, pd.DataFrame) and order.symbol in prices.columns:
                # Assuming the most recent price in the dataframe
                price = prices[order.symbol].iloc[-1]
                
        # Fall back to order price if not found
        if price is None:
            price = order.price
            
        if price is None or price <= 0:
            logger.warning(f"Invalid price for {order.symbol}: {price}")
            return None
            
        # Apply slippage
        adjusted_price = self.slippage_model.apply(price, order.action)
        
        # Calculate commission
        commission = self.commission_model.calculate(order.quantity, adjusted_price)
        
        # Calculate total cost/proceeds
        value = order.quantity * adjusted_price
        total_cost = value + commission if order.action == 'BUY' else value - commission
        
        # Check if we have enough cash for a buy order
        if order.action == 'BUY' and total_cost > self.portfolio.get_cash():
            logger.warning(f"Insufficient cash to execute buy order for {order.symbol}: "
                          f"Need {total_cost:.2f}, have {self.portfolio.get_cash():.2f}")
            return None
            
        # Execute the order in the portfolio
        if order.action == 'BUY':
            self.portfolio.add_position(order.symbol, order.quantity, adjusted_price)
            self.portfolio.remove_cash(total_cost)
        else:  # SELL
            self.portfolio.remove_position(order.symbol, order.quantity, adjusted_price)
            self.portfolio.add_cash(total_cost)
            
        # Create trade record
        trade = {
            'timestamp': order.execution_date or datetime.now(),
            'symbol': order.symbol,
            'action': order.action,
            'quantity': order.quantity,
            'price': adjusted_price,
            'value': value,
            'commission': commission,
            'total': total_cost
        }
        
        # Add to trade log
        self.trade_log.append(trade)
        
        logger.debug(f"Executed {order.action} order for {order.quantity} {order.symbol} "
                    f"at {adjusted_price:.2f} (commission: {commission:.2f})")
        
        return trade
    
    def get_trade_log(self):
        """
        Get the trade log.
        
        Returns:
            list: All executed trades
        """
        return self.trade_log
    
    def get_executed_orders(self):
        """
        Get all executed orders.
        
        Returns:
            list: All executed orders
        """
        return self.executed_orders
    
    def calculate_rebalance_orders(self, target_positions, current_prices):
        """
        Calculate orders needed to rebalance the portfolio to target positions.
        
        Args:
            target_positions (dict): Target position values {symbol: target_value}
            current_prices (dict): Current prices {symbol: price}
            
        Returns:
            list: Orders needed to achieve target positions
        """
        orders = []
        
        # Get current positions
        current_positions = self.portfolio.get_positions()
        
        # Calculate total target value
        total_target_value = sum(target_positions.values())
        
        # Check if we have enough cash
        if total_target_value > self.portfolio.get_total_value():
            logger.warning(f"Target positions total value ({total_target_value:.2f}) "
                          f"exceeds portfolio value ({self.portfolio.get_total_value():.2f})")
            # Scale down target positions
            scale_factor = self.portfolio.get_total_value() / total_target_value
            target_positions = {symbol: value * scale_factor for symbol, value in target_positions.items()}
            
        # Process each target position
        for symbol, target_value in target_positions.items():
            # Skip if target value is zero or price is missing
            if target_value <= 0 or symbol not in current_prices:
                continue
                
            price = current_prices[symbol]
            if price <= 0:
                logger.warning(f"Invalid price for {symbol}: {price}")
                continue
                
            # Calculate target quantity
            target_quantity = target_value / price
            
            # Get current quantity
            current_quantity = 0
            if symbol in current_positions:
                current_quantity = current_positions[symbol]['quantity']
                
            # Calculate quantity difference
            quantity_diff = target_quantity - current_quantity
            
            # Skip small trades
            if abs(quantity_diff) < 0.001:
                continue
                
            # Create order
            if quantity_diff > 0:
                # Buy order
                order = {
                    'symbol': symbol,
                    'action': 'BUY',
                    'quantity': quantity_diff,
                    'price': price
                }
            else:
                # Sell order
                order = {
                    'symbol': symbol,
                    'action': 'SELL',
                    'quantity': abs(quantity_diff),
                    'price': price
                }
                
            orders.append(order)
            
        return orders
