@echo off
SETLOCAL

echo === MEMORY TOOL SETUP - SIMPLE CHECK ===
echo.

:: Set Python executable path
SET PYTHON_EXE=F:\AI_Library\my_quant_env\Scripts\python.exe

:: Verify Python exists
if not exist "%PYTHON_EXE%" (
    echo [X] ERROR: Python not found at %PYTHON_EXE%
    pause
    exit /b 1
)

echo [✓] Python found at: %PYTHON_EXE%
echo.

echo Python Version:
"%PYTHON_EXE%" --version
echo.

echo Checking Python path:
"%PYTHON_EXE%" -c "import sys; print('
'.join(sys.path))"
echo.

echo Checking package imports:
echo.

echo 1. Checking pyan3:
"%PYTHON_EXE%" -c "try: import pyan3; print('  [✓] pyan3 found at:', pyan3.__file__)
except Exception as e: print('  [X] pyan3 import failed:', str(e))"
echo.

echo 2. Checking pylint:
"%PYTHON_EXE%" -c "try: import pylint; print('  [✓] pylint found at:', pylint.__file__)
except Exception as e: print('  [X] pylint import failed:', str(e))"
echo.

echo 3. Checking graphviz:
"%PYTHON_EXE%" -c "try: import graphviz; print('  [✓] graphviz found at:', graphviz.__file__)
except Exception as e: print('  [X] graphviz import failed:', str(e))"
echo.

pause
