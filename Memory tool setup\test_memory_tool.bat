@echo off
SETLOCAL

echo Testing Memory Tool...
echo ======================
echo.

:: Set project root directory
set "PROJECT_ROOT=%~dp0"
cd /d "%PROJECT_ROOT%"

:: Check Python environment
python --version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo Python is not in PATH. Please ensure Python is installed and in your system PATH.
    pause
    exit /b 1
)

:: Activate virtual environment
call F:\AI_Library\my_quant_env\Scripts\activate.bat
if %ERRORLEVEL% NEQ 0 (
    echo Failed to activate virtual environment
    pause
    exit /b 1
)

:: Create output directory if it doesn't exist
if not exist "memory-bank\code_analysis" mkdir "memory-bank\code_analysis"

echo [1/3] Running basic analysis...
python -c "import sys; sys.path.insert(0, r's:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template'); from memory_tool.module_scanner import analyze_project; analyze_project('.', 'memory-bank/code_analysis/structure.json')"
if %ERRORLEVEL% NEQ 0 (
    echo Basic analysis failed
    pause
    exit /b 1
)
echo Basic analysis completed

echo [2/3] Generating call graph...
python -c "import sys; sys.path.insert(0, r's:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template'); from memory_tool.graphviz_analyzer import generate_call_graph; generate_call_graph('memory_tool', 'memory-bank/code_analysis/call_graph.png')"
if %ERRORLEVEL% NEQ 0 (
    echo Call graph generation failed
    pause
    exit /b 1
)
echo Call graph generated

echo [3/3] Verifying output files...
if exist "memory-bank\code_analysis\structure.json" (
    echo Found structure.json
) else (
    echo structure.json not found
    set ERROR=1
)

if exist "memory-bank\code_analysis\call_graph.png" (
    echo Found call_graph.png
) else (
    echo call_graph.png not found
    set ERROR=1
)

if defined ERROR (
    echo.
    echo Some output files are missing
    pause
    exit /b 1
)

echo.
echo Memory tool test completed successfully!
echo Output files are in memory-bank\code_analysis\
pause
