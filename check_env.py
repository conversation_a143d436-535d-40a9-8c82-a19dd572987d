import sys
import importlib
from pathlib import Path

def check_package(pkg_name):
    try:
        module = importlib.import_module(pkg_name)
        print(f"[✓] {pkg_name} found at: {module.__file__}")
        return True
    except ImportError as e:
        print(f"[X] {pkg_name} import failed: {str(e)}")
        return False

print("=== PYTHON ENVIRONMENT CHECK ===")
print(f"Python executable: {sys.executable}")
print(f"Python version: {sys.version}\n")

print("Python path:")
for p in sys.path:
    print(f"  {p}")
print()

print("Checking packages:")
packages = ["pyan3", "pylint", "graphviz"]
all_ok = all(check_package(pkg) for pkg in packages)

if all_ok:
    print("\nAll packages imported successfully!")
else:
    print("\nSome packages failed to import. Check the output above for details.")

input("\nPress Enter to exit...")
