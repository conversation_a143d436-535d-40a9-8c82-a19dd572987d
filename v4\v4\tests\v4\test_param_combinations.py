#!/usr/bin/env python
# -*- coding: utf-8 -*-
# test_param_combinations.py
"""
Simple parameter combinations for optimization testing.
This file defines a permanent array of parameter combinations to test the optimization loop.
"""

# Simple parameter combination array - no fallbacks, no complexity
OPTIMIZATION_TEST_COMBINATIONS = [
    # Combination 1: Base case (should match pre-optimization behavior)
    {
        'st_lookback': 10,
        'mt_lookback': 50, 
        'lt_lookback': 150,
        'system_top_n': 2,
        'name': 'base_case'
    },
    
    # Combination 2: Shorter lookbacks
    {
        'st_lookback': 5,
        'mt_lookback': 25,
        'lt_lookback': 75,
        'system_top_n': 2,
        'name': 'short_lookbacks'
    },
    
    # Combination 3: Longer lookbacks  
    {
        'st_lookback': 20,
        'mt_lookback': 100,
        'lt_lookback': 200,
        'system_top_n': 2,
        'name': 'long_lookbacks'
    }
]

def get_param_combination(index):
    """Get a specific parameter combination by index."""
    if 0 <= index < len(OPTIMIZATION_TEST_COMBINATIONS):
        return OPTIMIZATION_TEST_COMBINATIONS[index]
    else:
        raise IndexError(f"Index {index} out of range. Available combinations: 0-{len(OPTIMIZATION_TEST_COMBINATIONS)-1}")

def get_all_combinations():
    """Get all parameter combinations."""
    return OPTIMIZATION_TEST_COMBINATIONS.copy()

if __name__ == '__main__':
    print("Available parameter combinations:")
    for i, combo in enumerate(OPTIMIZATION_TEST_COMBINATIONS):
        print(f"{i}: {combo['name']} - {combo}")
