#!/usr/bin/env python
# -*- coding: utf-8 -*-
# parameter_discovery_CPS_v4.py
"""
Parameter Discovery Tool for CPS v4

This script scans the existing V3 parameter registry and extracts all parameters,
their default values, types, and usage patterns. It then generates a comprehensive
INI file with all discovered parameters organized into logical sections.

Usage:
    python parameter_discovery_CPS_v4.py --output default_settings_CPS_v4.ini

Author: AI Assistant
Date: 2025-06-06
"""

import os
import sys
import re
import argparse
import logging
import configparser
from pathlib import Path
from typing import Dict, Any, List, Tuple, Set, Optional

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('parameter_discovery')

# Define paths relative to the script location
SCRIPT_DIR = Path(os.path.dirname(os.path.abspath(__file__)))
PROJECT_ROOT = SCRIPT_DIR.parent.parent
V3_ENGINE_DIR = PROJECT_ROOT / 'v3_engine'
V3_REPORTING_DIR = PROJECT_ROOT / 'v3_reporting'
OUTPUT_DIR = SCRIPT_DIR.parent

# List of active production files for parameter discovery
FILES_TO_SCAN = [
    PROJECT_ROOT / 'config' / 'config_v3.py',
    PROJECT_ROOT / 'v3_engine' / 'parameter_registry.py',
    PROJECT_ROOT / 'v3_engine' / 'parameters.py',
    PROJECT_ROOT / 'v3_engine' / 'strategy_parameter_set.py',
    PROJECT_ROOT / 'v3_engine' / 'parameter_optimizer.py',
    PROJECT_ROOT / 'v3_engine' / 'ema_v3_adapter.py',
    PROJECT_ROOT / 'v3_strategies' / 'ema_strategy.py',
    PROJECT_ROOT / 'v3_strategies' / 'strategy_base.py',
    PROJECT_ROOT / 'v3_reporting' / 'v3_performance_report.py',
    PROJECT_ROOT / 'v3_reporting' / 'v3_allocation_report.py',
    PROJECT_ROOT / 'v3_reporting' / 'reporting_parameters.py',
    PROJECT_ROOT / 'v3_reporting' / 'visualization_parameters.py',
    PROJECT_ROOT / 'v3_reporting' / 'parameter_registry_integration.py',
    PROJECT_ROOT / 'app' / 'gui' / 'v3_register_parameters.py'
]

# Parameter type mapping
TYPE_MAPPING = {
    'str': 'string',
    'int': 'integer',
    'float': 'float',
    'bool': 'boolean',
    'list': 'list',
    'dict': 'dict',
    'None': 'string',
}

class ParameterDiscovery:
    """Parameter discovery and extraction tool for CPS v4 migration."""
    
    def __init__(self):
        """Initialize the parameter discovery tool."""
        self.parameters = {}
        # Patterns for registry-based parameters
        self.registry_patterns = [
            r'registry\.register_parameter\([\'"]([^\'"]+)[\'"]',
            r'registry\.get_parameter\([\'"]([^\'"]+)[\'"]',
            r'registry\.set_parameter\([\'"]([^\'"]+)[\'"]',
        ]
        # Pattern for configuration define_parameter in config_v3.py
        self.define_pattern = (
            r"'(?P<name>[^']+)'\s*:\s*define_parameter\("
            r"\s*(?P<opt>True|False)\s*,\s*(?P<default>[^,]+)\s*,"
            r"\s*(?P<min>[^,]+)\s*,\s*(?P<max>[^,]+)\s*,\s*(?P<step>[^)]+)\)"
        )
        self.section_mappings = {
            'report': 'Report',
            'chart': 'Visualization',
            'performance': 'Performance',
            'allocation': 'Allocation',
            'output': 'Output',
            'system': 'System',
            'backtest': 'BacktestData',
        }
        
    def scan_file(self, file_path: Path) -> None:
        """
        Scan a single file for parameter registry usage.
        
        Args:
            file_path: Path to the file to scan
        """
        logger.info(f"Scanning file: {file_path}")
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # Look for parameter registrations
            for pattern in self.registry_patterns:
                for match in re.finditer(pattern, content):
                    param_name = match.group(1)
                    default_value = match.group(2).strip() if len(match.groups())>=2 and match.group(2) else None
                    
                    # Store the parameter
                    if param_name not in self.parameters:
                        self.parameters[param_name] = {
                            'default': default_value,
                            'type': self._infer_type(default_value),
                            'files': [str(file_path)],
                            'section': self._infer_section(param_name)
                        }
                    else:
                        if str(file_path) not in self.parameters[param_name]['files']:
                            self.parameters[param_name]['files'].append(str(file_path))
            
            # Look for optimization parameters in config_v3.py
            if file_path.name == 'config_v3.py':
                for match in re.finditer(self.define_pattern, content):
                    name = match.group('name')
                    opt_flag = match.group('opt') == 'True'
                    default = match.group('default').strip()
                    min_val = match.group('min').strip()
                    max_val = match.group('max').strip()
                    step = match.group('step').strip()
                    # Store or update
                    self.parameters[name] = {
                        'default': default,
                        'type': self._infer_type(default),
                        'optimization': {'optimize': opt_flag, 'min': min_val, 'max': max_val, 'step': step},
                        'files': [str(file_path)],
                        'section': self._infer_section(name)
                    }
        except Exception as e:
            logger.error(f"Error scanning file {file_path}: {str(e)}")
    
    def scan_directory(self, directory: Path) -> None:
        """
        Recursively scan a directory for Python files.
        
        Args:
            directory: Directory path to scan
        """
        logger.info(f"Scanning directory: {directory}")
        for item in directory.iterdir():
            if item.is_file() and item.suffix == '.py':
                self.scan_file(item)
            elif item.is_dir() and not item.name.startswith('.'):
                self.scan_directory(item)
    
    def _infer_type(self, value: Optional[str]) -> str:
        """
        Infer the type of a parameter from its default value.
        
        Args:
            value: String representation of the default value
            
        Returns:
            String representing the parameter type
        """
        if value is None:
            return 'string'
            
        value = value.strip()
        
        # Check for boolean values
        if value.lower() in ('true', 'false'):
            return 'boolean'
            
        # Check for numeric values
        try:
            if '.' in value:
                float(value)
                return 'float'
            else:
                int(value)
                return 'integer'
        except ValueError:
            pass
            
        # Check for lists
        if value.startswith('[') and value.endswith(']'):
            return 'list'
            
        # Check for dictionaries
        if value.startswith('{') and value.endswith('}'):
            return 'dict'
            
        # Check for None
        if value == 'None':
            return 'string'
            
        # Default to string
        return 'string'
    
    def _infer_section(self, param_name: str) -> str:
        """
        Infer the INI section for a parameter based on its name.
        
        Args:
            param_name: Name of the parameter
            
        Returns:
            Section name for the parameter
        """
        for prefix, section in self.section_mappings.items():
            if param_name.startswith(prefix):
                return section
                
        # Default to System section
        return 'System'
    
    def generate_ini(self, output_path: Path) -> None:
        """
        Generate an INI file with all discovered parameters.
        
        Args:
            output_path: Path to write the INI file
        """
        logger.info(f"Generating INI file: {output_path}")
        
        # Create a ConfigParser instance
        config = configparser.ConfigParser()
        
        # Group parameters by section
        sections = {}
        for param_name, param_info in self.parameters.items():
            section = param_info['section']
            if section not in sections:
                sections[section] = {}
                
            # Convert the parameter name to a more INI-friendly format
            ini_name = param_name.split('.')[-1]  # Remove any namespace prefixes
            
            # Format the default value based on type
            default_value = param_info['default']
            if default_value is None:
                default_value = ''
            elif param_info['type'] == 'boolean':
                default_value = default_value.lower()
            elif param_info['type'] == 'string' and default_value.startswith('"') and default_value.endswith('"'):
                default_value = default_value[1:-1]  # Remove quotes
                
            sections[section][ini_name] = default_value
        
        # Add sections and parameters to the config
        for section, params in sections.items():
            config[section] = params
            
        # Write the INI file
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write("; default_settings_CPS_v4.ini\n")
            f.write("; Auto-generated by parameter_discovery_CPS_v4.py\n")
            f.write("; Date: 2025-06-06\n\n")
            config.write(f)
            
        logger.info(f"INI file generated successfully: {output_path}")
        
    def generate_mapping_doc(self, output_path: Path) -> None:
        """
        Generate a markdown document mapping legacy parameters to CPS v4 parameters.
        
        Args:
            output_path: Path to write the markdown file
        """
        logger.info(f"Generating parameter mapping document: {output_path}")
        
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write("# Parameter Migration Mapping\n\n")
            f.write("This document maps legacy V3 parameters to the new CPS v4 parameters.\n\n")
            
            f.write("## Parameter Mapping Table\n\n")
            f.write("| Legacy Parameter | CPS v4 Parameter | Type | Default | Files |\n")
            f.write("|-----------------|------------------|------|---------|-------|\n")
            
            for param_name, param_info in sorted(self.parameters.items()):
                section = param_info['section']
                ini_name = param_name.split('.')[-1]
                cps_v4_name = f"{section.lower()}_{ini_name}"
                
                # Format the default value for display
                default_value = param_info['default']
                if default_value is None:
                    default_value = 'None'
                    
                # Format the files list
                files = ", ".join([os.path.basename(f) for f in param_info['files']])
                
                f.write(f"| {param_name} | {cps_v4_name} | {param_info['type']} | {default_value} | {files} |\n")
                
        logger.info(f"Parameter mapping document generated successfully: {output_path}")
        
    def run(self, output_ini: Path, output_mapping: Path) -> None:
        """
        Run the parameter discovery process.
        
        Args:
            output_ini: Path to write the INI file
            output_mapping: Path to write the mapping document
        """
        logger.info("Starting parameter discovery")
        
        # Scan only the active production V3 files
        for file_path in FILES_TO_SCAN:
            self.scan_file(file_path)
        
        # Generate the INI file
        self.generate_ini(output_ini)
        
        # Generate the mapping document
        self.generate_mapping_doc(output_mapping)
        
        logger.info(f"Parameter discovery complete. Found {len(self.parameters)} parameters.")

def main():
    """Main entry point for the script."""
    parser = argparse.ArgumentParser(description='Discover parameters from V3 registry')
    parser.add_argument('--output', default=str(OUTPUT_DIR / 'default_settings_CPS_v4.ini'),
                        help='Output path for the INI file')
    parser.add_argument('--mapping', default=str(OUTPUT_DIR / 'parameter_mapping_CPS_v4.md'),
                        help='Output path for the parameter mapping document')
    parser.add_argument('--verbose', '-v', action='store_true',
                        help='Enable verbose logging')
    
    args = parser.parse_args()
    
    # Set log level
    if args.verbose:
        logger.setLevel(logging.DEBUG)
    
    # Run the parameter discovery
    discovery = ParameterDiscovery()
    discovery.run(Path(args.output), Path(args.mapping))

if __name__ == '__main__':
    main()
