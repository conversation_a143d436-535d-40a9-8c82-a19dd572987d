# Financial Asset Allocation Backtesting Framework

A flexible framework for backtesting financial asset allocation strategies. This framework leverages the existing Custom Function Library to provide comprehensive backtesting capabilities.

## Features

- **Modular Design**: Easily swap allocation strategies and backtesting parameters
- **Multiple Allocation Strategies**: Equal weight, momentum, minimum variance, risk parity, and hierarchical risk parity
- **Parameter Optimization**: Grid search, random search, and Bayesian optimization for strategy parameters
- **Performance Reporting**: Comprehensive performance metrics and visualizations
- **Trade Logging**: Detailed trade execution logs
- **Visualization**: Performance charts including cumulative returns, drawdowns, and portfolio weights
- **EMA Calculation Tracking**: Optional detailed tracking of Exponential Moving Average (EMA) calculations, configurable via the GUI. See below for details.

## Directory Structure

```text
Backtest_FinAsset_Alloc_Template/
├── engine/                           # Core backtest engine modules
│   ├── backtest.py                  # Main backtest engine (v2)
│   ├── portfolio.py                 # Portfolio state tracking
│   ├── orders.py                    # Order and trade classes
│   ├── execution.py                 # Execution engine
│   ├── benchmark.py                 # Benchmark calculation (equal weight)
│   └── allocation.py                # Position comparison & order generation
├── models/                           # Strategy registry and models
│   └── strategy_registry.py         # Strategy registry for multi-strategy backtesting
├── data/                             # Data access and storage
│   └── data_loader.py               # Data loading/saving logic
├── config/                           # Configuration files
│   ├── config_v2.py                 # Active v2 configuration
│   ├── local_parameter_optimization.py # Parameter optimization tools
│   └── paths.py                     # Path configuration
├── reporting/                        # Reporting utilities
│   └── performance_reporting.py     # Performance metrics & Excel output
├── run_backtest_v2_with_metrics.py   # Main v2 runner script
├── run_backtest.bat                  # Batch file for v2 engine
├── output/                           # Output directory for results
└── docs/                             # Documentation
    └── README.md                    # Project documentation
```

## Documentation and Guides

- [Date Handling Guide](../../Custom Function Library/DATE_HANDLING_GUIDE.md): Best practices for date handling and writing numeric metrics to XLSX (see 'Best Practices writing data to XLSX files').

---

## EMA Calculation Tracking (GUI & Batch File Usage)

### Overview

The backtesting GUI now includes an option to enable or disable **Detailed EMA Tracking**. This allows users to control the level of detail in EMA calculation outputs for debugging and analysis.

- **Default (OFF):**
  - Only a standard EMA calculation output file is generated for each run, using default parameters.
  - This keeps output files manageable for routine use.
- **Enabled (ON):**
  - Additional detailed debug files are generated, tracking all intermediate EMA calculations step-by-step.
  - Useful for advanced debugging or model validation.

### How to Use

- **Launching the GUI:**
  - Use the batch file `run_gui_simple.bat` to activate the environment and launch the GUI:
    ```bat
    run_gui_simple.bat
    ```
- **Setting EMA Tracking:**
  - In the GUI, check the box labeled **Detailed EMA Tracking** to enable detailed debug output.
  - Leave it unchecked for standard output only (default behavior).

### Output Files

- **Standard Output (always generated):**
  - Example: `output/ema_performance_tables_2025-04-27.xlsx`
  - Contains summary EMA metrics and results using default parameters.
  - Note: only the EMA performance report (performance_reporting.py) loops through parameter combinations to emit multiple files. All other reports use the single current base/default parameter settings.
- **Detailed Debug Output (only if enabled):**
  - Example: `output/ema_debug_details_2025-04-27.xlsx`
  - Contains full time series of all intermediate EMA calculations and ratios for each asset and period.

All output files are saved in the `output/` directory. Filenames include the date of the run for easy tracking.

### Batch File/Command Line Usage

- The batch file will always generate the standard EMA output file.
- To enable detailed EMA tracking from the GUI, use the checkbox; there is no command-line flag for this feature at present.

---

## EMA Allocation Model Rules

The EMA allocation model selects assets and allocates weights based on the relative positions of short-term, medium-term, and long-term EMAs. The model computes the following for each asset:

- **STMTEMAX:** Ratio of short-term EMA to medium-term EMA
- **MTLTEMAX:** Ratio of medium-term EMA to long-term EMA
- **EMAXAvg:** Average of the above two ratios

These time series are used to rank assets and determine portfolio allocations at each rebalance period. For more details, see `models/ema_allocation_model.py`.

---

## Coding Guidelines

---

## Known Issues

### Strategy Function Argument Warning

**Summary:**
A warning may appear during backtest runs: `strategy_func` takes 1 positional argument but 2 were given. This is caused by a mismatch between the expected signature `(hist_data, hist_returns, **params)` and some legacy or user-defined strategy functions that only take one argument.

**Impact:**

- The warning does not interrupt backtest execution, but may indicate that some strategies are not fully compatible with the backtest engine.

**Recommendation:**

- Update all strategy functions to accept at least two positional arguments and `**params`.
- Consider adding a wrapper for legacy strategies if needed in the future.

**Status:**

- As of 2025-04-23, this warning is suppressed. See `handoff.md` for more details.
1. **File Size Management**:
   
   - Target file size <500 lines whenever possible
   - Split larger files into logical modules
   - Each file should focus on a single responsibility

2. **Library Usage**:
   
   - Prefer using functions from the Custom Function Library
   - Only create local versions when absolutely necessary
   - Document any local implementations with rationale

3. **Path Management**:
   
   - All paths centralized in `config/paths.py`
   - No hardcoded paths in individual modules
   - Relative paths preferred where possible

## Path Management

All system paths are centrally configured in `config/paths.py`. This includes:

- Project directory structure
- External library paths
- Output directories

Key principles:

- No hardcoded paths in individual modules
- All paths are relative to project root where possible
- External paths are configurable via paths.py

To modify paths:

- Edit `config/paths.py`
- All other files will automatically use the updated paths

## Usage

### Basic Usage

To run a backtest with default parameters, use the provided batch file (recommended, ensures correct environment):

```bat
run_backtest.bat
```

This batch file will:

- Activate the required Python environment (my_quant_env)
- Set the correct PYTHONPATH for the Custom Function Library
- Run the main v2 runner script: `run_backtest_v2_with_metrics.py`

**(Legacy/alternate)**: Directly running `python run_backtest.py` is not recommended and is only for legacy compatibility.

### Custom Parameters

To specify custom parameters, you can pass arguments to the batch file, which will forward them to `run_backtest_v2_with_metrics.py`. For example:

```bat
run_backtest.bat --strategy equal_weight --rebalance W-FRI --start-date 2015-01-01 --end-date 2023-12-31 --tickers SPY,QQQ,IWM,TLT
```

**(Legacy/alternate)**: Directly running `python run_backtest.py ...` is supported for legacy compatibility only.

### Parameter Optimization

To run parameter optimization:

```bat
run_backtest.bat --optimize
```

**(Legacy/alternate)**: Directly running `python run_backtest.py --optimize` is supported for legacy compatibility only.

## Data Handling and Storage Modes

The handling of historical price data is managed by `data/data_loader.py`, which operates according to the `data_storage_mode` parameter set in your configuration (see `config/config_v2.py`).

**Available modes:**

- **Save**: Downloads data using the existing logic (as in `get_adjusted_close_data`), saves it to an XLSX file in the `data/` directory, and returns a DataFrame for use in the backtest.
- **Read**: Loads data from an existing XLSX file in the `data/` directory. Raises an error if the file is missing (no download attempt).
- **New**: Downloads fresh data (like "Save"), but does NOT save it to disk—returns the DataFrame only.

**To configure:**
Set the `data_storage_mode` parameter in your config, e.g.:

```python
config_v2['data_params']['data_storage_mode'] = 'Save'   # or 'Read' or 'New'
```

- All downloaded or loaded data is handled via `data/data_loader.py`.
- Output files are stored in the `data/` directory when in "Save" mode.
- This system ensures reproducibility and flexibility for both offline and online workflows.

## Configuration

The active configuration file for v2 is `config/config_v2.py`, which defines all parameters for data, backtest, allocation, optimization, and reporting. Use the `config_v2` object in your scripts.

- **Data Parameters**: Tickers, date range, price field
- **Backtest Parameters**: Rebalance frequency, execution delay, transaction costs
- **Allocation Parameters**: Strategy, weight constraints, lookback period
- **Optimization Parameters**: Method, parameter grid, evaluation metric
- **Reporting Parameters**: Output formats, performance metrics

## Allocation Strategies

The framework includes the following allocation strategies:

- **Equal Weight**: Simple equal allocation to all assets
- **Momentum**: Allocates based on recent price momentum
- **Minimum Variance**: Minimizes portfolio volatility
- **Risk Parity**: Equalizes risk contribution from each asset
- **Hierarchical Risk Parity**: Tree-based portfolio optimization
- **Custom**: Template for custom allocation strategies

### EMA-based Allocation Strategy

Uses Short, Medium, and Long-term EMAs to determine asset allocation.

**Ranking Rule:**

- Rank all assets by EMAXAvg = (ShortEMA/MedEMA + MedEMA/LongEMA) / 2

**Signal Allocation Rule:**

- Select top `Y` assets (via `top_n` in config)
- Apply weight percentages from `allocation_rules[top_n]` in `config/config_v2.py`
  - **1 asset**: [100%]
  - **2 assets**: [60%, 40%]
  - **3 assets**: [45%, 35%, 20%]
  - **4 assets**: [35%, 30%, 20%, 15%]
  - **5 assets**: [30%, 25%, 20%, 15%, 10%]

## Allocation Rules by top_n

Defines weight percentages by rank position based on the number of selected assets (`top_n`):

- **1 asset**: 100%
- **2 assets**: 60% / 40%
- **3 assets**: 45% / 35% / 20%
- **4 assets**: 35% / 30% / 20% / 15%
- **5 assets**: 30% / 25% / 20% / 15% / 10%

## Run backtest (main v2 flow)

```python
from config.config_v2 import config_v2
from run_backtest_v2_with_metrics import run_backtest_with_metrics

# Customize configuration
config_v2['data_params']['tickers'] = ['SPY', 'QQQ', 'TLT', 'GLD']
config_v2['allocation_params']['strategy'] = 'risk_parity'
config_v2['backtest_params']['rebalance_freq'] = 'M'  # Monthly rebalancing

# Run backtest (main v2 flow)
results = run_backtest_with_metrics(config_v2)

# Access results
portfolio_returns = results['portfolio_returns']
performance = results['performance']
print(f"Sharpe Ratio: {performance['sharpe_ratio']:.2f}")

# LEGACY: For legacy/alternate script usage, see earlier documentation sections.

## Trade Log Standards

A standard trade log generated at the end of each backtest follows these conventions:

- Column order: `execution_date`, `trade_num`, `symbol`, `Buy/Sell`, `quantity`, `execution_price`, `Com/Slippage`, `Amount`, `Profit/Loss`
- `execution_date`: formatted as `YYYY-MM-DD` (date only, no timestamp)
- `trade_num`: sequential ID (blank for final liquidation entries)
- `Buy/Sell`: `BUY` if quantity > 0, `SELL` if quantity < 0
- `Com/Slippage`: commission or slippage cost applied per trade
- `Amount`: inverted cash flow; buys are negative, sells are positive
- `Profit/Loss`: realized P&L for each trade
- Final liquidation trades are appended at the end of the log for any remaining positions, using a date-only `execution_date`

## Dependencies

This framework depends on the Custom Function Library whose path is configured in `config/paths.py`

---

## Module Mapping Matrix (Active v2 Flow)

| Path/Module                              | Role                                                               | Key Classes/Functions Used                                                    |
| ---------------------------------------- | ------------------------------------------------------------------ | ----------------------------------------------------------------------------- |
| `engine/backtest.py`                     | Main backtest engine (v2)                                          | `BacktestEngine`, `run_backtest()`, `_calculate_results()`                    |
| `engine/portfolio.py`                    | Portfolio state and history                                        | `Portfolio`, `get_total_value()`, `get_returns_series()`                      |
| `engine/orders.py`                       | Order/trade objects and trade log                                  | `Order`, `Trade`, `TradeLog`                                                  |
| `engine/execution.py`                    | Order execution, slippage, commissions                             | `ExecutionEngine`, `execute_orders()`                                         |
| `engine/allocation.py`                   | Rebalance/order generation                                         | `compare_positions()`, `generate_orders()`, `calculate_rebalance_orders()`    |
| `engine/benchmark.py`                    | Benchmark calculation (equal weight)                               | `calculate_equal_weight_benchmark()`                                          |
| `data/data_loader.py`                    | Data loading and saving logic (handles all data access per config) | `load_data()`, `save_data()` (and related interfaces)                         |
| `reporting/performance_reporting.py`     | Performance metrics and Excel output                               | `generate_performance_report_local()`, `create_backtest_report()`             |
| `config/config_v2.py`                    | Active v2 configuration                                            | `config_v2`                                                                   |
| `config/local_parameter_optimization.py` | Parameter optimization tools                                       | `define_parameter()`, `get_parameter_range()`, `get_parameter_combinations()` |
| `run_backtest_v2_with_metrics.py`        | Main v2 runner script                                              | (entry point, parameter loop, reporting)                                      |
| `run_backtest.bat`                       | Batch file for v2 engine (current)                                 | (environment setup, runs main script)                                         |

<!-- LEGACY: `run_ema_backtest_v2_with_metrics.bat` | (legacy batch file, not used in current flow) -->

---

## Architecture & Data Flow (v2)

```mermaid
graph TD
    A[Batch File / Script] --> B[run_backtest_v2_with_metrics.py]
    B --> C[config_v2]
    B --> D[local_parameter_optimization.py]
    B --> E[BacktestEngine]
    E --> F[allocation.py]
    E --> K[benchmark.py]
    F --> G[orders.py]
    G --> H[execution.py]
    H --> I[portfolio.py]
    I --> J[performance_reporting.py]
    K --> J
```

### Benchmark Flow

- The benchmark used in all reports is the **equal weight portfolio**, calculated by `engine/benchmark.py:calculate_equal_weight_benchmark()`.
- The backtest engine computes benchmark returns in parallel with the main strategy and passes them as `benchmark_returns` to the reporting module.
- Performance tables and Excel output always include the benchmark for comparison.
- The benchmark rebalancing frequency is configurable (e.g., yearly, monthly) via the engine configuration.

---

## Potential Speed Improvements

Based on profiling and backtest run-time bottlenecks:

- **EMA pre-computation**: Precompute all EMAs once (in `models/ema_allocation_model.py`) and index into the resulting DataFrames to avoid repeated `.ewm()` calls (~20–35% speed-up).
- **Vectorize loops**: Replace per-ticker Python loops in `engine/allocation.py` with pandas/numpy operations (~15–25% speed-up).
- **Reduce object churn**: Reuse DataFrames/arrays in `engine/orders.py` and `engine/execution.py` rather than recreating Python objects each day (~10–15% speed-up).
- **I/O optimizations**: Use Parquet or Feather instead of XLSX for large datasets (5–10× faster I/O).

## Future Directions

- **Multiple strategy framework**: Generalize the allocation engine to loop through multiple strategy functions (EMA, Risk Parity, Minimum Variance, etc.) for comparative backtesting.
- **Plugin registry**: Implement a plugin-style registry of allocation models so new strategies can be added without core code modifications.
- **Automated comparison**: Enhance Excel reporting to automatically compile and contrast performance metrics across different allocation strategies.

## Adding New Allocation Strategies

To add a new allocation strategy to the v2 framework:

1. Implement your strategy function with the following signature:
   
   ```python
   def my_strategy(price_data, returns_data=None, **params):
       """Docstring describing inputs, outputs, and parameters"""
       # Your allocation logic here
       return allocations_series
   ```

2. Decorate the function in `models/strategy_registry.py`:
   
   ```python
   from models.strategy_registry import register_strategy
   
   @register_strategy('my_strategy')
   def my_strategy(...):
       ...
   ```

3. Place the function file in the `models/` directory (or your own module) and ensure imports are correct.

4. Add `'my_strategy'` to the `strategies` list in `config/config_v2.py`.

5. Define any new strategy-specific parameters under `strategy_params` in `config/config_v2.py` using the existing parameter definition pattern.

## Standards & Formatting

- File naming: snake_case.py for all modules.
- Strategy functions must accept two positional arguments (`price_data`, `returns_data`) and `**params`.
- Use `BackwardCompatDict` for configuration access; do not change existing parameter structures.
- Centralize all file paths in `config/paths.py`; avoid hardcoding paths elsewhere.
- Document new code with clear docstrings and maintain the existing style conventions.

## Example (v2)

```python
from config.config_v2 import config_v2
from run_backtest_v2_with_metrics import run_backtest_with_metrics

# Customize configuration
config_v2['data_params']['tickers'] = ['SPY', 'QQQ', 'TLT', 'GLD']
config_v2['allocation_params']['strategy'] = 'risk_parity'
config_v2['backtest_params']['rebalance_freq'] = 'M'  # Monthly rebalancing

# Run backtest (main v2 flow)
results = run_backtest_with_metrics(config_v2)

# Access results
portfolio_returns = results['portfolio_returns']
performance = results['performance']
print(f"Sharpe Ratio: {performance['sharpe_ratio']:.2f}")

# LEGACY: For legacy/alternate script usage, see earlier documentation sections.

## Trade Log Standards

A standard trade log generated at the end of each backtest follows these conventions:

- Column order: `execution_date`, `trade_num`, `symbol`, `Buy/Sell`, `quantity`, `execution_price`, `Com/Slippage`, `Amount`, `Profit/Loss`
- `execution_date`: formatted as `YYYY-MM-DD` (date only, no timestamp)
- `trade_num`: sequential ID (blank for final liquidation entries)
- `Buy/Sell`: `BUY` if quantity > 0, `SELL` if quantity < 0
- `Com/Slippage`: commission or slippage cost applied per trade
- `Amount`: inverted cash flow; buys are negative, sells are positive
- `Profit/Loss`: realized P&L for each trade
- Final liquidation trades are appended at the end of the log for any remaining positions, using a date-only `execution_date`
