"""
Performance metric calculation module for CPS v4.
Contains core functions for calculating various performance metrics.
"""

import pandas as pd
import numpy as np
from typing import Optional, Dict, Union
from pathlib import Path

def calculate_cagr(returns: pd.Series) -> float:
    """Calculate Compound Annual Growth Rate."""
    if returns is None or len(returns) == 0:
        return 0
    total_return = (1 + returns).prod() - 1
    years = len(returns) / 252  # Assuming 252 trading days per year
    if years > 0:
        return (1 + total_return) ** (1 / years) - 1
    return 0

def calculate_sharpe_ratio(returns: pd.Series, risk_free_rate: float = 0.0) -> float:
    """Calculate Sharpe Ratio."""
    if returns is None or len(returns) == 0:
        return 0
    excess = returns - risk_free_rate
    std = excess.std()
    if std == 0:
        return 0
    return excess.mean() / std * np.sqrt(252)

def calculate_sortino_ratio(returns: pd.Series, risk_free_rate: float = 0.0, target_return: float = 0.0) -> float:
    """Calculate Sortino Ratio."""
    if returns is None or len(returns) == 0:
        return 0
    excess = returns - risk_free_rate
    downside = excess[excess < target_return]
    std_down = downside.std()
    if len(downside) == 0 or std_down == 0:
        return 0
    return excess.mean() / std_down * np.sqrt(252)

def calculate_max_drawdown(returns: pd.Series) -> float:
    """Calculate Maximum Drawdown."""
    if returns is None or len(returns) == 0:
        return 0
    cum = (1 + returns).cumprod()
    drawdown = cum / cum.cummax() - 1
    return drawdown.min()

def get_calendar_year_returns(returns: pd.Series, year: int) -> Optional[float]:
    """Calculate returns for a specific calendar year."""
    if returns is None or len(returns) == 0:
        return None
    year_data = returns[returns.index.year == year]
    if year_data.empty:
        return None
    return (1 + year_data).prod() - 1

def get_ytd_returns(returns: pd.Series) -> Optional[float]:
    """Calculate Year-to-Date returns."""
    if returns is None or len(returns) == 0:
        return None
    current_year = pd.Timestamp.now().year
    ytd = returns[returns.index.year == current_year]
    if ytd.empty:
        return None
    return (1 + ytd).prod() - 1

def calculate_performance_metrics(
    returns: pd.Series,
    risk_free_rate: Optional[pd.Series] = None,
    benchmark_returns: Optional[pd.Series] = None
) -> Dict[str, float]:
    """
    Calculate performance metrics for a strategy.
    
    Args:
        returns: Series of strategy returns
        risk_free_rate: Series of risk-free rates
        benchmark_returns: Series of benchmark returns
        
    Returns:
        Dictionary of performance metrics
    """
    # If no risk-free rate provided, use zeros
    if risk_free_rate is None or len(risk_free_rate) == 0:
        risk_free_rate = pd.Series(0.0, index=returns.index if returns is not None and not returns.empty else None)
    
    # Ensure risk_free_rate has the same index as returns
    if returns is not None and not returns.empty and not risk_free_rate.index.equals(returns.index):
        risk_free_rate = risk_free_rate.reindex(returns.index, method='ffill')
    
    metrics = {}
    
    if returns is not None and not returns.empty:
        metrics['cagr'] = calculate_cagr(returns)
        metrics['sharpe'] = calculate_sharpe_ratio(returns, risk_free_rate.mean())
        metrics['sortino'] = calculate_sortino_ratio(returns, risk_free_rate.mean())
        metrics['max_drawdown'] = calculate_max_drawdown(returns)
        metrics['ytd'] = get_ytd_returns(returns)
        metrics['volatility'] = returns.std() * np.sqrt(252)
        metrics['total_return'] = (1 + returns).prod() - 1
        
        if benchmark_returns is not None and not benchmark_returns.empty:
            metrics['benchmark_cagr'] = calculate_cagr(benchmark_returns)
            metrics['benchmark_sharpe'] = calculate_sharpe_ratio(benchmark_returns, risk_free_rate.mean())
            metrics['benchmark_sortino'] = calculate_sortino_ratio(benchmark_returns, risk_free_rate.mean())
            metrics['benchmark_max_drawdown'] = calculate_max_drawdown(benchmark_returns)
            metrics['benchmark_ytd'] = get_ytd_returns(benchmark_returns)
            metrics['benchmark_volatility'] = benchmark_returns.std() * np.sqrt(252)
            metrics['benchmark_total_return'] = (1 + benchmark_returns).prod() - 1
            
            # Calculate tracking error and information ratio
            tracking_error = (returns - benchmark_returns).std() * np.sqrt(252)
            metrics['tracking_error'] = tracking_error
            if tracking_error > 0:
                metrics['information_ratio'] = (metrics['cagr'] - metrics['benchmark_cagr']) / tracking_error
            else:
                metrics['information_ratio'] = 0
    
    return metrics
