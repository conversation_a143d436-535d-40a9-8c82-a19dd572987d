# Transaction Logging and Backtesting Architecture

## Overview

This document outlines the transaction logging capabilities and backtesting architecture of the Financial Asset Allocation Backtesting Framework. The framework is designed with a layered architecture that provides comprehensive backtesting functionality, including detailed transaction logging.

## Backtesting Architecture

The backtesting system is organized in three layers:

### 1. Custom Function Library (Base Layer)
- **Location**: `S:\Dropbox\Scott Only Internal\Quant_Python_24\Custom Function Library\backtesting\backtest_framework.py`
- **Core Functions**: 
  - `backtest_strategy()`: Runs the backtest simulation
  - `calculate_trades()`: Calculates trades from weight changes
- **Role**: Provides the fundamental backtesting engine used by all higher layers

### 2. Backtest Engine (Middle Layer)
- **Location**: `S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\backtest\backtest_engine.py`
- **Main Function**: `run_backtest()`
- **Role**: 
  - Wraps the Custom Function Library's backtesting functions
  - Adds transaction logging and performance reporting
  - Handles configuration parameters and output formatting

### 3. Run Script (Top Layer)
- **Location**: `S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\run_backtest.py`
- **Role**:
  - Processes command-line arguments
  - Calls the Backtest Engine
  - Generates performance reports and visualizations
  - Executed by batch files like `run_ema_backtest.bat`

## Transaction Logging

The framework includes detailed transaction logging capabilities:

### TradeLogger Class
- **Location**: `S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\utils\trade_log.py`
- **Key Methods**:
  - `create_trade_log_from_trades()`: Creates trade log from weight changes
  - `save_trade_log()`: Saves basic trade log to CSV
  - `save_detailed_trade_log()`: Saves formatted trade log to Excel
  - `calculate_trade_statistics()`: Calculates summary statistics about trades

### Transaction Log Contents
Each transaction log includes:
- **Date**: Date of the transaction
- **Asset**: Ticker symbol
- **Action**: BUY or SELL
- **Shares**: Number of shares traded
- **Price**: Price per share
- **Amount**: Total transaction amount
- **Transaction Fee**: Cost of the transaction

### Configuration

Transaction logging is configured in `config/config.py` under the `reporting_params` section:

```python
# Reporting parameters
reporting_params = {
    # Whether to create Excel reports
    'create_excel': True,
    
    # Whether to save trade logs
    'save_trade_log': True,
    
    # Output directory for reports and logs
    'output_dir': str(OUTPUT_DIR),
    
    # Performance metrics to include
    'metrics': [
        'total_return',
        'annualized_return',
        'volatility',
        'sharpe_ratio',
        'max_drawdown',
        'win_rate',
    ],
}
```

## Output Files

When running a backtest with transaction logging enabled, the following files are generated:

1. **Detailed Transaction Log (Excel)**:
   - Filename: `{strategy_name}_trade_log_{date}.xlsx`
   - Format: Excel with formatted columns
   - Location: Configured output directory

2. **Simple Transaction Log (CSV)**:
   - Filename: `trade_log_{date}.csv`
   - Format: CSV
   - Location: Configured output directory

3. **Performance Reports**:
   - Various performance metrics and visualizations
   - Generated alongside transaction logs

## Running Backtests

Backtests can be run using the provided batch files:

```
run_ema_backtest.bat
```

Or directly with Python, specifying parameters:

```
python run_backtest.py --strategy ema --rebalance weekly --start-date 2020-01-01 --end-date 2025-04-04 --tickers SPY,SHV,EFA,TLT,PFF
```

All backtests will automatically generate transaction logs if `save_trade_log` is set to `True` in the configuration.
