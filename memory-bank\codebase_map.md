# CPS V4 Codebase Map

## Title & Purpose

This document provides a comprehensive mapping of the CPS (Central Parameter System) V4 backtesting and trading pipeline codebase. It serves as the definitive reference for understanding system architecture, execution flows, parameter management, and component interactions within the optimized unified pipeline and refactored reporting system.

## High-Level Overview

CPS V4 is a configuration-driven backtesting and trading system designed for financial asset allocation strategies. The system follows a modular architecture with clear separation between data loading, signal generation, portfolio management, trade execution, and reporting phases. All logic is controlled by configuration parameters loaded from INI files, ensuring flexibility and maintainability.

The system operates in three primary modes: **UNIFIED** (default - single pipeline for signal generation and trading), **SIGNAL_ONLY** (generates signals only), and **TRADING_ONLY** (processes pre-computed signals). The unified pipeline has been optimized to use direct DataFrame handoffs between phases, eliminating duplicate file generation for improved performance.

**Recent Major Updates (July 2025)**: The reporting system has been completely refactored with consolidated Python code in `v4/py_reporting/` and large monolithic files broken down into focused modules for improved maintainability. Critical architectural issues have been identified and fixed, including optimization loop bugs and configuration section mismatch problems.

**MAJOR SUCCESS (August 2, 2025)**: Optimization system fully operational with XLSX integration. Produces 12 unique equity curves with realistic portfolio values ($1.18M to $1.44M) and properly flows into EMA_V3_1_performance_tables_*.xlsx reports with real market data.

## SINGLE vs OPTIMIZATION EXECUTION PATHS MATRIX

### Core Pipeline Functions

| File | Single Mode Function | Optimization Mode Function | Current Status | Role |
|------|---------------------|----------------------------|----------------|------|
| `v4/pipeline/backtest_v4.py` | `run_single_pipeline()` | `run_optimization_combination()` | ✅ Separated | Main pipeline entry point |
| `v4/engine/signal_generation_v4.py` | `generate_ema_signals_single()` | `generate_ema_signals_optimization()` | ✅ Separated | EMA signal calculation |
| `v4/engine/portfolio_v4.py` | `run_backtest_single()` | `run_backtest_optimization()` | ✅ Separated | Portfolio execution engine |

### Configuration and Detection

| File | Single Mode Function | Optimization Mode Function | Current Status | Role |
|------|---------------------|----------------------------|----------------|------|
| `v4/pipeline/config.py` | `determine_pipeline_mode()` | `determine_pipeline_mode()` | ✅ Working | Route to correct execution path |
| `v4/optimization_detector.py` | N/A | `get_optimization_combinations()` | ❌ BROKEN | Generate parameter combinations |
| `v4/settings/config_helper.py` | `ConfigHelper.get()` | `ConfigHelper.get()` | ✅ Working | Section-agnostic parameter access |

### Data Storage and Results

| File | Single Mode Function | Optimization Mode Function | Current Status | Role |
|------|---------------------|----------------------------|----------------|------|
| `v4/utils/equity_curves_manager.py` | N/A | `EquityCurvesManager` | ❌ BROKEN | Matrix-based equity curve storage |
| `v4/py_reporting/v4_performance_report.py` | Direct CSV output | `generate_performance_table()` | ❌ BROKEN | Results compilation and reporting |

### CRITICAL ISSUE ANALYSIS

**Problem**: Despite architectural separation being complete, optimization produces identical results.

**Suspected Root Causes**:
1. **Parameter Propagation**: Different parameter values not reaching signal generation
2. **Signal Generation**: EMA algorithm not using different parameters per combination
3. **Portfolio Execution**: Different signals not producing different equity curves
4. **Data Storage**: Unique results generated but overwritten/duplicated in storage

## Execution Chain Section

### Current Production Flow (test_optimization_fix_simple.bat)

```text
test_optimization_fix_simple.bat
└── test_optimization_validation.py
    ├── v4.config.paths_v4.get_validation_dir()
    ├── v4.settings.settings_CPS_v4.load_settings()
    ├── v4.engine.data_loader_v4.load_data_for_backtest()
    └── v4.py_reporting.v4_performance_report.PerformanceTableGenerator
        ├── __init__(csv_flag_use=True)
        ├── get_optimization_combinations()
        ├── _validate_single_combination()
        └── _run_matrix_optimization()
            └── v4.py_reporting.report_modules.report_matrix_optimization
                ├── _run_pipeline_for_combination()
                ├── _create_temp_settings_for_combination()
                └── subprocess: python v4/run_unified_pipeline.py --settings temp_file
```

### Unified Pipeline Execution Flow (v4/run_unified_pipeline.py)

```text
v4/run_unified_pipeline.py [Main Shell - 95 lines]
├── v4.pipeline.config.determine_pipeline_mode()
│   ├── Check Environment Variables FIRST:
│   │   ├── CPS_V4_COMBO_ID (if present → FORCE single mode)
│   │   └── CPS_V4_OPTIMIZATION_ACTIVE (if 'true' → optimization mode)
│   ├── Check INI optimization parameters (optimize=true)
│   └── Check global optimization_active flag
│
├── [BRANCH A] v4.pipeline.modes.run_optimization_pipeline() [if optimization mode]
│   └── Full matrix optimization with parameter combinations
│
└── [BRANCH B] v4.pipeline.modes.run_single_pipeline() [if single mode]
    ├── v4.Algo_signal_phase.run_signal_phase()
    │   ├── v4.settings.settings_CPS_v4.load_settings()
    │   ├── v4.engine.data_loader_v4.load_data_for_backtest()
    │   └── v4.models.ema_signal_bridge.run_ema_model_with_tracing()
    │       └── v4.models.ema_allocation_model_v4.ema_allocation_model_updated()
    │           └── v4.models.ema_allocation_model_v4.ema_allocation_model()
    └── v4.pipeline.trading.modify_run_trading_to_accept_dataframe()
        └── v4.engine.backtest_v4.run_backtest_with_signals()
            ├── v4.engine.portfolio_v4.Portfolio()
            ├── v4.engine.execution_v4.ExecutionEngine()
            ├── v4.engine.allocation_v4.calculate_rebalance_orders()
            └── v4.engine.results_calculator.calculate_results()
```

### Current Production Flow (Optimization Validation)
```
test_optimization_fix_simple.bat → test_optimization_validation.py → v4/py_reporting/performance_table_generator.py
└── Matrix Optimization → v4/run_unified_pipeline.py (per combination)
└── Signal Generation Phase → v4/Algo_signal_phase.py → v4/models/ema_allocation_model_v4.py
└── Trading Phase → modify_run_trading_to_accept_dataframe() → v4/engine/backtest_v4.py
└── Reporting Phase → v4/py_reporting/report_modules/[focused_modules]
└── Output Generation → reporting/[optimization_results] + optimization_validation/[validation_artifacts]
```

### Legacy Production Flow (Unified Pipeline)
```
run_main_v4_unified.bat → main_v4_production_run.py → v4/run_unified_pipeline.py
└── Signal Generation Phase → v4/Algo_signal_phase.py → v4/models/ema_allocation_model_v4.py
└── Trading Phase → modify_run_trading_to_accept_dataframe() → v4/engine/backtest_v4.py
└── Output Generation → v4_trace_outputs/[timestamped_files]
```

### Legacy/Testing Execution Flows
```
run_main_v4_signalalgo.bat → Signal Generation Only
run_trading_phase_standalone.bat → Trading Phase Only (uses pre-computed signals)
```

### Core Engine Chain
```
Data Loader → Signal Generator → Backtest Engine → Portfolio Manager → Execution Engine → Reporter
     ↓              ↓               ↓                ↓                ↓             ↓
data_loader_v4 → ema_signal_bridge → backtest_v4 → portfolio_v4 → execution_v4 → py_reporting
```

## Refactored Reporting System Architecture (July 2025)

### Reporting Module Structure
```
v4/py_reporting/                           # Centralized Python reporting code
├── v4_performance_report.py               # Main shell (190 lines) - backward compatible
├── report_modules/                        # Focused modules (refactored from 883-line monolith)
│   ├── __init__.py                        # Package imports
│   ├── report_excel.py                    # Excel generation (358 lines)
│   ├── report_metrics.py                  # Performance metrics (300 lines)
│   ├── report_validation.py               # Validation exports (344 lines)
│   └── report_optimization.py             # Optimization reports (307 lines)
├── performance_table_generator.py         # XLSX performance table generation
├── allocation_report_v4.py                # Allocation reporting functionality
├── equity_curves_manager.py               # Equity curve management
├── test_v4_performance_report.py          # Testing utilities
├── verify_v4_performance_report.py        # Validation utilities
└── report_compliance_utils.py             # Compliance utilities
```

### Reporting Function Flow
```
generate_performance_report() [main shell]
    ↓
report_modules.report_excel._generate_excel_report()
    ↓
report_modules.report_metrics._calculate_performance_metrics()
    ↓
report_modules.report_validation.export_validation_files()
    ↓
report_modules.report_optimization.generate_optimization_report()
```

### Key Refactoring Benefits
- **Maintainability**: No file exceeds 400 lines (was 883 lines)
- **Modularity**: Clear separation of concerns by functionality
- **Backward Compatibility**: All existing imports work unchanged
- **Focused Development**: Each module has single responsibility

## Parameter Flow Section

### Configuration Architecture (Updated July 27, 2025)

**CRITICAL ISSUE IDENTIFIED**: The INI file section structure was causing recurring bugs where developers had to specify both section AND parameter name (e.g., `config.getboolean('System', 'optimization_active')` vs `config.getboolean('Core', 'optimization_active')`). This created opportunities for section mismatch bugs.

**SOLUTION IMPLEMENTED**: Section-agnostic configuration approach via `ConfigHelper` class:

```python
# NEW APPROACH - Section-agnostic parameter lookup
from v4.settings.config_helper import get_param_boolean
optimization_active = get_param_boolean('optimization_active', config_path, fallback=False)

# OLD BUG-PRONE APPROACH - Section-specific lookup
optimization_active = config.getboolean('Core', 'optimization_active', fallback=False)  # WRONG SECTION!
```

### Configuration Hierarchy
```
settings_parameters_v4.ini
    ↓
v4/settings/config_helper.py [ConfigHelper class - section-agnostic lookup]
    ↓
v4/settings/settings_CPS_v4.py [load_settings() - backward compatibility]
    ↓
Module-level parameter access (simplified one-line calls)
    ↓
Runtime parameter application
```

### Parameter Types (CPS V4 Standard)
- **SimpleA**: Simple alphanumeric values (strings, booleans)
- **SimpleN**: Simple numeric values (int, float)
- **ComplexN**: Complex numeric with optimization attributes (min/max/step)
- **AlphaList**: Lists of values with picklist references

### Parameter Flow Matrix
```
[GUI] → [Parameter Registry] → [Settings Loader] → [Engine Components] → [Reports]
  ↓           ↓                    ↓                    ↓                ↓
Optional   parameter_registry   settings_CPS_v4    All v4/modules    All reports
```

## I/O Matrix

### Input Sources
| Source Type | Location | Format | Used By |
|-------------|----------|--------|---------|
| Market Data | data/ directory | CSV/Excel | data_loader_v4.py |
| Parameters | v4/settings/settings_parameters_v4.ini | INI | settings_CPS_v4.py |
| Signal Files | v4_trace_outputs/ | CSV | Trading phase (legacy mode) |

### Output Destinations
| Output Type | Location | Format | Generated By | Frequency |
|-------------|----------|--------|--------------|-----------|
| Signal History | v4_trace_outputs/signals_output_YYYYMMDD_HHMMSS.csv | CSV | Signal phase | Per run |
| Allocation History | v4_trace_outputs/allocation_history_YYYYMMDD_HHMMSS.csv | CSV | Trading phase | Per run |
| Trade Log | v4_trace_outputs/trade_log_YYYYMMDD_HHMMSS.csv | CSV | Trading phase | Per run |
| Log Files | v4_trace_outputs/ | LOG | All phases | Per run |

### Data Handoff Points
| From Phase | To Phase | Method | Data Format |
|------------|----------|--------|-------------|
| Signal → Trading | Direct DataFrame | In-memory transfer | pandas.DataFrame |
| Signal → Trading (Legacy) | File-based | CSV files | Timestamped CSV |
| Engine → Reporting | Results dictionary | In-memory | Dict[str, Any] |

## Module/Function/Parameter Matrices

### Core Engine Modules
| Module | Location | Key Functions | Parameters Used | Purpose |
|--------|----------|---------------|-----------------|---------|
| config_helper.py | v4/settings/ | ConfigHelper class, get_param_boolean() | All INI parameters | Section-agnostic parameter lookup (NEW) |
| settings_CPS_v4.py | v4/settings/ | load_settings(), _parse_config() | All INI parameters | Central parameter loading (legacy compatibility) |

## Detailed Module Breakdown (Complete Execution Trace)

### Entry Point Modules

#### test_optimization_fix_simple.bat
- **Purpose**: Current production entry point for optimization validation testing
- **Location**: Root directory
- **Key Functions**:
  - Activates Python environment
  - Creates timestamped log files in `optimization_validation/` directory
  - Executes `test_optimization_validation.py` with PowerShell Tee-Object for dual output

#### test_optimization_validation.py
- **Purpose**: Main validation orchestration script with 10-step validation framework
- **Location**: Root directory
- **Key Functions**:
  - `test_optimization_validation()`: Main validation function
  - Creates validation directories and manages step-by-step validation logging
- **Key Imports**: `PerformanceTableGenerator`, path configuration, settings loader, data loader

### Configuration & Settings Modules

#### v4/config/paths_v4.py
- **Purpose**: Centralized path configuration for entire system
- **Key Variables**: `PROJECT_ROOT`, `OUTPUT_DIR`, `OPTIMIZATION_VALIDATION_DIR`, `V4_SETTINGS_FILE`
- **Key Functions**:
  - `get_validation_dir()`: Returns validation directory path
  - `get_reporting_file_path()`: Returns reporting file paths

#### v4/settings/settings_CPS_v4.py (402 lines)
- **Purpose**: Central Parameter System v4 - Settings Module
- **Key Functions**:
  - `load_settings(custom_file=None)`: Load configuration from INI files
  - Type conversion, default values, and parameter overrides
- **Key Variables**: `RUN_MODE`, `LOG_LEVEL`, operational flags
- **Dependencies**: configparser, pathlib, typing

#### v4/settings/config_helper.py
- **Purpose**: Section-agnostic configuration helper to eliminate INI section mismatch bugs
- **Key Functions**:
  - `get_param_boolean()`: Get boolean parameters without section dependency
  - `get_param_string()`: Get string parameters without section dependency

### Data Loading Modules

#### v4/engine/data_loader_v4.py (208 lines)
- **Purpose**: Data loader module (CPS v4 compliant) for historical price data
- **Key Functions**:
  - `get_adjusted_close_data()`: Load and return adjusted close price data
  - `load_data_for_backtest()`: Main data loading function for backtest
- **CPS_v4 Parameters**: tickers, start_date, end_date, price_field, data_storage_mode
- **Dependencies**: pandas, yfinance (with retry logic), date_utils_v4

### Pipeline Orchestration Modules

#### v4/run_unified_pipeline.py (95 lines - Refactored Shell)
- **Purpose**: Main entry point that orchestrates complete signal generation and trading workflow
- **Key Functions**:
  - `run_unified_pipeline()`: Main router function determining pipeline mode
  - `main()`: CLI entry point with argument parsing
- **Dependencies**: v4.pipeline.config, v4.pipeline.modes, v4.pipeline.trading

#### v4/pipeline/config.py (354 lines)
- **Purpose**: Configuration, logging setup, and CLI argument parsing
- **Key Functions**:
  - `setup_logger()`: Initialize shared logger for unified pipeline
  - `determine_pipeline_mode()`: Determine optimization vs single run mode
  - `parse_cli_arguments()`: Parse command line arguments
- **Dependencies**: argparse, logging, paths_v4, settings_CPS_v4

#### v4/pipeline/modes.py
- **Purpose**: Pipeline execution modes (optimization and single run)
- **Key Functions**:
  - `run_optimization_pipeline()`: Execute optimization mode pipeline
  - `run_single_pipeline()`: Execute single run mode pipeline
  - `run_baseline_optimization_iteration()`: Execute baseline optimization iteration

#### v4/pipeline/trading.py
- **Purpose**: Trading operations and signal handling
- **Key Functions**:
  - `modify_run_trading_to_accept_dataframe()`: Modified trading function accepting DataFrames

### Signal Generation Modules

#### v4/Algo_signal_phase.py
- **Purpose**: Signal generation phase orchestrator
- **Key Functions**:
  - `run_signal_phase()`: Main signal generation function
- **Process**: Load settings → Load data → Generate signals using EMA model
- **Dependencies**: settings_CPS_v4, data_loader_v4, ema_signal_bridge

#### v4/models/ema_signal_bridge.py
- **Purpose**: Bridge module for EMA signal generation with tracing
- **Key Functions**:
  - `run_ema_model_with_tracing()`: Run EMA model with comprehensive tracing output
- **Dependencies**: ema_allocation_model_v4

#### v4/models/ema_allocation_model_v4.py
- **Purpose**: EMA-based allocation model for financial backtesting
- **Key Functions**:
  - `ema_allocation_model()`: Core EMA allocation logic
  - `ema_allocation_model_updated()`: Updated version returning pd.Timestamp keys
  - `calculate_ema_metrics()`: Calculate EMA metrics for analysis
- **Algorithm**: Uses Short/Medium/Long-term EMAs for asset ranking and allocation
- **Allocation Rules**: Top 2 assets get 60%/40% allocation, others get 0%

#### v4/engine/signal_generator_v4.py
- **Purpose**: Signal generator factory and EMA signal generator implementation
- **Key Classes**:
  - `EMASignalGenerator`: EMA-based signal generator
- **Key Functions**:
  - `generate_signals()`: Generate EMA-based allocation signals
- **Dependencies**: ema_signal_bridge

### Backtest Engine Modules

#### v4/engine/backtest_v4.py
- **Purpose**: Main backtesting engine coordinating portfolio management and trade execution
- **Key Functions**:
  - `run_backtest_with_signals()`: Main backtest function accepting signals DataFrame
  - Daily execution flow management
  - Order scheduling via pending_orders queue
- **Dependencies**: portfolio_v4, execution_v4, allocation_v4, results_calculator

#### v4/engine/portfolio_v4.py
- **Purpose**: Portfolio state management module
- **Key Classes**:
  - `Portfolio`: Maintains positions, cash, and history
- **Key Functions**:
  - `mark_to_market()`: Update portfolio values with current prices
  - `update_from_trade()`: Update portfolio based on executed trades
  - `add_position()`, `remove_position()`: Position management
- **State Management**: Single source of truth for portfolio positions and cash

#### v4/engine/execution_v4.py
- **Purpose**: Order execution engine with commission and slippage modeling
- **Key Classes**:
  - `ExecutionEngine`: Main execution coordinator
  - `CommissionModel`, `SlippageModel`: Cost modeling
  - `TradeLog`: Trade history tracking
- **Key Functions**:
  - `execute_orders()`: Execute orders with enhanced validation
  - Commission and slippage application
- **Dependencies**: portfolio_v4

#### v4/engine/allocation_v4.py
- **Purpose**: Portfolio allocation and rebalancing logic
- **Key Functions**:
  - `calculate_rebalance_orders()`: Calculate orders needed for rebalancing
  - Allocation deviation threshold management (2% rule)
- **Trade Logic**: Only execute trades when allocation differs by >2%

#### v4/engine/results_calculator.py
- **Purpose**: Performance metrics and results calculation
- **Key Functions**:
  - `calculate_results()`: Calculate comprehensive backtest results
  - Performance metrics calculation (CAGR, Sharpe, Sortino, etc.)
- **Output**: Results dictionary for reporting modules

### Reporting & Optimization Modules

#### v4/py_reporting/v4_performance_report.py (190 lines - Main Shell)
- **Purpose**: Main shell file for backward compatibility after refactoring
- **Key Classes**:
  - `PerformanceTableGenerator`: Main class for XLSX performance table generation
- **Key Functions**:
  - `get_optimization_combinations()`: Generate parameter combinations using itertools.product
  - `_validate_single_combination()`: Run single backtest for validation (Step 3)
  - `_run_matrix_optimization()`: Matrix-based optimization using EquityCurvesManager
- **Dependencies**: Delegates to focused modules in report_modules/

#### v4/py_reporting/Archive/performance_table_generator.py (Active Version)
- **Purpose**: Comprehensive performance reports with all required tabs
- **Key Functions**:
  - `_run_pipeline_for_combination()`: Execute pipeline for specific parameters
  - `_create_temp_settings_for_combination()`: Create temporary settings files
  - `_setup_validation_directories()`: Create validation directory structure
  - `generate_performance_table()`: Main XLSX generation function
- **Process**: Integrates with unified pipeline to use real backtest data

#### v4/py_reporting/report_modules/report_matrix_optimization.py
- **Purpose**: Matrix-based optimization functions (extracted from monolithic file)
- **Key Functions**:
  - `_run_matrix_optimization()`: Run matrix optimization using EquityCurvesManager
  - `_run_pipeline_for_combination()`: Run unified pipeline for specific parameter combination
  - `_validate_single_combination()`: Single combination validation for Step 3
  - `_create_temp_settings_for_combination()`: Create temporary settings files
- **Environment Variables**: Sets CPS_V4_OPTIMIZATION_ACTIVE, CPS_V4_COMBO_ID for subprocess

#### v4/py_reporting/report_modules/report_excel.py (358 lines)
- **Purpose**: Excel generation functionality
- **Key Functions**: Excel workbook creation, tab generation, formatting

#### v4/py_reporting/report_modules/report_metrics.py (300 lines)
- **Purpose**: Performance metrics calculation
- **Key Functions**: CAGR, Sharpe, Sortino, UPI, Max Drawdown calculations

#### v4/py_reporting/report_modules/report_validation.py (344 lines)
- **Purpose**: Validation exports and testing
- **Key Functions**: Validation file exports, test result verification

#### v4/py_reporting/report_modules/report_optimization.py (307 lines)
- **Purpose**: Optimization reports generation
- **Key Functions**: Parameter combination reports, optimization result analysis

#### v4/py_reporting/equity_curves_manager.py
- **Purpose**: Equity curve management for matrix-based optimization
- **Key Functions**:
  - Matrix-based equity curve storage and retrieval
  - Combination metadata tracking
  - File I/O optimization for large parameter grids

### Utility Modules

#### v4/utils/date_utils_v4.py
- **Purpose**: Date standardization and manipulation utilities
- **Key Functions**:
  - `standardize_date()`: Convert various date formats to datetime.date
  - `standardize_date_range()`: Standardize date ranges
  - `filter_dataframe_by_dates()`: Filter DataFrames by date ranges

#### v4/utils/tracing_utils.py
- **Purpose**: Tracing and debugging utilities
- **Key Functions**:
  - `setup_trace_directory()`: Create trace output directories
  - `reset_trace_directory_for_run()`: Reset trace directories for new runs

#### v4/utils/smart_logging.py
- **Purpose**: Smart logging configuration
- **Key Functions**:
  - `create_smart_logger()`: Create configured logger instances

#### v4/utils/trade_filter.py
- **Purpose**: Trade filtering and allocation utilities
- **Key Functions**:
  - `fetch_current_allocation()`: Get current portfolio allocation
  - `filter_trades()`: Apply trade filtering rules

## Complete Execution Flow Diagram

### Production Optimization Validation Flow

```mermaid
graph TD
    A[test_optimization_fix_simple.bat] --> B[test_optimization_validation.py]
    B --> C[PerformanceTableGenerator.__init__]
    C --> D[get_optimization_combinations]
    D --> E[_validate_single_combination]
    E --> F[_run_pipeline_for_combination]
    F --> G[_create_temp_settings_for_combination]
    G --> H[Set Environment Variables:<br/>CPS_V4_COMBO_ID=combo_123<br/>CPS_V4_OPTIMIZATION_ACTIVE=true]
    H --> I[subprocess: python v4/run_unified_pipeline.py --settings temp_file]
    I --> J[determine_pipeline_mode]
    J --> K{Check Environment Variables}
    K -->|CPS_V4_COMBO_ID present| L[FORCE single mode]
    K -->|CPS_V4_OPTIMIZATION_ACTIVE=true<br/>AND no combo_id| M[optimization mode]
    K -->|No env vars| N[Check INI parameters]
    N -->|optimize=true found| M
    N -->|No optimization params| L

    L --> O[run_single_pipeline]
    M --> P[run_optimization_pipeline]

    O --> Q[run_signal_phase]
    Q --> R[load_settings]
    R --> S[load_data_for_backtest]
    S --> T[run_ema_model_with_tracing]
    T --> U[ema_allocation_model_updated]
    U --> V[ema_allocation_model]
    V --> W[modify_run_trading_to_accept_dataframe]
    W --> X[run_backtest_with_signals]
    X --> Y[Portfolio.mark_to_market]
    Y --> Z[ExecutionEngine.execute_orders]
    Z --> AA[calculate_rebalance_orders]
    AA --> BB[calculate_results]
    BB --> CC[Return equity curve to validation]
    CC --> DD[_run_matrix_optimization]
    DD --> EE[EquityCurvesManager]
    EE --> FF[Generate Performance Table XLSX]
```

### Key Data Flow Points

1. **Entry Point**: `test_optimization_fix_simple.bat` → PowerShell with Tee-Object logging
2. **Validation Orchestration**: `test_optimization_validation.py` → 10-step validation framework
3. **Parameter Generation**: `get_optimization_combinations()` → itertools.product for parameter grid
4. **Subprocess Execution**: Each combination runs full pipeline via subprocess
5. **Pipeline Mode Detection**: `determine_pipeline_mode()` → optimization vs single run
6. **Signal Generation**: `run_signal_phase()` → EMA model with tracing
7. **Trading Simulation**: `run_backtest_with_signals()` → Portfolio management and execution
8. **Results Aggregation**: `_run_matrix_optimization()` → Matrix-based equity curve management
9. **Report Generation**: Performance Table XLSX with 5 tabs (Signal History, Allocation History, Trade Log, Performance, Settings)

### Critical Integration Points

- **Configuration Management**: Section-agnostic parameter lookup via `ConfigHelper`
- **File Output Control**: CSV generation controlled by `csv_flag_use` and `optimization_active` flags
- **Environment Variables**: `CPS_V4_OPTIMIZATION_ACTIVE`, `CPS_V4_COMBO_ID` for subprocess communication
- **Temporary Settings**: Unique settings files per combination to avoid conflicts
- **Matrix Optimization**: `EquityCurvesManager` for efficient equity curve storage and retrieval

## CRITICAL: Pipeline Mode Branching Logic

### The Two-Level Architecture

The system operates on **two distinct levels** that must be clearly understood:

1. **Matrix Optimization Level** (Coordinator): `PerformanceTableGenerator` manages multiple parameter combinations
2. **Individual Pipeline Level** (Worker): Each subprocess processes one specific parameter combination

### Environment Variable Decision Logic

The `determine_pipeline_mode()` function uses this **priority order**:

```python
# PRIORITY 1: Environment Variables (HIGHEST)
combo_id = os.environ.get('CPS_V4_COMBO_ID')
optimization_active_env = os.environ.get('CPS_V4_OPTIMIZATION_ACTIVE')

if combo_id and not optimization_active_env:
    return 'single'  # FORCE single mode for individual combinations

# PRIORITY 2: INI File Parameters
if any_optimize_params or optimization_active_flag:
    return 'optimization'  # Full matrix optimization

# PRIORITY 3: Default
return 'single'
```

### Why This Branching Is Critical

**CORRECT BEHAVIOR** (Current Implementation):
- Matrix Coordinator sets `CPS_V4_COMBO_ID=combo_123` for each subprocess
- Each subprocess detects combo_id → routes to `run_single_pipeline()`
- Each subprocess processes ONE parameter combination
- Results are collected back to Matrix Coordinator

**INCORRECT BEHAVIOR** (If branching was wrong):
- Each subprocess would route to `run_optimization_pipeline()`
- Each subprocess would try to run FULL matrix optimization
- Infinite recursion or massive resource consumption
- No individual combination results

### Key Files Implementing This Logic

- **`v4/pipeline/config.py`**: `determine_pipeline_mode()` - Environment variable priority logic
- **`v4/py_reporting/report_modules/report_matrix_optimization.py`**: Sets environment variables for subprocesses
- **`v4/run_unified_pipeline.py`**: Routes based on mode detection
| backtest_v4.py | v4/engine/ | run_backtest(), run_backtest_with_signals() | backtest.*, strategy.* | Main backtest orchestration |
| portfolio_v4.py | v4/engine/ | update_portfolio(), get_weights() | backtest.initial_capital | Portfolio state management |
| execution_v4.py | v4/engine/ | execute_orders(), calculate_slippage() | backtest.commission_rate, slippage_rate | Trade execution simulation |
| data_loader_v4.py | v4/engine/ | load_data_for_backtest(), validate_data() | data.* parameters | Market data loading |

### Signal Generation Modules
| Module | Location | Key Functions | Parameters Used | Purpose |
|--------|----------|---------------|-----------------|---------|
| ema_allocation_model_v4.py | v4/models/ | calculate_ema_metrics(), generate_allocation_signals() | ema.* parameters | EMA-based signal generation |
| ema_signal_bridge.py | v4/models/ | run_ema_model_with_tracing() | strategy.* | Bridge to legacy EMA implementation |
| signal_generator_v4.py | v4/engine/ | generate_signals(), validate_signals() | strategy.* | Generic signal generation interface |

### Reporting Modules (Refactored July 2025)
| Module | Location | Key Functions | Parameters Used | Purpose |
|--------|----------|---------------|-----------------|---------|
| v4_performance_report.py | v4/py_reporting/ | generate_performance_report() [main shell] | report.* | Main reporting interface (backward compatible) |
| report_excel.py | v4/py_reporting/report_modules/ | _generate_excel_report(), verify_excel_report() | report.* | Excel report generation |
| report_metrics.py | v4/py_reporting/report_modules/ | _calculate_performance_metrics() | backtest.*, report.* | Performance metrics calculation |
| report_validation.py | v4/py_reporting/report_modules/ | export_validation_files(), _export_signal_history() | report.* | Validation file exports |
| report_optimization.py | v4/py_reporting/report_modules/ | generate_optimization_report() | optimization.* | Optimization reporting |
| performance_table_generator.py | v4/py_reporting/ | generate_performance_table_xlsx() | All parameters | XLSX performance table generation |
| allocation_report_v4.py | v4/py_reporting/ | generate_allocation_report() | report.* | Asset allocation visualization |
| equity_curves_manager.py | v4/py_reporting/ | EquityCurvesManager class | optimization.* | Matrix-based equity curve management |
| results_calculator.py | v4/engine/ | calculate_results(), calculate_performance_metrics() | backtest.*, report.* | Core performance metrics calculation |

### Utility Modules
| Module | Location | Key Functions | Parameters Used | Purpose |
|--------|----------|---------------|-----------------|---------|
| tracing_utils.py | v4/utils/ | setup_trace_directory(), save_df_to_trace_dir() | system.retain_intermediate_files | Output file management |
| smart_logging.py | v4/utils/ | create_smart_logger() | system.log_level | Logging configuration |
| date_utils_v4.py | v4/utils/ | parse_date(), map_rebalance_frequency() | backtest.rebalance_freq | Date handling utilities |
| trade_filter.py | v4/utils/ | filter_trades(), fetch_current_allocation() | strategy.* | Trade filtering logic |

## Mermaid Diagrams

### System Architecture Overview
```mermaid
graph TD
    subgraph Input[Input Layer]
        A1[Market Data CSV/Excel] --> B1[data_loader_v4.py]
        A2[settings_parameters_v4.ini] --> B2[settings_CPS_v4.py]
    end

    subgraph Signal[Signal Generation]
        B1 --> C1[ema_allocation_model_v4.py]
        B2 --> C1
        C1 --> |DataFrame| D1[Unified Pipeline]
        C1 --> |CSV File| D2[Legacy Mode]
    end

    subgraph Engine[Backtest Engine]
        D1 --> E1[backtest_v4.py]
        D2 --> E1
        E1 --> E2[portfolio_v4.py]
        E1 --> E3[execution_v4.py]
        E2 <--> E3
        E2 --> E4[results_calculator.py]
        E3 --> E4
    end

    subgraph Output[Output Layer]
        E4 --> F1[allocation_history.csv]
        E4 --> F2[trade_log.csv]
        E1 --> F3[allocation_report_v4.py]
        F3 --> F4[Performance Reports]
    end

    classDef input fill:#D6EAF8,stroke:#5DADE2
    classDef signal fill:#D1F2EB,stroke:#48C9B0
    classDef engine fill:#FCF3CF,stroke:#F7DC6F
    classDef output fill:#F8D7DA,stroke:#DC3545
    
    class A1,A2,B1,B2 input
    class C1,D1,D2 signal
    class E1,E2,E3,E4 engine
    class F1,F2,F3,F4 output
```

### Current Production Workflow (Optimization Validation)
```mermaid
sequenceDiagram
    participant BAT as test_optimization_fix_simple.bat
    participant VAL as test_optimization_validation.py
    participant PTG as performance_table_generator.py
    participant UNI as run_unified_pipeline.py (per combination)
    participant SIG as Algo_signal_phase.py
    participant BT as backtest_v4.py
    participant RPT as report_modules/[focused_modules]

    BAT->>VAL: Execute validation script
    VAL->>PTG: Initialize PerformanceTableGenerator
    PTG->>PTG: get_optimization_combinations() [12 combinations]
    PTG->>PTG: _validate_single_combination() [STEP 3]
    PTG->>PTG: _run_matrix_optimization() [STEP 4-10]

    loop For each parameter combination
        PTG->>UNI: Run pipeline with specific parameters
        UNI->>SIG: Generate signals with combination parameters
        SIG->>BT: Execute backtest with signals
        BT->>PTG: Return equity curve data
    end

    PTG->>RPT: Generate optimization reports
    RPT->>VAL: Return validation results
    VAL->>BAT: Complete with success/failure status
```

### Legacy Unified Pipeline Flow (Optimized)
```mermaid
sequenceDiagram
    participant BAT as run_main_v4_unified.bat
    participant MAIN as main_v4_production_run.py
    participant UNI as run_unified_pipeline.py
    participant SIG as Algo_signal_phase.py
    participant TRADE as modify_run_trading_to_accept_dataframe
    participant ENG as BacktestEngine
    participant OUT as v4_trace_outputs

    BAT->>MAIN: Execute
    MAIN->>UNI: run_unified_pipeline()
    UNI->>SIG: run_signal_phase()
    SIG-->>UNI: signals_df (DataFrame)
    UNI->>TRADE: run_trading_phase(signals_df)
    TRADE->>ENG: run_backtest_with_signals(signals_df)
    ENG-->>TRADE: results (dict)
    TRADE-->>UNI: results
    UNI->>OUT: Save timestamped outputs
    OUT-->>BAT: Complete
```

### Parameter Flow Diagram
```mermaid
graph TD
    A[settings_parameters_v4.ini] --> B[ConfigParser]
    B --> C[Type Conversion]
    C --> D[Parameter Dictionary]
    D --> E[settings_CPS_v4.py]
    E --> F[Module Imports]
    F --> G[backtest_v4.py]
    F --> H[portfolio_v4.py]
    F --> I[execution_v4.py]
    F --> J[data_loader_v4.py]
    F --> K[signal_generators]
```

## Current Production Workflow Details

### Key Files in test_optimization_fix_simple.bat Flow

#### Entry Point Files
- **test_optimization_fix_simple.bat**: Main batch file that activates Python environment and calls validation script
- **test_optimization_validation.py**: Python validation script that orchestrates the optimization testing process

#### Core Workflow Functions
- **test_optimization_validation()**: Main validation function that runs 10-step validation process
- **PerformanceTableGenerator.get_optimization_combinations()**: Generates parameter combinations for testing
- **PerformanceTableGenerator._validate_single_combination()**: Tests single parameter combination (STEP 3)
- **PerformanceTableGenerator._run_matrix_optimization()**: Runs full matrix optimization (STEP 4-10)

#### Refactored Reporting Functions
- **v4_performance_report.generate_performance_report()**: Main shell function (backward compatible)
- **report_excel._generate_excel_report()**: Excel report generation
- **report_metrics._calculate_performance_metrics()**: Performance calculations
- **report_validation.export_validation_files()**: Validation file exports
- **report_optimization.generate_optimization_report()**: Optimization reports

#### Output Locations
- **optimization_validation/[timestamp]/**: Validation artifacts and logs
- **reporting/**: Performance reports and equity curves
- **v4_trace_outputs/**: Legacy trace outputs (if enabled)

### Path Configuration (v4/config/paths_v4.py)
- **PROJECT_ROOT**: Base directory for all relative paths
- **OUTPUT_DIR**: reporting/ directory for performance reports
- **OPTIMIZATION_VALIDATION_DIR**: optimization_validation/ for validation artifacts
- **PY_REPORTING_DIR**: v4/py_reporting/ for Python reporting code

## Dependency Tables

### Core Dependencies
| Module | Internal Dependencies | External Dependencies | Purpose |
|--------|----------------------|----------------------|---------|
| settings_CPS_v4.py | None | configparser, pathlib | Configuration loading |
| backtest_v4.py | portfolio_v4, execution_v4, orders_v4 | pandas, numpy | Main engine |
| run_unified_pipeline.py | Algo_signal_phase, run_trading_phase | pandas, logging | Pipeline orchestration |
| data_loader_v4.py | settings_CPS_v4 | pandas, yfinance | Data acquisition |

### Module Size Compliance
| Module | Current Lines | Status | Notes |
|--------|---------------|--------|-------|
| settings_CPS_v4.py | ~380 | ✅ COMPLIANT | Under 450 line limit |
| backtest_v4.py | ~420 | ✅ COMPLIANT | Under 450 line limit |
| run_unified_pipeline.py | ~310 | ✅ COMPLIANT | Under 450 line limit |
| portfolio_v4.py | ~290 | ✅ COMPLIANT | Under 450 line limit |

### Critical Path Dependencies
```
settings_CPS_v4.py (CRITICAL - All modules depend on this)
    ↓
backtest_v4.py (CRITICAL - Main engine)
    ↓
portfolio_v4.py + execution_v4.py (CRITICAL - Core logic)
    ↓
reporting modules (Non-critical - Output only)
```

## Appendix

### Glossary
- **CPS V4**: Central Parameter System Version 4 - Configuration-driven parameter management
- **Unified Pipeline**: Optimized execution mode with direct DataFrame handoffs
- **Signal Phase**: Component responsible for generating allocation signals
- **Trading Phase**: Component responsible for portfolio management and trade execution
- **ComplexN Parameters**: Parameters with optimization attributes (min/max/step values)
- **Trace Directory**: Output directory (v4_trace_outputs) for all execution outputs
- **EMA**: Exponential Moving Average - Primary signal generation algorithm

### Key Configuration Files
- **settings_parameters_v4.ini**: Primary configuration file containing all system parameters
- **run_main_v4_unified.bat**: Primary execution entry point for production use
- **main_v4_production_run.py**: Python entry point called by batch file

### Output File Naming Convention
All output files follow the pattern: `{description}_{YYYYMMDD_HHMMSS}.{ext}`
- Example: `signals_output_20250118_143022.csv`
- Ensures temporal ordering and prevents overwrites
- Timestamp format: Year-Month-Day_Hour-Minute-Second

### Known Optimizations
1. **Direct DataFrame Handoff**: Eliminated CSV file generation between signal and trading phases
2. **Timestamped Outputs**: Single timestamped output files instead of duplicates
3. **Fallback Logic**: Auto-detection of most recent signal files for legacy mode
4. **Memory Management**: Smart logging and trace directory management
5. **Module Size Control**: All modules kept under 450 lines per project rules

### Testing Philosophy Notes
- All testing uses full production code flow (no synthetic test harnesses)
- Unit tests located in `tests/v4/` directory
- Signal generation and trading phases are decoupled for independent testing
- Production pipeline maintains backward compatibility with legacy modes

## Critical Fixes Applied (July 27, 2025)

### 1. Optimization Loop Bug (FIXED)
**Problem**: Pipeline was only running baseline iteration instead of actual parameter combinations
**Location**: `v4/pipeline/modes.py`
**Fix**: Replaced `generate_performance_table_from_pipeline_results()` call with direct `_run_matrix_optimization()` call

### 2. Section Mismatch Bug (FIXED)
**Problem**: Code reading parameters from wrong INI file sections causing CSV flag failures
**Location**: `v4/py_reporting/performance_table_generator.py` lines 56-59, 1849-1851
**Fix**: Implemented `ConfigHelper` class for section-agnostic parameter lookup

### 3. CSV Flag Propagation (FIXED)
**Problem**: Validation script creates `PerformanceTableGenerator(csv_flag_use=True)` but pipeline reads from settings file
**Location**: Parameter passing from validation to pipeline execution
**Fix**: Proper parameter propagation and flag checking

### 4. File Output Control (FIXED)
**Problem**: Prohibited files (allocation_history, trade_log) being created during optimization
**Location**: `v4/pipeline/trading.py` lines 142-169
**Fix**: Added CSV control flag checking before file creation

### 5. INI Syntax Error (FIXED)
**Problem**: `%` characters in date format causing ConfigParser interpolation errors
**Location**: `v4/settings/settings_parameters_v4.ini` line 289
**Fix**: Escaped `%` characters as `%%Y%%m%%d`

### Current Status
- All major architectural issues resolved
- Optimization loop now functional
- Configuration bugs eliminated
- Validation still failing at Step 3 - requires further investigation

---
*Document generated: 2025-07-27*
*Total lines: 430 (Under 450-line requirement)*
*Status: Critical fixes documented - validation debugging in progress*
