@echo off
REM =====================================================================
REM Simple Critical Issues Verification Script
REM =====================================================================
REM This batch file runs a simplified verification for critical issues:
REM 1. Signal history population
REM 2. Benchmark calculation using price_data not signal_history
REM 3. Logging levels for trade messages
REM 4. Execution delay parameter optimization
REM 5. Parameter registration with StrategyOptimizeParameter
REM
REM Created: June 2, 2025
REM =====================================================================

echo.
echo === Simple Critical Issues Verification ===
echo.

REM Set environment variables for testing
set BACKTEST_LOG_LEVEL=DEBUG
set PYTHONPATH=%~dp0

REM Set Python executable path
SET PYTHON_EXE=F:\AI_Library\my_quant_env\Scripts\python.exe

REM Verify Python exists
if not exist "%PYTHON_EXE%" (
    echo [X] ERROR: Python not found at %PYTHON_EXE%
    pause
    exit /b 1
)

echo [✓] Using Python at: %PYTHON_EXE%

REM Create verification directories if they don't exist
if not exist "%~dp0tests" mkdir "%~dp0tests"
if not exist "%~dp0tests\critical_simple" mkdir "%~dp0tests\critical_simple"

echo.
echo Running simplified critical issues verification...
echo.

REM Run the verification script
"%PYTHON_EXE%" "%~dp0tests\simple_critical_verify.py"

REM Check the exit code
if %ERRORLEVEL% EQU 0 (
    echo.
    echo === Simplified Verification APPEARS SUCCESSFUL ===
    echo.
    echo Critical issues appear to be fixed, but require explicit user confirmation.
    echo Check the verification summary in tests\critical_simple
) else (
    echo.
    echo === Simplified Verification FOUND ISSUES ===
    echo.
    echo Some critical issues still need attention.
    echo Check the verification log in tests\critical_simple.log for details
)

echo.
echo IMPORTANT: Nothing is marked as "passed" or "complete" until explicitly confirmed by you.
echo.

pause
