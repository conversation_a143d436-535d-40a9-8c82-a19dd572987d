# Design Document

## Overview

This design completes the refactoring of `performance_table_generator.py` by migrating the missing complex optimization functionality to the existing modular system. The approach creates a new `report_modules/report_matrix_optimization.py` module that handles matrix optimization, validation framework, and EquityCurvesManager integration while maintaining identical function signatures for seamless compatibility.

## Architecture

### Current State Analysis

**Existing Refactored System:**
- `v4_performance_report.py` (190 lines) - Main shell/wrapper
- `report_modules/report_excel.py` (358 lines) - Excel generation
- `report_modules/report_metrics.py` (300 lines) - Performance metrics
- `report_modules/report_validation.py` (344 lines) - Validation exports
- `report_modules/report_optimization.py` (307 lines) - Basic optimization reporting

**Missing Functionality (from performance_table_generator.py):**
- Matrix optimization engine (`get_optimization_combinations()`, `_run_matrix_optimization()`)
- Validation framework (`_validate_single_combination()`, 10-step validation)
- Subprocess management (`_run_pipeline_for_combination()`, `_create_temp_settings_for_combination()`)
- EquityCurvesManager integration
- Complex parameter parsing and temporary file management

### Target Architecture

```
v4_performance_report.py (Enhanced Shell)
├── report_modules/report_excel.py (Existing)
├── report_modules/report_metrics.py (Existing)  
├── report_modules/report_validation.py (Existing)
├── report_modules/report_optimization.py (Existing)
└── report_modules/report_matrix_optimization.py (NEW)
    ├── Matrix Optimization Engine
    ├── 10-Step Validation Framework
    ├── Subprocess Management
    ├── EquityCurvesManager Integration
    └── Parameter Combination Processing
```

## Components and Interfaces

### New Module: report_matrix_optimization.py

**Core Functions (Identical Signatures):**
```python
def get_optimization_combinations(config_path=None) -> List[Dict]
def _run_matrix_optimization(combinations: List[Dict]) -> Tuple[pd.DataFrame, Dict]
def _validate_single_combination(combination: Dict) -> bool
def _run_pipeline_for_combination(combination: Dict) -> pd.Series
def _create_temp_settings_for_combination(combination: Dict) -> str
def _load_unified_portfolio_for_combination(combo_id: str, timestamp: str) -> pd.Series
```

**Supporting Functions:**
```python
def _setup_validation_directories() -> None
def _log_validation_step(step_number: int, step_name: str, status: str) -> None
def generate_combo_id(combination_params: Dict) -> str
def _cleanup_temp_settings(temp_settings_path: str) -> None
```

### Enhanced v4_performance_report.py

**New Integration Points:**
```python
from .report_modules.report_matrix_optimization import (
    get_optimization_combinations,
    _run_matrix_optimization,
    _validate_single_combination,
    _run_pipeline_for_combination
)

class PerformanceReportGenerator:
    def __init__(self, config_path=None, csv_flag_use=False):
        # Initialize with optimization capabilities
        self.optimization_module = report_matrix_optimization
        
    # Expose optimization functions as class methods
    def get_optimization_combinations(self):
        return self.optimization_module.get_optimization_combinations(self.config_path)
        
    def _validate_single_combination(self, combination):
        return self.optimization_module._validate_single_combination(combination)
        
    def _run_matrix_optimization(self, combinations):
        return self.optimization_module._run_matrix_optimization(combinations)
```

### Pipeline Integration Points

**v4/pipeline/config.py Enhancement:**
```python
def determine_pipeline_mode(custom_settings_file=None):
    # Check environment variables FIRST
    combo_id = os.environ.get('CPS_V4_COMBO_ID')
    if combo_id:
        return 'single'  # Force single mode for individual combinations
    
    # Then check INI file settings
    config = load_config(custom_settings_file)
    optimization_active = config.getboolean('System', 'optimization_active', fallback=False)
    
    return 'optimization' if optimization_active else 'single'
```

**v4/pipeline/modes.py Enhancement:**
```python
def _create_unified_portfolio_data(results, timestamp):
    # Check for combination ID in environment
    combo_id = os.environ.get('CPS_V4_COMBO_ID')
    
    if combo_id:
        # Create combination-specific file
        filename = f"unified_portfolio_combo_{combo_id}_{timestamp}.csv"
    else:
        # Create standard file
        filename = f"unified_portfolio_{timestamp}.csv"
    
    # Save unified portfolio data with appropriate filename
    filepath = OUTPUT_DIR / filename
    unified_df.to_csv(filepath)
```

## Data Models

### Optimization Combination Structure
```python
{
    'st_lookback': int,      # Short-term lookback period
    'mt_lookback': int,      # Medium-term lookback period  
    'lt_lookback': int,      # Long-term lookback period (if used)
    'top_n': int,           # Number of top assets to select
    'execution_delay': int   # Execution delay in days
}
```

### Validation Step Structure
```python
{
    'step': int,                    # Step number (1-10)
    'status': str,                  # 'RUNNING', 'COMPLETED', 'FAILED'
    'timestamp': str,               # ISO timestamp
    'combination': Dict,            # Parameter combination
    'equity_curve_length': int,     # Number of data points
    'first_value': float,           # Starting portfolio value
    'last_value': float,            # Ending portfolio value
    'return_code': int,             # Subprocess return code
    'error': Optional[str]          # Error message if failed
}
```

### EquityCurvesManager Integration
```python
# Direct import and usage - no wrapper complexity
from v4.py_reporting.equity_curves_manager import EquityCurvesManager

def _run_matrix_optimization(combinations):
    equity_manager = EquityCurvesManager(output_dir="reporting")
    
    for combo in combinations:
        equity_curve = _run_pipeline_for_combination(combo)
        column_name = equity_manager.add_combination_result(combo, equity_curve)
    
    return equity_manager.save_to_disk()
```

## Error Handling

### Subprocess Error Management
```python
def _run_pipeline_for_combination(combination):
    try:
        # Create temp settings and run subprocess
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=600)
        
        if result.returncode != 0:
            logger.error(f"Subprocess failed: return code {result.returncode}")
            logger.error(f"STDOUT: {result.stdout}")
            logger.error(f"STDERR: {result.stderr}")
            raise RuntimeError(f"Pipeline failed for combo {combination}")
            
    except subprocess.TimeoutExpired:
        logger.error(f"Subprocess timeout for combination {combination}")
        raise
    except Exception as e:
        logger.error(f"Subprocess execution failed: {e}")
        raise
```

### Validation Error Recovery
```python
def _validate_single_combination(combination):
    try:
        equity_curve = _run_pipeline_for_combination(combination)
        if equity_curve is not None and len(equity_curve) > 0:
            _log_validation_step(3, "Single Combination Test", "COMPLETED")
            return True
        else:
            _log_validation_step(3, "Single Combination Test", "FAILED", "No equity curve returned")
            return False
    except Exception as e:
        _log_validation_step(3, "Single Combination Test", "FAILED", str(e))
        return False
```

## Testing Strategy

### Unit Testing Approach
1. **Module Isolation**: Test each function in `report_matrix_optimization.py` independently
2. **Mock Subprocess**: Use mock subprocess calls for testing parameter combination processing
3. **File System Testing**: Test temporary file creation and cleanup
4. **EquityCurvesManager Integration**: Test matrix operations with sample data

### Integration Testing
1. **End-to-End Validation**: Run `test_optimization_validation.py` through refactored system
2. **Comparison Testing**: Compare outputs between original and refactored systems
3. **Performance Testing**: Verify no performance degradation in optimization loops

### Validation Testing
1. **Step-by-Step Verification**: Ensure all 10 validation steps complete successfully
2. **Parameter Combination Testing**: Verify all 12 combinations generate unique equity curves
3. **File Output Testing**: Confirm correct file naming and content generation

## Implementation Plan

### Phase 1: Create New Module Structure
1. Create `report_modules/report_matrix_optimization.py`
2. Extract and migrate optimization functions from `performance_table_generator.py`
3. Maintain identical function signatures and behavior
4. Add proper imports for EquityCurvesManager

### Phase 2: Enhance v4_performance_report.py
1. Import new optimization module functions
2. Expose functions as class methods for backward compatibility
3. Ensure `test_optimization_validation.py` can call through refactored interface
4. Add initialization parameters for optimization mode

### Phase 3: Fix Pipeline Integration
1. Update `v4/pipeline/config.py` to check environment variables first
2. Modify `v4/pipeline/modes.py` to create combination-specific output files
3. Ensure subprocess inherits proper context and environment variables
4. Add logging for debugging subprocess execution

### Phase 4: Testing and Validation
1. Run comprehensive testing suite
2. Execute `test_optimization_validation.py` to verify Step 3 completion
3. Validate all 12 parameter combinations process correctly
4. Confirm unique equity curves are generated

### Phase 5: Documentation and Cleanup
1. Update documentation to reflect completed refactoring
2. Archive `performance_table_generator.py` after verification
3. Update import statements in any remaining references
4. Create migration guide for any external dependencies

This design ensures maximum simplicity by building on existing refactored modules while maintaining complete backward compatibility and functionality.