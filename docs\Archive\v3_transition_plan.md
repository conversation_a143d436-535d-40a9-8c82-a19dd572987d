# V3 Engine Transition Plan

## Objective

Establish a robust, extensible parameter system for the next-generation (v3) backtest engine, addressing:

1) Support plug and play, controlled by user, of new independent strategy modules and ticker lists, such that the variables and parameter flow through all steps seemlessly

2) Allow all designated variables to be optimizable, via the GUI, and step through lists or parameter increments, and output the results of each loop or step to the reporting

3) No more problems of AI confusion regarding how to convert formats / classes of variables from each stage → GUI → engine → reporting
- Consistent parameter flow from definition → GUI → engine → reporting
- Numeric and categorical (list-based) parameter types
- Seamless optimization and auto-inclusion of new parameters
- User-maintained configuration for lists (e.g., ticker groups)
- Strong type safety and validation across all boundaries
- Modular, pluggable strategies with self-contained parameter definitions

---

## Table of Contents

1. [Summary Table: Modules & Classes](#summary-table-modules--classes)
2. [Parameter System Overview](#parameter-system-overview)
3. [Code Structure & Class Outline](#code-structure--class-outline)
4. [Parameter Flow Documentation](#parameter-flow-documentation)
5. [Configuration File Examples](#configuration-file-examples)
6. [Implementation Steps](#implementation-steps)
7. [Backward Compatibility](#backward-compatibility)
8. [Review & Clarifying Questions](#review--clarifying-questions)
9. [Next Steps](#next-steps)

---

## Summary Table: Modules & Classes

| Module/Class                     | Purpose/Role                                                          |
| -------------------------------- | --------------------------------------------------------------------- |
| `parameters.py`                  | Defines all parameter classes (Numeric, Categorical, CategoricalList) |
| `parameter_registry.py`          | Central registry for all parameters, grouped by core/strategy         |
| `strategy_parameter_set.py`      | Encapsulates parameters for each strategy, supports plug-and-play     |
| `gui_parameter_manager.py`       | Converts parameter definitions for GUI controls                       |
| `parameter_optimizer.py`         | Handles optimization loops for all parameter types                    |
| `performance_reporter.py`        | Generates performance tables using actual parameter values            |
| `config/ticker_lists.py`         | User-maintained ticker group lists                                    |
| `conversion_utils.py` (optional) | Utilities for migrating legacy config formats                         |

---

## Parameter System Overview

### Parameter Classes

- **NumericParameter**: For values with default, min, max, step, and optimization flag. Used for lookbacks, execution delays, thresholds, etc.
- **CategoricalParameter**: For single-choice options (e.g., strategy algorithm, rebalance frequency). Choices defined in config or code.
- **CategoricalListParameter**: For user-maintained lists (e.g., ticker groups). Reads from config file (e.g., `config/ticker_lists.py`). All list maintenance is external and version-controlled.

### Parameter Registry

- Central registry for all parameters
- Groups: 'core' (engine), 'strategy_<name>' (per-strategy), 'all'
- Used by GUI, engine, and reporting

### Strategy Parameter Sets

- Each strategy registers its own swap-out parameters (lookbacks, rules, etc.)
- Core parameters remain in the engine
- When a new strategy is selected, its parameters are auto-included in GUI and outputs

### GUI Integration

- GUI queries registry for parameter definitions
- Numeric: slider/spinbox with min/max/step
- Categorical: dropdown/select
- CategoricalList: dropdown for group name (e.g., 'tech', 'finance'), with group content shown in tooltip or info panel
- All optimization flags are surfaced as checkboxes
- When strategy changes, GUI reloads relevant parameters

### Optimization Loop

- Registry provides all combinations for parameters marked optimize=True
- Categorical and CategoricalList parameters are looped just like numerics
- For CategoricalList (e.g., ticker_group), each group is a separate optimization run
- Engine receives only primitive values (not tuples)

### Performance Table

- Each optimization run's parameters (numeric, categorical, list) are recorded as columns
- No tuples or config objects in output—only actual values used
- Table is always in sync with optimization loop and GUI

---

## Code Structure & Class Outline

```
/v3_engine/
    parameters.py
        - class NumericParameter
        - class CategoricalParameter
        - class CategoricalListParameter
    parameter_registry.py
        - class ParameterRegistry
    strategy_parameter_set.py
        - class StrategyParameterSet
    gui_parameter_manager.py
        - class GuiParameterManager
    parameter_optimizer.py
        - class ParameterOptimizer
    performance_reporter.py
        - class PerformanceReporter
    conversion_utils.py (optional)
        - functions for legacy config migration
/config/
    ticker_lists.py
        - TICKER_LISTS = { 'tech': [...], 'finance': [...] }
/docs/
    v3_transition_plan.md (this file)
    parameter_system_usage.md (examples, templates)
```

---

## Parameter Flow Documentation

1. **Definition**: Parameters are defined in strategy or core engine modules, registered with the `ParameterRegistry`.
2. **GUI Integration**: GUI queries the registry to auto-generate controls for all parameters (numeric: sliders, categorical: dropdowns, lists: dropdowns with tooltips).
3. **Engine Usage**: Selected/optimized parameter values are passed to the backtest engine as primitives.
4. **Reporting**: Performance tables and logs display only the actual values used, not parameter objects or tuples.
5. **Extensibility**: Adding a new strategy only requires defining its parameter set and registering it.

---

## Configuration File Examples

**Ticker List Example (`config/ticker_lists.py`):**

```python
TICKER_LISTS = {
    'tech': ['AAPL', 'MSFT', 'GOOGL'],
    'finance': ['JPM', 'BAC'],
    'energy': ['XOM', 'CVX']
}
```

**Numeric Parameter Example:**

```python
class NumericParameter:
    """
    Parameter for numeric values (int or float) with optimization support.
    Enforces bounds and step size. Used for lookbacks, thresholds, execution delays, etc.
    """
    def __init__(self, name, default, min_value, max_value, step, optimize=False):
        """
        Args:
            name (str): Name of the parameter (e.g., 'lookback')
            default (int|float): Default value
            min_value (int|float): Minimum allowed value
            max_value (int|float): Maximum allowed value
            step (int|float): Step size for optimization
            optimize (bool): Whether to optimize this parameter
        """
        self.name = name
        self.default = default
        self.min_value = min_value
        self.max_value = max_value
        self.step = step
        self.optimize = optimize
        self._validate()

    def get_range(self):
        """
        Returns a list of values from min to max using the step, for optimization.
        """
        if isinstance(self.default, int):
            return list(range(self.min_value, self.max_value + 1, self.step))
        else:
            vals = []
            val = self.min_value
            while val <= self.max_value:
                vals.append(round(val, 8))
                val += self.step
            return vals

    def _validate(self):
        """
        Validates parameter bounds and step.
        """
        if not (self.min_value <= self.default <= self.max_value):
            raise ValueError(f"Default {self.default} not in range [{self.min_value}, {self.max_value}] for '{self.name}'")
        if self.step <= 0:
            raise ValueError(f"Step must be positive for '{self.name}'")

# Example usage:
lookback = NumericParameter(
    name='lookback', default=20, min_value=5, max_value=100, step=5, optimize=True
)
# To get the optimization range:
lookback_range = lookback.get_range()
```

**Categorical Parameter Example:**

```python
class CategoricalParameter:
    """
    Parameter for categorical values (single-choice from a fixed list).
    Used for options like rebalance frequency, strategy type, etc.
    """
    def __init__(self, name, choices, default, optimize=False):
        """
        Args:
            name (str): Name of the parameter (e.g., 'rebalance_freq')
            choices (list): List of valid choices (e.g., ['monthly', 'quarterly', 'yearly'])
            default: Default choice (must be in choices)
            optimize (bool): Whether to optimize over all choices
        """
        self.name = name
        self.choices = choices
        self.default = default
        self.optimize = optimize
        self._validate()

    def _validate(self):
        """
        Validates that the default is in the list of choices and choices is not empty.
        """
        if not self.choices or not isinstance(self.choices, list):
            raise ValueError(f"Choices must be a non-empty list for '{self.name}'")
        if self.default not in self.choices:
            raise ValueError(f"Default '{self.default}' not in choices {self.choices} for '{self.name}'")

# Example usage:
rebalance_freq = CategoricalParameter(
    name='rebalance_freq', choices=['monthly', 'quarterly', 'yearly'], default='monthly', optimize=True
)
# To get all possible choices:
choices = rebalance_freq.choices
```

**CategoricalList Parameter Example:**

```python
class CategoricalListParameter(CategoricalParameter):
    """
    Parameter for selecting a user-defined group from an external config file (e.g., ticker groups).
    Loads available choices at runtime and validates the selected group.
    All group lists must be maintained externally (e.g., in config/ticker_lists.py) and version-controlled.
    """
    def __init__(self, name, list_name, config_file, optimize=False):
        """
        Args:
            name (str): Name of the parameter (e.g., 'ticker_group')
            list_name (str): Name of the group to use as default (e.g., 'tech')
            config_file (str): Path to the config file containing group definitions
            optimize (bool): Whether to optimize over all groups
        """
        # Load available groups from config file
        choices = self._load_choices(config_file)
        if list_name not in choices:
            raise ValueError(f"List name '{list_name}' not found in config file {config_file}.")
        super().__init__(name, choices=list(choices.keys()), default=list_name, optimize=optimize)
        self.list_name = list_name
        self.config_file = config_file
        self.choices_dict = choices  # Dict of group_name -> list of items

    def _load_choices(self, config_file):
        """
        Loads group definitions from the given config file.
        Expects the file to define a dict variable (e.g., TICKER_LISTS).
        Returns:
            dict: group_name -> list of items
        """
        import importlib.util
        import os
        config_path = os.path.abspath(config_file)
        module_name = os.path.splitext(os.path.basename(config_file))[0]
        spec = importlib.util.spec_from_file_location(module_name, config_path)
        module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(module)
        # Find the first dict in the module (e.g., TICKER_LISTS)
        for attr in dir(module):
            value = getattr(module, attr)
            if isinstance(value, dict):
                return value
        raise ValueError(f"No dict found in config file {config_file}.")

    def get_group_items(self):
        """
        Returns the list of items for the currently selected group (self.list_name).
        """
        return self.choices_dict[self.list_name]

    def validate(self):
        """
        Validates that the current list_name is still present in the config file.
        """
        self.choices_dict = self._load_choices(self.config_file)
        if self.list_name not in self.choices_dict:
            raise ValueError(f"List name '{self.list_name}' not found in config file {self.config_file}.")

# Example usage:
ticker_group = CategoricalListParameter(
    name='ticker_group', list_name='tech', config_file='config/ticker_lists.py', optimize=True
)
# To get the tickers in the selected group:
tickers = ticker_group.get_group_items()
```

---

## Implementation Steps

1. Implement new parameter classes and registry
2. Refactor core engine and strategies to register and use new parameters
3. Refactor GUI to auto-generate controls from registry
4. Refactor optimization and reporting to use registry for all loops and outputs
5. Migrate all config files to new documented formats
6. Test with multiple strategies, ticker groups, and optimization runs

---

## Backward Compatibility

- Provide conversion utilities to translate v2 config tuples/lists to v3 parameter system
- Legacy configs can be imported and registered on startup

---

## Review & Clarifying Questions

**For Reviewers and Implementers:**

- Are there any protected files or modules that should not be changed during this transition?
- Should the parameter classes and registry be placed in a new `/v3_engine/` folder, or do you have a preferred location?
- Should conversion utilities for legacy configs be included in this initial phase?
- Is there a preferred location or framework for unit tests?
- Should the GUI parameter manager be a new module, or integrated into an existing system?
- Where should documentation and config templates be placed for easy access by users?
- Are there any additional constraints or requirements not captured here?
- Is the outlined code structure compatible with your current environment and workflow?
- Are there any sections or functions that must be marked as "WORKING ONLY Change with Permission"?
- Should all new config files (e.g., ticker lists) be version-controlled and externally maintained?

**General Questions:**

- Are all parameter types and flows clearly documented?
- Is the registry pattern sufficient for future extensibility?
- Are all optimization and reporting requirements met by this architecture?
- Is the migration path for legacy configs clear and actionable?

---

## Next Steps

- After this plan is reviewed and confirmed, reset context and proceed to implementation phase
- All new code and docs to be placed in `/v3_engine` and `/docs` folders

---

*Prepared for V3 engine transition. Review, answer questions, and confirm before proceeding to code.*
