#!/usr/bin/env python
"""
Test script to verify optimization parameter fix works correctly
"""

from v4.py_reporting.v4_performance_report import PerformanceTableGenerator
import pandas as pd
from v4.engine.data_loader_v4 import load_data_for_backtest
from v4.settings.settings_CPS_v4 import load_settings

def test_optimization_fix():
    print("Testing CPS V4 Optimization Fix...")
    print("=" * 50)
    
    # Load base settings and data
    settings = load_settings()
    ptg = PerformanceTableGenerator()
    all_combinations = ptg.get_optimization_combinations()

    print(f'Found {len(all_combinations)} total combinations')
    print('First 5 combinations:')
    for i, combo in enumerate(all_combinations[:5]):
        st = combo.get('st_lookback', 'N/A')
        mt = combo.get('mt_lookback', 'N/A')
        print(f'  {i}: st_lookback={st}, mt_lookback={mt}')

    # Test only first 3 combinations for speed
    combinations = all_combinations[:3]
    print(f'\nTesting {len(combinations)} combinations for verification:')
    
    # Load data and setup dummy allocation for compatibility
    data_result = load_data_for_backtest(settings)
    price_data = data_result['price_data']
    dummy_allocation = pd.DataFrame(index=price_data.index)
    for col in price_data.columns:
        dummy_allocation[col] = 0.0
    dummy_allocation['Cash'] = 1.0
    ptg.allocation_df = dummy_allocation
    
    # Test matrix optimization with only 3 combinations
    print('\nRunning matrix optimization...')
    equity_matrix, metadata = ptg._run_matrix_optimization(combinations)
    
    print(f'\nResult matrix shape: {equity_matrix.shape}')
    print('Final values per combination:')
    for col in equity_matrix.columns:
        final_value = equity_matrix[col].iloc[-1]
        print(f'  {col}: ${final_value:,.2f}')
    
    # Check if values are different
    final_values = [equity_matrix[col].iloc[-1] for col in equity_matrix.columns]
    unique_values = set(final_values)
    
    print(f'\nUnique final values: {len(unique_values)}')
    if len(unique_values) > 1:
        print('✓ SUCCESS: Different final values detected - parameters are working!')
        return True
    else:
        print('✗ FAILURE: All final values are identical - parameters not working')
        return False

if __name__ == "__main__":
    success = test_optimization_fix()
    exit(0 if success else 1)
