# filename: utils/trade_log_v4.py
"""
Trade logging utilities for backtesting strategies.
"""

import pandas as pd
import numpy as np
import os
import sys
from pathlib import Path
import logging
from datetime import date
from v4.config.paths_v4 import *
from v4.config.transaction_log_spec_v4 import TRADE_LOG_COLUMNS, TRADE_LOG_TYPES, LEGACY_COLUMN_MAP

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Add Custom Function Library path
parent_dir = Path(__file__).resolve().parent.parent
if CUSTOM_LIB_PATH not in sys.path:
    sys.path.append(CUSTOM_LIB_PATH)


class TradeLogger:
    """
    Class for creating and managing trade logs.
    """
    
    def __init__(self):
        """Initialize TradeLogger."""
        pass
    
    def create_trade_log_from_trades(self, trades, price_data, initial_capital, transaction_cost):
        """
        Create trade log from trades DataFrame and price data.
        
        Args:
            trades (DataFrame): DataFrame with trades (from calculate_trades)
            price_data (DataFrame): DataFrame with price data
            initial_capital (float): Initial capital
            transaction_cost (float): Transaction cost as fraction of trade value
            
        Returns:
            DataFrame: Trade log with detailed trade information
        """
        if trades.empty:
            return pd.DataFrame(columns=TRADE_LOG_COLUMNS)

        # Convert legacy columns to new format
        trades = trades.rename(columns=LEGACY_COLUMN_MAP)

        trade_log = []
        last_buy_price = {}  # For profit/loss calculation
        last_buy_shares = {}

        for date in sorted(trades.index):
            if date not in price_data.index:
                continue
            prices = price_data.loc[date]

            for asset in trades.columns:
                trade_size = trades.loc[date, asset]
                # Skip very small or zero trades
                if abs(trade_size) < 0.0001:
                    continue
                if asset not in prices or pd.isna(prices[asset]):
                    logger.warning(f"Missing price for {asset} on {date}, skipping trade")
                    continue
                price = prices[asset]
                # Trade Amount: allocation fraction * initial_capital
                trade_amount = abs(trade_size) * initial_capital
                shares = trade_amount / price
                transaction_fee = trade_amount * transaction_cost
                action = 'BUY' if trade_size > 0 else 'SELL'
                profit_loss = None
                if action == 'SELL' and asset in last_buy_price and asset in last_buy_shares:
                    # Calculate P/L for closed position
                    profit_loss = (price - last_buy_price[asset]) * min(shares, last_buy_shares[asset])
                    # Reduce last_buy_shares for partial sells
                    last_buy_shares[asset] -= min(shares, last_buy_shares[asset])
                    if last_buy_shares[asset] <= 0:
                        del last_buy_price[asset]
                        del last_buy_shares[asset]
                if action == 'BUY':
                    last_buy_price[asset] = price
                    last_buy_shares[asset] = last_buy_shares.get(asset, 0) + shares
                trade_log.append({
                    'date': date,
                    'asset': asset,
                    'action': action,
                    'shares': shares,
                    'price': price,
                    'amount': trade_amount,
                    'transaction_fee': transaction_fee,
                    'profit_loss': profit_loss
                })

        df = pd.DataFrame(trade_log)
        # Ensure all required columns exist
        for col in TRADE_LOG_COLUMNS:
            if col not in df.columns:
                df[col] = pd.NA
        # Add profit_loss if not in TRADE_LOG_COLUMNS
        if 'profit_loss' not in TRADE_LOG_COLUMNS:
            df['profit_loss'] = df.get('profit_loss', pd.NA)
        return df
    
    def save_trade_log(self, trade_log, output_path):
        """
        Save trade log to CSV file in the required format.
        Args:
            trade_log (DataFrame): DataFrame with trade log
            output_path (str): Path to save the file
        Returns:
            str: Path to the saved file
        """
        # Create directory if it doesn't exist
        os.makedirs(os.path.dirname(output_path), exist_ok=True)

        # Output columns required by user
        required_cols = [
            'Date', 'Ticker', 'Buy or Sell', 'Trade Price', '# of Shares', 'Total $s', 'Profit/loss'
        ]
        # Map internal column names to output names
        column_map = {
            'date': 'Date',
            'asset': 'Ticker',
            'action': 'Buy or Sell',
            'price': 'Trade Price',
            'shares': '# of Shares',
            'amount': 'Total $s',
            'profit_loss': 'Profit/loss'
        }
        # Rename columns for output
        df = trade_log.rename(columns=column_map)
        # Ensure all required columns exist
        for col in required_cols:
            if col not in df.columns:
                df[col] = ''
        # Format columns
        if 'Date' in df.columns:
            df['Date'] = pd.to_datetime(df['Date']).dt.strftime('%Y-%m-%d')
        if 'Trade Price' in df.columns:
            df['Trade Price'] = pd.to_numeric(df['Trade Price'], errors='coerce').map(lambda x: f'{x:,.2f}' if pd.notnull(x) else '')
        if 'Total $s' in df.columns:
            df['Total $s'] = pd.to_numeric(df['Total $s'], errors='coerce').map(lambda x: f'{x:,.2f}' if pd.notnull(x) else '')
        if '# of Shares' in df.columns:
            df['# of Shares'] = pd.to_numeric(df['# of Shares'], errors='coerce').map(lambda x: f'{x:,.4f}' if pd.notnull(x) else '')
        if 'Profit/loss' in df.columns:
            df['Profit/loss'] = pd.to_numeric(df['Profit/loss'], errors='coerce').map(lambda x: f'{x:,.2f}' if pd.notnull(x) else '')
        # Reorder and select only required columns
        df_out = df[required_cols]
        # Save trade log
        df_out.to_csv(output_path, index=False)
        logger.info(f"Trade log saved to {output_path}")
        return output_path
    
    def calculate_trade_statistics(self, trade_log):
        """
        Calculate statistics about trades.
        
        Args:
            trade_log (DataFrame): DataFrame with trade log
            
        Returns:
            dict: Dictionary with trade statistics
        """
        if trade_log.empty:
            return {
                'total_trades': 0,
                'buy_trades': 0,
                'sell_trades': 0,
                'total_amount': 0,
                'total_fees': 0
            }
        
        stats = {
            'total_trades': len(trade_log),
            'buy_trades': len(trade_log[trade_log['action'] == 'BUY']),
            'sell_trades': len(trade_log[trade_log['action'] == 'SELL']),
            'total_amount': trade_log['amount'].sum(),
            'total_fees': trade_log['transaction_fee'].sum()
        }
        
        # Calculate per-asset statistics
        assets = trade_log['asset'].unique()
        asset_stats = {}
        
        for asset in assets:
            asset_trades = trade_log[trade_log['asset'] == asset]
            asset_stats[asset] = {
                'trades': len(asset_trades),
                'buy_trades': len(asset_trades[asset_trades['action'] == 'BUY']),
                'sell_trades': len(asset_trades[asset_trades['action'] == 'SELL']),
                'total_amount': asset_trades['amount'].sum(),
                'total_fees': asset_trades['transaction_fee'].sum()
            }
        
        stats['per_asset'] = asset_stats
        
        return stats
    
    def save_detailed_trade_log(self, trade_log, output_dir, filename_prefix=''):
        """
        Save a detailed trade log to Excel file with proper formatting.
        
        Args:
            trade_log (DataFrame): DataFrame with trade log
            output_dir (str): Directory to save the file
            filename_prefix (str): Optional prefix for the filename
            
        Returns:
            str: Path to the saved file
        """
        # Create directory if it doesn't exist
        os.makedirs(output_dir, exist_ok=True)
        
        # Generate filename with timestamp
        timestamp = pd.Timestamp.now().strftime('%Y-%m-%d_%H%M%S')
        if filename_prefix:
            filename = f"{filename_prefix}_trade_log_{timestamp}.xlsx"
        else:
            filename = f"trade_log_{timestamp}.xlsx"
        
        output_path = os.path.join(output_dir, filename)
        
        # Format the trade log for better readability
        if not trade_log.empty:
            # Create a copy to avoid modifying the original
            formatted_log = trade_log.copy()
            
            # Format date column if it exists
            if 'date' in formatted_log.columns:
                formatted_log['date'] = pd.to_datetime(formatted_log['date']).dt.strftime('%Y-%m-%d')
            
            # Format numeric columns
            if 'price' in formatted_log.columns:
                formatted_log['price'] = formatted_log['price'].map('${:,.2f}'.format)
            if 'amount' in formatted_log.columns:
                formatted_log['amount'] = formatted_log['amount'].map('${:,.2f}'.format)
            if 'transaction_fee' in formatted_log.columns:
                formatted_log['transaction_fee'] = formatted_log['transaction_fee'].map('${:,.2f}'.format)
            if 'shares' in formatted_log.columns:
                formatted_log['shares'] = formatted_log['shares'].map('{:,.4f}'.format)
            
            # Save to Excel with formatting
            try:
                # Try to use ExcelWriter for better formatting
                with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
                    formatted_log.to_excel(writer, sheet_name='Transactions', index=False)
                    
                    # Get the workbook and the worksheet
                    workbook = writer.book
                    worksheet = writer.sheets['Transactions']
                    
                    # Auto-adjust column widths
                    for i, col in enumerate(formatted_log.columns):
                        max_length = max(
                            formatted_log[col].astype(str).map(len).max(),
                            len(col)
                        ) + 2
                        # Excel has a max column width of 255
                        worksheet.column_dimensions[chr(65 + i)].width = min(max_length, 40)
            except Exception as e:
                # Fall back to simple save if ExcelWriter fails
                logger.warning(f"Excel formatting failed, saving without formatting: {e}")
                trade_log.to_excel(output_path, sheet_name='Transactions', index=False)
        else:
            # If trade log is empty, save an empty DataFrame
            pd.DataFrame(columns=[
                'date', 'asset', 'action', 'shares', 'price', 'amount', 'transaction_fee'
            ]).to_excel(output_path, index=False)
            
        logger.info(f"Detailed trade log saved to {output_path}")
        return output_path
