@echo on
echo =====================================================
echo OPTIMIZATION FIX SIMPLE TEST - %date% %time%
echo =====================================================

REM Set up virtual environment path
set VENV_PATH=F:\AI_Library\my_quant_env

REM Activate virtual environment
echo Activating virtual environment at %VENV_PATH%...
call %VENV_PATH%\Scripts\activate.bat
if %errorlevel% neq 0 (
    echo ERROR: Failed to activate virtual environment! Error code: %errorlevel%
    goto :error
)

echo.
echo Python environment diagnostics:
echo -------------------------------
echo PATH = %PATH%
echo.
where python
echo.
python --version
echo.

echo.
echo ------------------------------------------------------
echo Running optimization parameter fix validation test...
echo ------------------------------------------------------
echo.

REM Run the optimization fix test
echo.
echo Running: python tests\v4\test_optimization_fix.py
echo.
python tests\v4\test_optimization_fix.py
echo.
echo Python script exit code: %errorlevel%
if %errorlevel% neq 0 (
    echo ERROR: Optimization test failed with exit code: %errorlevel%
    goto :error
)

echo.
echo ------------------------------------------------------
echo Optimization fix test execution complete!
echo ------------------------------------------------------

goto :end

:error
echo.
echo ** AN ERROR OCCURRED - See details above **
echo Check optimization_validation directory for artifacts...

:end
echo.
echo Press any key to close this window...
pause > nul
