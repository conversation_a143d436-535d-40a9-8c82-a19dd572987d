#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
v4/py_reporting/report_excel.py

Excel Report Generation Module for CPS V4 Performance Reporting

This module contains all Excel-related functionality extracted from v4_performance_report.py:
- Excel report generation for single strategy reports
- Excel report generation for optimization reports  
- Excel report verification and validation
- Excel file structure and formatting utilities

Functions included:
- _generate_excel_report(): Creates Excel reports for single strategy backtests
- _generate_optimization_excel(): Creates Excel reports for optimization results
- verify_excel_report(): Validates Excel report structure and content
- _write_simple_output(): Utility for writing DataFrames to multiple formats

Author: AI Assistant
Date: 2025-07-26 (Refactored from v4_performance_report.py)
"""

import os
import sys
import logging
import datetime
import pandas as pd
import openpyxl
from pathlib import Path
from typing import Dict, List, Tuple, Any, Optional
from openpyxl.styles import Font, Alignment, PatternFill
from openpyxl.utils.dataframe import dataframe_to_rows

# Set up logging
logger = logging.getLogger(__name__)

# Standard sheet names for the performance report
REQUIRED_SHEETS = [
    'Signal History',
    'Allocation History', 
    'Trade Log',
    'Performance'
]


def _write_simple_output(df: pd.DataFrame, base_path: Path, sheet_name: str) -> None:
    """Write DataFrame to both CSV and TSV formats for easier debugging.
    
    Args:
        df: DataFrame to write
        base_path: Base path for output files
        sheet_name: Name to use in filename
    """
    try:
        # Clean sheet name for filename
        clean_name = sheet_name.replace(' ', '_').lower()
        
        # Write CSV
        csv_path = base_path.parent / f"{base_path.stem}_{clean_name}.csv"
        df.to_csv(csv_path, index=False)
        
        # Write TSV for Excel compatibility
        tsv_path = base_path.parent / f"{base_path.stem}_{clean_name}.tsv"
        df.to_csv(tsv_path, sep='\t', index=False)
        
        logger.debug(f"Wrote {sheet_name} to {csv_path} and {tsv_path}")
        
    except Exception as e:
        logger.warning(f"Failed to write simple output for {sheet_name}: {e}")


def _generate_excel_report(
    backtest_results: Dict[str, Any],
    output_path: Path,
    strategy_name: str,
    settings: Dict[str, Any]
) -> str:
    """
    Generate Excel report from backtest results.
    
    Args:
        backtest_results: Results dictionary from backtest
        output_path: Path where to save the Excel file
        strategy_name: Name of the strategy
        settings: Settings dictionary
        
    Returns:
        str: Path to the generated Excel file
    """
    try:
        logger.info(f"Generating Excel report: {output_path}")
        
        # Create workbook
        workbook = openpyxl.Workbook()
        
        # Remove default sheet
        if 'Sheet' in workbook.sheetnames:
            workbook.remove(workbook['Sheet'])
        
        # Create sheets in order
        for sheet_name in REQUIRED_SHEETS:
            workbook.create_sheet(sheet_name)
        
        # Populate Signal History sheet
        if 'signal_history' in backtest_results:
            _populate_signal_history_sheet(workbook['Signal History'], backtest_results['signal_history'])
        
        # Populate Allocation History sheet  
        if 'allocation_history' in backtest_results:
            _populate_allocation_history_sheet(workbook['Allocation History'], backtest_results['allocation_history'])
        
        # Populate Trade Log sheet
        if 'trades' in backtest_results:
            _populate_trade_log_sheet(workbook['Trade Log'], backtest_results['trades'])
        
        # Populate Performance sheet
        _populate_performance_sheet(workbook['Performance'], backtest_results, strategy_name, settings)
        
        # Save workbook
        workbook.save(output_path)
        logger.info(f"Excel report saved to: {output_path}")
        
        return str(output_path)
        
    except Exception as e:
        logger.error(f"Error generating Excel report: {e}")
        raise


def _populate_signal_history_sheet(worksheet, signal_history: Dict[str, Dict[str, float]]) -> None:
    """Populate the Signal History sheet with signal data."""
    try:
        # Convert signal history to DataFrame
        if isinstance(signal_history, dict):
            df = pd.DataFrame.from_dict(signal_history, orient='index')
            df.index.name = 'Date'
            df = df.reset_index()
        else:
            df = signal_history
        
        # Write headers
        for col_num, column_title in enumerate(df.columns, 1):
            cell = worksheet.cell(row=1, column=col_num)
            cell.value = column_title
            cell.font = Font(bold=True)
            cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")
        
        # Write data
        for row_num, row_data in enumerate(df.itertuples(index=False), 2):
            for col_num, value in enumerate(row_data, 1):
                worksheet.cell(row=row_num, column=col_num, value=value)
        
        logger.debug("Signal History sheet populated")
        
    except Exception as e:
        logger.error(f"Error populating Signal History sheet: {e}")


def _populate_allocation_history_sheet(worksheet, allocation_history: pd.DataFrame) -> None:
    """Populate the Allocation History sheet with allocation data."""
    try:
        # Ensure DataFrame has proper structure
        if not isinstance(allocation_history, pd.DataFrame):
            logger.warning("Allocation history is not a DataFrame, skipping")
            return
        
        # Write headers
        for col_num, column_title in enumerate(allocation_history.columns, 1):
            cell = worksheet.cell(row=1, column=col_num)
            cell.value = column_title
            cell.font = Font(bold=True)
            cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")
        
        # Write data
        for row_num, (index, row_data) in enumerate(allocation_history.iterrows(), 2):
            for col_num, value in enumerate(row_data, 1):
                worksheet.cell(row=row_num, column=col_num, value=value)
        
        logger.debug("Allocation History sheet populated")
        
    except Exception as e:
        logger.error(f"Error populating Allocation History sheet: {e}")


def _populate_trade_log_sheet(worksheet, trades: List[Dict[str, Any]]) -> None:
    """Populate the Trade Log sheet with trade data."""
    try:
        if not trades:
            logger.warning("No trades to populate in Trade Log sheet")
            return
        
        # Convert trades to DataFrame
        df = pd.DataFrame(trades)
        
        # Write headers
        for col_num, column_title in enumerate(df.columns, 1):
            cell = worksheet.cell(row=1, column=col_num)
            cell.value = column_title
            cell.font = Font(bold=True)
            cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")
        
        # Write data
        for row_num, row_data in enumerate(df.itertuples(index=False), 2):
            for col_num, value in enumerate(row_data, 1):
                worksheet.cell(row=row_num, column=col_num, value=value)
        
        logger.debug("Trade Log sheet populated")
        
    except Exception as e:
        logger.error(f"Error populating Trade Log sheet: {e}")


def _populate_performance_sheet(worksheet, backtest_results: Dict[str, Any], strategy_name: str, settings: Dict[str, Any]) -> None:
    """Populate the Performance sheet with performance metrics."""
    try:
        # Import performance calculation function
        from .report_metrics import _calculate_performance_metrics
        
        # Calculate performance metrics
        performance_data = _calculate_performance_metrics(backtest_results.get('portfolio_values', pd.DataFrame()), settings)
        
        # Write strategy name
        worksheet.cell(row=1, column=1, value="Strategy").font = Font(bold=True)
        worksheet.cell(row=1, column=2, value=strategy_name)
        
        # Write performance metrics
        row = 3
        worksheet.cell(row=row, column=1, value="Performance Metrics").font = Font(bold=True, size=14)
        row += 2
        
        for metric, value in performance_data.items():
            worksheet.cell(row=row, column=1, value=metric).font = Font(bold=True)
            worksheet.cell(row=row, column=2, value=value)
            row += 1
        
        logger.debug("Performance sheet populated")
        
    except Exception as e:
        logger.error(f"Error populating Performance sheet: {e}")


def verify_excel_report(file_path: Path) -> bool:
    """
    Verify that the Excel report has all required sheets and basic structure.

    Args:
        file_path: Path to the Excel file to verify

    Returns:
        bool: True if verification passes, False otherwise
    """
    try:
        if not file_path.exists():
            logger.error(f"Excel file does not exist: {file_path}")
            return False

        # Load workbook
        workbook = openpyxl.load_workbook(file_path, read_only=True)

        # Check required sheets
        missing_sheets = []
        for required_sheet in REQUIRED_SHEETS:
            if required_sheet not in workbook.sheetnames:
                missing_sheets.append(required_sheet)

        if missing_sheets:
            logger.error(f"Missing required sheets: {missing_sheets}")
            return False

        # Basic structure checks
        for sheet_name in REQUIRED_SHEETS:
            worksheet = workbook[sheet_name]
            if worksheet.max_row < 1:
                logger.warning(f"Sheet '{sheet_name}' appears to be empty")

        workbook.close()
        logger.info(f"Excel report verification passed: {file_path}")
        return True

    except Exception as e:
        logger.error(f"Error verifying Excel report: {e}")
        return False


def _generate_optimization_excel(
    optimization_results: List[Tuple[Dict[str, Any], Dict[str, Any]]],
    output_path: Path,
    strategy_name: str,
    settings: Dict[str, Any]
) -> str:
    """
    Generate Excel report for optimization results.

    Args:
        optimization_results: List of (parameters, results) tuples
        output_path: Path where to save the Excel file
        strategy_name: Name of the strategy
        settings: Settings dictionary

    Returns:
        str: Path to the generated Excel file
    """
    try:
        logger.info(f"Generating optimization Excel report: {output_path}")

        # Create workbook
        workbook = openpyxl.Workbook()

        # Remove default sheet
        if 'Sheet' in workbook.sheetnames:
            workbook.remove(workbook['Sheet'])

        # Create optimization summary sheet
        summary_sheet = workbook.create_sheet("Optimization Summary")

        # Populate optimization summary
        _populate_optimization_summary_sheet(summary_sheet, optimization_results, strategy_name)

        # Save workbook
        workbook.save(output_path)
        logger.info(f"Optimization Excel report saved to: {output_path}")

        return str(output_path)

    except Exception as e:
        logger.error(f"Error generating optimization Excel report: {e}")
        raise


def _populate_optimization_summary_sheet(worksheet, optimization_results: List[Tuple[Dict[str, Any], Dict[str, Any]]], strategy_name: str) -> None:
    """Populate the Optimization Summary sheet with optimization results."""
    try:
        # Write title
        worksheet.cell(row=1, column=1, value=f"Optimization Results - {strategy_name}").font = Font(bold=True, size=16)

        # Write headers
        row = 3
        headers = ["Run", "Parameters", "Total Return", "Sharpe Ratio", "Max Drawdown"]
        for col, header in enumerate(headers, 1):
            cell = worksheet.cell(row=row, column=col)
            cell.value = header
            cell.font = Font(bold=True)
            cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")

        # Write optimization results
        for run_num, (params, results) in enumerate(optimization_results, 1):
            row += 1
            worksheet.cell(row=row, column=1, value=run_num)
            worksheet.cell(row=row, column=2, value=str(params))
            worksheet.cell(row=row, column=3, value=results.get('total_return', 'N/A'))
            worksheet.cell(row=row, column=4, value=results.get('sharpe_ratio', 'N/A'))
            worksheet.cell(row=row, column=5, value=results.get('max_drawdown', 'N/A'))

        logger.debug("Optimization Summary sheet populated")

    except Exception as e:
        logger.error(f"Error populating Optimization Summary sheet: {e}")
