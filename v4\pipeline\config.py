#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
v4/pipeline/config.py

Configuration, Logging Setup, and CLI Argument Parsing for CPS v4 Pipeline

This module contains configuration-related functions extracted from run_unified_pipeline.py:
- setup_logger(): Initialize shared logger for the unified pipeline
- determine_pipeline_mode(): Determine if this should be optimization or single run
- parse_cli_arguments(): Parse command line arguments for the unified pipeline

All functions preserve their original functionality and imports exactly as they were.

Author: AI Assistant
Date: 2025-07-26
"""

import sys
import argparse
from pathlib import Path
import logging
from datetime import datetime
from typing import Optional, Dict, Any

# Import centralized path configuration
from v4.config.paths_v4 import V4_SETTINGS_FILE
from v4.settings.settings_CPS_v4 import load_settings
from v4.utils.tracing_utils import setup_trace_directory


# Global variable to store the shared log file path
_SHARED_LOG_FILE = None

def setup_logger(settings: Dict[str, Any]) -> logging.Logger:
    """Initialize shared logger for the unified pipeline.

    Args:
        settings: Configuration settings from CPS v4

    Returns:
        Configured logger instance
    """
    global _SHARED_LOG_FILE

    # Get log level from settings, default to INFO
    log_level = settings.get('system', {}).get('log_level', 'INFO').upper()

    # Configure logging with both console and file output
    logger = logging.getLogger('unified_pipeline')
    logger.setLevel(getattr(logging, log_level, logging.INFO))

    # Remove existing handlers to avoid duplicates
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)

    # Create formatter
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')

    # Console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)

    # File handler - use shared log file across all optimization subprocess calls
    import os
    shared_log_file = os.environ.get('CPS_V4_SHARED_LOG_FILE')

    if shared_log_file:
        # Use the shared log file from environment (optimization mode)
        _SHARED_LOG_FILE = Path(shared_log_file)
        logger.info(f"Using shared log file from environment: {_SHARED_LOG_FILE}")
    else:
        # Create new log file (single run mode)
        if _SHARED_LOG_FILE is None:
            trace_dir = setup_trace_directory()
            _SHARED_LOG_FILE = trace_dir / f"unified_pipeline_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
            # Set environment variable for any child processes
            os.environ['CPS_V4_SHARED_LOG_FILE'] = str(_SHARED_LOG_FILE)

    file_handler = logging.FileHandler(str(_SHARED_LOG_FILE))
    file_handler.setLevel(getattr(logging, log_level, logging.INFO))
    file_handler.setFormatter(formatter)
    logger.addHandler(file_handler)

    logger.info(f"Logger initialized. Log file: {_SHARED_LOG_FILE}")
    return logger


def determine_pipeline_mode(custom_settings_file: Optional[str] = None) -> tuple[str, Dict[str, Any], Optional[int]]:
    """Determine if this should be an optimization run or single run.

    SIMPLIFIED GATEWAY APPROACH: CPS_V4_OPTIMIZATION_ACTIVE is the primary decision point.
    This eliminates complex nested logic and provides clean separation between flows.

    Args:
        custom_settings_file: Optional path to custom settings file

    Returns:
        Tuple of (mode, settings, num_combinations)
        mode: 'optimization' or 'single'
        settings: Loaded settings dictionary
        num_combinations: Number of parameter combinations (None for single mode)
    """
    import os
    from v4.settings.settings_CPS_v4 import load_settings
    from v4.config.paths_v4 import V4_SETTINGS_FILE

    # Simple pipeline mode debugging
    debug_log_file = Path("optimization_validation/pipeline_mode_debug.log")
    debug_log_file.parent.mkdir(exist_ok=True)

    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    with open(debug_log_file, 'a') as f:
        f.write(f"\n[{timestamp}] SIMPLIFIED PIPELINE MODE DETECTION\n")
        f.write(f"Process ID: {os.getpid()}\n")

    # FORCE OPTIMIZATION MODE: Always direct all flow to optimization, never single
    optimization_active_env = os.environ.get('CPS_V4_OPTIMIZATION_ACTIVE')
    combo_id = os.environ.get('CPS_V4_COMBO_ID')

    print(f"\n[PIPELINE GATEWAY] CPS_V4_OPTIMIZATION_ACTIVE: {optimization_active_env}")
    print(f"[PIPELINE DEBUG] CPS_V4_COMBO_ID: {combo_id}")
    print(f"[PIPELINE DEBUG] Custom settings: {custom_settings_file}")
    print(f"[PIPELINE DEBUG] Process ID: {os.getpid()}")

    # ALWAYS FORCE OPTIMIZATION MODE - Never use single mode
    print(f"[PIPELINE MODE] OPTIMIZATION mode (FORCED - never using single mode)")

    with open(debug_log_file, 'a') as f:
        f.write(f"DECISION: OPTIMIZATION mode (FORCED - never using single mode)\n")

    # Load settings
    if custom_settings_file:
        settings = load_settings(custom_file=custom_settings_file)
    else:
        settings = load_settings()

    # Force CSV flag for optimization mode to ensure file creation
    settings['csv_flag_use'] = True
    print(f"[PIPELINE CONFIG] Forced csv_flag_use = True for optimization")

    try:
        from v4.optimization_detector import get_optimization_combinations
        config_path = str(V4_SETTINGS_FILE)
        combinations = get_optimization_combinations(config_path)
        print(f"[PIPELINE MODE] Found {len(combinations)} parameter combinations")
        return 'optimization', settings, len(combinations)
    except Exception as e:
        print(f"FATAL ERROR: Optimization mode requested but failed to get combinations: {e}")
        raise RuntimeError(f"Optimization mode detection failed: {e}") from e



def parse_cli_arguments() -> argparse.Namespace:
    """Parse command line arguments for the unified pipeline.
    
    Returns:
        Parsed arguments namespace
    """
    parser = argparse.ArgumentParser(
        description='Unified Pipeline v4 - Complete Signal Generation and Trading Workflow',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python run_unified_pipeline.py                           # Run complete pipeline
  python run_unified_pipeline.py --settings custom.ini    # Use custom settings
  python run_unified_pipeline.py --signals signals.csv    # Use existing signals
  python run_unified_pipeline.py --skip-signals           # Skip signal generation
        """
    )
    
    parser.add_argument(
        '--settings', 
        type=str, 
        help='Path to custom settings file (default: use settings_CPS_v4.ini)'
    )
    
    parser.add_argument(
        '--signals', 
        type=str, 
        help='Path to pre-computed signals file (skips signal generation)'
    )
    
    parser.add_argument(
        '--skip-signals', 
        action='store_true', 
        help='Skip signal generation phase and use existing signals'
    )
    
    parser.add_argument(
        '--verbose', 
        action='store_true', 
        help='Enable verbose logging output'
    )
    
    return parser.parse_args()
