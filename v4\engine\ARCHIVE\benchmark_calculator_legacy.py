"""
v4/engine/benchmark_calculator.py
Module for calculating benchmark returns and performance.
Part of the backtest engine system (CPS v4 compliant).
"""

import pandas as pd
import numpy as np
import logging

logger = logging.getLogger(__name__)

def calculate_equal_weight_benchmark(asset_returns, rebalance_freq='yearly', config=None):
    """
    Calculate equal-weight benchmark returns.
    
    Args:
        asset_returns (DataFrame): Asset returns data
        rebalance_freq (str): Rebalancing frequency ('daily', 'monthly', 'quarterly', 'yearly')
        config (dict): Configuration parameters
        
    Returns:
        Series: Benchmark returns
    """
    if asset_returns is None or asset_returns.empty:
        logger.warning("Cannot calculate benchmark: No asset returns data provided")
        return pd.Series()
    
    # Convert rebalance frequency to pandas frequency string
    freq_map = {
        'daily': 'B',
        'weekly': 'W',
        'monthly': 'M',
        'quarterly': 'Q',
        'yearly': 'Y'
    }
    pd_freq = freq_map.get(rebalance_freq.lower(), 'Y')  # Default to yearly
    
    # Get all assets
    assets = asset_returns.columns
    n_assets = len(assets)
    
    if n_assets == 0:
        logger.warning("Cannot calculate benchmark: No assets in returns data")
        return pd.Series()
    
    # Initialize with equal weights
    weights = {asset: 1.0 / n_assets for asset in assets}
    
    # Initialize benchmark returns
    benchmark_returns = pd.Series(index=asset_returns.index)
    benchmark_returns.iloc[0] = 0  # First day has no return
    
    # Track portfolio value
    portfolio_value = 1.0
    asset_values = {asset: portfolio_value * weights[asset] for asset in assets}
    
    # Calculate benchmark returns
    rebalance_dates = pd.date_range(
        start=asset_returns.index[0], 
        end=asset_returns.index[-1],
        freq=pd_freq
    )
    
    # Convert to datetime for easier comparison
    rebalance_dates = pd.DatetimeIndex([pd.Timestamp(d) for d in rebalance_dates])
    
    # Iterate through dates
    for i in range(1, len(asset_returns.index)):
        current_date = asset_returns.index[i]
        prev_date = asset_returns.index[i-1]
        
        # Update asset values based on returns
        for asset in assets:
            if asset in asset_returns.columns:
                asset_return = asset_returns.loc[current_date, asset]
                asset_values[asset] *= (1 + asset_return)
        
        # Calculate new portfolio value
        new_portfolio_value = sum(asset_values.values())
        
        # Calculate portfolio return
        portfolio_return = (new_portfolio_value / portfolio_value) - 1
        benchmark_returns.loc[current_date] = portfolio_return
        
        # Update portfolio value
        portfolio_value = new_portfolio_value
        
        # Rebalance if needed
        if any(abs((current_date - rd)).days < 2 for rd in rebalance_dates):
            # Rebalance to equal weights
            for asset in assets:
                asset_values[asset] = portfolio_value / n_assets
    
    return benchmark_returns

def calculate_custom_benchmark(price_data, weights, rebalance_freq='yearly'):
    """
    Calculate custom benchmark returns with specified weights.
    
    Args:
        price_data (DataFrame): Price data for assets
        weights (dict): Asset weights {asset: weight}
        rebalance_freq (str): Rebalancing frequency
        
    Returns:
        Series: Benchmark returns
    """
    # Calculate returns from prices
    returns = price_data.pct_change().fillna(0)
    
    # Validate weights
    assets = price_data.columns
    if not all(asset in assets for asset in weights.keys()):
        missing = [asset for asset in weights.keys() if asset not in assets]
        logger.warning(f"Assets in weights not found in price data: {missing}")
        # Filter weights to only include available assets
        weights = {k: v for k, v in weights.items() if k in assets}
    
    # Normalize weights to sum to 1
    total_weight = sum(weights.values())
    if total_weight > 0:
        weights = {k: v / total_weight for k, v in weights.items()}
    else:
        # Default to equal weight if weights don't sum to a positive number
        weights = {asset: 1.0 / len(assets) for asset in assets}
    
    # Convert rebalance frequency to pandas frequency string
    freq_map = {
        'daily': 'B',
        'weekly': 'W',
        'monthly': 'M',
        'quarterly': 'Q',
        'yearly': 'Y'
    }
    pd_freq = freq_map.get(rebalance_freq.lower(), 'Y')  # Default to yearly
    
    # Initialize benchmark returns
    benchmark_returns = pd.Series(index=returns.index)
    benchmark_returns.iloc[0] = 0  # First day has no return
    
    # Track portfolio value
    portfolio_value = 1.0
    asset_values = {asset: portfolio_value * weights.get(asset, 0) for asset in assets}
    
    # Calculate benchmark returns
    rebalance_dates = pd.date_range(
        start=returns.index[0], 
        end=returns.index[-1],
        freq=pd_freq
    )
    
    # Convert to datetime for easier comparison
    rebalance_dates = pd.DatetimeIndex([pd.Timestamp(d) for d in rebalance_dates])
    
    # Iterate through dates
    for i in range(1, len(returns.index)):
        current_date = returns.index[i]
        prev_date = returns.index[i-1]
        
        # Update asset values based on returns
        for asset in assets:
            if asset in returns.columns:
                asset_return = returns.loc[current_date, asset]
                asset_values[asset] *= (1 + asset_return)
        
        # Calculate new portfolio value
        new_portfolio_value = sum(asset_values.values())
        
        # Calculate portfolio return
        portfolio_return = (new_portfolio_value / portfolio_value) - 1
        benchmark_returns.loc[current_date] = portfolio_return
        
        # Update portfolio value
        portfolio_value = new_portfolio_value
        
        # Rebalance if needed
        if any(abs((current_date - rd)).days < 2 for rd in rebalance_dates):
            # Rebalance to target weights
            for asset in assets:
                asset_values[asset] = portfolio_value * weights.get(asset, 0)
    
    return benchmark_returns
