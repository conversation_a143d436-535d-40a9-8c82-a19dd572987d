# Benchmark Model in Backtest Engine v2

## Overview
The benchmark in the v2 backtest engine is the **equal weight portfolio**, calculated with a configurable rebalancing frequency (e.g., yearly, monthly). This benchmark is used for performance comparison in all reports—both in terminal output and Excel files.

---

## Key Modules and Functions

| Module/Path                      | Role                                  | Key Functions/Classes               |
|-----------------------------------|---------------------------------------|-------------------------------------|
| `engine/benchmark.py`            | Benchmark calculation (equal weight)  | `calculate_equal_weight_benchmark()`|
| `engine/backtest.py`             | Main engine, orchestrates benchmark   | `BacktestEngine._calculate_results()`|
| `reporting/performance_reporting.py` | Reporting, Excel/terminal output  | `generate_performance_report_local()`, `create_backtest_report()`|
| `config/config_v2.py`            | Configures benchmark frequency        | `benchmark_rebalance_freq` (param)  |

---

## Data Flow and Integration

```mermaid
graph TD
    A[Backtest Runner] --> B[BacktestEngine]
    B --> C[allocation.py]
    B --> D[benchmark.py]
    D --> E[benchmark_returns]
    B --> F[portfolio.py]
    F --> G[performance_reporting.py]
    E --> G
```
- The **BacktestEngine** (in `engine/backtest.py`) computes the main strategy and the benchmark in parallel.
- The benchmark is calculated by calling `calculate_equal_weight_benchmark()` from `engine/benchmark.py`, using asset return data and the configured rebalancing frequency.
- The result, `benchmark_returns`, is included in the results dictionary returned by the engine.
- The reporting module (`reporting/performance_reporting.py`) expects and uses `benchmark_returns` for all comparative analytics and reporting.
- Excel and terminal output always include the benchmark for side-by-side comparison with the main strategy.

---

## Benchmark Calculation Logic

### Function: `calculate_equal_weight_benchmark`
- **Location:** `engine/benchmark.py`
- **Inputs:**
    - `asset_returns` (DataFrame): Daily returns for each asset.
    - `rebalance_freq` (str): Rebalancing frequency (e.g., 'yearly', 'monthly', 'weekly', 'daily').
- **Process:**
    1. Determines rebalance dates based on the specified frequency
    2. Initializes portfolio with equal weights
    3. Rebalances to equal weights on each rebalance date
    4. Between rebalancing, weights drift based on asset performance
    5. Calculates daily returns of the benchmark portfolio
- **Output:**
    - Series of benchmark returns, indexed by date.

---

## Configuration
- The benchmark rebalancing frequency is controlled by the `benchmark_rebalance_freq` parameter in `config/config_v2.py` (or passed to the engine at initialization).
- Typical values: `'yearly'`, `'monthly'`, `'weekly'`, `'daily'`.

---

## Usage Example

```python
from engine.benchmark import calculate_equal_weight_benchmark
returns = price_data.pct_change().fillna(0)
benchmark_returns = calculate_equal_weight_benchmark(returns, rebalance_freq='yearly')
```

In the full backtest flow, this is handled automatically by the engine and results are routed to reporting.

---

## Reporting
- The benchmark appears as a separate column/tab in all Excel outputs and as a reference line/statistic in terminal output.
- All comparative metrics (e.g., Sharpe, CAGR, drawdown) are shown for both the strategy and the benchmark.
- The benchmark is labeled as "Benchmark" in tables and charts.

---

## Notes
- The benchmark is always the equal weight portfolio unless otherwise specified.
- The benchmark calculation is robust to missing data and asset list changes.
- If you need to use a different benchmark, you must implement and register a new calculation function in `engine/benchmark.py` and update the engine and reporting flow accordingly.

---

*This document is current as of v2 engine architecture and reflects all modules and functions used for benchmark calculation and reporting.*
