2025-06-18 12:29:15,157 - INFO - Script setup complete. Settings loaded and loggers configured.
2025-06-18 12:29:15,158 - INFO - EMA Production Flow Trace Log - 20250618_122915
2025-06-18 12:29:15,158 - INFO - Period: 2021-01-20 to 2021-02-28
2025-06-18 12:29:15,613 - INFO - Price data loaded. Shape: (1370, 5)
2025-06-18 12:29:15,618 - INFO - --- Effective Settings for Trace ---
2025-06-18 12:29:15,619 - INFO - EMA Lookbacks: ST=15, MT=70, LT=100
2025-06-18 12:29:15,619 - INFO - EMA Weights: Min=0.0, Max=1.0
2025-06-18 12:29:15,619 - INFO - EMA Strategy Top N: 3
2025-06-18 12:29:15,620 - INFO - System Top N (for allocation rules): 2
2025-06-18 12:29:15,648 - INFO - Signal Algorithm for Rules: {'default': "'ema'", 'picklist': ['Strategy_EMA', 'Strategy_SMA', 'Strategy_MACD', 'Strategy_RSI', 'Strategy_BB']}
2025-06-18 12:29:15,649 - INFO - -----------------------------------
2025-06-18 12:29:15,655 - INFO - 
========== Tracing for Date: 2021-01-20 ==========
2025-06-18 12:29:15,655 - INFO - --- Step 1: Calculate EMA Metrics ---
2025-06-18 12:29:15,656 - INFO - Input price_data_slice shape: (265, 5)
2025-06-18 12:29:15,656 - INFO - Setting module-level ema_params in ema_model_module: {'st_lookback': 15, 'mt_lookback': 70, 'lt_lookback': 100, 'min_weight': 0.0, 'max_weight': 1.0, 'top_n_strat_specific': 3}
2025-06-18 12:29:15,673 - INFO - Output short_ema_df tail(1):
                   SPY       SHV        EFA         TLT        PFF
Date                                                              
2021-01-20  354.597918  97.17208  66.021205  134.545233  29.691865
2025-06-18 12:29:15,676 - INFO - Output med_ema_df tail (1):
                   SPY        SHV        EFA         TLT        PFF
Date                                                               
2021-01-20  339.835007  97.173336  62.416445  137.835581  29.151148
2025-06-18 12:29:15,678 - INFO - Output long_ema_df tail (1):
                   SPY        SHV        EFA         TLT        PFF
Date                                                               
2021-01-20  333.307234  97.166897  61.070301  138.596178  28.839638
2025-06-18 12:29:15,678 - INFO - --- Step 2: Calculate EMA Ratios ---
2025-06-18 12:29:15,684 - INFO - Output stmtemax_series (latest):
                 SPY       SHV       EFA       TLT       PFF
Date                                                        
2021-01-20  1.043441  0.999987  1.057753  0.976128  1.018549
2025-06-18 12:29:15,687 - INFO - Output mtltemax_series (latest):
                 SPY       SHV       EFA       TLT       PFF
Date                                                        
2021-01-20  1.019585  1.000066  1.022043  0.994512  1.010801
2025-06-18 12:29:15,689 - INFO - Output emaxavg_series (latest for ranking):
                 SPY       SHV       EFA      TLT       PFF
Date                                                       
2021-01-20  1.031513  1.000027  1.039898  0.98532  1.014675
2025-06-18 12:29:15,689 - INFO - --- Step 3: Determine Allocation Weights ---
2025-06-18 12:29:15,690 - INFO - Calling ema_model_module.ema_allocation_model_updated with price_data up to 2021-01-20
2025-06-18 12:29:15,690 - INFO - Attempting to call ema_model_module.ema_allocation_model_updated...
2025-06-18 12:29:15,690 - INFO -   ema_model_module.st_lookback=15, mt_lookback=70, lt_lookback=100
2025-06-18 12:29:15,690 - INFO -   ema_model_module.min_weight=0.0, ema_model_module.max_weight=1.0
2025-06-18 12:29:15,690 - INFO -   Trace script's signal_algo: {'default': "'ema'", 'picklist': ['Strategy_EMA', 'Strategy_SMA', 'Strategy_MACD', 'Strategy_RSI', 'Strategy_BB']}, system_top_n: 2 (Note: production module uses its own settings for these)
2025-06-18 12:29:15,701 - INFO - Successfully called ema_model_module.ema_allocation_model_updated.
2025-06-18 12:29:15,702 - INFO - Output final_weights for 2021-01-20:
SPY    0.4
SHV    0.0
EFA    0.6
TLT    0.0
PFF    0.0
2025-06-18 12:29:15,703 - INFO - Final normalized weights for 2021-01-20: {'SPY': np.float64(0.4), 'SHV': np.float64(0.0), 'EFA': np.float64(0.6), 'TLT': np.float64(0.0), 'PFF': np.float64(0.0)}
2025-06-18 12:29:15,704 - INFO - 
========== Tracing for Date: 2021-01-21 ==========
2025-06-18 12:29:15,705 - INFO - --- Step 1: Calculate EMA Metrics ---
2025-06-18 12:29:15,705 - INFO - Input price_data_slice shape: (266, 5)
2025-06-18 12:29:15,705 - INFO - Setting module-level ema_params in ema_model_module: {'st_lookback': 15, 'mt_lookback': 70, 'lt_lookback': 100, 'min_weight': 0.0, 'max_weight': 1.0, 'top_n_strat_specific': 3}
2025-06-18 12:29:15,716 - INFO - Output short_ema_df tail(1):
                   SPY        SHV        EFA         TLT        PFF
Date                                                               
2021-01-21  355.498505  97.171578  66.137961  134.332903  29.710302
2025-06-18 12:29:15,718 - INFO - Output med_ema_df tail (1):
                   SPY        SHV        EFA         TLT        PFF
Date                                                               
2021-01-21  340.453813  97.173187  62.544299  137.695046  29.170534
2025-06-18 12:29:15,721 - INFO - Output long_ema_df tail (1):
                   SPY       SHV        EFA         TLT        PFF
Date                                                              
2021-01-21  333.871499  97.16692  61.186835  138.482325  28.859434
2025-06-18 12:29:15,721 - INFO - --- Step 2: Calculate EMA Ratios ---
2025-06-18 12:29:15,728 - INFO - Output stmtemax_series (latest):
                SPY       SHV       EFA       TLT       PFF
Date                                                       
2021-01-21  1.04419  0.999983  1.057458  0.975583  1.018504
2025-06-18 12:29:15,731 - INFO - Output mtltemax_series (latest):
                 SPY       SHV       EFA       TLT      PFF
Date                                                       
2021-01-21  1.019715  1.000064  1.022186  0.994315  1.01078
2025-06-18 12:29:15,734 - INFO - Output emaxavg_series (latest for ranking):
                 SPY       SHV       EFA       TLT       PFF
Date                                                        
2021-01-21  1.031953  1.000024  1.039822  0.984949  1.014642
2025-06-18 12:29:15,735 - INFO - --- Step 3: Determine Allocation Weights ---
2025-06-18 12:29:15,735 - INFO - Calling ema_model_module.ema_allocation_model_updated with price_data up to 2021-01-21
2025-06-18 12:29:15,735 - INFO - Attempting to call ema_model_module.ema_allocation_model_updated...
2025-06-18 12:29:15,735 - INFO -   ema_model_module.st_lookback=15, mt_lookback=70, lt_lookback=100
2025-06-18 12:29:15,736 - INFO -   ema_model_module.min_weight=0.0, ema_model_module.max_weight=1.0
2025-06-18 12:29:15,736 - INFO -   Trace script's signal_algo: {'default': "'ema'", 'picklist': ['Strategy_EMA', 'Strategy_SMA', 'Strategy_MACD', 'Strategy_RSI', 'Strategy_BB']}, system_top_n: 2 (Note: production module uses its own settings for these)
2025-06-18 12:29:15,744 - INFO - Successfully called ema_model_module.ema_allocation_model_updated.
2025-06-18 12:29:15,746 - INFO - Output final_weights for 2021-01-21:
SPY    0.4
SHV    0.0
EFA    0.6
TLT    0.0
PFF    0.0
2025-06-18 12:29:15,746 - INFO - Final normalized weights for 2021-01-21: {'SPY': np.float64(0.4), 'SHV': np.float64(0.0), 'EFA': np.float64(0.6), 'TLT': np.float64(0.0), 'PFF': np.float64(0.0)}
2025-06-18 12:29:15,747 - INFO - 
========== Tracing for Date: 2021-01-22 ==========
2025-06-18 12:29:15,747 - INFO - --- Step 1: Calculate EMA Metrics ---
2025-06-18 12:29:15,748 - INFO - Input price_data_slice shape: (267, 5)
2025-06-18 12:29:15,749 - INFO - Setting module-level ema_params in ema_model_module: {'st_lookback': 15, 'mt_lookback': 70, 'lt_lookback': 100, 'min_weight': 0.0, 'max_weight': 1.0, 'top_n_strat_specific': 3}
2025-06-18 12:29:15,764 - INFO - Output short_ema_df tail(1):
                   SPY        SHV        EFA         TLT        PFF
Date                                                               
2021-01-22  356.126446  97.171138  66.200219  134.200864  29.733249
2025-06-18 12:29:15,767 - INFO - Output med_ema_df tail (1):
                   SPY        SHV        EFA         TLT       PFF
Date                                                              
2021-01-22  341.019115  97.173043  62.659559  137.570582  29.19091
2025-06-18 12:29:15,769 - INFO - Output long_ema_df tail (1):
                   SPY        SHV       EFA         TLT        PFF
Date                                                              
2021-01-22  334.399232  97.166943  61.29474  138.379241  28.879919
2025-06-18 12:29:15,770 - INFO - --- Step 2: Calculate EMA Ratios ---
2025-06-18 12:29:15,775 - INFO - Output stmtemax_series (latest):
                 SPY      SHV       EFA       TLT       PFF
Date                                                       
2021-01-22  1.044301  0.99998  1.056506  0.975506  1.018579
2025-06-18 12:29:15,777 - INFO - Output mtltemax_series (latest):
                 SPY       SHV       EFA       TLT       PFF
Date                                                        
2021-01-22  1.019796  1.000063  1.022266  0.994156  1.010768
2025-06-18 12:29:15,779 - INFO - Output emaxavg_series (latest for ranking):
                 SPY       SHV       EFA       TLT       PFF
Date                                                        
2021-01-22  1.032048  1.000022  1.039386  0.984831  1.014674
2025-06-18 12:29:15,780 - INFO - --- Step 3: Determine Allocation Weights ---
2025-06-18 12:29:15,780 - INFO - Calling ema_model_module.ema_allocation_model_updated with price_data up to 2021-01-22
2025-06-18 12:29:15,781 - INFO - Attempting to call ema_model_module.ema_allocation_model_updated...
2025-06-18 12:29:15,781 - INFO -   ema_model_module.st_lookback=15, mt_lookback=70, lt_lookback=100
2025-06-18 12:29:15,781 - INFO -   ema_model_module.min_weight=0.0, ema_model_module.max_weight=1.0
2025-06-18 12:29:15,781 - INFO -   Trace script's signal_algo: {'default': "'ema'", 'picklist': ['Strategy_EMA', 'Strategy_SMA', 'Strategy_MACD', 'Strategy_RSI', 'Strategy_BB']}, system_top_n: 2 (Note: production module uses its own settings for these)
2025-06-18 12:29:15,790 - INFO - Successfully called ema_model_module.ema_allocation_model_updated.
2025-06-18 12:29:15,792 - INFO - Output final_weights for 2021-01-22:
SPY    0.4
SHV    0.0
EFA    0.6
TLT    0.0
PFF    0.0
2025-06-18 12:29:15,792 - INFO - Final normalized weights for 2021-01-22: {'SPY': np.float64(0.4), 'SHV': np.float64(0.0), 'EFA': np.float64(0.6), 'TLT': np.float64(0.0), 'PFF': np.float64(0.0)}
2025-06-18 12:29:15,792 - INFO - 
========== Tracing for Date: 2021-01-25 ==========
2025-06-18 12:29:15,793 - INFO - --- Step 1: Calculate EMA Metrics ---
2025-06-18 12:29:15,793 - INFO - Input price_data_slice shape: (268, 5)
2025-06-18 12:29:15,793 - INFO - Setting module-level ema_params in ema_model_module: {'st_lookback': 15, 'mt_lookback': 70, 'lt_lookback': 100, 'min_weight': 0.0, 'max_weight': 1.0, 'top_n_strat_specific': 3}
2025-06-18 12:29:15,809 - INFO - Output short_ema_df tail(1):
                   SPY        SHV        EFA         TLT        PFF
Date                                                               
2021-01-25  356.853621  97.170753  66.244718  134.281677  29.743593
2025-06-18 12:29:15,812 - INFO - Output med_ema_df tail (1):
                   SPY        SHV        EFA         TLT        PFF
Date                                                               
2021-01-25  341.608544  97.172902  62.769323  137.493872  29.208518
2025-06-18 12:29:15,815 - INFO - Output long_ema_df tail (1):
                  SPY        SHV        EFA         TLT        PFF
Date                                                              
2021-01-25  334.94467  97.166965  61.398927  138.309303  28.898455
2025-06-18 12:29:15,815 - INFO - --- Step 2: Calculate EMA Ratios ---
2025-06-18 12:29:15,820 - INFO - Output stmtemax_series (latest):
                 SPY       SHV       EFA       TLT       PFF
Date                                                        
2021-01-25  1.044627  0.999978  1.055368  0.976638  1.018319
2025-06-18 12:29:15,823 - INFO - Output mtltemax_series (latest):
                 SPY       SHV      EFA       TLT       PFF
Date                                                       
2021-01-25  1.019895  1.000061  1.02232  0.994104  1.010729
2025-06-18 12:29:15,825 - INFO - Output emaxavg_series (latest for ranking):
                 SPY       SHV       EFA       TLT       PFF
Date                                                        
2021-01-25  1.032261  1.000019  1.038844  0.985371  1.014524
2025-06-18 12:29:15,826 - INFO - --- Step 3: Determine Allocation Weights ---
2025-06-18 12:29:15,827 - INFO - Calling ema_model_module.ema_allocation_model_updated with price_data up to 2021-01-25
2025-06-18 12:29:15,827 - INFO - Attempting to call ema_model_module.ema_allocation_model_updated...
2025-06-18 12:29:15,828 - INFO -   ema_model_module.st_lookback=15, mt_lookback=70, lt_lookback=100
2025-06-18 12:29:15,828 - INFO -   ema_model_module.min_weight=0.0, ema_model_module.max_weight=1.0
2025-06-18 12:29:15,828 - INFO -   Trace script's signal_algo: {'default': "'ema'", 'picklist': ['Strategy_EMA', 'Strategy_SMA', 'Strategy_MACD', 'Strategy_RSI', 'Strategy_BB']}, system_top_n: 2 (Note: production module uses its own settings for these)
2025-06-18 12:29:15,838 - INFO - Successfully called ema_model_module.ema_allocation_model_updated.
2025-06-18 12:29:15,839 - INFO - Output final_weights for 2021-01-25:
SPY    0.4
SHV    0.0
EFA    0.6
TLT    0.0
PFF    0.0
2025-06-18 12:29:15,840 - INFO - Final normalized weights for 2021-01-25: {'SPY': np.float64(0.4), 'SHV': np.float64(0.0), 'EFA': np.float64(0.6), 'TLT': np.float64(0.0), 'PFF': np.float64(0.0)}
2025-06-18 12:29:15,840 - INFO - 
========== Tracing for Date: 2021-01-26 ==========
2025-06-18 12:29:15,841 - INFO - --- Step 1: Calculate EMA Metrics ---
2025-06-18 12:29:15,841 - INFO - Input price_data_slice shape: (269, 5)
2025-06-18 12:29:15,841 - INFO - Setting module-level ema_params in ema_model_module: {'st_lookback': 15, 'mt_lookback': 70, 'lt_lookback': 100, 'min_weight': 0.0, 'max_weight': 1.0, 'top_n_strat_specific': 3}
2025-06-18 12:29:15,856 - INFO - Output short_ema_df tail(1):
                   SPY        SHV        EFA         TLT        PFF
Date                                                               
2021-01-26  357.419286  97.170417  66.295848  134.320578  29.742908
2025-06-18 12:29:15,858 - INFO - Output med_ema_df tail (1):
                   SPY        SHV        EFA         TLT        PFF
Date                                                               
2021-01-26  342.165456  97.172766  62.878744  137.412154  29.223436
2025-06-18 12:29:15,860 - INFO - Output long_ema_df tail (1):
                   SPY        SHV        EFA        TLT        PFF
Date                                                              
2021-01-26  335.468121  97.166986  61.502983  138.23571  28.915082
2025-06-18 12:29:15,861 - INFO - --- Step 2: Calculate EMA Ratios ---
2025-06-18 12:29:15,867 - INFO - Output stmtemax_series (latest):
                SPY       SHV       EFA       TLT       PFF
Date                                                       
2021-01-26  1.04458  0.999976  1.054344  0.977501  1.017776
2025-06-18 12:29:15,871 - INFO - Output mtltemax_series (latest):
                 SPY       SHV       EFA       TLT       PFF
Date                                                        
2021-01-26  1.019964  1.000059  1.022369  0.994042  1.010664
2025-06-18 12:29:15,874 - INFO - Output emaxavg_series (latest for ranking):
                 SPY       SHV       EFA       TLT      PFF
Date                                                       
2021-01-26  1.032272  1.000018  1.038357  0.985772  1.01422
2025-06-18 12:29:15,875 - INFO - --- Step 3: Determine Allocation Weights ---
2025-06-18 12:29:15,875 - INFO - Calling ema_model_module.ema_allocation_model_updated with price_data up to 2021-01-26
2025-06-18 12:29:15,875 - INFO - Attempting to call ema_model_module.ema_allocation_model_updated...
2025-06-18 12:29:15,875 - INFO -   ema_model_module.st_lookback=15, mt_lookback=70, lt_lookback=100
2025-06-18 12:29:15,875 - INFO -   ema_model_module.min_weight=0.0, ema_model_module.max_weight=1.0
2025-06-18 12:29:15,876 - INFO -   Trace script's signal_algo: {'default': "'ema'", 'picklist': ['Strategy_EMA', 'Strategy_SMA', 'Strategy_MACD', 'Strategy_RSI', 'Strategy_BB']}, system_top_n: 2 (Note: production module uses its own settings for these)
2025-06-18 12:29:15,884 - INFO - Successfully called ema_model_module.ema_allocation_model_updated.
2025-06-18 12:29:15,886 - INFO - Output final_weights for 2021-01-26:
SPY    0.4
SHV    0.0
EFA    0.6
TLT    0.0
PFF    0.0
2025-06-18 12:29:15,887 - INFO - Final normalized weights for 2021-01-26: {'SPY': np.float64(0.4), 'SHV': np.float64(0.0), 'EFA': np.float64(0.6), 'TLT': np.float64(0.0), 'PFF': np.float64(0.0)}
2025-06-18 12:29:15,887 - INFO - 
========== Tracing for Date: 2021-01-27 ==========
2025-06-18 12:29:15,888 - INFO - --- Step 1: Calculate EMA Metrics ---
2025-06-18 12:29:15,888 - INFO - Input price_data_slice shape: (270, 5)
2025-06-18 12:29:15,888 - INFO - Setting module-level ema_params in ema_model_module: {'st_lookback': 15, 'mt_lookback': 70, 'lt_lookback': 100, 'min_weight': 0.0, 'max_weight': 1.0, 'top_n_strat_specific': 3}
2025-06-18 12:29:15,900 - INFO - Output short_ema_df tail(1):
                 SPY        SHV        EFA         TLT        PFF
Date                                                             
2021-01-27  356.8102  97.171222  66.149945  134.397388  29.690708
2025-06-18 12:29:15,903 - INFO - Output med_ema_df tail (1):
                   SPY        SHV        EFA         TLT        PFF
Date                                                               
2021-01-27  342.457883  97.172881  62.942121  137.342377  29.226306
2025-06-18 12:29:15,905 - INFO - Output long_ema_df tail (1):
                   SPY        SHV        EFA         TLT        PFF
Date                                                               
2021-01-27  335.806309  97.167182  61.574778  138.170351  28.923205
2025-06-18 12:29:15,906 - INFO - --- Step 2: Calculate EMA Ratios ---
2025-06-18 12:29:15,911 - INFO - Output stmtemax_series (latest):
                SPY       SHV       EFA       TLT      PFF
Date                                                      
2021-01-27  1.04191  0.999983  1.050965  0.978557  1.01589
2025-06-18 12:29:15,913 - INFO - Output mtltemax_series (latest):
                 SPY       SHV       EFA       TLT      PFF
Date                                                       
2021-01-27  1.019808  1.000059  1.022206  0.994008  1.01048
2025-06-18 12:29:15,916 - INFO - Output emaxavg_series (latest for ranking):
                 SPY       SHV       EFA       TLT       PFF
Date                                                        
2021-01-27  1.030859  1.000021  1.036585  0.986282  1.013185
2025-06-18 12:29:15,917 - INFO - --- Step 3: Determine Allocation Weights ---
2025-06-18 12:29:15,918 - INFO - Calling ema_model_module.ema_allocation_model_updated with price_data up to 2021-01-27
2025-06-18 12:29:15,918 - INFO - Attempting to call ema_model_module.ema_allocation_model_updated...
2025-06-18 12:29:15,919 - INFO -   ema_model_module.st_lookback=15, mt_lookback=70, lt_lookback=100
2025-06-18 12:29:15,919 - INFO -   ema_model_module.min_weight=0.0, ema_model_module.max_weight=1.0
2025-06-18 12:29:15,919 - INFO -   Trace script's signal_algo: {'default': "'ema'", 'picklist': ['Strategy_EMA', 'Strategy_SMA', 'Strategy_MACD', 'Strategy_RSI', 'Strategy_BB']}, system_top_n: 2 (Note: production module uses its own settings for these)
2025-06-18 12:29:15,931 - INFO - Successfully called ema_model_module.ema_allocation_model_updated.
2025-06-18 12:29:15,933 - INFO - Output final_weights for 2021-01-27:
SPY    0.4
SHV    0.0
EFA    0.6
TLT    0.0
PFF    0.0
2025-06-18 12:29:15,933 - INFO - Final normalized weights for 2021-01-27: {'SPY': np.float64(0.4), 'SHV': np.float64(0.0), 'EFA': np.float64(0.6), 'TLT': np.float64(0.0), 'PFF': np.float64(0.0)}
2025-06-18 12:29:15,934 - INFO - 
========== Tracing for Date: 2021-01-28 ==========
2025-06-18 12:29:15,934 - INFO - --- Step 1: Calculate EMA Metrics ---
2025-06-18 12:29:15,935 - INFO - Input price_data_slice shape: (271, 5)
2025-06-18 12:29:15,935 - INFO - Setting module-level ema_params in ema_model_module: {'st_lookback': 15, 'mt_lookback': 70, 'lt_lookback': 100, 'min_weight': 0.0, 'max_weight': 1.0, 'top_n_strat_specific': 3}
2025-06-18 12:29:15,948 - INFO - Output short_ema_df tail(1):
                   SPY        SHV        EFA         TLT        PFF
Date                                                               
2021-01-28  356.656252  97.170826  66.074374  134.371367  29.679108
2025-06-18 12:29:15,950 - INFO - Output med_ema_df tail (1):
                   SPY        SHV        EFA         TLT        PFF
Date                                                               
2021-01-28  342.827481  97.172745  63.015452  137.253555  29.236774
2025-06-18 12:29:15,953 - INFO - Output long_ema_df tail (1):
                  SPY        SHV        EFA         TLT        PFF
Date                                                              
2021-01-28  336.19784  97.167199  61.653404  138.091517  28.936565
2025-06-18 12:29:15,953 - INFO - --- Step 2: Calculate EMA Ratios ---
2025-06-18 12:29:15,958 - INFO - Output stmtemax_series (latest):
                 SPY      SHV       EFA       TLT       PFF
Date                                                       
2021-01-28  1.040337  0.99998  1.048542  0.979001  1.015129
2025-06-18 12:29:15,960 - INFO - Output mtltemax_series (latest):
                 SPY       SHV       EFA       TLT       PFF
Date                                                        
2021-01-28  1.019719  1.000057  1.022092  0.993932  1.010375
2025-06-18 12:29:15,962 - INFO - Output emaxavg_series (latest for ranking):
                 SPY       SHV       EFA       TLT       PFF
Date                                                        
2021-01-28  1.030028  1.000019  1.035317  0.986466  1.012752
2025-06-18 12:29:15,963 - INFO - --- Step 3: Determine Allocation Weights ---
2025-06-18 12:29:15,963 - INFO - Calling ema_model_module.ema_allocation_model_updated with price_data up to 2021-01-28
2025-06-18 12:29:15,963 - INFO - Attempting to call ema_model_module.ema_allocation_model_updated...
2025-06-18 12:29:15,963 - INFO -   ema_model_module.st_lookback=15, mt_lookback=70, lt_lookback=100
2025-06-18 12:29:15,963 - INFO -   ema_model_module.min_weight=0.0, ema_model_module.max_weight=1.0
2025-06-18 12:29:15,963 - INFO -   Trace script's signal_algo: {'default': "'ema'", 'picklist': ['Strategy_EMA', 'Strategy_SMA', 'Strategy_MACD', 'Strategy_RSI', 'Strategy_BB']}, system_top_n: 2 (Note: production module uses its own settings for these)
2025-06-18 12:29:15,973 - INFO - Successfully called ema_model_module.ema_allocation_model_updated.
2025-06-18 12:29:15,976 - INFO - Output final_weights for 2021-01-28:
SPY    0.4
SHV    0.0
EFA    0.6
TLT    0.0
PFF    0.0
2025-06-18 12:29:15,976 - INFO - Final normalized weights for 2021-01-28: {'SPY': np.float64(0.4), 'SHV': np.float64(0.0), 'EFA': np.float64(0.6), 'TLT': np.float64(0.0), 'PFF': np.float64(0.0)}
2025-06-18 12:29:15,977 - INFO - 
========== Tracing for Date: 2021-01-29 ==========
2025-06-18 12:29:15,978 - INFO - --- Step 1: Calculate EMA Metrics ---
2025-06-18 12:29:15,978 - INFO - Input price_data_slice shape: (272, 5)
2025-06-18 12:29:15,978 - INFO - Setting module-level ema_params in ema_model_module: {'st_lookback': 15, 'mt_lookback': 70, 'lt_lookback': 100, 'min_weight': 0.0, 'max_weight': 1.0, 'top_n_strat_specific': 3}
2025-06-18 12:29:15,989 - INFO - Output short_ema_df tail(1):
                   SPY       SHV        EFA         TLT        PFF
Date                                                              
2021-01-29  355.631727  97.17158  65.838668  134.247682  29.654355
2025-06-18 12:29:15,991 - INFO - Output med_ema_df tail (1):
                   SPY        SHV        EFA         TLT        PFF
Date                                                               
2021-01-29  342.986145  97.172861  63.048502  137.144494  29.243656
2025-06-18 12:29:15,995 - INFO - Output long_ema_df tail (1):
                   SPY       SHV        EFA         TLT        PFF
Date                                                              
2021-01-29  336.440656  97.16739  61.703608  137.998257  28.947348
2025-06-18 12:29:15,995 - INFO - --- Step 2: Calculate EMA Ratios ---
2025-06-18 12:29:16,001 - INFO - Output stmtemax_series (latest):
                 SPY       SHV       EFA       TLT       PFF
Date                                                        
2021-01-29  1.036869  0.999987  1.044254  0.978878  1.014044
2025-06-18 12:29:16,003 - INFO - Output mtltemax_series (latest):
                 SPY       SHV       EFA       TLT       PFF
Date                                                        
2021-01-29  1.019455  1.000056  1.021796  0.993813  1.010236
2025-06-18 12:29:16,005 - INFO - Output emaxavg_series (latest for ranking):
                 SPY       SHV       EFA       TLT      PFF
Date                                                       
2021-01-29  1.028162  1.000022  1.033025  0.986345  1.01214
2025-06-18 12:29:16,005 - INFO - --- Step 3: Determine Allocation Weights ---
2025-06-18 12:29:16,005 - INFO - Calling ema_model_module.ema_allocation_model_updated with price_data up to 2021-01-29
2025-06-18 12:29:16,006 - INFO - Attempting to call ema_model_module.ema_allocation_model_updated...
2025-06-18 12:29:16,006 - INFO -   ema_model_module.st_lookback=15, mt_lookback=70, lt_lookback=100
2025-06-18 12:29:16,006 - INFO -   ema_model_module.min_weight=0.0, ema_model_module.max_weight=1.0
2025-06-18 12:29:16,006 - INFO -   Trace script's signal_algo: {'default': "'ema'", 'picklist': ['Strategy_EMA', 'Strategy_SMA', 'Strategy_MACD', 'Strategy_RSI', 'Strategy_BB']}, system_top_n: 2 (Note: production module uses its own settings for these)
2025-06-18 12:29:16,014 - INFO - Successfully called ema_model_module.ema_allocation_model_updated.
2025-06-18 12:29:16,015 - INFO - Output final_weights for 2021-01-29:
SPY    0.4
SHV    0.0
EFA    0.6
TLT    0.0
PFF    0.0
2025-06-18 12:29:16,016 - INFO - Final normalized weights for 2021-01-29: {'SPY': np.float64(0.4), 'SHV': np.float64(0.0), 'EFA': np.float64(0.6), 'TLT': np.float64(0.0), 'PFF': np.float64(0.0)}
2025-06-18 12:29:16,016 - INFO - 
========== Tracing for Date: 2021-02-01 ==========
2025-06-18 12:29:16,017 - INFO - --- Step 1: Calculate EMA Metrics ---
2025-06-18 12:29:16,017 - INFO - Input price_data_slice shape: (273, 5)
2025-06-18 12:29:16,017 - INFO - Setting module-level ema_params in ema_model_module: {'st_lookback': 15, 'mt_lookback': 70, 'lt_lookback': 100, 'min_weight': 0.0, 'max_weight': 1.0, 'top_n_strat_specific': 3}
2025-06-18 12:29:16,029 - INFO - Output short_ema_df tail(1):
                 SPY       SHV        EFA         TLT        PFF
Date                                                            
2021-02-01  355.4603  97.17114  65.727746  134.158786  29.653223
2025-06-18 12:29:16,031 - INFO - Output med_ema_df tail (1):
                   SPY        SHV        EFA         TLT        PFF
Date                                                               
2021-02-01  343.303727  97.172726  63.102102  137.042861  29.254969
2025-06-18 12:29:16,033 - INFO - Output long_ema_df tail (1):
                  SPY        SHV        EFA         TLT        PFF
Date                                                              
2021-02-01  336.79352  97.167404  61.767919  137.909905  28.961169
2025-06-18 12:29:16,034 - INFO - --- Step 2: Calculate EMA Ratios ---
2025-06-18 12:29:16,038 - INFO - Output stmtemax_series (latest):
                 SPY       SHV       EFA       TLT       PFF
Date                                                        
2021-02-01  1.035411  0.999984  1.041609  0.978955  1.013613
2025-06-18 12:29:16,041 - INFO - Output mtltemax_series (latest):
                SPY       SHV     EFA       TLT       PFF
Date                                                     
2021-02-01  1.01933  1.000055  1.0216  0.993713  1.010145
2025-06-18 12:29:16,043 - INFO - Output emaxavg_series (latest for ranking):
                SPY       SHV       EFA       TLT       PFF
Date                                                       
2021-02-01  1.02737  1.000019  1.031605  0.986334  1.011879
2025-06-18 12:29:16,044 - INFO - --- Step 3: Determine Allocation Weights ---
2025-06-18 12:29:16,044 - INFO - Calling ema_model_module.ema_allocation_model_updated with price_data up to 2021-02-01
2025-06-18 12:29:16,044 - INFO - Attempting to call ema_model_module.ema_allocation_model_updated...
2025-06-18 12:29:16,045 - INFO -   ema_model_module.st_lookback=15, mt_lookback=70, lt_lookback=100
2025-06-18 12:29:16,045 - INFO -   ema_model_module.min_weight=0.0, ema_model_module.max_weight=1.0
2025-06-18 12:29:16,045 - INFO -   Trace script's signal_algo: {'default': "'ema'", 'picklist': ['Strategy_EMA', 'Strategy_SMA', 'Strategy_MACD', 'Strategy_RSI', 'Strategy_BB']}, system_top_n: 2 (Note: production module uses its own settings for these)
2025-06-18 12:29:16,054 - INFO - Successfully called ema_model_module.ema_allocation_model_updated.
2025-06-18 12:29:16,056 - INFO - Output final_weights for 2021-02-01:
SPY    0.4
SHV    0.0
EFA    0.6
TLT    0.0
PFF    0.0
2025-06-18 12:29:16,057 - INFO - Final normalized weights for 2021-02-01: {'SPY': np.float64(0.4), 'SHV': np.float64(0.0), 'EFA': np.float64(0.6), 'TLT': np.float64(0.0), 'PFF': np.float64(0.0)}
2025-06-18 12:29:16,057 - INFO - 
========== Tracing for Date: 2021-02-02 ==========
2025-06-18 12:29:16,058 - INFO - --- Step 1: Calculate EMA Metrics ---
2025-06-18 12:29:16,059 - INFO - Input price_data_slice shape: (274, 5)
2025-06-18 12:29:16,059 - INFO - Setting module-level ema_params in ema_model_module: {'st_lookback': 15, 'mt_lookback': 70, 'lt_lookback': 100, 'min_weight': 0.0, 'max_weight': 1.0, 'top_n_strat_specific': 3}
2025-06-18 12:29:16,072 - INFO - Output short_ema_df tail(1):
                   SPY        SHV        EFA         TLT        PFF
Date                                                               
2021-02-02  355.936477  97.170755  65.712708  133.973383  29.665917
2025-06-18 12:29:16,074 - INFO - Output med_ema_df tail (1):
                   SPY        SHV        EFA         TLT        PFF
Date                                                               
2021-02-02  343.753473  97.172594  63.172675  136.919839  29.269048
2025-06-18 12:29:16,076 - INFO - Output long_ema_df tail (1):
                   SPY        SHV        EFA         TLT        PFF
Date                                                               
2021-02-02  337.238593  97.167417  61.843949  137.806255  28.976884
2025-06-18 12:29:16,076 - INFO - --- Step 2: Calculate EMA Ratios ---
2025-06-18 12:29:16,081 - INFO - Output stmtemax_series (latest):
                 SPY       SHV       EFA      TLT       PFF
Date                                                       
2021-02-02  1.035441  0.999981  1.040208  0.97848  1.013559
2025-06-18 12:29:16,083 - INFO - Output mtltemax_series (latest):
                 SPY       SHV       EFA       TLT       PFF
Date                                                        
2021-02-02  1.019318  1.000053  1.021485  0.993568  1.010083
2025-06-18 12:29:16,085 - INFO - Output emaxavg_series (latest for ranking):
                SPY       SHV       EFA       TLT       PFF
Date                                                       
2021-02-02  1.02738  1.000017  1.030846  0.986024  1.011821
2025-06-18 12:29:16,086 - INFO - --- Step 3: Determine Allocation Weights ---
2025-06-18 12:29:16,086 - INFO - Calling ema_model_module.ema_allocation_model_updated with price_data up to 2021-02-02
2025-06-18 12:29:16,086 - INFO - Attempting to call ema_model_module.ema_allocation_model_updated...
2025-06-18 12:29:16,087 - INFO -   ema_model_module.st_lookback=15, mt_lookback=70, lt_lookback=100
2025-06-18 12:29:16,087 - INFO -   ema_model_module.min_weight=0.0, ema_model_module.max_weight=1.0
2025-06-18 12:29:16,087 - INFO -   Trace script's signal_algo: {'default': "'ema'", 'picklist': ['Strategy_EMA', 'Strategy_SMA', 'Strategy_MACD', 'Strategy_RSI', 'Strategy_BB']}, system_top_n: 2 (Note: production module uses its own settings for these)
2025-06-18 12:29:16,097 - INFO - Successfully called ema_model_module.ema_allocation_model_updated.
2025-06-18 12:29:16,099 - INFO - Output final_weights for 2021-02-02:
SPY    0.4
SHV    0.0
EFA    0.6
TLT    0.0
PFF    0.0
2025-06-18 12:29:16,100 - INFO - Final normalized weights for 2021-02-02: {'SPY': np.float64(0.4), 'SHV': np.float64(0.0), 'EFA': np.float64(0.6), 'TLT': np.float64(0.0), 'PFF': np.float64(0.0)}
2025-06-18 12:29:16,100 - INFO - 
========== Tracing for Date: 2021-02-03 ==========
2025-06-18 12:29:16,101 - INFO - --- Step 1: Calculate EMA Metrics ---
2025-06-18 12:29:16,102 - INFO - Input price_data_slice shape: (275, 5)
2025-06-18 12:29:16,102 - INFO - Setting module-level ema_params in ema_model_module: {'st_lookback': 15, 'mt_lookback': 70, 'lt_lookback': 100, 'min_weight': 0.0, 'max_weight': 1.0, 'top_n_strat_specific': 3}
2025-06-18 12:29:16,112 - INFO - Output short_ema_df tail(1):
                   SPY        SHV        EFA         TLT        PFF
Date                                                               
2021-02-03  356.388433  97.172616  65.719501  133.664003  29.663339
2025-06-18 12:29:16,114 - INFO - Output med_ema_df tail (1):
                   SPY        SHV        EFA        TLT        PFF
Date                                                              
2021-02-03  344.198506  97.172962  63.245756  136.76712  29.279647
2025-06-18 12:29:16,116 - INFO - Output long_ema_df tail (1):
                   SPY        SHV        EFA         TLT       PFF
Date                                                              
2021-02-03  337.680446  97.167778  61.921634  137.681346  28.99012
2025-06-18 12:29:16,116 - INFO - --- Step 2: Calculate EMA Ratios ---
2025-06-18 12:29:16,123 - INFO - Output stmtemax_series (latest):
                 SPY       SHV       EFA       TLT       PFF
Date                                                        
2021-02-03  1.035415  0.999996  1.039113  0.977311  1.013104
2025-06-18 12:29:16,125 - INFO - Output mtltemax_series (latest):
                 SPY       SHV       EFA      TLT       PFF
Date                                                       
2021-02-03  1.019302  1.000053  1.021384  0.99336  1.009987
2025-06-18 12:29:16,127 - INFO - Output emaxavg_series (latest for ranking):
                 SPY       SHV       EFA       TLT       PFF
Date                                                        
2021-02-03  1.027359  1.000025  1.030249  0.985335  1.011546
2025-06-18 12:29:16,127 - INFO - --- Step 3: Determine Allocation Weights ---
2025-06-18 12:29:16,127 - INFO - Calling ema_model_module.ema_allocation_model_updated with price_data up to 2021-02-03
2025-06-18 12:29:16,128 - INFO - Attempting to call ema_model_module.ema_allocation_model_updated...
2025-06-18 12:29:16,128 - INFO -   ema_model_module.st_lookback=15, mt_lookback=70, lt_lookback=100
2025-06-18 12:29:16,128 - INFO -   ema_model_module.min_weight=0.0, ema_model_module.max_weight=1.0
2025-06-18 12:29:16,128 - INFO -   Trace script's signal_algo: {'default': "'ema'", 'picklist': ['Strategy_EMA', 'Strategy_SMA', 'Strategy_MACD', 'Strategy_RSI', 'Strategy_BB']}, system_top_n: 2 (Note: production module uses its own settings for these)
2025-06-18 12:29:16,137 - INFO - Successfully called ema_model_module.ema_allocation_model_updated.
2025-06-18 12:29:16,138 - INFO - Output final_weights for 2021-02-03:
SPY    0.4
SHV    0.0
EFA    0.6
TLT    0.0
PFF    0.0
2025-06-18 12:29:16,139 - INFO - Final normalized weights for 2021-02-03: {'SPY': np.float64(0.4), 'SHV': np.float64(0.0), 'EFA': np.float64(0.6), 'TLT': np.float64(0.0), 'PFF': np.float64(0.0)}
2025-06-18 12:29:16,139 - INFO - 
========== Tracing for Date: 2021-02-04 ==========
2025-06-18 12:29:16,141 - INFO - --- Step 1: Calculate EMA Metrics ---
2025-06-18 12:29:16,141 - INFO - Input price_data_slice shape: (276, 5)
2025-06-18 12:29:16,142 - INFO - Setting module-level ema_params in ema_model_module: {'st_lookback': 15, 'mt_lookback': 70, 'lt_lookback': 100, 'min_weight': 0.0, 'max_weight': 1.0, 'top_n_strat_specific': 3}
2025-06-18 12:29:16,153 - INFO - Output short_ema_df tail(1):
                   SPY        SHV        EFA         TLT        PFF
Date                                                               
2021-02-04  357.294724  97.173146  65.734312  133.349374  29.670859
2025-06-18 12:29:16,155 - INFO - Output med_ema_df tail (1):
                   SPY        SHV        EFA         TLT       PFF
Date                                                              
2021-02-04  344.746119  97.173072  63.318777  136.608806  29.29215
2025-06-18 12:29:16,158 - INFO - Output long_ema_df tail (1):
                   SPY        SHV        EFA         TLT        PFF
Date                                                               
2021-02-04  338.194472  97.167957  61.999186  137.551952  29.004642
2025-06-18 12:29:16,158 - INFO - --- Step 2: Calculate EMA Ratios ---
2025-06-18 12:29:16,166 - INFO - Output stmtemax_series (latest):
               SPY       SHV       EFA      TLT       PFF
Date                                                     
2021-02-04  1.0364  1.000001  1.038149  0.97614  1.012929
2025-06-18 12:29:16,169 - INFO - Output mtltemax_series (latest):
                 SPY       SHV       EFA       TLT       PFF
Date                                                        
2021-02-04  1.019372  1.000053  1.021284  0.993143  1.009912
2025-06-18 12:29:16,171 - INFO - Output emaxavg_series (latest for ranking):
                 SPY       SHV       EFA       TLT       PFF
Date                                                        
2021-02-04  1.027886  1.000027  1.029716  0.984642  1.011421
2025-06-18 12:29:16,172 - INFO - --- Step 3: Determine Allocation Weights ---
2025-06-18 12:29:16,172 - INFO - Calling ema_model_module.ema_allocation_model_updated with price_data up to 2021-02-04
2025-06-18 12:29:16,172 - INFO - Attempting to call ema_model_module.ema_allocation_model_updated...
2025-06-18 12:29:16,172 - INFO -   ema_model_module.st_lookback=15, mt_lookback=70, lt_lookback=100
2025-06-18 12:29:16,173 - INFO -   ema_model_module.min_weight=0.0, ema_model_module.max_weight=1.0
2025-06-18 12:29:16,173 - INFO -   Trace script's signal_algo: {'default': "'ema'", 'picklist': ['Strategy_EMA', 'Strategy_SMA', 'Strategy_MACD', 'Strategy_RSI', 'Strategy_BB']}, system_top_n: 2 (Note: production module uses its own settings for these)
2025-06-18 12:29:16,185 - INFO - Successfully called ema_model_module.ema_allocation_model_updated.
2025-06-18 12:29:16,186 - INFO - Output final_weights for 2021-02-04:
SPY    0.4
SHV    0.0
EFA    0.6
TLT    0.0
PFF    0.0
2025-06-18 12:29:16,187 - INFO - Final normalized weights for 2021-02-04: {'SPY': np.float64(0.4), 'SHV': np.float64(0.0), 'EFA': np.float64(0.6), 'TLT': np.float64(0.0), 'PFF': np.float64(0.0)}
2025-06-18 12:29:16,187 - INFO - 
========== Tracing for Date: 2021-02-05 ==========
2025-06-18 12:29:16,188 - INFO - --- Step 1: Calculate EMA Metrics ---
2025-06-18 12:29:16,188 - INFO - Input price_data_slice shape: (277, 5)
2025-06-18 12:29:16,188 - INFO - Setting module-level ema_params in ema_model_module: {'st_lookback': 15, 'mt_lookback': 70, 'lt_lookback': 100, 'min_weight': 0.0, 'max_weight': 1.0, 'top_n_strat_specific': 3}
2025-06-18 12:29:16,200 - INFO - Output short_ema_df tail(1):
                   SPY        SHV        EFA       TLT        PFF
Date                                                             
2021-02-05  358.266631  97.174707  65.798257  132.9368  29.686235
2025-06-18 12:29:16,204 - INFO - Output med_ema_df tail (1):
                   SPY        SHV       EFA         TLT        PFF
Date                                                              
2021-02-05  345.318622  97.173426  63.40123  136.424017  29.306283
2025-06-18 12:29:16,207 - INFO - Output long_ema_df tail (1):
                  SPY        SHV        EFA         TLT       PFF
Date                                                             
2021-02-05  338.72666  97.168307  62.083279  137.403375  29.02027
2025-06-18 12:29:16,207 - INFO - --- Step 2: Calculate EMA Ratios ---
2025-06-18 12:29:16,212 - INFO - Output stmtemax_series (latest):
                 SPY       SHV       EFA       TLT       PFF
Date                                                        
2021-02-05  1.037496  1.000013  1.037807  0.974438  1.012965
2025-06-18 12:29:16,214 - INFO - Output mtltemax_series (latest):
                 SPY       SHV       EFA       TLT       PFF
Date                                                        
2021-02-05  1.019461  1.000053  1.021229  0.992872  1.009856
2025-06-18 12:29:16,217 - INFO - Output emaxavg_series (latest for ranking):
                 SPY       SHV       EFA       TLT      PFF
Date                                                       
2021-02-05  1.028478  1.000033  1.029518  0.983655  1.01141
2025-06-18 12:29:16,217 - INFO - --- Step 3: Determine Allocation Weights ---
2025-06-18 12:29:16,217 - INFO - Calling ema_model_module.ema_allocation_model_updated with price_data up to 2021-02-05
2025-06-18 12:29:16,218 - INFO - Attempting to call ema_model_module.ema_allocation_model_updated...
2025-06-18 12:29:16,218 - INFO -   ema_model_module.st_lookback=15, mt_lookback=70, lt_lookback=100
2025-06-18 12:29:16,218 - INFO -   ema_model_module.min_weight=0.0, ema_model_module.max_weight=1.0
2025-06-18 12:29:16,218 - INFO -   Trace script's signal_algo: {'default': "'ema'", 'picklist': ['Strategy_EMA', 'Strategy_SMA', 'Strategy_MACD', 'Strategy_RSI', 'Strategy_BB']}, system_top_n: 2 (Note: production module uses its own settings for these)
2025-06-18 12:29:16,230 - INFO - Successfully called ema_model_module.ema_allocation_model_updated.
2025-06-18 12:29:16,269 - INFO - Output final_weights for 2021-02-05:
SPY    0.4
SHV    0.0
EFA    0.6
TLT    0.0
PFF    0.0
2025-06-18 12:29:16,269 - INFO - Final normalized weights for 2021-02-05: {'SPY': np.float64(0.4), 'SHV': np.float64(0.0), 'EFA': np.float64(0.6), 'TLT': np.float64(0.0), 'PFF': np.float64(0.0)}
2025-06-18 12:29:16,270 - INFO - 
========== Tracing for Date: 2021-02-08 ==========
2025-06-18 12:29:16,271 - INFO - --- Step 1: Calculate EMA Metrics ---
2025-06-18 12:29:16,271 - INFO - Input price_data_slice shape: (278, 5)
2025-06-18 12:29:16,271 - INFO - Setting module-level ema_params in ema_model_module: {'st_lookback': 15, 'mt_lookback': 70, 'lt_lookback': 100, 'min_weight': 0.0, 'max_weight': 1.0, 'top_n_strat_specific': 3}
2025-06-18 12:29:16,283 - INFO - Output short_ema_df tail(1):
                   SPY        SHV        EFA         TLT      PFF
Date                                                             
2021-02-08  359.446616  97.174976  65.911846  132.647175  29.7036
2025-06-18 12:29:16,285 - INFO - Output med_ema_df tail (1):
                   SPY        SHV        EFA         TLT        PFF
Date                                                               
2021-02-08  345.949266  97.173522  63.494349  136.260518  29.320899
2025-06-18 12:29:16,289 - INFO - Output long_ema_df tail (1):
                   SPY        SHV        EFA         TLT        PFF
Date                                                               
2021-02-08  339.300518  97.168477  62.174837  137.269046  29.036209
2025-06-18 12:29:16,289 - INFO - --- Step 2: Calculate EMA Ratios ---
2025-06-18 12:29:16,294 - INFO - Output stmtemax_series (latest):
                 SPY       SHV       EFA       TLT       PFF
Date                                                        
2021-02-08  1.039015  1.000015  1.038074  0.973482  1.013052
2025-06-18 12:29:16,296 - INFO - Output mtltemax_series (latest):
                 SPY       SHV       EFA       TLT       PFF
Date                                                        
2021-02-08  1.019595  1.000052  1.021223  0.992653  1.009805
2025-06-18 12:29:16,298 - INFO - Output emaxavg_series (latest for ranking):
                 SPY       SHV       EFA       TLT       PFF
Date                                                        
2021-02-08  1.029305  1.000033  1.029648  0.983068  1.011428
2025-06-18 12:29:16,299 - INFO - --- Step 3: Determine Allocation Weights ---
2025-06-18 12:29:16,299 - INFO - Calling ema_model_module.ema_allocation_model_updated with price_data up to 2021-02-08
2025-06-18 12:29:16,299 - INFO - Attempting to call ema_model_module.ema_allocation_model_updated...
2025-06-18 12:29:16,299 - INFO -   ema_model_module.st_lookback=15, mt_lookback=70, lt_lookback=100
2025-06-18 12:29:16,299 - INFO -   ema_model_module.min_weight=0.0, ema_model_module.max_weight=1.0
2025-06-18 12:29:16,300 - INFO -   Trace script's signal_algo: {'default': "'ema'", 'picklist': ['Strategy_EMA', 'Strategy_SMA', 'Strategy_MACD', 'Strategy_RSI', 'Strategy_BB']}, system_top_n: 2 (Note: production module uses its own settings for these)
2025-06-18 12:29:16,309 - INFO - Successfully called ema_model_module.ema_allocation_model_updated.
2025-06-18 12:29:16,310 - INFO - Output final_weights for 2021-02-08:
SPY    0.4
SHV    0.0
EFA    0.6
TLT    0.0
PFF    0.0
2025-06-18 12:29:16,311 - INFO - Final normalized weights for 2021-02-08: {'SPY': np.float64(0.4), 'SHV': np.float64(0.0), 'EFA': np.float64(0.6), 'TLT': np.float64(0.0), 'PFF': np.float64(0.0)}
2025-06-18 12:29:16,311 - INFO - 
========== Tracing for Date: 2021-02-09 ==========
2025-06-18 12:29:16,312 - INFO - --- Step 1: Calculate EMA Metrics ---
2025-06-18 12:29:16,312 - INFO - Input price_data_slice shape: (279, 5)
2025-06-18 12:29:16,312 - INFO - Setting module-level ema_params in ema_model_module: {'st_lookback': 15, 'mt_lookback': 70, 'lt_lookback': 100, 'min_weight': 0.0, 'max_weight': 1.0, 'top_n_strat_specific': 3}
2025-06-18 12:29:16,324 - INFO - Output short_ema_df tail(1):
                   SPY        SHV        EFA        TLT        PFF
Date                                                              
2021-02-09  360.448494  97.176309  66.056679  132.40583  29.721726
2025-06-18 12:29:16,327 - INFO - Output med_ema_df tail (1):
                   SPY        SHV        EFA         TLT        PFF
Date                                                               
2021-02-09  346.555249  97.173864  63.595086  136.104346  29.335764
2025-06-18 12:29:16,329 - INFO - Output long_ema_df tail (1):
                   SPY        SHV        EFA         TLT        PFF
Date                                                               
2021-02-09  339.858164  97.168817  62.271781  137.139291  29.052296
2025-06-18 12:29:16,329 - INFO - --- Step 2: Calculate EMA Ratios ---
2025-06-18 12:29:16,336 - INFO - Output stmtemax_series (latest):
                SPY       SHV       EFA       TLT       PFF
Date                                                       
2021-02-09  1.04009  1.000025  1.038707  0.972826  1.013157
2025-06-18 12:29:16,338 - INFO - Output mtltemax_series (latest):
                 SPY       SHV      EFA       TLT       PFF
Date                                                       
2021-02-09  1.019706  1.000052  1.02125  0.992453  1.009757
2025-06-18 12:29:16,340 - INFO - Output emaxavg_series (latest for ranking):
                 SPY       SHV       EFA      TLT       PFF
Date                                                       
2021-02-09  1.029898  1.000039  1.029979  0.98264  1.011457
2025-06-18 12:29:16,341 - INFO - --- Step 3: Determine Allocation Weights ---
2025-06-18 12:29:16,341 - INFO - Calling ema_model_module.ema_allocation_model_updated with price_data up to 2021-02-09
2025-06-18 12:29:16,341 - INFO - Attempting to call ema_model_module.ema_allocation_model_updated...
2025-06-18 12:29:16,341 - INFO -   ema_model_module.st_lookback=15, mt_lookback=70, lt_lookback=100
2025-06-18 12:29:16,341 - INFO -   ema_model_module.min_weight=0.0, ema_model_module.max_weight=1.0
2025-06-18 12:29:16,341 - INFO -   Trace script's signal_algo: {'default': "'ema'", 'picklist': ['Strategy_EMA', 'Strategy_SMA', 'Strategy_MACD', 'Strategy_RSI', 'Strategy_BB']}, system_top_n: 2 (Note: production module uses its own settings for these)
2025-06-18 12:29:16,352 - INFO - Successfully called ema_model_module.ema_allocation_model_updated.
2025-06-18 12:29:16,353 - INFO - Output final_weights for 2021-02-09:
SPY    0.4
SHV    0.0
EFA    0.6
TLT    0.0
PFF    0.0
2025-06-18 12:29:16,354 - INFO - Final normalized weights for 2021-02-09: {'SPY': np.float64(0.4), 'SHV': np.float64(0.0), 'EFA': np.float64(0.6), 'TLT': np.float64(0.0), 'PFF': np.float64(0.0)}
2025-06-18 12:29:16,354 - INFO - 
========== Tracing for Date: 2021-02-10 ==========
2025-06-18 12:29:16,355 - INFO - --- Step 1: Calculate EMA Metrics ---
2025-06-18 12:29:16,355 - INFO - Input price_data_slice shape: (280, 5)
2025-06-18 12:29:16,356 - INFO - Setting module-level ema_params in ema_model_module: {'st_lookback': 15, 'mt_lookback': 70, 'lt_lookback': 100, 'min_weight': 0.0, 'max_weight': 1.0, 'top_n_strat_specific': 3}
2025-06-18 12:29:16,368 - INFO - Output short_ema_df tail(1):
                   SPY        SHV       EFA         TLT        PFF
Date                                                              
2021-02-10  361.305133  97.177475  66.15459  132.303381  29.735633
2025-06-18 12:29:16,373 - INFO - Output med_ema_df tail (1):
                   SPY        SHV        EFA         TLT       PFF
Date                                                              
2021-02-10  347.139653  97.174195  63.686491  135.977075  29.34977
2025-06-18 12:29:16,375 - INFO - Output long_ema_df tail (1):
                   SPY       SHV       EFA        TLT        PFF
Date                                                            
2021-02-10  340.401599  97.16915  62.36224  137.02933  29.067755
2025-06-18 12:29:16,375 - INFO - --- Step 2: Calculate EMA Ratios ---
2025-06-18 12:29:16,380 - INFO - Output stmtemax_series (latest):
                 SPY       SHV       EFA       TLT       PFF
Date                                                        
2021-02-10  1.040806  1.000034  1.038754  0.972983  1.013147
2025-06-18 12:29:16,383 - INFO - Output mtltemax_series (latest):
                 SPY       SHV       EFA       TLT       PFF
Date                                                        
2021-02-10  1.019794  1.000052  1.021235  0.992321  1.009702
2025-06-18 12:29:16,386 - INFO - Output emaxavg_series (latest for ranking):
               SPY       SHV       EFA       TLT       PFF
Date                                                      
2021-02-10  1.0303  1.000043  1.029994  0.982652  1.011425
2025-06-18 12:29:16,386 - INFO - --- Step 3: Determine Allocation Weights ---
2025-06-18 12:29:16,387 - INFO - Calling ema_model_module.ema_allocation_model_updated with price_data up to 2021-02-10
2025-06-18 12:29:16,387 - INFO - Attempting to call ema_model_module.ema_allocation_model_updated...
2025-06-18 12:29:16,387 - INFO -   ema_model_module.st_lookback=15, mt_lookback=70, lt_lookback=100
2025-06-18 12:29:16,387 - INFO -   ema_model_module.min_weight=0.0, ema_model_module.max_weight=1.0
2025-06-18 12:29:16,387 - INFO -   Trace script's signal_algo: {'default': "'ema'", 'picklist': ['Strategy_EMA', 'Strategy_SMA', 'Strategy_MACD', 'Strategy_RSI', 'Strategy_BB']}, system_top_n: 2 (Note: production module uses its own settings for these)
2025-06-18 12:29:16,396 - INFO - Successfully called ema_model_module.ema_allocation_model_updated.
2025-06-18 12:29:16,397 - INFO - Output final_weights for 2021-02-10:
SPY    0.6
SHV    0.0
EFA    0.4
TLT    0.0
PFF    0.0
2025-06-18 12:29:16,398 - INFO - Final normalized weights for 2021-02-10: {'SPY': np.float64(0.6), 'SHV': np.float64(0.0), 'EFA': np.float64(0.4), 'TLT': np.float64(0.0), 'PFF': np.float64(0.0)}
2025-06-18 12:29:16,398 - INFO - 
========== Tracing for Date: 2021-02-11 ==========
2025-06-18 12:29:16,399 - INFO - --- Step 1: Calculate EMA Metrics ---
2025-06-18 12:29:16,399 - INFO - Input price_data_slice shape: (281, 5)
2025-06-18 12:29:16,399 - INFO - Setting module-level ema_params in ema_model_module: {'st_lookback': 15, 'mt_lookback': 70, 'lt_lookback': 100, 'min_weight': 0.0, 'max_weight': 1.0, 'top_n_strat_specific': 3}
2025-06-18 12:29:16,409 - INFO - Output short_ema_df tail(1):
                   SPY        SHV        EFA         TLT        PFF
Date                                                               
2021-02-11  362.128834  97.177398  66.296789  132.123687  29.739003
2025-06-18 12:29:16,412 - INFO - Output med_ema_df tail (1):
                   SPY       SHV       EFA         TLT        PFF
Date                                                             
2021-02-11  347.724304  97.17427  63.78806  135.833097  29.361399
2025-06-18 12:29:16,414 - INFO - Output long_ema_df tail (1):
                   SPY        SHV        EFA        TLT        PFF
Date                                                              
2021-02-11  340.946017  97.169302  62.459863  136.90728  29.081514
2025-06-18 12:29:16,414 - INFO - --- Step 2: Calculate EMA Ratios ---
2025-06-18 12:29:16,419 - INFO - Output stmtemax_series (latest):
                 SPY       SHV       EFA       TLT       PFF
Date                                                        
2021-02-11  1.041425  1.000032  1.039329  0.972691  1.012861
2025-06-18 12:29:16,421 - INFO - Output mtltemax_series (latest):
                 SPY       SHV       EFA       TLT       PFF
Date                                                        
2021-02-11  1.019881  1.000051  1.021265  0.992154  1.009624
2025-06-18 12:29:16,423 - INFO - Output emaxavg_series (latest for ranking):
                 SPY       SHV       EFA       TLT       PFF
Date                                                        
2021-02-11  1.030653  1.000042  1.030297  0.982423  1.011242
2025-06-18 12:29:16,424 - INFO - --- Step 3: Determine Allocation Weights ---
2025-06-18 12:29:16,424 - INFO - Calling ema_model_module.ema_allocation_model_updated with price_data up to 2021-02-11
2025-06-18 12:29:16,424 - INFO - Attempting to call ema_model_module.ema_allocation_model_updated...
2025-06-18 12:29:16,424 - INFO -   ema_model_module.st_lookback=15, mt_lookback=70, lt_lookback=100
2025-06-18 12:29:16,424 - INFO -   ema_model_module.min_weight=0.0, ema_model_module.max_weight=1.0
2025-06-18 12:29:16,425 - INFO -   Trace script's signal_algo: {'default': "'ema'", 'picklist': ['Strategy_EMA', 'Strategy_SMA', 'Strategy_MACD', 'Strategy_RSI', 'Strategy_BB']}, system_top_n: 2 (Note: production module uses its own settings for these)
2025-06-18 12:29:16,438 - INFO - Successfully called ema_model_module.ema_allocation_model_updated.
2025-06-18 12:29:16,440 - INFO - Output final_weights for 2021-02-11:
SPY    0.6
SHV    0.0
EFA    0.4
TLT    0.0
PFF    0.0
2025-06-18 12:29:16,440 - INFO - Final normalized weights for 2021-02-11: {'SPY': np.float64(0.6), 'SHV': np.float64(0.0), 'EFA': np.float64(0.4), 'TLT': np.float64(0.0), 'PFF': np.float64(0.0)}
2025-06-18 12:29:16,441 - INFO - 
========== Tracing for Date: 2021-02-12 ==========
2025-06-18 12:29:16,441 - INFO - --- Step 1: Calculate EMA Metrics ---
2025-06-18 12:29:16,441 - INFO - Input price_data_slice shape: (282, 5)
2025-06-18 12:29:16,441 - INFO - Setting module-level ema_params in ema_model_module: {'st_lookback': 15, 'mt_lookback': 70, 'lt_lookback': 100, 'min_weight': 0.0, 'max_weight': 1.0, 'top_n_strat_specific': 3}
2025-06-18 12:29:16,452 - INFO - Output short_ema_df tail(1):
                   SPY       SHV        EFA         TLT        PFF
Date                                                              
2021-02-12  363.076742  97.17733  66.467766  131.763288  29.731199
2025-06-18 12:29:16,455 - INFO - Output med_ema_df tail (1):
                   SPY        SHV        EFA         TLT        PFF
Date                                                               
2021-02-12  348.343678  97.174343  63.897259  135.647389  29.370277
2025-06-18 12:29:16,457 - INFO - Output long_ema_df tail (1):
                   SPY        SHV        EFA         TLT        PFF
Date                                                               
2021-02-12  341.515643  97.169452  62.562927  136.755463  29.093297
2025-06-18 12:29:16,458 - INFO - --- Step 2: Calculate EMA Ratios ---
2025-06-18 12:29:16,462 - INFO - Output stmtemax_series (latest):
                 SPY       SHV       EFA       TLT       PFF
Date                                                        
2021-02-12  1.042295  1.000031  1.040229  0.971366  1.012289
2025-06-18 12:29:16,464 - INFO - Output mtltemax_series (latest):
                 SPY      SHV       EFA       TLT      PFF
Date                                                      
2021-02-12  1.019993  1.00005  1.021328  0.991897  1.00952
2025-06-18 12:29:16,466 - INFO - Output emaxavg_series (latest for ranking):
                 SPY       SHV       EFA       TLT       PFF
Date                                                        
2021-02-12  1.031144  1.000041  1.030778  0.981632  1.010905
2025-06-18 12:29:16,466 - INFO - --- Step 3: Determine Allocation Weights ---
2025-06-18 12:29:16,467 - INFO - Calling ema_model_module.ema_allocation_model_updated with price_data up to 2021-02-12
2025-06-18 12:29:16,467 - INFO - Attempting to call ema_model_module.ema_allocation_model_updated...
2025-06-18 12:29:16,467 - INFO -   ema_model_module.st_lookback=15, mt_lookback=70, lt_lookback=100
2025-06-18 12:29:16,468 - INFO -   ema_model_module.min_weight=0.0, ema_model_module.max_weight=1.0
2025-06-18 12:29:16,468 - INFO -   Trace script's signal_algo: {'default': "'ema'", 'picklist': ['Strategy_EMA', 'Strategy_SMA', 'Strategy_MACD', 'Strategy_RSI', 'Strategy_BB']}, system_top_n: 2 (Note: production module uses its own settings for these)
2025-06-18 12:29:16,478 - INFO - Successfully called ema_model_module.ema_allocation_model_updated.
2025-06-18 12:29:16,480 - INFO - Output final_weights for 2021-02-12:
SPY    0.6
SHV    0.0
EFA    0.4
TLT    0.0
PFF    0.0
2025-06-18 12:29:16,480 - INFO - Final normalized weights for 2021-02-12: {'SPY': np.float64(0.6), 'SHV': np.float64(0.0), 'EFA': np.float64(0.4), 'TLT': np.float64(0.0), 'PFF': np.float64(0.0)}
2025-06-18 12:29:16,480 - INFO - 
========== Tracing for Date: 2021-02-16 ==========
2025-06-18 12:29:16,481 - INFO - --- Step 1: Calculate EMA Metrics ---
2025-06-18 12:29:16,481 - INFO - Input price_data_slice shape: (283, 5)
2025-06-18 12:29:16,481 - INFO - Setting module-level ema_params in ema_model_module: {'st_lookback': 15, 'mt_lookback': 70, 'lt_lookback': 100, 'min_weight': 0.0, 'max_weight': 1.0, 'top_n_strat_specific': 3}
2025-06-18 12:29:16,494 - INFO - Output short_ema_df tail(1):
                  SPY        SHV        EFA         TLT      PFF
Date                                                            
2021-02-16  363.86613  97.177271  66.678333  131.201951  29.7058
2025-06-18 12:29:16,496 - INFO - Output med_ema_df tail (1):
                   SPY        SHV        EFA        TLT       PFF
Date                                                             
2021-02-16  348.936584  97.174414  64.017119  135.41148  29.37472
2025-06-18 12:29:16,498 - INFO - Output long_ema_df tail (1):
                   SPY        SHV        EFA         TLT        PFF
Date                                                               
2021-02-16  342.067647  97.169599  62.673607  136.567683  29.101905
2025-06-18 12:29:16,498 - INFO - --- Step 2: Calculate EMA Ratios ---
2025-06-18 12:29:16,502 - INFO - Output stmtemax_series (latest):
                 SPY       SHV      EFA       TLT       PFF
Date                                                       
2021-02-16  1.042786  1.000029  1.04157  0.968913  1.011271
2025-06-18 12:29:16,504 - INFO - Output mtltemax_series (latest):
                 SPY      SHV       EFA       TLT       PFF
Date                                                       
2021-02-16  1.020081  1.00005  1.021437  0.991534  1.009374
2025-06-18 12:29:16,507 - INFO - Output emaxavg_series (latest for ranking):
                 SPY       SHV       EFA       TLT       PFF
Date                                                        
2021-02-16  1.031433  1.000039  1.031503  0.980223  1.010323
2025-06-18 12:29:16,507 - INFO - --- Step 3: Determine Allocation Weights ---
2025-06-18 12:29:16,508 - INFO - Calling ema_model_module.ema_allocation_model_updated with price_data up to 2021-02-16
2025-06-18 12:29:16,508 - INFO - Attempting to call ema_model_module.ema_allocation_model_updated...
2025-06-18 12:29:16,508 - INFO -   ema_model_module.st_lookback=15, mt_lookback=70, lt_lookback=100
2025-06-18 12:29:16,508 - INFO -   ema_model_module.min_weight=0.0, ema_model_module.max_weight=1.0
2025-06-18 12:29:16,508 - INFO -   Trace script's signal_algo: {'default': "'ema'", 'picklist': ['Strategy_EMA', 'Strategy_SMA', 'Strategy_MACD', 'Strategy_RSI', 'Strategy_BB']}, system_top_n: 2 (Note: production module uses its own settings for these)
2025-06-18 12:29:16,517 - INFO - Successfully called ema_model_module.ema_allocation_model_updated.
2025-06-18 12:29:16,519 - INFO - Output final_weights for 2021-02-16:
SPY    0.4
SHV    0.0
EFA    0.6
TLT    0.0
PFF    0.0
2025-06-18 12:29:16,519 - INFO - Final normalized weights for 2021-02-16: {'SPY': np.float64(0.4), 'SHV': np.float64(0.0), 'EFA': np.float64(0.6), 'TLT': np.float64(0.0), 'PFF': np.float64(0.0)}
2025-06-18 12:29:16,519 - INFO - 
========== Tracing for Date: 2021-02-17 ==========
2025-06-18 12:29:16,520 - INFO - --- Step 1: Calculate EMA Metrics ---
2025-06-18 12:29:16,520 - INFO - Input price_data_slice shape: (284, 5)
2025-06-18 12:29:16,520 - INFO - Setting module-level ema_params in ema_model_module: {'st_lookback': 15, 'mt_lookback': 70, 'lt_lookback': 100, 'min_weight': 0.0, 'max_weight': 1.0, 'top_n_strat_specific': 3}
2025-06-18 12:29:16,530 - INFO - Output short_ema_df tail(1):
                   SPY        SHV        EFA         TLT        PFF
Date                                                               
2021-02-17  364.567449  97.177219  66.812702  130.811816  29.681619
2025-06-18 12:29:16,534 - INFO - Output med_ema_df tail (1):
                   SPY        SHV        EFA         TLT        PFF
Date                                                               
2021-02-17  349.515179  97.174483  64.122363  135.204984  29.378597
2025-06-18 12:29:16,536 - INFO - Output long_ema_df tail (1):
                 SPY        SHV        EFA         TLT        PFF
Date                                                             
2021-02-17  342.6104  97.169742  62.774195  136.399627  29.110033
2025-06-18 12:29:16,537 - INFO - --- Step 2: Calculate EMA Ratios ---
2025-06-18 12:29:16,542 - INFO - Output stmtemax_series (latest):
                 SPY       SHV       EFA       TLT       PFF
Date                                                        
2021-02-17  1.043066  1.000028  1.041956  0.967507  1.010314
2025-06-18 12:29:16,544 - INFO - Output mtltemax_series (latest):
                 SPY       SHV       EFA       TLT       PFF
Date                                                        
2021-02-17  1.020153  1.000049  1.021476  0.991242  1.009226
2025-06-18 12:29:16,546 - INFO - Output emaxavg_series (latest for ranking):
                SPY       SHV       EFA       TLT      PFF
Date                                                      
2021-02-17  1.03161  1.000038  1.031716  0.979374  1.00977
2025-06-18 12:29:16,547 - INFO - --- Step 3: Determine Allocation Weights ---
2025-06-18 12:29:16,547 - INFO - Calling ema_model_module.ema_allocation_model_updated with price_data up to 2021-02-17
2025-06-18 12:29:16,547 - INFO - Attempting to call ema_model_module.ema_allocation_model_updated...
2025-06-18 12:29:16,547 - INFO -   ema_model_module.st_lookback=15, mt_lookback=70, lt_lookback=100
2025-06-18 12:29:16,547 - INFO -   ema_model_module.min_weight=0.0, ema_model_module.max_weight=1.0
2025-06-18 12:29:16,548 - INFO -   Trace script's signal_algo: {'default': "'ema'", 'picklist': ['Strategy_EMA', 'Strategy_SMA', 'Strategy_MACD', 'Strategy_RSI', 'Strategy_BB']}, system_top_n: 2 (Note: production module uses its own settings for these)
2025-06-18 12:29:16,556 - INFO - Successfully called ema_model_module.ema_allocation_model_updated.
2025-06-18 12:29:16,557 - INFO - Output final_weights for 2021-02-17:
SPY    0.4
SHV    0.0
EFA    0.6
TLT    0.0
PFF    0.0
2025-06-18 12:29:16,558 - INFO - Final normalized weights for 2021-02-17: {'SPY': np.float64(0.4), 'SHV': np.float64(0.0), 'EFA': np.float64(0.6), 'TLT': np.float64(0.0), 'PFF': np.float64(0.0)}
2025-06-18 12:29:16,558 - INFO - 
========== Tracing for Date: 2021-02-18 ==========
2025-06-18 12:29:16,559 - INFO - --- Step 1: Calculate EMA Metrics ---
2025-06-18 12:29:16,559 - INFO - Input price_data_slice shape: (285, 5)
2025-06-18 12:29:16,560 - INFO - Setting module-level ema_params in ema_model_module: {'st_lookback': 15, 'mt_lookback': 70, 'lt_lookback': 100, 'min_weight': 0.0, 'max_weight': 1.0, 'top_n_strat_specific': 3}
2025-06-18 12:29:16,571 - INFO - Output short_ema_df tail(1):
                   SPY        SHV        EFA         TLT        PFF
Date                                                               
2021-02-18  364.984544  97.177174  66.888156  130.405658  29.660461
2025-06-18 12:29:16,573 - INFO - Output med_ema_df tail (1):
                   SPY       SHV        EFA         TLT        PFF
Date                                                              
2021-02-18  350.033179  97.17455  64.215151  134.989704  29.382365
2025-06-18 12:29:16,576 - INFO - Output long_ema_df tail (1):
                   SPY        SHV        EFA         TLT     PFF
Date                                                            
2021-02-18  343.111268  97.169883  62.866119  136.224636  29.118
2025-06-18 12:29:16,576 - INFO - --- Step 2: Calculate EMA Ratios ---
2025-06-18 12:29:16,581 - INFO - Output stmtemax_series (latest):
                 SPY       SHV       EFA       TLT       PFF
Date                                                        
2021-02-18  1.042714  1.000027  1.041626  0.966042  1.009465
2025-06-18 12:29:16,584 - INFO - Output mtltemax_series (latest):
                 SPY       SHV       EFA       TLT       PFF
Date                                                        
2021-02-18  1.020174  1.000048  1.021459  0.990935  1.009079
2025-06-18 12:29:16,586 - INFO - Output emaxavg_series (latest for ranking):
                 SPY       SHV       EFA       TLT       PFF
Date                                                        
2021-02-18  1.031444  1.000038  1.031542  0.978488  1.009272
2025-06-18 12:29:16,587 - INFO - --- Step 3: Determine Allocation Weights ---
2025-06-18 12:29:16,587 - INFO - Calling ema_model_module.ema_allocation_model_updated with price_data up to 2021-02-18
2025-06-18 12:29:16,587 - INFO - Attempting to call ema_model_module.ema_allocation_model_updated...
2025-06-18 12:29:16,587 - INFO -   ema_model_module.st_lookback=15, mt_lookback=70, lt_lookback=100
2025-06-18 12:29:16,588 - INFO -   ema_model_module.min_weight=0.0, ema_model_module.max_weight=1.0
2025-06-18 12:29:16,588 - INFO -   Trace script's signal_algo: {'default': "'ema'", 'picklist': ['Strategy_EMA', 'Strategy_SMA', 'Strategy_MACD', 'Strategy_RSI', 'Strategy_BB']}, system_top_n: 2 (Note: production module uses its own settings for these)
2025-06-18 12:29:16,598 - INFO - Successfully called ema_model_module.ema_allocation_model_updated.
2025-06-18 12:29:16,600 - INFO - Output final_weights for 2021-02-18:
SPY    0.4
SHV    0.0
EFA    0.6
TLT    0.0
PFF    0.0
2025-06-18 12:29:16,600 - INFO - Final normalized weights for 2021-02-18: {'SPY': np.float64(0.4), 'SHV': np.float64(0.0), 'EFA': np.float64(0.6), 'TLT': np.float64(0.0), 'PFF': np.float64(0.0)}
2025-06-18 12:29:16,601 - INFO - 
========== Tracing for Date: 2021-02-19 ==========
2025-06-18 12:29:16,602 - INFO - --- Step 1: Calculate EMA Metrics ---
2025-06-18 12:29:16,602 - INFO - Input price_data_slice shape: (286, 5)
2025-06-18 12:29:16,602 - INFO - Setting module-level ema_params in ema_model_module: {'st_lookback': 15, 'mt_lookback': 70, 'lt_lookback': 100, 'min_weight': 0.0, 'max_weight': 1.0, 'top_n_strat_specific': 3}
2025-06-18 12:29:16,614 - INFO - Output short_ema_df tail(1):
                   SPY        SHV        EFA        TLT       PFF
Date                                                             
2021-02-19  365.268287  97.177134  66.978562  129.83832  29.63706
2025-06-18 12:29:16,619 - INFO - Output med_ema_df tail (1):
                   SPY        SHV       EFA         TLT        PFF
Date                                                              
2021-02-19  350.518287  97.174615  64.31082  134.732725  29.384925
2025-06-18 12:29:16,622 - INFO - Output long_ema_df tail (1):
                   SPY        SHV        EFA         TLT        PFF
Date                                                               
2021-02-19  343.589351  97.170021  62.960085  136.019533  29.125034
2025-06-18 12:29:16,622 - INFO - --- Step 2: Calculate EMA Ratios ---
2025-06-18 12:29:16,628 - INFO - Output stmtemax_series (latest):
                 SPY       SHV       EFA       TLT      PFF
Date                                                       
2021-02-19  1.042081  1.000026  1.041482  0.963673  1.00858
2025-06-18 12:29:16,630 - INFO - Output mtltemax_series (latest):
                 SPY       SHV       EFA      TLT       PFF
Date                                                       
2021-02-19  1.020166  1.000047  1.021454  0.99054  1.008923
2025-06-18 12:29:16,632 - INFO - Output emaxavg_series (latest for ranking):
                 SPY       SHV       EFA       TLT       PFF
Date                                                        
2021-02-19  1.031123  1.000037  1.031468  0.977106  1.008752
2025-06-18 12:29:16,632 - INFO - --- Step 3: Determine Allocation Weights ---
2025-06-18 12:29:16,632 - INFO - Calling ema_model_module.ema_allocation_model_updated with price_data up to 2021-02-19
2025-06-18 12:29:16,633 - INFO - Attempting to call ema_model_module.ema_allocation_model_updated...
2025-06-18 12:29:16,633 - INFO -   ema_model_module.st_lookback=15, mt_lookback=70, lt_lookback=100
2025-06-18 12:29:16,633 - INFO -   ema_model_module.min_weight=0.0, ema_model_module.max_weight=1.0
2025-06-18 12:29:16,633 - INFO -   Trace script's signal_algo: {'default': "'ema'", 'picklist': ['Strategy_EMA', 'Strategy_SMA', 'Strategy_MACD', 'Strategy_RSI', 'Strategy_BB']}, system_top_n: 2 (Note: production module uses its own settings for these)
2025-06-18 12:29:16,647 - INFO - Successfully called ema_model_module.ema_allocation_model_updated.
2025-06-18 12:29:16,648 - INFO - Output final_weights for 2021-02-19:
SPY    0.4
SHV    0.0
EFA    0.6
TLT    0.0
PFF    0.0
2025-06-18 12:29:16,649 - INFO - Final normalized weights for 2021-02-19: {'SPY': np.float64(0.4), 'SHV': np.float64(0.0), 'EFA': np.float64(0.6), 'TLT': np.float64(0.0), 'PFF': np.float64(0.0)}
2025-06-18 12:29:16,650 - INFO - 
========== Tracing for Date: 2021-02-22 ==========
2025-06-18 12:29:16,651 - INFO - --- Step 1: Calculate EMA Metrics ---
2025-06-18 12:29:16,651 - INFO - Input price_data_slice shape: (287, 5)
2025-06-18 12:29:16,651 - INFO - Setting module-level ema_params in ema_model_module: {'st_lookback': 15, 'mt_lookback': 70, 'lt_lookback': 100, 'min_weight': 0.0, 'max_weight': 1.0, 'top_n_strat_specific': 3}
2025-06-18 12:29:16,663 - INFO - Output short_ema_df tail(1):
                   SPY      SHV        EFA         TLT        PFF
Date                                                             
2021-02-22  365.163462  97.1771  67.036608  129.222205  29.600944
2025-06-18 12:29:16,665 - INFO - Output med_ema_df tail (1):
                   SPY        SHV        EFA         TLT        PFF
Date                                                               
2021-02-22  350.910157  97.174678  64.399048  134.456012  29.383889
2025-06-18 12:29:16,667 - INFO - Output long_ema_df tail (1):
                   SPY        SHV        EFA         TLT        PFF
Date                                                               
2021-02-22  344.002031  97.170157  63.048854  135.799531  29.129452
2025-06-18 12:29:16,668 - INFO - --- Step 2: Calculate EMA Ratios ---
2025-06-18 12:29:16,673 - INFO - Output stmtemax_series (latest):
                 SPY       SHV       EFA       TLT       PFF
Date                                                        
2021-02-22  1.040618  1.000025  1.040956  0.961074  1.007387
2025-06-18 12:29:16,675 - INFO - Output mtltemax_series (latest):
                 SPY       SHV       EFA       TLT       PFF
Date                                                        
2021-02-22  1.020082  1.000047  1.021415  0.990107  1.008735
2025-06-18 12:29:16,677 - INFO - Output emaxavg_series (latest for ranking):
                SPY       SHV       EFA      TLT       PFF
Date                                                      
2021-02-22  1.03035  1.000036  1.031186  0.97559  1.008061
2025-06-18 12:29:16,678 - INFO - --- Step 3: Determine Allocation Weights ---
2025-06-18 12:29:16,679 - INFO - Calling ema_model_module.ema_allocation_model_updated with price_data up to 2021-02-22
2025-06-18 12:29:16,679 - INFO - Attempting to call ema_model_module.ema_allocation_model_updated...
2025-06-18 12:29:16,679 - INFO -   ema_model_module.st_lookback=15, mt_lookback=70, lt_lookback=100
2025-06-18 12:29:16,679 - INFO -   ema_model_module.min_weight=0.0, ema_model_module.max_weight=1.0
2025-06-18 12:29:16,679 - INFO -   Trace script's signal_algo: {'default': "'ema'", 'picklist': ['Strategy_EMA', 'Strategy_SMA', 'Strategy_MACD', 'Strategy_RSI', 'Strategy_BB']}, system_top_n: 2 (Note: production module uses its own settings for these)
2025-06-18 12:29:16,690 - INFO - Successfully called ema_model_module.ema_allocation_model_updated.
2025-06-18 12:29:16,692 - INFO - Output final_weights for 2021-02-22:
SPY    0.4
SHV    0.0
EFA    0.6
TLT    0.0
PFF    0.0
2025-06-18 12:29:16,692 - INFO - Final normalized weights for 2021-02-22: {'SPY': np.float64(0.4), 'SHV': np.float64(0.0), 'EFA': np.float64(0.6), 'TLT': np.float64(0.0), 'PFF': np.float64(0.0)}
2025-06-18 12:29:16,692 - INFO - 
========== Tracing for Date: 2021-02-23 ==========
2025-06-18 12:29:16,693 - INFO - --- Step 1: Calculate EMA Metrics ---
2025-06-18 12:29:16,693 - INFO - Input price_data_slice shape: (288, 5)
2025-06-18 12:29:16,693 - INFO - Setting module-level ema_params in ema_model_module: {'st_lookback': 15, 'mt_lookback': 70, 'lt_lookback': 100, 'min_weight': 0.0, 'max_weight': 1.0, 'top_n_strat_specific': 3}
2025-06-18 12:29:16,709 - INFO - Output short_ema_df tail(1):
                   SPY        SHV        EFA         TLT        PFF
Date                                                               
2021-02-23  365.127057  97.177069  67.095157  128.638076  29.566411
2025-06-18 12:29:16,711 - INFO - Output med_ema_df tail (1):
                   SPY        SHV       EFA         TLT        PFF
Date                                                              
2021-02-23  351.303455  97.174739  64.48654  134.176946  29.382221
2025-06-18 12:29:16,713 - INFO - Output long_ema_df tail (1):
                   SPY        SHV        EFA         TLT        PFF
Date                                                               
2021-02-23  344.415302  97.170289  63.137094  135.576751  29.133318
2025-06-18 12:29:16,713 - INFO - --- Step 2: Calculate EMA Ratios ---
2025-06-18 12:29:16,722 - INFO - Output stmtemax_series (latest):
                 SPY       SHV       EFA      TLT       PFF
Date                                                       
2021-02-23  1.039349  1.000024  1.040452  0.95872  1.006269
2025-06-18 12:29:16,725 - INFO - Output mtltemax_series (latest):
             SPY       SHV       EFA       TLT       PFF
Date                                                    
2021-02-23  1.02  1.000046  1.021373  0.989675  1.008544
2025-06-18 12:29:16,727 - INFO - Output emaxavg_series (latest for ranking):
                 SPY       SHV       EFA       TLT       PFF
Date                                                        
2021-02-23  1.029675  1.000035  1.030913  0.974197  1.007406
2025-06-18 12:29:16,727 - INFO - --- Step 3: Determine Allocation Weights ---
2025-06-18 12:29:16,727 - INFO - Calling ema_model_module.ema_allocation_model_updated with price_data up to 2021-02-23
2025-06-18 12:29:16,728 - INFO - Attempting to call ema_model_module.ema_allocation_model_updated...
2025-06-18 12:29:16,728 - INFO -   ema_model_module.st_lookback=15, mt_lookback=70, lt_lookback=100
2025-06-18 12:29:16,728 - INFO -   ema_model_module.min_weight=0.0, ema_model_module.max_weight=1.0
2025-06-18 12:29:16,728 - INFO -   Trace script's signal_algo: {'default': "'ema'", 'picklist': ['Strategy_EMA', 'Strategy_SMA', 'Strategy_MACD', 'Strategy_RSI', 'Strategy_BB']}, system_top_n: 2 (Note: production module uses its own settings for these)
2025-06-18 12:29:16,740 - INFO - Successfully called ema_model_module.ema_allocation_model_updated.
2025-06-18 12:29:16,741 - INFO - Output final_weights for 2021-02-23:
SPY    0.4
SHV    0.0
EFA    0.6
TLT    0.0
PFF    0.0
2025-06-18 12:29:16,741 - INFO - Final normalized weights for 2021-02-23: {'SPY': np.float64(0.4), 'SHV': np.float64(0.0), 'EFA': np.float64(0.6), 'TLT': np.float64(0.0), 'PFF': np.float64(0.0)}
2025-06-18 12:29:16,741 - INFO - 
========== Tracing for Date: 2021-02-24 ==========
2025-06-18 12:29:16,742 - INFO - --- Step 1: Calculate EMA Metrics ---
2025-06-18 12:29:16,742 - INFO - Input price_data_slice shape: (289, 5)
2025-06-18 12:29:16,743 - INFO - Setting module-level ema_params in ema_model_module: {'st_lookback': 15, 'mt_lookback': 70, 'lt_lookback': 100, 'min_weight': 0.0, 'max_weight': 1.0, 'top_n_strat_specific': 3}
2025-06-18 12:29:16,755 - INFO - Output short_ema_df tail(1):
                   SPY        SHV       EFA         TLT        PFF
Date                                                              
2021-02-24  365.597785  97.177043  67.17299  128.024836  29.531308
2025-06-18 12:29:16,758 - INFO - Output med_ema_df tail (1):
                   SPY        SHV        EFA         TLT        PFF
Date                                                               
2021-02-24  351.798932  97.174799  64.577562  133.882726  29.379499
2025-06-18 12:29:16,761 - INFO - Output long_ema_df tail (1):
                   SPY        SHV        EFA         TLT        PFF
Date                                                               
2021-02-24  344.900007  97.170419  63.227802  135.342205  29.136333
2025-06-18 12:29:16,761 - INFO - --- Step 2: Calculate EMA Ratios ---
2025-06-18 12:29:16,766 - INFO - Output stmtemax_series (latest):
                 SPY       SHV       EFA       TLT       PFF
Date                                                        
2021-02-24  1.039224  1.000023  1.040191  0.956246  1.005167
2025-06-18 12:29:16,768 - INFO - Output mtltemax_series (latest):
                 SPY       SHV       EFA       TLT       PFF
Date                                                        
2021-02-24  1.020003  1.000045  1.021348  0.989216  1.008346
2025-06-18 12:29:16,770 - INFO - Output emaxavg_series (latest for ranking):
                 SPY       SHV       EFA       TLT       PFF
Date                                                        
2021-02-24  1.029613  1.000034  1.030769  0.972731  1.006756
2025-06-18 12:29:16,771 - INFO - --- Step 3: Determine Allocation Weights ---
2025-06-18 12:29:16,771 - INFO - Calling ema_model_module.ema_allocation_model_updated with price_data up to 2021-02-24
2025-06-18 12:29:16,772 - INFO - Attempting to call ema_model_module.ema_allocation_model_updated...
2025-06-18 12:29:16,772 - INFO -   ema_model_module.st_lookback=15, mt_lookback=70, lt_lookback=100
2025-06-18 12:29:16,772 - INFO -   ema_model_module.min_weight=0.0, ema_model_module.max_weight=1.0
2025-06-18 12:29:16,772 - INFO -   Trace script's signal_algo: {'default': "'ema'", 'picklist': ['Strategy_EMA', 'Strategy_SMA', 'Strategy_MACD', 'Strategy_RSI', 'Strategy_BB']}, system_top_n: 2 (Note: production module uses its own settings for these)
2025-06-18 12:29:16,784 - INFO - Successfully called ema_model_module.ema_allocation_model_updated.
2025-06-18 12:29:16,786 - INFO - Output final_weights for 2021-02-24:
SPY    0.4
SHV    0.0
EFA    0.6
TLT    0.0
PFF    0.0
2025-06-18 12:29:16,786 - INFO - Final normalized weights for 2021-02-24: {'SPY': np.float64(0.4), 'SHV': np.float64(0.0), 'EFA': np.float64(0.6), 'TLT': np.float64(0.0), 'PFF': np.float64(0.0)}
2025-06-18 12:29:16,787 - INFO - 
========== Tracing for Date: 2021-02-25 ==========
2025-06-18 12:29:16,787 - INFO - --- Step 1: Calculate EMA Metrics ---
2025-06-18 12:29:16,787 - INFO - Input price_data_slice shape: (290, 5)
2025-06-18 12:29:16,787 - INFO - Setting module-level ema_params in ema_model_module: {'st_lookback': 15, 'mt_lookback': 70, 'lt_lookback': 100, 'min_weight': 0.0, 'max_weight': 1.0, 'top_n_strat_specific': 3}
2025-06-18 12:29:16,800 - INFO - Output short_ema_df tail(1):
                   SPY       SHV        EFA         TLT        PFF
Date                                                              
2021-02-25  364.898581  97.17592  67.097003  127.235668  29.471267
2025-06-18 12:29:16,802 - INFO - Output med_ema_df tail (1):
                   SPY        SHV        EFA         TLT        PFF
Date                                                               
2021-02-25  352.030064  97.174609  64.633549  133.539875  29.370245
2025-06-18 12:29:16,804 - INFO - Output long_ema_df tail (1):
                   SPY        SHV        EFA        TLT        PFF
Date                                                              
2021-02-25  345.199099  97.170373  63.293887  135.07229  29.134643
2025-06-18 12:29:16,805 - INFO - --- Step 2: Calculate EMA Ratios ---
2025-06-18 12:29:16,809 - INFO - Output stmtemax_series (latest):
                 SPY       SHV       EFA       TLT      PFF
Date                                                       
2021-02-25  1.036555  1.000013  1.038114  0.952792  1.00344
2025-06-18 12:29:16,811 - INFO - Output mtltemax_series (latest):
                 SPY       SHV       EFA       TLT       PFF
Date                                                        
2021-02-25  1.019788  1.000044  1.021166  0.988655  1.008087
2025-06-18 12:29:16,814 - INFO - Output emaxavg_series (latest for ranking):
                 SPY       SHV      EFA       TLT       PFF
Date                                                       
2021-02-25  1.028172  1.000029  1.02964  0.970723  1.005763
2025-06-18 12:29:16,814 - INFO - --- Step 3: Determine Allocation Weights ---
2025-06-18 12:29:16,814 - INFO - Calling ema_model_module.ema_allocation_model_updated with price_data up to 2021-02-25
2025-06-18 12:29:16,815 - INFO - Attempting to call ema_model_module.ema_allocation_model_updated...
2025-06-18 12:29:16,815 - INFO -   ema_model_module.st_lookback=15, mt_lookback=70, lt_lookback=100
2025-06-18 12:29:16,815 - INFO -   ema_model_module.min_weight=0.0, ema_model_module.max_weight=1.0
2025-06-18 12:29:16,815 - INFO -   Trace script's signal_algo: {'default': "'ema'", 'picklist': ['Strategy_EMA', 'Strategy_SMA', 'Strategy_MACD', 'Strategy_RSI', 'Strategy_BB']}, system_top_n: 2 (Note: production module uses its own settings for these)
2025-06-18 12:29:16,825 - INFO - Successfully called ema_model_module.ema_allocation_model_updated.
2025-06-18 12:29:16,826 - INFO - Output final_weights for 2021-02-25:
SPY    0.4
SHV    0.0
EFA    0.6
TLT    0.0
PFF    0.0
2025-06-18 12:29:16,827 - INFO - Final normalized weights for 2021-02-25: {'SPY': np.float64(0.4), 'SHV': np.float64(0.0), 'EFA': np.float64(0.6), 'TLT': np.float64(0.0), 'PFF': np.float64(0.0)}
2025-06-18 12:29:16,827 - INFO - 
========== Tracing for Date: 2021-02-26 ==========
2025-06-18 12:29:16,828 - INFO - --- Step 1: Calculate EMA Metrics ---
2025-06-18 12:29:16,828 - INFO - Input price_data_slice shape: (291, 5)
2025-06-18 12:29:16,828 - INFO - Setting module-level ema_params in ema_model_module: {'st_lookback': 15, 'mt_lookback': 70, 'lt_lookback': 100, 'min_weight': 0.0, 'max_weight': 1.0, 'top_n_strat_specific': 3}
2025-06-18 12:29:16,838 - INFO - Output short_ema_df tail(1):
                   SPY        SHV        EFA         TLT        PFF
Date                                                               
2021-02-26  364.054908  97.176037  66.913026  127.048107  29.440235
2025-06-18 12:29:16,840 - INFO - Output med_ema_df tail (1):
                   SPY        SHV        EFA         TLT        PFF
Date                                                               
2021-02-26  352.202435  97.174672  64.661482  133.320024  29.366097
2025-06-18 12:29:16,842 - INFO - Output long_ema_df tail (1):
                   SPY        SHV        EFA         TLT        PFF
Date                                                               
2021-02-26  345.455536  97.170501  63.340051  134.887397  29.136393
2025-06-18 12:29:16,843 - INFO - --- Step 2: Calculate EMA Ratios ---
2025-06-18 12:29:16,848 - INFO - Output stmtemax_series (latest):
                 SPY       SHV      EFA       TLT       PFF
Date                                                       
2021-02-26  1.033652  1.000014  1.03482  0.952956  1.002525
2025-06-18 12:29:16,850 - INFO - Output mtltemax_series (latest):
                SPY       SHV       EFA      TLT       PFF
Date                                                      
2021-02-26  1.01953  1.000043  1.020862  0.98838  1.007884
2025-06-18 12:29:16,853 - INFO - Output emaxavg_series (latest for ranking):
                 SPY       SHV       EFA       TLT       PFF
Date                                                        
2021-02-26  1.026591  1.000028  1.027841  0.970668  1.005204
2025-06-18 12:29:16,853 - INFO - --- Step 3: Determine Allocation Weights ---
2025-06-18 12:29:16,853 - INFO - Calling ema_model_module.ema_allocation_model_updated with price_data up to 2021-02-26
2025-06-18 12:29:16,853 - INFO - Attempting to call ema_model_module.ema_allocation_model_updated...
2025-06-18 12:29:16,854 - INFO -   ema_model_module.st_lookback=15, mt_lookback=70, lt_lookback=100
2025-06-18 12:29:16,854 - INFO -   ema_model_module.min_weight=0.0, ema_model_module.max_weight=1.0
2025-06-18 12:29:16,854 - INFO -   Trace script's signal_algo: {'default': "'ema'", 'picklist': ['Strategy_EMA', 'Strategy_SMA', 'Strategy_MACD', 'Strategy_RSI', 'Strategy_BB']}, system_top_n: 2 (Note: production module uses its own settings for these)
2025-06-18 12:29:16,862 - INFO - Successfully called ema_model_module.ema_allocation_model_updated.
2025-06-18 12:29:16,864 - INFO - Output final_weights for 2021-02-26:
SPY    0.4
SHV    0.0
EFA    0.6
TLT    0.0
PFF    0.0
2025-06-18 12:29:16,865 - INFO - Final normalized weights for 2021-02-26: {'SPY': np.float64(0.4), 'SHV': np.float64(0.0), 'EFA': np.float64(0.6), 'TLT': np.float64(0.0), 'PFF': np.float64(0.0)}
2025-06-18 12:29:16,867 - INFO - Attempting to save final weights to S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\test_logs\production_flow_weights_20210120_20210228_20250618_122915.csv...
2025-06-18 12:29:16,874 - INFO - Final weights CSV saved to: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\test_logs\production_flow_weights_20210120_20210228_20250618_122915.csv
2025-06-18 12:29:16,879 - INFO - Trace script finished (end of try block).
2025-06-18 12:29:16,880 - INFO - EMA production flow trace finished (finally block).
