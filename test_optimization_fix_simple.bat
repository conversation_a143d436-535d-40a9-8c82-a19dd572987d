@echo off
echo =====================================================
echo CPS V4 Step 3 Validation Debug Test
echo =====================================================
echo.

rem Activate Python virtual environment
call F:\AI_Library\my_quant_env\Scripts\activate.bat

echo Current directory: %CD%
echo Start time: %DATE% %TIME%

rem Change to script directory
cd /d "%~dp0"
echo Working directory: %CD%

rem Create log file
set TIMESTAMP=%date:~10,4%%date:~4,2%%date:~7,2%_%time:~0,2%%time:~3,2%%time:~6,2%
set TIMESTAMP=%TIMESTAMP: =0%
if not exist "optimization_validation" mkdir "optimization_validation"
set LOG_FILE=optimization_validation\validation_run_%TIMESTAMP%.log

echo ===== STARTING VALIDATION TEST ===== > %LOG_FILE%
echo Start time: %DATE% %TIME% >> %LOG_FILE%
echo. >> %LOG_FILE%

echo Running Step 3 validation test...
echo Running Step 3 validation test... >> %LOG_FILE%

rem Run the validation with real-time output and logging
echo Running Python script with real-time output...
python test_optimization_validation.py 2>&1 | powershell -Command "& {$input | ForEach-Object { Write-Host $_; Add-Content -Path '%LOG_FILE%' -Value $_ }}"

set EXIT_CODE=%errorlevel%
echo. >> %LOG_FILE%
echo ===== TEST COMPLETED ===== >> %LOG_FILE%
echo End time: %DATE% %TIME% >> %LOG_FILE%
echo Exit code: %EXIT_CODE% >> %LOG_FILE%

if %EXIT_CODE% neq 0 (
    echo.
    echo =====================================================
    echo TEST FAILED - Exit code: %EXIT_CODE%
    echo =====================================================
    echo.
    echo Key logs to check:
    echo 1. Main log: %LOG_FILE%
    
    rem Find latest validation directory
    for /f %%i in ('dir /b /od "optimization_validation\2*" 2^>nul') do set LATEST_DIR=%%i
    if defined LATEST_DIR (
        echo 2. Validation dir: optimization_validation\%LATEST_DIR%\
        echo 3. Subprocess log: optimization_validation\%LATEST_DIR%\step03__subprocess_*.log
        echo 4. Pipeline mode: optimization_validation\pipeline_mode_debug.log
    )
    echo.
    exit /b 1
) else (
    echo.
    echo =====================================================
    echo TEST PASSED!
    echo =====================================================
    echo Log file: %LOG_FILE%
    echo.
)

