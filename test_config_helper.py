"""
Test the new ConfigHelper to verify it works correctly.
"""

from v4.settings.config_helper import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, get_param_boolean, get_param
from v4.config.paths_v4 import V4_SETTINGS_FILE

def test_config_helper():
    """Test the ConfigHelper functionality."""
    print("Testing ConfigHelper...")
    
    # Test with full helper
    helper = ConfigHelper(V4_SETTINGS_FILE)
    
    print("\n=== Testing parameter lookup ===")
    
    # Test the problematic parameters
    optimization_active = helper.getboolean('optimization_active')
    csv_flag_use = helper.getboolean('csv_flag_use')
    csv_valid_det = helper.getboolean('csv_valid_det')
    
    print(f"optimization_active = {optimization_active} (section: {helper.find_param_section('optimization_active')})")
    print(f"csv_flag_use = {csv_flag_use} (section: {helper.find_param_section('csv_flag_use')})")
    print(f"csv_valid_det = {csv_valid_det} (section: {helper.find_param_section('csv_valid_det')})")
    
    # Test convenience functions
    print("\n=== Testing convenience functions ===")
    opt_active_conv = get_param_boolean('optimization_active')
    csv_flag_conv = get_param_boolean('csv_flag_use')
    
    print(f"optimization_active (convenience) = {opt_active_conv}")
    print(f"csv_flag_use (convenience) = {csv_flag_conv}")
    
    # Test some other parameters
    print("\n=== Testing other parameters ===")
    initial_capital = helper.getfloat('initial_capital')
    commission_rate = helper.getfloat('commission_rate')
    strategy_algorithm = helper.get('strategy_algorithm')
    
    print(f"initial_capital = {initial_capital} (section: {helper.find_param_section('initial_capital')})")
    print(f"commission_rate = {commission_rate} (section: {helper.find_param_section('commission_rate')})")
    print(f"strategy_algorithm = {strategy_algorithm} (section: {helper.find_param_section('strategy_algorithm')})")
    
    print("\n=== SUCCESS: ConfigHelper works! ===")
    print("This eliminates the need to know which section parameters are in.")

if __name__ == "__main__":
    test_config_helper()
