"""
Run EMA Backtest V2 using config-driven parameters. All outputs are saved to the configured output directory.
"""
import os
import sys
import pandas as pd
from config.config_v2 import config_v2
from data.data_loader import load_data_for_backtest
from models.ema_allocation_model import ema_allocation_model
from engine.backtest import BacktestEngine

# Ensure output directory exists
output_dir = config_v2['output_dir']
os.makedirs(output_dir, exist_ok=True)

# Load all data using config dict and standardize dates per README_DATE_HANDLING.md
from utils.date_utils import standardize_dataframe_index, format_dataframe_index_for_display, display_date

data_dict = load_data_for_backtest(config_v2)
price_data = standardize_dataframe_index(data_dict['price_data'])
# Optionally standardize other data as needed
def safe_standardize(df):
    try:
        return standardize_dataframe_index(df)
    except Exception:
        return df
returns_data = safe_standardize(data_dict.get('returns_data'))
risk_free_rate = data_dict.get('risk_free_rate')
benchmark_returns = data_dict.get('benchmark_returns')

# Initialize backtest engine
engine = BacktestEngine(
    initial_capital=config_v2['backtest_params']['initial_capital'],
    commission_rate=config_v2['backtest_params']['commission_rate'],
    slippage_rate=config_v2['backtest_params']['slippage_rate']
)

# Run backtest with EMA signal
def run_ema_backtest():
    results = engine.run_backtest(
        price_data=price_data,
        signal_generator=ema_allocation_model,
        rebalance_freq=config_v2['backtest_params']['rebalance_freq'],
        execution_delay=config_v2['backtest_params']['execution_delay'],
        st_lookback=config_v2['strategy_params']['st_lookback'],
        mt_lookback=config_v2['strategy_params']['mt_lookback'],
        lt_lookback=config_v2['strategy_params']['lt_lookback']
    )
    # Save summary to Excel (dates formatted per README_DATE_HANDLING.md)
    summary_path = os.path.join(output_dir, 'ema_backtest_summary.xlsx')
    pd.DataFrame([{
        'final_value': results['final_value'],
        'total_return': results['total_return'],
        'sharpe_ratio': results['performance']['sharpe'],
        'max_drawdown': results['performance']['max_drawdown'],
        'win_rate': results['performance'].get('win_rate', None)
    }]).to_excel(summary_path, index=False)
    print(f"Summary saved to: {summary_path}")
    # Optionally save full results and trade log, formatting indices as date-only
    if 'trade_log' in results:
        trade_log_path = os.path.join(output_dir, 'ema_trade_log.xlsx')
        trade_log_df = pd.DataFrame(results['trade_log'])
        if not trade_log_df.empty and 'date' in trade_log_df.columns:
            trade_log_df['date'] = trade_log_df['date'].apply(lambda d: display_date(d) if pd.notnull(d) else d)
        trade_log_df.to_excel(trade_log_path, index=False)
        print(f"Trade log saved to: {trade_log_path}")
    if 'portfolio_history' in results:
        portfolio_path = os.path.join(output_dir, 'ema_portfolio_history.xlsx')
        portfolio_df = pd.DataFrame(results['portfolio_history'])
        if not portfolio_df.empty:
            portfolio_df = format_dataframe_index_for_display(portfolio_df)
        portfolio_df.to_excel(portfolio_path, index=False)
        print(f"Portfolio history saved to: {portfolio_path}")
    return results

if __name__ == "__main__":
    run_ema_backtest()
