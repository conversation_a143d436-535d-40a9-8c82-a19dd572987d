@echo off
SETLOCAL

echo === ENHANCED MEMORY TOOL ===
echo.

call F:\AI_Library\my_quant_env\Scripts\activate.bat

:: Run the Python script which will handle all the logic
python docs\memory_tool\ai_context_gen_v2_1.py

:: Check if the script ran successfully
if %ERRORLEVEL% NEQ 0 (
    echo [X] ERROR: Memory tool execution failed
    pause
    exit /b 1
)

echo [✓] Memory tool completed successfully
pause
echo.

pause
