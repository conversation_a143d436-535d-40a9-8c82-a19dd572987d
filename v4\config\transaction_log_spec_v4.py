# TRANSACTION LOG SPECIFICATION (DO NOT <PERSON>ANGE WITHOUT APPROVAL)
TRADE_LOG_COLUMNS = [
    'timestamp', 'strategy_id', 'asset', 
    'quantity', 'price', 'commission',
    'trade_type', 'fill_status'
]

TRADE_LOG_TYPES = {
    'timestamp': 'datetime64[ns]',
    'strategy_id': 'category',
    'asset': 'category',
    'quantity': 'float64',
    'price': 'float64',
    'commission': 'float64', 
    'trade_type': 'category',
    'fill_status': 'category'
}

# Compatibility mapping for legacy systems
LEGACY_COLUMN_MAP = {
    'date': 'timestamp',
    'action': 'trade_type',
    'shares': 'quantity'
}
