# Report Generation System Implementation Plan - Task A

**Date:** 2025-07-22  
**Status:** Ready to Begin Implementation  
**Priority:** 1 (Reports First, then GUI)  
**Next Session:** Start with detailed requirements analysis

## 🎯 IMPLEMENTATION OVERVIEW

### Current System Analysis (Based on 2:59 PM Run - 20250722_145937)

**✅ AVAILABLE DATA SOURCES:**
1. **Signal History**: `signals_output_20250722_145931.csv` (1,396 rows, 2020-01-02 to 2025-07-21)
   - Columns: Date, SPY, SHV, EFA, TLT, PFF
   - Values: Target allocations (0.0 to 1.0, sum to 1.0 per row)
   - Format: Perfect for "Signal History Tab" requirement

2. **Allocation History**: `allocation_history_20250722_145937.csv` (1,396 rows)
   - Columns: Date, Cash, SPY, SHV, TLT, PFF, EFA, Total
   - Values: Actual executed allocations with cash component
   - Format: Perfect for "Allocation History Tab" requirement

3. **Trade Log**: `trade_log_20250722_145937.csv` (272 trades)
   - Columns: date, ticker, action, quantity, price per share executed, total $s, pnl
   - Format: Needs mapping to required columns (trade_num, symbol, execution_date, execution_price, commission+slippage, amount)

**🔍 MISSING DATA SOURCES (Need to Create):**
1. **Performance Metrics**: CAGR, volatility, Sharpe ratio, max drawdown, turnover, win rate
2. **Parameter Summary**: All key parameters from settings_parameters_v4.ini
3. **Benchmark Data**: Equal-weight benchmark or SPY benchmark returns
4. **Monthly/Annual Returns**: For heatmap generation
5. **Cumulative Returns**: For equity curve charts
6. **Drawdown Series**: For drawdown charts

## 📋 DETAILED REQUIREMENTS ANALYSIS

### Report 1: Performance Table (XLSX) - `EMA_V3_1_performance_tables_YYYYMMDD_HHMMSS.xlsx`

**Tab 1: Signal History** ✅ DATA AVAILABLE
- Source: `signals_output_20250722_145931.csv`
- Format: Date (YYYY-MM-DD), Tickers (0.00%), Sum to 100%
- Status: Ready to implement

**Tab 2: Allocation History** ✅ DATA AVAILABLE  
- Source: `allocation_history_20250722_145937.csv`
- Format: Date (YYYY-MM-DD), Tickers + Cash (0.00%), Sum to 100%
- Status: Ready to implement

**Tab 3: Trade Log** ⚠️ NEEDS MAPPING
- Source: `trade_log_20250722_145937.csv`
- Required Columns: trade_num, symbol, quantity, execution_date, execution_price, commission+slippage, amount, pnl
- Current Columns: date, ticker, action, quantity, price per share executed, total $s, pnl
- Status: Need column mapping and trade_num generation

**Tab 4: Performance** ❌ NEEDS CREATION
- Required: Parameters (left), Metrics (right), Benchmark row
- Missing: All performance calculations, parameter extraction
- Status: Need full implementation

**Header (Cell A1)** ❌ NEEDS CREATION
- Required: Main parameters display
- Status: Need parameter extraction from settings_parameters_v4.ini

### Report 2: Monthly Returns Heatmap (PNG) - `EMA_V3_1_monthly_returns_YYYYMMDD_HHMMSS.png`

**Data Source** ❌ NEEDS CREATION
- Required: Monthly returns calculation from allocation_history
- Missing: Monthly return calculations, heatmap generation
- Status: Need full implementation

### Report 3: Cumulative Returns & Drawdown (PNG) - `EMA_V3_1_cumulative_returns_drawdown_YYYYMMDD_HHMMSS.png`

**Data Source** ❌ NEEDS CREATION
- Required: Daily portfolio values, cumulative returns, drawdown series
- Missing: Portfolio value calculations, benchmark comparison
- Status: Need full implementation

## 🔧 IMPLEMENTATION STRATEGY

### Phase 1: Data Pipeline Creation (Week 1)
1. **Extract Performance Metrics** from backtest results
2. **Calculate Portfolio Values** from allocation_history + price data
3. **Generate Monthly/Annual Returns** from portfolio values
4. **Calculate Benchmark Returns** (equal-weight or SPY)
5. **Create Drawdown Series** from portfolio values

### Phase 2: Excel Report Generation (Week 2)
1. **Implement Signal History Tab** (straightforward mapping)
2. **Implement Allocation History Tab** (straightforward mapping)
3. **Implement Trade Log Tab** (column mapping + trade_num)
4. **Implement Performance Tab** (metrics + parameters)
5. **Add Header with Parameters** (Cell A1)

### Phase 3: Chart Generation (Week 3)
1. **Monthly Returns Heatmap** (matplotlib/seaborn)
2. **Cumulative Returns Chart** (dual-axis with benchmark)
3. **Drawdown Chart** (bottom panel)
4. **High DPI Export** (≥300 DPI)

## 🎯 CRITICAL QUESTIONS FOR NEXT SESSION

### 1. Performance Metrics Calculation
- **Q1**: Should CAGR be calculated from allocation_history portfolio values or from backtest results?
- **Q2**: What benchmark should be used? Equal-weight of all tickers or SPY?
- **Q3**: How should commission/slippage be calculated if not in current trade log?

### 2. Parameter Display
- **Q4**: Which parameters from settings_parameters_v4.ini should be displayed in Cell A1?
- **Q5**: Should optimizable parameters show (current_value, min, max) or just current_value?

### 3. Data Validation
- **Q6**: Should portfolio values be calculated from allocation_history or from backtest engine results?
- **Q7**: How should cash component be handled in performance calculations?

### 4. Chart Specifications
- **Q8**: What color scheme for monthly returns heatmap? (Red/Green, Blue/Red, Custom?)
- **Q9**: Should benchmark be SPY, equal-weight, or user-configurable?

### 5. File Naming & Output
- **Q10**: Should timestamp match the backtest run timestamp or report generation timestamp?
- **Q11**: Where should reports be saved? (v4_trace_outputs, separate reports folder?)

## 🚀 NEXT SESSION STARTUP INSTRUCTIONS

**FOR AI IN NEXT SESSION:**

1. **Load this plan**: Read `memory-bank/report_generation_implementation_plan.md`
2. **Ask all critical questions** (Q1-Q11 above) - GET DEFINITIVE ANSWERS
3. **Examine current data** in `v4_trace_outputs/*_20250722_145937.*` files
4. **Start with Phase 1**: Data pipeline creation once requirements are clarified
5. **Use iterative approach**: Code → Run → Evaluate → Fix → Repeat (one report component at a time)

**DEVELOPMENT APPROACH:**
- **Zero assumptions** - confirm every requirement detail
- **One component at a time** - don't move to next until current is perfect
- **User validation required** - each report/chart must be confirmed perfect before proceeding
- **Automated testing** - create validation scripts for each component

**SUCCESS CRITERIA:**
- User confirms each report component is perfect
- All requirements from `docs\CPS_v4\reporting_output_requirements_v4.md` met
- Reports generated automatically from current CSV outputs
- Ready for GUI integration (Task B)

---

**READY TO BEGIN**: All analysis complete, data sources identified, implementation strategy defined. Next session should start with detailed requirements clarification (Q1-Q11) then begin Phase 1 implementation.
