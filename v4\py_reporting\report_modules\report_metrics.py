#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
v4/py_reporting/report_metrics.py

Performance Metrics Calculation Module for CPS V4 Performance Reporting

This module contains all performance metrics calculation functionality extracted from v4_performance_report.py:
- Performance metrics calculation from portfolio values
- Risk metrics (Sharpe ratio, Sortino ratio, max drawdown)
- Return metrics (CAGR, total return, volatility)
- Benchmark comparison metrics

Functions included:
- _calculate_performance_metrics(): Main performance metrics calculation function
- _calculate_sharpe_ratio(): Sharpe ratio calculation
- _calculate_max_drawdown(): Maximum drawdown calculation
- _calculate_cagr(): Compound Annual Growth Rate calculation
- _calculate_volatility(): Volatility calculation

Author: AI Assistant
Date: 2025-07-26 (Refactored from v4_performance_report.py)
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, Any, Optional

# Set up logging
logger = logging.getLogger(__name__)


def _calculate_performance_metrics(df: pd.DataFrame, settings: Dict[str, Any]) -> Dict[str, Any]:
    """
    Calculate performance metrics from the performance data.

    Args:
        df: DataFrame containing portfolio values with Date and portfolio_value columns
        settings: Settings dictionary containing risk-free rate and other parameters

    Returns:
        Dict containing calculated performance metrics (formatted strings)
    """
    try:
        logger.debug("Calculating performance metrics")

        if df.empty:
            logger.warning("Empty DataFrame provided for performance calculation")
            return _get_default_metrics()

        # Ensure we have the required columns
        if 'portfolio_value' not in df.columns:
            logger.warning("portfolio_value column not found in DataFrame")
            return _get_default_metrics()

        # Calculate returns
        portfolio_values = df['portfolio_value'].dropna()
        if len(portfolio_values) < 2:
            logger.warning("Insufficient data points for performance calculation")
            return _get_default_metrics()

        returns = portfolio_values.pct_change().dropna()

        # Get risk-free rate from settings
        risk_free_rate = _get_risk_free_rate(settings)

        # Calculate metrics
        metrics = {}

        # Basic return metrics
        metrics['Total Return'] = _calculate_total_return(portfolio_values)
        metrics['CAGR'] = _calculate_cagr(portfolio_values, df)
        metrics['Volatility'] = _calculate_volatility(returns)

        # Risk metrics
        metrics['Sharpe Ratio'] = _calculate_sharpe_ratio(returns, risk_free_rate)
        metrics['Sortino Ratio'] = _calculate_sortino_ratio(returns, risk_free_rate)
        metrics['Max Drawdown'] = _calculate_max_drawdown(portfolio_values)

        # Additional metrics
        metrics['Best Month'] = _calculate_best_month(returns)
        metrics['Worst Month'] = _calculate_worst_month(returns)
        metrics['Win Rate'] = _calculate_win_rate(returns)
        metrics['Calmar Ratio'] = _calculate_calmar_ratio(metrics['CAGR'], metrics['Max Drawdown'])

        # UPI (Ulcer Performance Index) calculation
        metrics['UPI'] = _calculate_upi(portfolio_values, metrics['CAGR'])

        # Format metrics for display
        formatted_metrics = _format_metrics(metrics)

        logger.debug(f"Calculated {len(formatted_metrics)} performance metrics")
        return formatted_metrics

    except Exception as e:
        logger.error(f"Error calculating performance metrics: {e}")
        return _get_default_metrics()


def _calculate_performance_metrics_raw(df: pd.DataFrame, settings: Dict[str, Any]) -> Dict[str, float]:
    """
    Calculate performance metrics from the performance data (RAW numeric values for Excel).

    Args:
        df: DataFrame containing portfolio values with Date and portfolio_value columns
        settings: Settings dictionary containing risk-free rate and other parameters

    Returns:
        Dict containing calculated performance metrics (raw numeric values)
    """
    try:
        logger.debug("Calculating performance metrics (raw values)")

        if df.empty:
            logger.warning("Empty DataFrame provided for performance calculation")
            return _get_default_metrics_raw()

        # Ensure we have the required columns
        if 'portfolio_value' not in df.columns:
            logger.warning("portfolio_value column not found in DataFrame")
            return _get_default_metrics_raw()

        # Calculate returns
        portfolio_values = df['portfolio_value'].dropna()
        if len(portfolio_values) < 2:
            logger.warning("Insufficient data points for performance calculation")
            return _get_default_metrics_raw()

        returns = portfolio_values.pct_change().dropna()

        # Get risk-free rate
        risk_free_rate = _get_risk_free_rate(settings)

        # Calculate metrics (RAW VALUES - no formatting)
        metrics = {}

        # Basic return metrics
        metrics['Total Return'] = _calculate_total_return(portfolio_values)
        metrics['CAGR'] = _calculate_cagr(portfolio_values, df)
        metrics['Volatility'] = _calculate_volatility(returns)

        # Risk metrics
        metrics['Sharpe Ratio'] = _calculate_sharpe_ratio(returns, risk_free_rate)
        metrics['Sortino Ratio'] = _calculate_sortino_ratio(returns, risk_free_rate)
        metrics['Max Drawdown'] = _calculate_max_drawdown(portfolio_values)

        # Additional metrics
        metrics['Best Month'] = _calculate_best_month(returns)
        metrics['Worst Month'] = _calculate_worst_month(returns)
        metrics['Win Rate'] = _calculate_win_rate(returns)
        metrics['Calmar Ratio'] = _calculate_calmar_ratio(metrics['CAGR'], metrics['Max Drawdown'])

        # UPI (Ulcer Performance Index) calculation
        metrics['UPI'] = _calculate_upi(portfolio_values, metrics['CAGR'])

        logger.debug(f"Calculated {len(metrics)} raw performance metrics")
        return metrics

    except Exception as e:
        logger.error(f"Error calculating raw performance metrics: {e}")
        return _get_default_metrics_raw()


def _get_risk_free_rate(settings: Dict[str, Any]) -> float:
    """Extract risk-free rate from settings."""
    try:
        # Try different possible locations for risk-free rate
        if 'performance' in settings and 'risk_free_rate' in settings['performance']:
            return float(settings['performance']['risk_free_rate'])
        elif 'risk_free_rate' in settings:
            return float(settings['risk_free_rate'])
        else:
            raise ValueError("Risk-free rate not found in settings - configuration error. Check [Performance] section in settings file.")
    except (ValueError, TypeError) as e:
        raise ValueError(f"Invalid or missing risk-free rate in settings: {e}. Check [Performance] section in settings file.")


def _calculate_total_return(portfolio_values: pd.Series) -> float:
    """Calculate total return from portfolio values."""
    try:
        if len(portfolio_values) < 2:
            return 0.0
        return (portfolio_values.iloc[-1] / portfolio_values.iloc[0]) - 1.0
    except Exception as e:
        logger.error(f"Error calculating total return: {e}")
        return 0.0


def _calculate_cagr(portfolio_values: pd.Series, df: pd.DataFrame) -> float:
    """Calculate Compound Annual Growth Rate."""
    try:
        if len(portfolio_values) < 2:
            return 0.0
        
        # Calculate number of years
        if 'Date' in df.columns:
            dates = pd.to_datetime(df['Date'])
            years = (dates.iloc[-1] - dates.iloc[0]).days / 365.25
        else:
            # Assume daily data if no date column
            years = len(portfolio_values) / 252.0  # 252 trading days per year
        
        if years <= 0:
            return 0.0
        
        total_return = _calculate_total_return(portfolio_values)
        cagr = (1 + total_return) ** (1 / years) - 1
        return cagr
        
    except Exception as e:
        logger.error(f"Error calculating CAGR: {e}")
        return 0.0


def _calculate_volatility(returns: pd.Series) -> float:
    """Calculate annualized volatility."""
    try:
        if len(returns) < 2:
            return 0.0
        return returns.std() * np.sqrt(252)  # Annualized
    except Exception as e:
        logger.error(f"Error calculating volatility: {e}")
        return 0.0


def _calculate_sharpe_ratio(returns: pd.Series, risk_free_rate: float) -> float:
    """Calculate Sharpe ratio."""
    try:
        if len(returns) < 2:
            return 0.0
        
        excess_returns = returns - (risk_free_rate / 252)  # Daily risk-free rate
        if excess_returns.std() == 0:
            return 0.0
        
        return (excess_returns.mean() / excess_returns.std()) * np.sqrt(252)
    except Exception as e:
        logger.error(f"Error calculating Sharpe ratio: {e}")
        return 0.0


def _calculate_sortino_ratio(returns: pd.Series, risk_free_rate: float) -> float:
    """Calculate Sortino ratio."""
    try:
        if len(returns) < 2:
            return 0.0
        
        excess_returns = returns - (risk_free_rate / 252)
        downside_returns = excess_returns[excess_returns < 0]
        
        if len(downside_returns) == 0 or downside_returns.std() == 0:
            return 0.0
        
        return (excess_returns.mean() / downside_returns.std()) * np.sqrt(252)
    except Exception as e:
        logger.error(f"Error calculating Sortino ratio: {e}")
        return 0.0


def _calculate_max_drawdown(portfolio_values: pd.Series) -> float:
    """Calculate maximum drawdown."""
    try:
        if len(portfolio_values) < 2:
            return 0.0
        
        # Calculate running maximum
        running_max = portfolio_values.expanding().max()
        
        # Calculate drawdown
        drawdown = (portfolio_values - running_max) / running_max
        
        # Return maximum drawdown (most negative value)
        return drawdown.min()
    except Exception as e:
        logger.error(f"Error calculating max drawdown: {e}")
        return 0.0


def _calculate_best_month(returns: pd.Series) -> float:
    """Calculate best monthly return."""
    try:
        if len(returns) < 20:  # Need at least ~1 month of daily data
            return 0.0
        
        # Convert to monthly returns (approximate)
        monthly_returns = returns.rolling(window=21).apply(lambda x: (1 + x).prod() - 1)
        return monthly_returns.max()
    except Exception as e:
        logger.error(f"Error calculating best month: {e}")
        return 0.0


def _calculate_worst_month(returns: pd.Series) -> float:
    """Calculate worst monthly return."""
    try:
        if len(returns) < 20:
            return 0.0
        
        # Convert to monthly returns (approximate)
        monthly_returns = returns.rolling(window=21).apply(lambda x: (1 + x).prod() - 1)
        return monthly_returns.min()
    except Exception as e:
        logger.error(f"Error calculating worst month: {e}")
        return 0.0


def _calculate_win_rate(returns: pd.Series) -> float:
    """Calculate win rate (percentage of positive returns)."""
    try:
        if len(returns) == 0:
            return 0.0
        return (returns > 0).mean()
    except Exception as e:
        logger.error(f"Error calculating win rate: {e}")
        return 0.0


def _calculate_calmar_ratio(cagr: float, max_drawdown: float) -> float:
    """Calculate Calmar ratio (CAGR / Max Drawdown)."""
    try:
        if max_drawdown == 0:
            return 0.0
        return cagr / abs(max_drawdown)
    except Exception as e:
        logger.error(f"Error calculating Calmar ratio: {e}")
        return 0.0


def _calculate_upi(portfolio_values: pd.Series, cagr: float) -> float:
    """Calculate UPI (Ulcer Performance Index)."""
    try:
        if len(portfolio_values) < 2:
            return 0.0

        # Calculate drawdown series
        rolling_max = portfolio_values.expanding().max()
        drawdown = (portfolio_values - rolling_max) / rolling_max

        # Calculate Ulcer Index = sqrt(mean(drawdown^2))
        ulcer_index = np.sqrt((drawdown ** 2).mean()) if len(drawdown) > 0 else 0

        # UPI = Annualized Return / Ulcer Index
        if ulcer_index > 0:
            upi = (cagr * 100) / (ulcer_index * 100)  # Convert to percentage terms
            return upi
        else:
            return 0.0

    except Exception as e:
        logger.error(f"Error calculating UPI: {e}")
        return 0.0


def _format_metrics(metrics: Dict[str, float]) -> Dict[str, Any]:
    """Format metrics for display."""
    try:
        formatted = {}
        
        # Percentage metrics
        percentage_metrics = ['Total Return', 'CAGR', 'Volatility', 'Max Drawdown', 
                            'Best Month', 'Worst Month', 'Win Rate']
        
        for key, value in metrics.items():
            if key in percentage_metrics:
                formatted[key] = f"{value:.2%}"
            else:
                formatted[key] = f"{value:.4f}"
        
        return formatted
        
    except Exception as e:
        logger.error(f"Error formatting metrics: {e}")
        return metrics


def _get_default_metrics() -> Dict[str, Any]:
    """Return default metrics when calculation fails."""
    return {
        'Total Return': '0.00%',
        'CAGR': '0.00%',
        'Volatility': '0.00%',
        'Sharpe Ratio': '0.0000',
        'Sortino Ratio': '0.0000',
        'Max Drawdown': '0.00%',
        'Best Month': '0.00%',
        'Worst Month': '0.00%',
        'Win Rate': '0.00%',
        'Calmar Ratio': '0.0000'
    }


def _get_default_metrics_raw() -> Dict[str, float]:
    """Return default raw metrics when calculation fails."""
    return {
        'Total Return': 0.0,
        'CAGR': 0.0,
        'Volatility': 0.0,
        'Sharpe Ratio': 0.0,
        'Sortino Ratio': 0.0,
        'Max Drawdown': 0.0,
        'Best Month': 0.0,
        'Worst Month': 0.0,
        'Win Rate': 0.0,
        'Calmar Ratio': 0.0,
        'UPI': 0.0
    }
