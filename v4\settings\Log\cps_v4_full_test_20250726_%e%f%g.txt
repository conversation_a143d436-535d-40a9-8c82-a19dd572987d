[INFO] Activating virtual environment... 
[INFO] Setting PYTHONPATH... 
[INFO] Running parameter discovery... 
2025-07-26 19:19:45,928 - parameter_discovery - INFO - Starting parameter discovery
2025-07-26 19:19:45,928 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\config\config_v3.py
2025-07-26 19:19:45,928 - parameter_discovery - ERROR - Error scanning file S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\config\config_v3.py: [Errno 2] No such file or directory: 'S:\\Dropbox\\Scott Only Internal\\Quant_Python_24\\Backtest_FinAsset_Alloc_Template\\config\\config_v3.py'
2025-07-26 19:19:45,951 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_engine\parameter_registry.py
2025-07-26 19:19:45,952 - parameter_discovery - ERROR - Error scanning file S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_engine\parameter_registry.py: [Errno 2] No such file or directory: 'S:\\Dropbox\\Scott Only Internal\\Quant_Python_24\\Backtest_FinAsset_Alloc_Template\\v3_engine\\parameter_registry.py'
2025-07-26 19:19:45,952 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_engine\parameters.py
2025-07-26 19:19:45,952 - parameter_discovery - ERROR - Error scanning file S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_engine\parameters.py: [Errno 2] No such file or directory: 'S:\\Dropbox\\Scott Only Internal\\Quant_Python_24\\Backtest_FinAsset_Alloc_Template\\v3_engine\\parameters.py'
2025-07-26 19:19:45,952 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_engine\strategy_parameter_set.py
2025-07-26 19:19:45,952 - parameter_discovery - ERROR - Error scanning file S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_engine\strategy_parameter_set.py: [Errno 2] No such file or directory: 'S:\\Dropbox\\Scott Only Internal\\Quant_Python_24\\Backtest_FinAsset_Alloc_Template\\v3_engine\\strategy_parameter_set.py'
2025-07-26 19:19:45,952 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_engine\parameter_optimizer.py
2025-07-26 19:19:45,952 - parameter_discovery - ERROR - Error scanning file S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_engine\parameter_optimizer.py: [Errno 2] No such file or directory: 'S:\\Dropbox\\Scott Only Internal\\Quant_Python_24\\Backtest_FinAsset_Alloc_Template\\v3_engine\\parameter_optimizer.py'
2025-07-26 19:19:45,952 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_engine\ema_v3_adapter.py
2025-07-26 19:19:45,952 - parameter_discovery - ERROR - Error scanning file S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_engine\ema_v3_adapter.py: [Errno 2] No such file or directory: 'S:\\Dropbox\\Scott Only Internal\\Quant_Python_24\\Backtest_FinAsset_Alloc_Template\\v3_engine\\ema_v3_adapter.py'
2025-07-26 19:19:45,952 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_strategies\ema_strategy.py
2025-07-26 19:19:45,952 - parameter_discovery - ERROR - Error scanning file S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_strategies\ema_strategy.py: [Errno 2] No such file or directory: 'S:\\Dropbox\\Scott Only Internal\\Quant_Python_24\\Backtest_FinAsset_Alloc_Template\\v3_strategies\\ema_strategy.py'
2025-07-26 19:19:45,952 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_strategies\strategy_base.py
2025-07-26 19:19:45,952 - parameter_discovery - ERROR - Error scanning file S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_strategies\strategy_base.py: [Errno 2] No such file or directory: 'S:\\Dropbox\\Scott Only Internal\\Quant_Python_24\\Backtest_FinAsset_Alloc_Template\\v3_strategies\\strategy_base.py'
2025-07-26 19:19:45,953 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_reporting\v3_performance_report.py
2025-07-26 19:19:45,953 - parameter_discovery - ERROR - Error scanning file S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_reporting\v3_performance_report.py: [Errno 2] No such file or directory: 'S:\\Dropbox\\Scott Only Internal\\Quant_Python_24\\Backtest_FinAsset_Alloc_Template\\v3_reporting\\v3_performance_report.py'
2025-07-26 19:19:45,953 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_reporting\v3_allocation_report.py
2025-07-26 19:19:45,953 - parameter_discovery - ERROR - Error scanning file S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_reporting\v3_allocation_report.py: [Errno 2] No such file or directory: 'S:\\Dropbox\\Scott Only Internal\\Quant_Python_24\\Backtest_FinAsset_Alloc_Template\\v3_reporting\\v3_allocation_report.py'
2025-07-26 19:19:45,953 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_reporting\reporting_parameters.py
2025-07-26 19:19:45,953 - parameter_discovery - ERROR - Error scanning file S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_reporting\reporting_parameters.py: [Errno 2] No such file or directory: 'S:\\Dropbox\\Scott Only Internal\\Quant_Python_24\\Backtest_FinAsset_Alloc_Template\\v3_reporting\\reporting_parameters.py'
2025-07-26 19:19:45,953 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_reporting\visualization_parameters.py
2025-07-26 19:19:45,953 - parameter_discovery - ERROR - Error scanning file S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_reporting\visualization_parameters.py: [Errno 2] No such file or directory: 'S:\\Dropbox\\Scott Only Internal\\Quant_Python_24\\Backtest_FinAsset_Alloc_Template\\v3_reporting\\visualization_parameters.py'
2025-07-26 19:19:45,953 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_reporting\parameter_registry_integration.py
2025-07-26 19:19:45,953 - parameter_discovery - ERROR - Error scanning file S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_reporting\parameter_registry_integration.py: [Errno 2] No such file or directory: 'S:\\Dropbox\\Scott Only Internal\\Quant_Python_24\\Backtest_FinAsset_Alloc_Template\\v3_reporting\\parameter_registry_integration.py'
2025-07-26 19:19:45,953 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\app\gui\v3_register_parameters.py
2025-07-26 19:19:45,953 - parameter_discovery - ERROR - Error scanning file S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\app\gui\v3_register_parameters.py: [Errno 2] No such file or directory: 'S:\\Dropbox\\Scott Only Internal\\Quant_Python_24\\Backtest_FinAsset_Alloc_Template\\app\\gui\\v3_register_parameters.py'
2025-07-26 19:19:45,953 - parameter_discovery - INFO - Generating INI file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\CPS_v4\default_settings_CPS_v4.ini
2025-07-26 19:19:45,968 - parameter_discovery - INFO - INI file generated successfully: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\CPS_v4\default_settings_CPS_v4.ini
2025-07-26 19:19:45,968 - parameter_discovery - INFO - Generating parameter mapping document: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\CPS_v4\parameter_mapping_CPS_v4.md
2025-07-26 19:19:45,969 - parameter_discovery - INFO - Parameter mapping document generated successfully: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\CPS_v4\parameter_mapping_CPS_v4.md
2025-07-26 19:19:45,969 - parameter_discovery - INFO - Parameter discovery complete. Found 0 parameters.
[INFO] Parameter discovery complete. 
[INFO] Running production verification... 
2025-07-26 19:19:46,846 - verify_CPS_v4 - INFO - Running with PRODUCTION data validation
2025-07-26 19:19:46,848 - verify_CPS_v4 - INFO - Adding settings validation tests
2025-07-26 19:19:46,848 - verify_CPS_v4 - INFO - Adding report validation tests
2025-07-26 19:19:46,848 - verify_CPS_v4 - INFO - Running production validation tests
test_load_default_settings (__main__.TestSettingsModule.test_load_default_settings)
Test loading default settings. ... ERROR
test_settings_override (__main__.TestSettingsModule.test_settings_override)
Test settings override mechanism. ... ERROR
test_type_conversion (__main__.TestSettingsModule.test_type_conversion)
Test type conversion of settings values. ... ok
test_report_module_structure (__main__.TestReportGeneration.test_report_module_structure)
Test that report modules follow size constraints. ... ok

======================================================================
ERROR: test_load_default_settings (__main__.TestSettingsModule.test_load_default_settings)
Test loading default settings.
----------------------------------------------------------------------
Traceback (most recent call last):
  File "S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\CPS_v4\V4_transition\verify_CPS_v4.py", line 116, in test_load_default_settings
    self.assertEqual(settings['system_log_level'], 'INFO')
                     ~~~~~~~~^^^^^^^^^^^^^^^^^^^^
KeyError: 'system_log_level'

======================================================================
ERROR: test_settings_override (__main__.TestSettingsModule.test_settings_override)
Test settings override mechanism.
----------------------------------------------------------------------
Traceback (most recent call last):
  File "S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\CPS_v4\V4_transition\verify_CPS_v4.py", line 140, in test_settings_override
    self.assertEqual(settings['system_log_level'], 'DEBUG')  # Overridden
                     ~~~~~~~~^^^^^^^^^^^^^^^^^^^^
KeyError: 'system_log_level'

----------------------------------------------------------------------
Ran 4 tests in 0.032s

FAILED (errors=2)
[INFO] Production verification complete. 
