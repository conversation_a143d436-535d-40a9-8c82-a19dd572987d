#!/usr/bin/env python
# -*- coding: utf-8 -*-
# v4_performance_report.py
"""
V4 Performance Reporting Module

Handles performance report generation using direct CPS V4 parameter access.
No adapters or fallbacks are used - parameters must be explicitly provided.

This module follows the CPS V4 design principles:
- Direct parameter access (no adapters)
- Clear error handling for missing parameters
- Modular design (<450 lines)

Author: AI Assistant
Date: 2025-06-11
"""

import os
import sys
import logging
import datetime
import traceback
from pathlib import Path
from typing import Dict, Any, Optional, List, Tuple

# Set up logging
logger = logging.getLogger(__name__)

# Add the project root to the path if needed to ensure imports work properly
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# Import V4 settings module
try:
    from v4.settings.settings_CPS_v4 import load_settings
except ImportError as e:
    logger.error(f"Error importing V4 settings module: {e}")
    raise

def generate_performance_report(
    backtest_results: Dict[str, Any],
    strategy_name: str,
    output_dir: Optional[str] = None,
    is_new_file: bool = True,
    **kwargs
) -> str:
    """
    Generate a performance report using CPS V4 parameters.
    
    Args:
        backtest_results: Results dictionary from backtest
        strategy_name: Name of the strategy
        output_dir: Optional directory to save the report (defaults to settings value)
        is_new_file: Whether to create a new file
        **kwargs: Additional parameters
        
    Returns:
        str: Path to the generated report file
        
    Raises:
        ValueError: If required parameters are missing
        FileNotFoundError: If output directory doesn't exist
    """
    try:
        # Load settings directly from CPS V4
        settings = load_settings()
        
        # Validate backtest results
        if not backtest_results:
            raise ValueError("Backtest results are empty or None")
        
        # Get reporting parameters directly from settings
        reporting_settings = settings.get('reporting', {})
        
        # Check if Excel report should be created
        create_excel = reporting_settings.get('create_excel', True)
        if not create_excel:
            logger.info("Excel report creation disabled by parameter settings")
            return ""
        
        # Generate timestamp for filename
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Determine output directory
        if output_dir is None:
            # Get from settings or use default
            output_dir = reporting_settings.get('output_directory', 'output')
        
        # Create Path object
        output_path = Path(output_dir)
        
        # Ensure output directory exists
        if not output_path.exists():
            raise FileNotFoundError(f"Output directory does not exist: {output_path}")
        
        # Create output filename
        report_filename = f"performance_report_{strategy_name}_{timestamp}.xlsx"
        report_path = output_path / report_filename
        
        logger.info(f"Generating V4 performance report for {strategy_name}")
        logger.debug(f"Output path: {report_path}")
        logger.debug(f"Is new file: {is_new_file}")
        
        # Generate the report using the performance reporting module
        # In V4, we directly use the reporting functionality without adapters
        _generate_excel_report(
            backtest_results=backtest_results,
            output_path=report_path,
            strategy_name=strategy_name,
            settings=settings,
            is_new_file=is_new_file,
            **kwargs
        )
        
        logger.info(f"Generated V4 performance report: {report_path}")
        return str(report_path)
        
    except Exception as e:
        logger.error(f"Error in generate_performance_report: {str(e)}")
        logger.error(traceback.format_exc())
        # No fallbacks - we need to fix the primary reporting path
        raise

def _generate_excel_report(
    backtest_results: Dict[str, Any],
    output_path: Path,
    strategy_name: str,
    settings: Dict[str, Any],
    is_new_file: bool = True,
    **kwargs
) -> None:
    """
    Generate the Excel performance report.
    
    Args:
        backtest_results: Results dictionary from backtest
        output_path: Path to save the report
        strategy_name: Name of the strategy
        settings: Settings dictionary from CPS V4
        is_new_file: Whether to create a new file
        **kwargs: Additional parameters
    """
    try:
        # Import pandas and openpyxl for Excel report generation
        import pandas as pd
        
        # Extract key performance metrics from backtest results
        portfolio_values = backtest_results.get('portfolio_values', [])
        benchmark_values = backtest_results.get('benchmark_values', [])
        dates = backtest_results.get('dates', [])
        trades = backtest_results.get('trades', [])
        
        # Validate required data
        if not portfolio_values or not dates:
            raise ValueError("Missing required portfolio data in backtest results")
        
        # Create performance metrics DataFrame
        performance_data = {
            'Date': dates,
            'Portfolio Value': portfolio_values
        }
        
        if benchmark_values:
            performance_data['Benchmark'] = benchmark_values
            
        df_performance = pd.DataFrame(performance_data)
        
        # Calculate performance metrics
        metrics = _calculate_performance_metrics(df_performance, settings)
        
        # Create Excel writer
        with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
            # Write performance data
            df_performance.to_excel(writer, sheet_name='Performance Data', index=False)
            
            # Write metrics summary
            pd.DataFrame(metrics.items(), columns=['Metric', 'Value']).to_excel(
                writer, sheet_name='Performance Metrics', index=False
            )
            
            # Write trade log if available
            if trades:
                pd.DataFrame(trades).to_excel(
                    writer, sheet_name='Trade Log', index=False
                )
        
        logger.info(f"Excel report generated successfully: {output_path}")
        
    except ImportError as e:
        logger.error(f"Required libraries not found: {e}")
        raise
    except Exception as e:
        logger.error(f"Error generating Excel report: {e}")
        raise

def _calculate_performance_metrics(df: 'pd.DataFrame', settings: Dict[str, Any]) -> Dict[str, Any]:
    """
    Calculate performance metrics from the performance data.
    
    Args:
        df: DataFrame with performance data
        settings: Settings dictionary from CPS V4
        
    Returns:
        Dictionary of performance metrics
    """
    import numpy as np
    
    # Extract reporting settings
    reporting_settings = settings.get('reporting', {})
    
    # Get risk-free rate from settings or use default
    risk_free_rate = reporting_settings.get('risk_free_rate', 0.0)
    
    # Calculate returns
    df['Daily Return'] = df['Portfolio Value'].pct_change()
    
    # Remove NaN values
    returns = df['Daily Return'].dropna().values
    
    # Calculate metrics
    total_days = len(returns)
    if total_days == 0:
        return {'Error': 'No valid return data available'}
    
    # Calculate basic metrics
    total_return = (df['Portfolio Value'].iloc[-1] / df['Portfolio Value'].iloc[0]) - 1
    annualized_return = (1 + total_return) ** (252 / total_days) - 1
    volatility = np.std(returns) * np.sqrt(252)
    
    # Calculate drawdowns
    df['Cumulative Return'] = (1 + df['Daily Return']).cumprod()
    df['Cumulative Max'] = df['Cumulative Return'].cummax()
    df['Drawdown'] = (df['Cumulative Return'] / df['Cumulative Max']) - 1
    max_drawdown = df['Drawdown'].min()
    
    # Calculate Sharpe ratio
    excess_returns = returns - (risk_free_rate / 252)
    sharpe_ratio = (np.mean(excess_returns) * 252) / (np.std(excess_returns) * np.sqrt(252)) if np.std(excess_returns) > 0 else 0
    
    # Calculate Sortino ratio
    downside_returns = returns[returns < 0]
    downside_deviation = np.std(downside_returns) * np.sqrt(252) if len(downside_returns) > 0 else 0
    sortino_ratio = (np.mean(excess_returns) * 252) / downside_deviation if downside_deviation > 0 else 0
    
    # Calculate benchmark comparison if available
    benchmark_metrics = {}
    if 'Benchmark' in df.columns:
        df['Benchmark Return'] = df['Benchmark'].pct_change()
        benchmark_returns = df['Benchmark Return'].dropna().values
        
        benchmark_total_return = (df['Benchmark'].iloc[-1] / df['Benchmark'].iloc[0]) - 1
        benchmark_annualized_return = (1 + benchmark_total_return) ** (252 / total_days) - 1
        
        # Calculate alpha and beta
        cov_matrix = np.cov(returns, benchmark_returns)
        beta = cov_matrix[0, 1] / cov_matrix[1, 1] if cov_matrix[1, 1] != 0 else 1
        alpha = annualized_return - (beta * benchmark_annualized_return)
        
        benchmark_metrics = {
            'Benchmark Total Return': benchmark_total_return,
            'Benchmark Annualized Return': benchmark_annualized_return,
            'Alpha': alpha,
            'Beta': beta
        }
    
    # Combine all metrics
    metrics = {
        'Start Date': df['Date'].iloc[0],
        'End Date': df['Date'].iloc[-1],
        'Total Days': total_days,
        'Total Return': total_return,
        'Annualized Return': annualized_return,
        'Volatility (Annualized)': volatility,
        'Max Drawdown': max_drawdown,
        'Sharpe Ratio': sharpe_ratio,
        'Sortino Ratio': sortino_ratio,
        'Risk-Free Rate': risk_free_rate
    }
    
    # Add benchmark metrics if available
    metrics.update(benchmark_metrics)
    
    return metrics

def generate_optimization_report(
    optimization_results: List[Tuple[Dict[str, Any], Dict[str, Any]]],
    output_dir: str,
    strategy_name: str,
    ticker_group: str,
    **kwargs
) -> str:
    """
    Generate a report for optimization results using CPS V4 parameters.
    
    Args:
        optimization_results: List of (params, results) tuples from optimization
        output_dir: Directory to save the report
        strategy_name: Name of the strategy
        ticker_group: Ticker group name
        **kwargs: Additional parameters
        
    Returns:
        str: Path to the generated report file
    """
    try:
        # Load settings directly from CPS V4
        settings = load_settings()
        
        # Get reporting parameters directly from settings
        reporting_settings = settings.get('reporting', {})
        
        # Check if Excel report should be created
        create_excel = reporting_settings.get('create_excel', True)
        if not create_excel:
            logger.info("Excel optimization report creation disabled by parameter settings")
            return ""
            
        # Generate timestamp for filename
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Create Path object
        output_path = Path(output_dir)
        
        # Ensure output directory exists
        if not output_path.exists():
            raise FileNotFoundError(f"Output directory does not exist: {output_path}")
        
        # Create output filename
        report_filename = f"optimization_report_{strategy_name}_{ticker_group}_{timestamp}.xlsx"
        report_path = output_path / report_filename
        
        # Generate the optimization report
        _generate_optimization_excel(
            optimization_results=optimization_results,
            output_path=report_path,
            strategy_name=strategy_name,
            ticker_group=ticker_group,
            settings=settings,
            **kwargs
        )
        
        logger.info(f"Generated V4 optimization report: {report_path}")
        return str(report_path)
        
    except Exception as e:
        logger.error(f"Error in generate_optimization_report: {str(e)}")
        logger.error(traceback.format_exc())
        # No fallbacks - we need to fix the primary reporting path
        raise

def _generate_optimization_excel(
    optimization_results: List[Tuple[Dict[str, Any], Dict[str, Any]]],
    output_path: Path,
    strategy_name: str,
    ticker_group: str,
    settings: Dict[str, Any],
    **kwargs
) -> None:
    """
    Generate the Excel optimization report.
    
    Args:
        optimization_results: List of (params, results) tuples from optimization
        output_path: Path to save the report
        strategy_name: Name of the strategy
        ticker_group: Ticker group name
        settings: Settings dictionary from CPS V4
        **kwargs: Additional parameters
    """
    try:
        # Import pandas for Excel report generation
        import pandas as pd
        
        # Extract optimization results
        params_list = []
        metrics_list = []
        
        for params, results in optimization_results:
            # Add parameters to list
            params_list.append(params)
            
            # Extract key metrics
            metrics = results.get('metrics', {})
            metrics_list.append(metrics)
        
        # Create DataFrames
        df_params = pd.DataFrame(params_list)
        df_metrics = pd.DataFrame(metrics_list)
        
        # Combine parameters and metrics
        df_combined = pd.concat([df_params, df_metrics], axis=1)
        
        # Sort by specified metric if available
        sort_metric = kwargs.get('sort_metric', 'sharpe_ratio')
        if sort_metric in df_combined.columns:
            df_combined = df_combined.sort_values(by=sort_metric, ascending=False)
        
        # Create Excel writer
        with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
            # Write combined results
            df_combined.to_excel(writer, sheet_name='Optimization Results', index=False)
            
            # Write summary information
            summary_data = {
                'Strategy Name': strategy_name,
                'Ticker Group': ticker_group,
                'Optimization Date': datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                'Number of Iterations': len(optimization_results)
            }
            
            pd.DataFrame(list(summary_data.items()), columns=['Metric', 'Value']).to_excel(
                writer, sheet_name='Summary', index=False
            )
        
        logger.info(f"Optimization report generated successfully: {output_path}")
        
    except ImportError as e:
        logger.error(f"Required libraries not found: {e}")
        raise
    except Exception as e:
        logger.error(f"Error generating optimization report: {e}")
        raise

if __name__ == '__main__':
    # Simple test to verify module loads correctly
    print("V4 Performance Report module loaded successfully")
    
    # Load settings to verify connectivity
    try:
        from v4.settings.settings_CPS_v4 import load_settings
        settings = load_settings()
        print(f"Successfully loaded {len(settings)} settings sections")
    except Exception as e:
        print(f"Error loading settings: {e}")
