---
type: "agent_requested"
description: "Universal Rules to Always Must Follow"
---
This document outlines the universal rules and procedures I must follow for all development tasks.

## 1. Core Directives

- **Preserve Functionality:** Never remove existing functionality or code without explicit user approval.
- **Respect Protected Code:** Never modify code sections marked with `WORKING - ONLY CHANGE WITH PERMISSION` or similar markers without explicit user approval.
- **Adhere to this Document:** Review and follow all rules in this document before writing or modifying any code.

## 2. Pre-Change Checklist

Before implementing any change, I will perform the following steps:

1. **Review Project Documentation:** First, I will read project-specific guidelines (`AIAgent_Guidelines.md`), `README.md`, and any other relevant documentation in the `docs/` or `memory-bank/` directories.
2. **Analyze Existing Code:** I will review the existing code to understand current patterns, conventions, and logic.
3. **Scan for Existing Solutions:** I will check for existing modules or functions that can be reused, both in the current codebase and in the shared library at `S:\Dropbox\Scott Only Internal\Quant_Python_24\Custom Function Library`.
4. **Identify Protected Sections:** I will explicitly identify and respect any code marked as protected or "DO NOT CHANGE".

## 3. Environment & Tooling

- **Virtual Environment:** I will exclusively use the isolated Python virtual environment specified in the project's `.bat` files. I will never use `conda`.
- **Environment Path:** For the "PC1" setup, the environment is located at `F:\AI_Library\my_quant_env`. In Windows 11, have defined an environment Variable; QUANT_ENV_PATH; it is set the the path to the virtual env (F:\AI_Library\my_quant_env); can be accessed via bat file setup.
- **Path Management:** I will never hardcode absolute paths in Python source files. Paths should be relative or defined in configuration files (`.ini`, `.bat`).
- All testing shall use a bat file process which activates the virtual env; 
- To execute a Windows .bat file in PowerShell, use:
  .\{name}. 

## 4. Coding & Documentation Standards

- **Language Version:** All new development will use the latest available Python version.
- **Date and Time:**
  - For daily data, I will always use Python's `date` object.
  - I will **never** use `datetime` objects, time elements, or timezone information in the core logic or data processing steps.
  - Timestamps are only permitted in log messages and output filenames.
- **Parameterization:** All key parameters must be defined at the top of a configuration file, not within the code logic.
- **File Naming:** Default output files will be in `.xlsx` format and named using the convention: `{description}_{ticker_list}_{date}_{time}.xlsx`.
- **Docstrings & Comments:**
  - I will add clear docstrings to all new functions and classes.
  - I will place the filename in a comment at the top of every new code file.
  - I will use start/end markers to delineate core functions for clarity.

## 5. Architectural Principles
- All coding must use as simple a code structure with as few lines as possible.
- **Strict Module Size:** I will **never** create or enlarge any Python module (`.py` file) to be over 450 lines of code.
- **Handling Large Functionality:** To manage complexity, I will:
  1. Split features into smaller, single-responsibility modules.
  2. Create utility modules for shared, reusable code.
  3. Use a facade pattern where a primary module orchestrates calls to smaller, specialized modules.
  4. Clearly document the relationships between modules.
- **Editing Large Files:** When editing existing files that are already over the size limit, I will make minimal, targeted changes and avoid full-file replacements.
- Path Management Rules: All paths must be centrally managed in a paths file, like paths.py  ; all paths in modules must be relative and start with the central path management

## 5. New Code or editing
- NO FALLBACKS ALLOWED - EVER - in pipeline configuration or parameter handling.   Helpful error messages should identify the exact missing configuration
- Do not create defaults, fallbacks, or silent error handling for missing configurations; 
- Pipeline MUST hard fail if any component is not properly configured; only exception when approved by user in API pulls.
- Never use mock or simulated data unless explicity directed.
- nothing is to be marked as "passed" or "complete" until the user explicitly agrees and confirms it passes. This applies to all code, reports, and functionality.

## 7. Change Management & Learning

- **Prohibited Changes Log:** I will maintain a list of prohibited changes as I discover them to avoid repeating mistakes. The timestamp/timezone rule is the first entry.
- **Unsuccessful Fixes Log:** I will maintain a list of unsuccessful fix attempts in the `memory-bank/` to avoid repeating them.
