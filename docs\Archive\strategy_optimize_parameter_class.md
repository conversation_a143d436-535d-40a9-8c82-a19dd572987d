# Strategy-Optimize Parameter Class Design

## Overview

This document outlines the design for a new `StrategyOptimizeParameter` class that will handle strategy-specific parameters for optimization in the V3 engine. This builds on the transition from `strategy` to `signal_algo` while adding optimization capabilities.

## Design Requirements

1. **Core Functionality**:
   - Must support both numeric and categorical parameters
   - Must maintain type safety throughout optimization
   - Must integrate with existing parameter registry system

2. **Optimization Features**:
   - Support for ranges (min/max/step for numeric, choices for categorical)
   - Support for optimization constraints
   - Support for parameter grouping

3. **GUI Integration**:
   - Must expose all relevant metadata for GUI display
   - Must support dynamic parameter loading based on selected strategy

## Proposed Implementation

### Class Definition

```python
class StrategyOptimizeParameter:
    """
    A parameter class designed specifically for strategy optimization.
    
    Attributes:
        name (str): Parameter name
        param_type (str): 'numeric' or 'categorical'
        default: Default value
        min_val: Minimum value (for numeric)
        max_val: Maximum value (for numeric)
        step: Step size (for numeric)
        choices: List of choices (for categorical)
        group: Parameter group name
        optimize (bool): Whether this parameter should be included in optimization
    """
    
    def __init__(self, name, param_type, default, 
                 min_val=None, max_val=None, step=None, 
                 choices=None, group=None, optimize=True):
        self.name = name
        self.param_type = param_type
        self.default = default
        self.min_val = min_val
        self.max_val = max_val
        self.step = step
        self.choices = choices
        self.group = group
        self.optimize = optimize
        
        self._validate()
    
    def _validate(self):
        """Validate parameter configuration."""
        if self.param_type not in ('numeric', 'categorical'):
            raise ValueError("param_type must be 'numeric' or 'categorical'")
            
        if self.param_type == 'numeric':
            if None in (self.min_val, self.max_val, self.step):
                raise ValueError("Numeric parameters require min_val, max_val and step")
        else:
            if not self.choices:
                raise ValueError("Categorical parameters require choices")
    
    def to_dict(self):
        """Convert parameter to dictionary for serialization."""
        return {
            'name': self.name,
            'type': self.param_type,
            'default': self.default,
            'min': self.min_val,
            'max': self.max_val,
            'step': self.step,
            'choices': self.choices,
            'group': self.group,
            'optimize': self.optimize
        }
    
    def get_optimization_space(self):
        """
        Get optimization space definition for this parameter.
        Returns:
            dict: Optimization space definition compatible with optimization frameworks
        """
        if self.param_type == 'numeric':
            return {
                'type': 'range',
                'bounds': [self.min_val, self.max_val],
                'step': self.step
            }
        else:
            return {
                'type': 'choice',
                'choices': self.choices
            }
```

### Integration with Parameter Registry

```python
def register_strategy_parameters(registry, strategy_name, parameters):
    """
    Register a set of strategy-specific optimization parameters.
    
    Args:
        registry: ParameterRegistry instance
        strategy_name: Name of the strategy
        parameters: List of StrategyOptimizeParameter instances
    """
    group_name = f"strategy_{strategy_name}"
    
    for param in parameters:
        # Convert to standard parameter format
        param_def = {
            'default': param.default,
            'optimize': param.optimize,
            'meta': {
                'type': param.param_type,
                'min': param.min_val,
                'max': param.max_val,
                'step': param.step,
                'choices': param.choices,
                'group': param.group
            }
        }
        
        registry.register_parameter(
            group=group_name,
            name=param.name,
            parameter=param_def
        )
```

### Example Usage

```python
# Define EMA strategy parameters
ema_params = [
    StrategyOptimizeParameter(
        name='st_lookback',
        param_type='numeric',
        default=15,
        min_val=5,
        max_val=50,
        step=5,
        group='ema'
    ),
    StrategyOptimizeParameter(
        name='mt_lookback',
        param_type='numeric',
        default=70,
        min_val=20,
        max_val=100,
        step=5,
        group='ema'
    ),
    StrategyOptimizeParameter(
        name='signal_type',
        param_type='categorical',
        default='cross',
        choices=['cross', 'threshold', 'slope'],
        group='ema'
    )
]

# Register with parameter registry
registry = ParameterRegistry()
register_strategy_parameters(registry, 'ema', ema_params)

# Get optimization space for all EMA parameters
opt_space = {
    param.name: param.get_optimization_space()
    for param in ema_params
    if param.optimize
}
```

## Implementation Steps

1. **Create Base Class**:
   - Implement `StrategyOptimizeParameter` class in `v3_engine/strategy_params.py`
   - Add comprehensive unit tests

2. **Registry Integration**:
   - Add `register_strategy_parameters` function to parameter_registry.py
   - Update registry to handle optimization metadata

3. **GUI Integration**:
   - Modify GUI to read optimization metadata
   - Add controls for parameter optimization configuration

4. **Optimization Engine**:
   - Update optimization engine to use the new parameter class
   - Add support for parameter constraints

5. **Documentation**:
   - Update developer documentation
   - Add examples for common strategy configurations

## Testing Plan

1. **Unit Tests**:
   - Parameter validation
   - Serialization/deserialization
   - Optimization space generation

2. **Integration Tests**:
   - Registry integration
   - Full optimization runs
   - GUI interaction

3. **Performance Tests**:
   - Large parameter space optimization
   - Parallel optimization scenarios

## Dependencies

1. **Existing Systems**:
   - ParameterRegistry
   - OptimizationEngine
   - StrategyManager

2. **New Dependencies**:
   - None (pure Python implementation)

## Open Questions

1. Should we support nested parameter groups?
2. How to handle parameter constraints between different parameters?
3. Should we add support for distribution-based parameters (e.g., normal distribution)?

## Timeline

1. **Phase 1 (1 week)**:
   - Implement base class and registry integration
   - Basic unit tests

2. **Phase 2 (1 week)**:
   - GUI integration
   - Optimization engine updates

3. **Phase 3 (3 days)**:
   - Comprehensive testing
   - Documentation updates

## Risk Mitigation

1. **Backward Compatibility**:
   - Maintain old parameter format support during transition
   - Provide migration path for existing strategies

2. **Performance Impact**:
   - Benchmark optimization performance
   - Add caching for parameter metadata

3. **Complexity Management**:
   - Keep interface simple
   - Provide clear examples and documentation
