@echo off
REM ============================================
REM Script: run_main_v4_prod_tmp.bat
REM Description: Extremely simplified version that works reliably
REM ============================================

REM --- Set fixed paths with proper quoting ---
SET "PYTHON_EXE=F:\AI_Library\my_quant_env\Scripts\python.exe"
SET "SCRIPT_PATH=%~dp0main_v4_production_run.py"
SET "OUTPUT_DIR=S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4_trace_outputs"

REM --- Create output directory if it doesn't exist ---
IF NOT EXIST "%OUTPUT_DIR%" mkdir "%OUTPUT_DIR%"

REM --- Create simple timestamp (no parsing) ---
SET "TIMESTAMP=%DATE:~10,4%%DATE:~4,2%%DATE:~7,2%_%TIME:~0,2%%TIME:~3,2%%TIME:~6,2%"
SET "TIMESTAMP=%TIMESTAMP: =0%"

REM --- Define output files ---
SET "FULL_LOG=%OUTPUT_DIR%\full_output_%TIMESTAMP%.txt"
SET "FILTERED_LOG=%OUTPUT_DIR%\filtered_output_%TIMESTAMP%.txt"

echo.
echo Running Python script...
echo Full output will be saved to: "%FULL_LOG%"

REM --- Run Python script and capture all output ---
"%PYTHON_EXE%" "%SCRIPT_PATH%" > "%FULL_LOG%" 2>&1

echo Python script completed.

REM --- Create filtered log with important messages ---
echo Creating filtered log file...

echo ===== FILTERED OUTPUT SUMMARY ===== > "%FILTERED_LOG%"
echo Run Time: %DATE% %TIME% >> "%FILTERED_LOG%"
echo. >> "%FILTERED_LOG%"

echo ===== MILESTONES ===== >> "%FILTERED_LOG%"
findstr /C:"[MILESTONE]" "%FULL_LOG%" >> "%FILTERED_LOG%" 2>nul

echo. >> "%FILTERED_LOG%"
echo ===== WARNINGS AND ERRORS ===== >> "%FILTERED_LOG%"
findstr /C:"[ERROR]" /C:"[WARNING]" /C:"[CRITICAL]" "%FULL_LOG%" >> "%FILTERED_LOG%" 2>nul
findstr /i "error warning exception traceback failed" "%FULL_LOG%" >> "%FILTERED_LOG%" 2>nul

echo.
echo Full log saved to: "%FULL_LOG%"
echo Filtered log saved to: "%FILTERED_LOG%"
echo.
