# Production Code Issues & Fixes

## Current Critical Issues

### [Issue #1] EMA Allocation Model Function
**Task**: 3.1  
**File**: `models/ema_allocation_model.py`  
**Status**: OPEN  
**Priority**: HIGH  
**Description**: `ema_allocation_model_updated` function not properly defined/exported  
**Root Cause**: Test expects `ema_allocation_model_updated` but it's not properly exposed  
**Fix Required**: 
1. Ensure both `ema_allocation_model` and `ema_allocation_model_updated` are properly exported in `__init__.py`
2. Verify function signatures match expected format
3. Update any references to use consistent naming

### [Issue #2] Order Class Constructor Mismatch
**Task**: 3.1  
**File**: `engine/orders.py`  
**Status**: OPEN  
**Priority**: HIGH  
**Description**: `Order.__init__()` receives unexpected `ticker` argument  
**Root Cause**: Test uses parameter name `ticker` but class expects `symbol`  
**Fix Required**: 
1. Update Order class to handle both parameter names or standardize on one
2. Update all call sites to use the correct parameter name
3. Add deprecation warning if needed for backward compatibility

### [Issue #3] Timestamp Handling in Execution Delay
**Task**: 3.1  
**File**: `v3_engine/V3_perf_repadapt_legacybridge.py`  
**Status**: OPEN  
**Priority**: MEDIUM  
**Description**: Error when handling pandas Timestamp objects  
**Root Cause**: Timestamp objects not properly serialized in parameter flow  
**Fix Required**:
1. Convert Timestamp objects to ISO format strings
2. Update parameter validation to handle both string and Timestamp
3. Add type checking and conversion where needed

### [Issue #4] StrategyOptimizeParameter Import Path
**Task**: 3.1  
**File**: `v3_engine/strategy_optimize_parameter.py`  
**Status**: OPEN  
**Priority**: HIGH  
**Description**: Could not import StrategyOptimizeParameter class  
**Root Cause**: Inconsistent import paths across modules  
**Fix Required**:
1. Standardize import paths throughout codebase
2. Update `__init__.py` files to expose necessary classes
3. Fix any circular imports

### [Issue #5] DataFrame Truth Value Ambiguity
**Task**: 3.1  
**Files**: Multiple files handling DataFrames  
**Status**: OPEN  
**Priority**: MEDIUM  
**Description**: Ambiguous truth value when evaluating DataFrames  
**Root Cause**: Using DataFrames directly in boolean contexts  
**Fix Required**:
1. Replace all ambiguous DataFrame comparisons with explicit `.empty`, `.any()`, or `.all()`
2. Add type checking before boolean operations
3. Update tests to verify correct behavior

## Recently Fixed Issues

### [Fixed] Benchmark Calculation Compliance
**Task**: 1.1  
**File**: `tests/verify_critical_issues.py`  
**Status**: FIXED (2025-06-05)  
**Resolution**: Updated test to use official `calculate_equal_weight_benchmark` function  
**Verification**: Benchmark returns now correctly calculated from price data returns

## Issue Tracking Process

1. **Reporting New Issues**:
   - Add to this file with all relevant details
   - Assign a task number and priority
   - Include steps to reproduce and expected behavior

2. **Resolving Issues**:
   - Create a branch for the fix
   - Reference the issue number in commit messages
   - Update status when fixed
   - Move to "Recently Fixed" section after verification

3. **Verification**:
   - Run relevant test cases
   - Update documentation if needed
   - Get code review before merging

## Related Documents
- [Task List & Tracking](./parameter_Task_List_Tracking.md)
- [Overview](./parameter_refactoring_overview.md)
- [Problem Changes & Fixes Log](./Problem_Changes_Fixes_C_Log.md)
