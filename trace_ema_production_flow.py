"""
trace_ema_production_flow.py
----------------------------
Traces the EMA signal generation process by calling production functions
from `v4.models.ema_allocation_model_v4.py` and replicating its orchestration
logic to show detailed step-by-step data flow.

Outputs:
1.  CSV file with final daily allocation weights.
2.  TXT log file with detailed trace of inputs/outputs for each step.
"""

import logging
import sys
import os
from pathlib import Path
import pandas as pd
from datetime import datetime

print(f"Initial PYTHONPATH: {os.environ.get('PYTHONPATH')}")
print(f"Initial sys.path: {sys.path}")

# --- Path Setup ---
import numpy as np

# Determine project root
_script_path = Path(__file__).resolve()
# This script is now in PROJECT_ROOT/, so its parent directory is PROJECT_ROOT.
_project_root = _script_path.parent 

# Ensure project root is in sys.path at the beginning
if str(_project_root) not in sys.path:
    sys.path.insert(0, str(_project_root))
    print(f"PROJECT_ROOT '{_project_root}' added to sys.path.")

print(f"sys.path after ensuring PROJECT_ROOT: {sys.path}")

# Attempt to import config.paths for further path configurations
try:
    from config import paths
    print(f"Successfully imported config.paths. PROJECT_ROOT according to paths.py: {paths.PROJECT_ROOT}")
    # paths.py should handle adding CUSTOM_LIB_PATH and other necessary paths
except ImportError as e:
    print(f"CRITICAL ERROR: Could not import config.paths. Error: {e}")
    print("Ensure config/paths.py exists and PROJECT_ROOT is correctly in sys.path before this import.")
    # Fallback or error handling if config.paths is essential and not found

# --- Settings Import ---
try:
    from v4.settings.settings_CPS_v4 import load_settings
    settings = load_settings()
    if not settings:
        raise RuntimeError("load_settings() returned empty or None.")
    print("Successfully loaded settings.")
except (ImportError, RuntimeError) as e:
    print(f"CRITICAL ERROR: Could not load settings. Error: {e}")
    sys.exit(1)

# --- Configuration & Output Paths ---
trace_config = settings.get('trace_ema_production_flow', {})
START_DATE_STR = trace_config.get('start_date', "2021-01-20")
END_DATE_STR = trace_config.get('end_date', "2021-02-28")

# User-specified output directory. All logs and CSVs will be saved here.
OUTPUT_BASE_DIR = Path(r"S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\test_logs")
OUTPUT_BASE_DIR.mkdir(exist_ok=True, parents=True)

run_timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
file_suffix = f"{START_DATE_STR.replace('-', '')}_{END_DATE_STR.replace('-', '')}_{run_timestamp}"

FINAL_WEIGHTS_CSV = OUTPUT_BASE_DIR / f"production_flow_weights_{file_suffix}.csv"
TRACE_LOG_TXT = OUTPUT_BASE_DIR / f"production_flow_trace_{file_suffix}.txt"

# --- Logger Setup ---
console_logger = logging.getLogger("console_trace")
if not console_logger.handlers:
    console_handler = logging.StreamHandler(sys.stdout)
    console_formatter = logging.Formatter("%(asctime)s - %(levelname)s - %(message)s")
    console_handler.setFormatter(console_formatter)
    console_logger.addHandler(console_handler)
    console_logger.setLevel(logging.INFO)

trace_file_logger = logging.getLogger("file_trace")
if not trace_file_logger.handlers:
    file_handler = logging.FileHandler(TRACE_LOG_TXT, mode='w')
    file_formatter = logging.Formatter("%(asctime)s - %(levelname)s - %(message)s")
    file_handler.setFormatter(file_formatter)
    trace_file_logger.addHandler(file_handler)
    trace_file_logger.setLevel(logging.INFO)

# --- Module Imports ---
import v4.models.ema_allocation_model_v4 as ema_model_module
from v4.config.allocation_rules_v4 import get_allocation_weights
from v4.engine.data_loader_v4 import load_data_for_backtest

console_logger.info("Script setup complete. Settings loaded and loggers configured.")
trace_file_logger.info("Script setup complete. Settings loaded and loggers configured.")

def _fetch_setting(settings_dict, path_list, default_value):
    """Helper to fetch a nested value from a dictionary."""
    current_level = settings_dict
    for key in path_list:
        if isinstance(current_level, dict) and key in current_level:
            current_level = current_level[key]
        else:
            return default_value
    return current_level

def get_ema_parameters(settings: dict) -> dict:
    """Constructs the 'parameters' dictionary for EMA calculations based on settings."""
    params = {}
    params['st_lookback'] = ema_model_module._to_numeric(_fetch_setting(settings, ['ema_model', 'st_lookback'], _fetch_setting(settings, ['strategy', 'st_lookback'], 15)))
    params['mt_lookback'] = ema_model_module._to_numeric(_fetch_setting(settings, ['ema_model', 'mt_lookback'], _fetch_setting(settings, ['strategy', 'mt_lookback'], 70)))
    params['lt_lookback'] = ema_model_module._to_numeric(_fetch_setting(settings, ['ema_model', 'lt_lookback'], _fetch_setting(settings, ['strategy', 'lt_lookback'], 100)))
    params['min_weight'] = ema_model_module._to_numeric(_fetch_setting(settings, ['ema_model', 'min_weight'], _fetch_setting(settings, ['strategy', 'min_weight'], 0.0)))
    params['max_weight'] = ema_model_module._to_numeric(_fetch_setting(settings, ['ema_model', 'max_weight'], _fetch_setting(settings, ['strategy', 'max_weight'], 1.0)))
    # This parameter is strategy-specific for EMA model, not system_top_n
    params['top_n_strat_specific'] = ema_model_module._to_numeric(_fetch_setting(settings, ['ema_model', 'top_n'], _fetch_setting(settings, ['strategy', 'top_n'], 3)))
    return params

def main():
    console_logger.info(f"Starting EMA production flow trace.")
    try:
        trace_file_logger.info(f"EMA Production Flow Trace Log - {run_timestamp}")
        trace_file_logger.info(f"Period: {START_DATE_STR} to {END_DATE_STR}")

        # --- Load Data and Settings ---
        console_logger.info("Loading price data...")
        all_data = load_data_for_backtest()
        price_df = all_data["price_data"].copy()
        console_logger.info(f"Price data loaded: {price_df.shape[0]} rows, {price_df.shape[1]} assets.")
        trace_file_logger.info(f"Price data loaded. Shape: {price_df.shape}")

        console_logger.info("Loading CPS V4 settings...")
        settings = load_settings()
        ema_params = get_ema_parameters(settings)
        # Refactored to use new parameter paths: settings['system']['system_top_n']['default_value']
        from v4.settings.settings_utils import _extract_value_from_complex_dict
        system_top_n_raw = _fetch_setting(settings, ['system', 'system_top_n'], None)
        if system_top_n_raw is not None:
            system_top_n = ema_model_module._to_numeric(_extract_value_from_complex_dict(system_top_n_raw))
        else:
            system_top_n = None
        signal_algo = _fetch_setting(settings, ['strategy', 'signal_algo'], 'ema_default') # Default from prod code
        
        trace_file_logger.info("--- Effective Settings for Trace ---")
        trace_file_logger.info(f"EMA Lookbacks: ST={ema_params['st_lookback']}, MT={ema_params['mt_lookback']}, LT={ema_params['lt_lookback']}")
        trace_file_logger.info(f"EMA Weights: Min={ema_params['min_weight']}, Max={ema_params['max_weight']}")
        trace_file_logger.info(f"EMA Strategy Top N: {ema_params['top_n_strat_specific']}") # This is what ema_allocation_model uses for its 'top_n'
        trace_file_logger.info(f"System Top N (for allocation rules): {system_top_n}") # This is passed to get_allocation_weights
        trace_file_logger.info(f"Signal Algorithm for Rules: {signal_algo}")
        trace_file_logger.info("-----------------------------------")

        # --- Prepare Date Range ---
        price_df = price_df.sort_index()
        start_date_dt = pd.to_datetime(START_DATE_STR)
        end_date_dt = pd.to_datetime(END_DATE_STR)
        analysis_dates = price_df.loc[start_date_dt:end_date_dt].index

        if analysis_dates.empty:
            console_logger.error("No price data available in the requested date range.")
            return

        console_logger.info(f"Tracing {len(analysis_dates)} business days from {START_DATE_STR} to {END_DATE_STR}.")

        all_daily_weights_records = []

        for current_date in analysis_dates:
            trace_file_logger.info(f"\n========== Tracing for Date: {current_date.strftime('%Y-%m-%d')} ==========")
            current_price_slice = price_df.loc[:current_date]

            # Step 1: Calculate EMA Metrics (using production function)
            console_logger.info("--- Step 1: Calculate EMA Metrics ---")
            trace_file_logger.info("--- Step 1: Calculate EMA Metrics ---")
            trace_file_logger.info(f"Input price_data_slice shape: {current_price_slice.shape}")
            trace_file_logger.info(f"Setting module-level ema_params in ema_model_module: {ema_params}")
            
            # Set module-level parameters in the imported ema_model_module
            ema_model_module.st_lookback = ema_params['st_lookback']
            ema_model_module.mt_lookback = ema_params['mt_lookback']
            ema_model_module.lt_lookback = ema_params['lt_lookback']
            # Also set min_weight and max_weight if they are used as module-level vars by allocation logic
            # Based on ema_allocation_model_v4.py, min_weight and max_weight are module-level
            ema_model_module.min_weight = ema_params['min_weight']
            ema_model_module.max_weight = ema_params['max_weight']
            # top_n_strat_specific from ema_params is not directly used as a module-level var in the same way by ema_allocation_model.py
            # It uses settings['system_top_n'] and allocation_rules_v4.get_allocation_weights for 'top_n' logic.

            short_ema_df, med_ema_df, long_ema_df = ema_model_module.calculate_ema_metrics(current_price_slice)
            trace_file_logger.info(f"Output short_ema_df tail(1):\n{short_ema_df.tail(1)}")
            trace_file_logger.info(f"Output med_ema_df tail (1):\n{med_ema_df.iloc[-1:].to_string()}")
            trace_file_logger.info(f"Output long_ema_df tail (1):\n{long_ema_df.iloc[-1:].to_string()}")

            # Step 2: Calculate EMA Ratios (using production function)
            trace_file_logger.info("--- Step 2: Calculate EMA Ratios ---")
            # Inputs are the full EMA dataframes from previous step
            _, _, _, stmtemax_series, mtltemax_series, emaxavg_series = ema_model_module.calculate_ema_ratios(short_ema_df, med_ema_df, long_ema_df)
            trace_file_logger.info(f"Output stmtemax_series (latest):\n{stmtemax_series.tail(1).to_string()}")
            trace_file_logger.info(f"Output mtltemax_series (latest):\n{mtltemax_series.tail(1).to_string()}")
            trace_file_logger.info(f"Output emaxavg_series (latest for ranking):\n{emaxavg_series.tail(1).to_string()}")

            # --- Step 3: Determine Allocation Weights (using production function) ---
            console_logger.info("--- Step 3: Determine Allocation Weights ---")
            trace_file_logger.info("--- Step 3: Determine Allocation Weights ---")
            
            # The production function ema_allocation_model_updated will use its own module-level settings
            # (st_lookback, mt_lookback, lt_lookback, min_weight, max_weight) which we have set above.
            # It will also use its own internally loaded `settings` for `system_top_n` and `signal_algo`.
            trace_file_logger.info(f"Calling ema_model_module.ema_allocation_model_updated with price_data up to {current_date.strftime('%Y-%m-%d')}")
            trace_file_logger.info("Attempting to call ema_model_module.ema_allocation_model_updated...")
            trace_file_logger.info(f"  ema_model_module.st_lookback={ema_model_module.st_lookback}, mt_lookback={ema_model_module.mt_lookback}, lt_lookback={ema_model_module.lt_lookback}")
            trace_file_logger.info(f"  ema_model_module.min_weight={ema_model_module.min_weight}, ema_model_module.max_weight={ema_model_module.max_weight}")
            trace_file_logger.info(f"  Trace script's signal_algo: {signal_algo}, system_top_n: {system_top_n} (Note: production module uses its own settings for these)")

            final_weights_dict = ema_model_module.ema_allocation_model_updated_single(price_data=current_price_slice)
            trace_file_logger.info("Successfully called ema_model_module.ema_allocation_model_updated.")
            
            # Process the output from the production function
            final_weights = pd.Series(index=price_df.columns, dtype=float).fillna(0.0) # Default to zero weights
            if not final_weights_dict:
                console_logger.error(f"ERROR: ema_allocation_model_updated returned empty dictionary for date {current_date.strftime('%Y-%m-%d')}")
                trace_file_logger.error(f"ERROR: ema_allocation_model_updated returned empty dictionary for date {current_date.strftime('%Y-%m-%d')}")
            elif current_date not in final_weights_dict:
                console_logger.error(f"ERROR: ema_allocation_model_updated did not return weights for current_date {current_date.strftime('%Y-%m-%d')}. Keys: {list(final_weights_dict.keys())}")
                trace_file_logger.error(f"ERROR: ema_allocation_model_updated did not return weights for current_date {current_date.strftime('%Y-%m-%d')}. Keys: {list(final_weights_dict.keys())}")
            elif not isinstance(final_weights_dict[current_date], dict):
                console_logger.error(f"ERROR: Weights for {current_date.strftime('%Y-%m-%d')} is not a dict: {type(final_weights_dict[current_date])}")
                trace_file_logger.error(f"ERROR: Weights for {current_date.strftime('%Y-%m-%d')} is not a dict: {type(final_weights_dict[current_date])}")
            else:
                # Ensure all assets from price_df.columns are present in final_weights, defaulting to 0.0 if not returned by prod
                for asset_col in price_df.columns:
                    final_weights[asset_col] = final_weights_dict[current_date].get(asset_col, 0.0)
                
            trace_file_logger.info(f"Output final_weights for {current_date.strftime('%Y-%m-%d')}:\n{final_weights.to_string()}")

            # Ensure all original assets are in the final_day_weights for consistent CSV columns
            output_weights_for_csv = {asset: final_weights.get(asset, 0.0) for asset in price_df.columns}

            trace_file_logger.info(f"Final normalized weights for {current_date.strftime('%Y-%m-%d')}: {output_weights_for_csv}")
            
            record = {"Date": current_date.strftime("%Y-%m-%d")}
            record.update(output_weights_for_csv)
            all_daily_weights_records.append(record)

        # --- Save Final Weights CSV ---
        if all_daily_weights_records:
            weights_out_df = pd.DataFrame(all_daily_weights_records).set_index("Date")
            trace_file_logger.info(f"Attempting to save final weights to {FINAL_WEIGHTS_CSV}...")
            weights_out_df.to_csv(FINAL_WEIGHTS_CSV)
            console_logger.info(f"Final weights CSV saved to: {FINAL_WEIGHTS_CSV}")
            trace_file_logger.info(f"Final weights CSV saved to: {FINAL_WEIGHTS_CSV}")
            console_logger.info(f"\nPreview of final weights:\n{weights_out_df.head().to_string()}")
        else:
            console_logger.info("No weight records generated.")

        console_logger.info(f"Detailed trace log saved to: {TRACE_LOG_TXT}")
        console_logger.info("Trace script finished (end of try block).")
        trace_file_logger.info("Trace script finished (end of try block).")
    finally:
        console_logger.info("EMA production flow trace finished (finally block).")
        trace_file_logger.info("EMA production flow trace finished (finally block).")

if __name__ == "__main__":
    main()
