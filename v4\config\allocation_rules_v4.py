"""
Allocation Rules for CPS V4

This module defines the allocation rules for different numbers of selected assets.
These rules determine the weight distribution for assets based on their ranking.

The module supports algorithm-specific allocation rules, allowing different
weight distributions for different algorithms (EMA, equal weight, etc.).
"""

# Algorithm-specific allocation rules by number of selected assets (top_n)
# Each algorithm has its own set of rules for different numbers of assets
ALGO_ALLOCATION_RULES = {
    # EMA allocation rules
    'ema': {
        1: [1.0],
        2: [0.6, 0.4],
        3: [0.45, 0.35, 0.20],
        4: [0.35, 0.30, 0.20, 0.15],
        5: [0.30, 0.25, 0.20, 0.15, 0.10],
    },
    # Equal weight allocation rules
    'equal_weight': {
        1: [1.0],
        2: [0.5, 0.5],
        3: [0.33, 0.33, 0.34],
        4: [0.25, 0.25, 0.25, 0.25],
        5: [0.2, 0.2, 0.2, 0.2, 0.2],
    },
    # Risk parity allocation (example - actual values would be calculated dynamically)
    'risk_parity': {
        1: [1.0],
        2: [0.55, 0.45],
        3: [0.40, 0.35, 0.25],
        4: [0.30, 0.28, 0.22, 0.20],
        5: [0.25, 0.23, 0.20, 0.17, 0.15],
    },
    # Add more algorithm rule sets as needed
}

# Default allocation rules (for backward compatibility)
# Uses EMA rules as the default
ALLOCATION_RULES = ALGO_ALLOCATION_RULES['ema']

def get_allocation_weights(top_n, algorithm='ema'):
    """
    Get allocation weights for the specified number of top assets and algorithm.
    
    Args:
        top_n (int): Number of top assets to allocate to
        algorithm (str): Algorithm name to use for allocation rules (default: 'ema')
        
    Returns:
        list: List of allocation weights in descending order
        
    Raises:
        ValueError: If top_n is not supported or algorithm is not recognized
    """
    # Check if algorithm exists
    if algorithm not in ALGO_ALLOCATION_RULES:
        available_algos = list(ALGO_ALLOCATION_RULES.keys())
        raise ValueError(f"Unsupported algorithm: {algorithm}. Available options: {available_algos}")
    
    # Get rules for the specified algorithm
    rules = ALGO_ALLOCATION_RULES[algorithm]
    
    # Check if top_n is supported for this algorithm
    if top_n not in rules:
        raise ValueError(f"Unsupported top_n value: {top_n} for algorithm {algorithm}.")
    
    return rules[top_n]
