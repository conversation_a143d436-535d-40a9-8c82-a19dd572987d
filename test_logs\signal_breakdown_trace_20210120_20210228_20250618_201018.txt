2025-06-18 20:10:18,709 - INFO - Script setup complete. Settings loaded and loggers configured.
2025-06-18 20:10:18,709 - INFO - EMA Signal Breakdown Trace Log - 20250618_201018
2025-06-18 20:10:18,709 - INFO - Period: 2021-01-20 to 2021-02-28
2025-06-18 20:10:18,710 - INFO - Initializing Backtest Engine components...
2025-06-18 20:10:18,710 - INFO - Backtest Engine initialized with initial capital: $1,000,000.00 and execution delay: 0 days.
2025-06-18 20:10:19,074 - INFO - Price data loaded. Shape: (1370, 5)
2025-06-18 20:10:19,078 - INFO - --- Effective Settings for Trace ---
2025-06-18 20:10:19,100 - INFO - EMA Lookbacks: ST=15, MT=70, LT=100
2025-06-18 20:10:19,100 - INFO - EMA Weights: Min=0.0, Max=1.0
2025-06-18 20:10:19,100 - INFO - EMA Strategy Top N: 3
2025-06-18 20:10:19,100 - INFO - System Top N (for allocation rules): 2
2025-06-18 20:10:19,100 - INFO - Signal Algorithm for Rules: ema
2025-06-18 20:10:19,100 - INFO - -----------------------------------

2025-06-18 20:10:19,103 - INFO - 
========== Tracing for Date: 2021-01-20 ==========
2025-06-18 20:10:19,104 - INFO - --- Calling Production Model with trace_mode=True ---
2025-06-18 20:10:19,115 - INFO - --- Step 1: Ratios (EMAXAvg) ---
2025-06-18 20:10:19,116 - INFO - Ratios received:
SPY    1.031513
SHV    1.000027
EFA    1.039898
TLT    0.985320
PFF    1.014675
2025-06-18 20:10:19,116 - INFO - --- Step 2: Ranks ---
2025-06-18 20:10:19,118 - INFO - Ranks received:
  Asset  EMAXAvg_Value  Rank_Ordinal
0   EFA       1.039898             1
1   SPY       1.031513             2
2   PFF       1.014675             3
3   SHV       1.000027             4
4   TLT       0.985320             5
2025-06-18 20:10:19,119 - INFO - --- Step 3: Signal (Allocation) ---
2025-06-18 20:10:19,119 - INFO - Signal received:
SPY    0.4
SHV    0.0
EFA    0.6
TLT    0.0
PFF    0.0
2025-06-18 20:10:19,119 - INFO - --- Processing Backtest Engine for date: 2021-01-20 ---
2025-06-18 20:10:19,120 - INFO - No orders from previous signals to execute for 2021-01-20
2025-06-18 20:10:19,120 - INFO - Generated 2 new orders based on today's signal for 2021-01-20
2025-06-18 20:10:19,120 - INFO - T+0 Execution: Processing 2 orders generated from today's signal.
2025-06-18 20:10:19,120 - INFO - Executed 2 T+0 trades from today's signal for 2021-01-20
2025-06-18 20:10:19,120 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine BEFORE mark_to_market: <class 'pandas.core.series.Series'>
2025-06-18 20:10:19,121 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine BEFORE direct get_weights call: <class 'pandas.core.series.Series'>
2025-06-18 20:10:19,122 - INFO - Final post-trade allocations for 2021-01-20:
EFA     0.599269
SPY     0.399664
Cash    0.001067
2025-06-18 20:10:19,122 - INFO - 
========== Tracing for Date: 2021-01-21 ==========
2025-06-18 20:10:19,122 - INFO - --- Calling Production Model with trace_mode=True ---
2025-06-18 20:10:19,131 - INFO - --- Step 1: Ratios (EMAXAvg) ---
2025-06-18 20:10:19,131 - INFO - Ratios received:
SPY    1.031953
SHV    1.000024
EFA    1.039822
TLT    0.984949
PFF    1.014642
2025-06-18 20:10:19,131 - INFO - --- Step 2: Ranks ---
2025-06-18 20:10:19,133 - INFO - Ranks received:
  Asset  EMAXAvg_Value  Rank_Ordinal
0   EFA       1.039822             1
1   SPY       1.031953             2
2   PFF       1.014642             3
3   SHV       1.000024             4
4   TLT       0.984949             5
2025-06-18 20:10:19,133 - INFO - --- Step 3: Signal (Allocation) ---
2025-06-18 20:10:19,134 - INFO - Signal received:
SPY    0.4
SHV    0.0
EFA    0.6
TLT    0.0
PFF    0.0
2025-06-18 20:10:19,134 - INFO - --- Processing Backtest Engine for date: 2021-01-21 ---
2025-06-18 20:10:19,134 - INFO - No orders from previous signals to execute for 2021-01-21
2025-06-18 20:10:19,135 - INFO - Generated 2 new orders based on today's signal for 2021-01-21
2025-06-18 20:10:19,135 - INFO - T+0 Execution: Processing 2 orders generated from today's signal.
2025-06-18 20:10:19,135 - INFO - Executed 2 T+0 trades from today's signal for 2021-01-21
2025-06-18 20:10:19,135 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine BEFORE mark_to_market: <class 'pandas.core.series.Series'>
2025-06-18 20:10:19,135 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine BEFORE direct get_weights call: <class 'pandas.core.series.Series'>
2025-06-18 20:10:19,136 - INFO - Final post-trade allocations for 2021-01-21:
EFA     0.599912
SPY     0.400055
Cash    0.000032
2025-06-18 20:10:19,136 - INFO - 
========== Tracing for Date: 2021-01-22 ==========
2025-06-18 20:10:19,136 - INFO - --- Calling Production Model with trace_mode=True ---
2025-06-18 20:10:19,144 - INFO - --- Step 1: Ratios (EMAXAvg) ---
2025-06-18 20:10:19,145 - INFO - Ratios received:
SPY    1.032048
SHV    1.000022
EFA    1.039386
TLT    0.984831
PFF    1.014674
2025-06-18 20:10:19,146 - INFO - --- Step 2: Ranks ---
2025-06-18 20:10:19,147 - INFO - Ranks received:
  Asset  EMAXAvg_Value  Rank_Ordinal
0   EFA       1.039386             1
1   SPY       1.032048             2
2   PFF       1.014674             3
3   SHV       1.000022             4
4   TLT       0.984831             5
2025-06-18 20:10:19,147 - INFO - --- Step 3: Signal (Allocation) ---
2025-06-18 20:10:19,148 - INFO - Signal received:
SPY    0.4
SHV    0.0
EFA    0.6
TLT    0.0
PFF    0.0
2025-06-18 20:10:19,148 - INFO - --- Processing Backtest Engine for date: 2021-01-22 ---
2025-06-18 20:10:19,148 - INFO - No orders from previous signals to execute for 2021-01-22
2025-06-18 20:10:19,148 - INFO - Generated 2 new orders based on today's signal for 2021-01-22
2025-06-18 20:10:19,149 - INFO - T+0 Execution: Processing 2 orders generated from today's signal.
2025-06-18 20:10:19,149 - INFO - Executed 2 T+0 trades from today's signal for 2021-01-22
2025-06-18 20:10:19,149 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine BEFORE mark_to_market: <class 'pandas.core.series.Series'>
2025-06-18 20:10:19,149 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine BEFORE direct get_weights call: <class 'pandas.core.series.Series'>
2025-06-18 20:10:19,150 - INFO - Final post-trade allocations for 2021-01-22:
EFA     0.599951
SPY     0.399989
Cash    0.000060
2025-06-18 20:10:19,150 - INFO - 
========== Tracing for Date: 2021-01-25 ==========
2025-06-18 20:10:19,150 - INFO - --- Calling Production Model with trace_mode=True ---
2025-06-18 20:10:19,158 - INFO - --- Step 1: Ratios (EMAXAvg) ---
2025-06-18 20:10:19,159 - INFO - Ratios received:
SPY    1.032261
SHV    1.000019
EFA    1.038844
TLT    0.985371
PFF    1.014524
2025-06-18 20:10:19,159 - INFO - --- Step 2: Ranks ---
2025-06-18 20:10:19,160 - INFO - Ranks received:
  Asset  EMAXAvg_Value  Rank_Ordinal
0   EFA       1.038844             1
1   SPY       1.032261             2
2   PFF       1.014524             3
3   SHV       1.000019             4
4   TLT       0.985371             5
2025-06-18 20:10:19,160 - INFO - --- Step 3: Signal (Allocation) ---
2025-06-18 20:10:19,161 - INFO - Signal received:
SPY    0.4
SHV    0.0
EFA    0.6
TLT    0.0
PFF    0.0
2025-06-18 20:10:19,161 - INFO - --- Processing Backtest Engine for date: 2021-01-25 ---
2025-06-18 20:10:19,161 - INFO - No orders from previous signals to execute for 2021-01-25
2025-06-18 20:10:19,162 - INFO - Generated 2 new orders based on today's signal for 2021-01-25
2025-06-18 20:10:19,162 - INFO - T+0 Execution: Processing 2 orders generated from today's signal.
2025-06-18 20:10:19,162 - INFO - Executed 2 T+0 trades from today's signal for 2021-01-25
2025-06-18 20:10:19,162 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine BEFORE mark_to_market: <class 'pandas.core.series.Series'>
2025-06-18 20:10:19,162 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine BEFORE direct get_weights call: <class 'pandas.core.series.Series'>
2025-06-18 20:10:19,163 - INFO - Final post-trade allocations for 2021-01-25:
EFA     0.599855
SPY     0.400132
Cash    0.000013
2025-06-18 20:10:19,164 - INFO - 
========== Tracing for Date: 2021-01-26 ==========
2025-06-18 20:10:19,164 - INFO - --- Calling Production Model with trace_mode=True ---
2025-06-18 20:10:19,173 - INFO - --- Step 1: Ratios (EMAXAvg) ---
2025-06-18 20:10:19,173 - INFO - Ratios received:
SPY    1.032272
SHV    1.000018
EFA    1.038357
TLT    0.985772
PFF    1.014220
2025-06-18 20:10:19,173 - INFO - --- Step 2: Ranks ---
2025-06-18 20:10:19,174 - INFO - Ranks received:
  Asset  EMAXAvg_Value  Rank_Ordinal
0   EFA       1.038357             1
1   SPY       1.032272             2
2   PFF       1.014220             3
3   SHV       1.000018             4
4   TLT       0.985772             5
2025-06-18 20:10:19,175 - INFO - --- Step 3: Signal (Allocation) ---
2025-06-18 20:10:19,175 - INFO - Signal received:
SPY    0.4
SHV    0.0
EFA    0.6
TLT    0.0
PFF    0.0
2025-06-18 20:10:19,177 - INFO - --- Processing Backtest Engine for date: 2021-01-26 ---
2025-06-18 20:10:19,177 - INFO - No orders from previous signals to execute for 2021-01-26
2025-06-18 20:10:19,178 - INFO - Generated 2 new orders based on today's signal for 2021-01-26
2025-06-18 20:10:19,178 - INFO - T+0 Execution: Processing 2 orders generated from today's signal.
2025-06-18 20:10:19,178 - INFO - Executed 2 T+0 trades from today's signal for 2021-01-26
2025-06-18 20:10:19,178 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine BEFORE mark_to_market: <class 'pandas.core.series.Series'>
2025-06-18 20:10:19,178 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine BEFORE direct get_weights call: <class 'pandas.core.series.Series'>
2025-06-18 20:10:19,179 - INFO - Final post-trade allocations for 2021-01-26:
EFA     0.599979
SPY     0.399769
Cash    0.000252
2025-06-18 20:10:19,180 - INFO - 
========== Tracing for Date: 2021-01-27 ==========
2025-06-18 20:10:19,180 - INFO - --- Calling Production Model with trace_mode=True ---
2025-06-18 20:10:19,189 - INFO - --- Step 1: Ratios (EMAXAvg) ---
2025-06-18 20:10:19,189 - INFO - Ratios received:
SPY    1.030859
SHV    1.000021
EFA    1.036585
TLT    0.986282
PFF    1.013185
2025-06-18 20:10:19,189 - INFO - --- Step 2: Ranks ---
2025-06-18 20:10:19,190 - INFO - Ranks received:
  Asset  EMAXAvg_Value  Rank_Ordinal
0   EFA       1.036585             1
1   SPY       1.030859             2
2   PFF       1.013185             3
3   SHV       1.000021             4
4   TLT       0.986282             5
2025-06-18 20:10:19,191 - INFO - --- Step 3: Signal (Allocation) ---
2025-06-18 20:10:19,191 - INFO - Signal received:
SPY    0.4
SHV    0.0
EFA    0.6
TLT    0.0
PFF    0.0
2025-06-18 20:10:19,191 - INFO - --- Processing Backtest Engine for date: 2021-01-27 ---
2025-06-18 20:10:19,192 - INFO - No orders from previous signals to execute for 2021-01-27
2025-06-18 20:10:19,192 - INFO - Generated 2 new orders based on today's signal for 2021-01-27
2025-06-18 20:10:19,192 - INFO - T+0 Execution: Processing 2 orders generated from today's signal.
2025-06-18 20:10:19,193 - INFO - Executed 2 T+0 trades from today's signal for 2021-01-27
2025-06-18 20:10:19,193 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine BEFORE mark_to_market: <class 'pandas.core.series.Series'>
2025-06-18 20:10:19,193 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine BEFORE direct get_weights call: <class 'pandas.core.series.Series'>
2025-06-18 20:10:19,194 - INFO - Final post-trade allocations for 2021-01-27:
EFA     0.600024
SPY     0.399746
Cash    0.000230
2025-06-18 20:10:19,194 - INFO - 
========== Tracing for Date: 2021-01-28 ==========
2025-06-18 20:10:19,194 - INFO - --- Calling Production Model with trace_mode=True ---
2025-06-18 20:10:19,203 - INFO - --- Step 1: Ratios (EMAXAvg) ---
2025-06-18 20:10:19,203 - INFO - Ratios received:
SPY    1.030028
SHV    1.000019
EFA    1.035317
TLT    0.986466
PFF    1.012752
2025-06-18 20:10:19,204 - INFO - --- Step 2: Ranks ---
2025-06-18 20:10:19,205 - INFO - Ranks received:
  Asset  EMAXAvg_Value  Rank_Ordinal
0   EFA       1.035317             1
1   SPY       1.030028             2
2   PFF       1.012752             3
3   SHV       1.000019             4
4   TLT       0.986466             5
2025-06-18 20:10:19,205 - INFO - --- Step 3: Signal (Allocation) ---
2025-06-18 20:10:19,206 - INFO - Signal received:
SPY    0.4
SHV    0.0
EFA    0.6
TLT    0.0
PFF    0.0
2025-06-18 20:10:19,206 - INFO - --- Processing Backtest Engine for date: 2021-01-28 ---
2025-06-18 20:10:19,206 - INFO - No orders from previous signals to execute for 2021-01-28
2025-06-18 20:10:19,206 - INFO - Generated 2 new orders based on today's signal for 2021-01-28
2025-06-18 20:10:19,206 - INFO - T+0 Execution: Processing 2 orders generated from today's signal.
2025-06-18 20:10:19,207 - INFO - Executed 2 T+0 trades from today's signal for 2021-01-28
2025-06-18 20:10:19,207 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine BEFORE mark_to_market: <class 'pandas.core.series.Series'>
2025-06-18 20:10:19,207 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine BEFORE direct get_weights call: <class 'pandas.core.series.Series'>
2025-06-18 20:10:19,208 - INFO - Final post-trade allocations for 2021-01-28:
EFA     0.599970
SPY     0.399908
Cash    0.000122
2025-06-18 20:10:19,208 - INFO - 
========== Tracing for Date: 2021-01-29 ==========
2025-06-18 20:10:19,208 - INFO - --- Calling Production Model with trace_mode=True ---
2025-06-18 20:10:19,216 - INFO - --- Step 1: Ratios (EMAXAvg) ---
2025-06-18 20:10:19,216 - INFO - Ratios received:
SPY    1.028162
SHV    1.000022
EFA    1.033025
TLT    0.986345
PFF    1.012140
2025-06-18 20:10:19,216 - INFO - --- Step 2: Ranks ---
2025-06-18 20:10:19,217 - INFO - Ranks received:
  Asset  EMAXAvg_Value  Rank_Ordinal
0   EFA       1.033025             1
1   SPY       1.028162             2
2   PFF       1.012140             3
3   SHV       1.000022             4
4   TLT       0.986345             5
2025-06-18 20:10:19,218 - INFO - --- Step 3: Signal (Allocation) ---
2025-06-18 20:10:19,218 - INFO - Signal received:
SPY    0.4
SHV    0.0
EFA    0.6
TLT    0.0
PFF    0.0
2025-06-18 20:10:19,218 - INFO - --- Processing Backtest Engine for date: 2021-01-29 ---
2025-06-18 20:10:19,219 - INFO - No orders from previous signals to execute for 2021-01-29
2025-06-18 20:10:19,219 - INFO - Generated 1 new orders based on today's signal for 2021-01-29
2025-06-18 20:10:19,219 - INFO - T+0 Execution: Processing 1 orders generated from today's signal.
2025-06-18 20:10:19,219 - INFO - Executed 1 T+0 trades from today's signal for 2021-01-29
2025-06-18 20:10:19,219 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine BEFORE mark_to_market: <class 'pandas.core.series.Series'>
2025-06-18 20:10:19,219 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine BEFORE direct get_weights call: <class 'pandas.core.series.Series'>
2025-06-18 20:10:19,220 - INFO - Final post-trade allocations for 2021-01-29:
EFA     0.599869
SPY     0.400073
Cash    0.000058
2025-06-18 20:10:19,220 - INFO - 
========== Tracing for Date: 2021-02-01 ==========
2025-06-18 20:10:19,220 - INFO - --- Calling Production Model with trace_mode=True ---
2025-06-18 20:10:19,228 - INFO - --- Step 1: Ratios (EMAXAvg) ---
2025-06-18 20:10:19,229 - INFO - Ratios received:
SPY    1.027370
SHV    1.000019
EFA    1.031605
TLT    0.986334
PFF    1.011879
2025-06-18 20:10:19,229 - INFO - --- Step 2: Ranks ---
2025-06-18 20:10:19,230 - INFO - Ranks received:
  Asset  EMAXAvg_Value  Rank_Ordinal
0   EFA       1.031605             1
1   SPY       1.027370             2
2   PFF       1.011879             3
3   SHV       1.000019             4
4   TLT       0.986334             5
2025-06-18 20:10:19,230 - INFO - --- Step 3: Signal (Allocation) ---
2025-06-18 20:10:19,231 - INFO - Signal received:
SPY    0.4
SHV    0.0
EFA    0.6
TLT    0.0
PFF    0.0
2025-06-18 20:10:19,231 - INFO - --- Processing Backtest Engine for date: 2021-02-01 ---
2025-06-18 20:10:19,231 - INFO - No orders from previous signals to execute for 2021-02-01
2025-06-18 20:10:19,231 - INFO - Generated 2 new orders based on today's signal for 2021-02-01
2025-06-18 20:10:19,232 - INFO - T+0 Execution: Processing 2 orders generated from today's signal.
2025-06-18 20:10:19,232 - INFO - Executed 2 T+0 trades from today's signal for 2021-02-01
2025-06-18 20:10:19,232 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine BEFORE mark_to_market: <class 'pandas.core.series.Series'>
2025-06-18 20:10:19,232 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine BEFORE direct get_weights call: <class 'pandas.core.series.Series'>
2025-06-18 20:10:19,233 - INFO - Final post-trade allocations for 2021-02-01:
EFA     0.599876
SPY     0.400110
Cash    0.000013
2025-06-18 20:10:19,233 - INFO - 
========== Tracing for Date: 2021-02-02 ==========
2025-06-18 20:10:19,233 - INFO - --- Calling Production Model with trace_mode=True ---
2025-06-18 20:10:19,241 - INFO - --- Step 1: Ratios (EMAXAvg) ---
2025-06-18 20:10:19,241 - INFO - Ratios received:
SPY    1.027380
SHV    1.000017
EFA    1.030846
TLT    0.986024
PFF    1.011821
2025-06-18 20:10:19,241 - INFO - --- Step 2: Ranks ---
2025-06-18 20:10:19,243 - INFO - Ranks received:
  Asset  EMAXAvg_Value  Rank_Ordinal
0   EFA       1.030846             1
1   SPY       1.027380             2
2   PFF       1.011821             3
3   SHV       1.000017             4
4   TLT       0.986024             5
2025-06-18 20:10:19,243 - INFO - --- Step 3: Signal (Allocation) ---
2025-06-18 20:10:19,244 - INFO - Signal received:
SPY    0.4
SHV    0.0
EFA    0.6
TLT    0.0
PFF    0.0
2025-06-18 20:10:19,244 - INFO - --- Processing Backtest Engine for date: 2021-02-02 ---
2025-06-18 20:10:19,244 - INFO - No orders from previous signals to execute for 2021-02-02
2025-06-18 20:10:19,244 - INFO - Generated 2 new orders based on today's signal for 2021-02-02
2025-06-18 20:10:19,245 - INFO - T+0 Execution: Processing 2 orders generated from today's signal.
2025-06-18 20:10:19,245 - INFO - Executed 2 T+0 trades from today's signal for 2021-02-02
2025-06-18 20:10:19,245 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine BEFORE mark_to_market: <class 'pandas.core.series.Series'>
2025-06-18 20:10:19,245 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine BEFORE direct get_weights call: <class 'pandas.core.series.Series'>
2025-06-18 20:10:19,246 - INFO - Final post-trade allocations for 2021-02-02:
EFA     0.599985
SPY     0.399974
Cash    0.000041
2025-06-18 20:10:19,246 - INFO - 
========== Tracing for Date: 2021-02-03 ==========
2025-06-18 20:10:19,246 - INFO - --- Calling Production Model with trace_mode=True ---
2025-06-18 20:10:19,254 - INFO - --- Step 1: Ratios (EMAXAvg) ---
2025-06-18 20:10:19,254 - INFO - Ratios received:
SPY    1.027359
SHV    1.000025
EFA    1.030249
TLT    0.985335
PFF    1.011546
2025-06-18 20:10:19,254 - INFO - --- Step 2: Ranks ---
2025-06-18 20:10:19,255 - INFO - Ranks received:
  Asset  EMAXAvg_Value  Rank_Ordinal
0   EFA       1.030249             1
1   SPY       1.027359             2
2   PFF       1.011546             3
3   SHV       1.000025             4
4   TLT       0.985335             5
2025-06-18 20:10:19,256 - INFO - --- Step 3: Signal (Allocation) ---
2025-06-18 20:10:19,256 - INFO - Signal received:
SPY    0.4
SHV    0.0
EFA    0.6
TLT    0.0
PFF    0.0
2025-06-18 20:10:19,257 - INFO - --- Processing Backtest Engine for date: 2021-02-03 ---
2025-06-18 20:10:19,257 - INFO - No orders from previous signals to execute for 2021-02-03
2025-06-18 20:10:19,257 - INFO - Generated 2 new orders based on today's signal for 2021-02-03
2025-06-18 20:10:19,257 - INFO - T+0 Execution: Processing 2 orders generated from today's signal.
2025-06-18 20:10:19,257 - INFO - Executed 2 T+0 trades from today's signal for 2021-02-03
2025-06-18 20:10:19,258 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine BEFORE mark_to_market: <class 'pandas.core.series.Series'>
2025-06-18 20:10:19,258 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine BEFORE direct get_weights call: <class 'pandas.core.series.Series'>
2025-06-18 20:10:19,258 - INFO - Final post-trade allocations for 2021-02-03:
EFA     0.599980
SPY     0.399944
Cash    0.000076
2025-06-18 20:10:19,259 - INFO - 
========== Tracing for Date: 2021-02-04 ==========
2025-06-18 20:10:19,259 - INFO - --- Calling Production Model with trace_mode=True ---
2025-06-18 20:10:19,266 - INFO - --- Step 1: Ratios (EMAXAvg) ---
2025-06-18 20:10:19,267 - INFO - Ratios received:
SPY    1.027886
SHV    1.000027
EFA    1.029716
TLT    0.984642
PFF    1.011421
2025-06-18 20:10:19,267 - INFO - --- Step 2: Ranks ---
2025-06-18 20:10:19,268 - INFO - Ranks received:
  Asset  EMAXAvg_Value  Rank_Ordinal
0   EFA       1.029716             1
1   SPY       1.027886             2
2   PFF       1.011421             3
3   SHV       1.000027             4
4   TLT       0.984642             5
2025-06-18 20:10:19,269 - INFO - --- Step 3: Signal (Allocation) ---
2025-06-18 20:10:19,269 - INFO - Signal received:
SPY    0.4
SHV    0.0
EFA    0.6
TLT    0.0
PFF    0.0
2025-06-18 20:10:19,269 - INFO - --- Processing Backtest Engine for date: 2021-02-04 ---
2025-06-18 20:10:19,269 - INFO - No orders from previous signals to execute for 2021-02-04
2025-06-18 20:10:19,270 - INFO - Generated 2 new orders based on today's signal for 2021-02-04
2025-06-18 20:10:19,270 - INFO - T+0 Execution: Processing 2 orders generated from today's signal.
2025-06-18 20:10:19,270 - INFO - Executed 2 T+0 trades from today's signal for 2021-02-04
2025-06-18 20:10:19,270 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine BEFORE mark_to_market: <class 'pandas.core.series.Series'>
2025-06-18 20:10:19,270 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine BEFORE direct get_weights call: <class 'pandas.core.series.Series'>
2025-06-18 20:10:19,271 - INFO - Final post-trade allocations for 2021-02-04:
EFA     0.599983
SPY     0.399833
Cash    0.000183
2025-06-18 20:10:19,271 - INFO - 
========== Tracing for Date: 2021-02-05 ==========
2025-06-18 20:10:19,271 - INFO - --- Calling Production Model with trace_mode=True ---
2025-06-18 20:10:19,279 - INFO - --- Step 1: Ratios (EMAXAvg) ---
2025-06-18 20:10:19,279 - INFO - Ratios received:
SPY    1.028478
SHV    1.000033
EFA    1.029518
TLT    0.983655
PFF    1.011410
2025-06-18 20:10:19,279 - INFO - --- Step 2: Ranks ---
2025-06-18 20:10:19,281 - INFO - Ranks received:
  Asset  EMAXAvg_Value  Rank_Ordinal
0   EFA       1.029518             1
1   SPY       1.028478             2
2   PFF       1.011410             3
3   SHV       1.000033             4
4   TLT       0.983655             5
2025-06-18 20:10:19,281 - INFO - --- Step 3: Signal (Allocation) ---
2025-06-18 20:10:19,282 - INFO - Signal received:
SPY    0.4
SHV    0.0
EFA    0.6
TLT    0.0
PFF    0.0
2025-06-18 20:10:19,282 - INFO - --- Processing Backtest Engine for date: 2021-02-05 ---
2025-06-18 20:10:19,282 - INFO - No orders from previous signals to execute for 2021-02-05
2025-06-18 20:10:19,282 - INFO - Generated 2 new orders based on today's signal for 2021-02-05
2025-06-18 20:10:19,282 - INFO - T+0 Execution: Processing 2 orders generated from today's signal.
2025-06-18 20:10:19,282 - INFO - Executed 2 T+0 trades from today's signal for 2021-02-05
2025-06-18 20:10:19,283 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine BEFORE mark_to_market: <class 'pandas.core.series.Series'>
2025-06-18 20:10:19,283 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine BEFORE direct get_weights call: <class 'pandas.core.series.Series'>
2025-06-18 20:10:19,283 - INFO - Final post-trade allocations for 2021-02-05:
EFA     0.599991
SPY     0.399661
Cash    0.000348
2025-06-18 20:10:19,284 - INFO - 
========== Tracing for Date: 2021-02-08 ==========
2025-06-18 20:10:19,284 - INFO - --- Calling Production Model with trace_mode=True ---
2025-06-18 20:10:19,292 - INFO - --- Step 1: Ratios (EMAXAvg) ---
2025-06-18 20:10:19,292 - INFO - Ratios received:
SPY    1.029305
SHV    1.000033
EFA    1.029648
TLT    0.983068
PFF    1.011428
2025-06-18 20:10:19,292 - INFO - --- Step 2: Ranks ---
2025-06-18 20:10:19,294 - INFO - Ranks received:
  Asset  EMAXAvg_Value  Rank_Ordinal
0   EFA       1.029648             1
1   SPY       1.029305             2
2   PFF       1.011428             3
3   SHV       1.000033             4
4   TLT       0.983068             5
2025-06-18 20:10:19,294 - INFO - --- Step 3: Signal (Allocation) ---
2025-06-18 20:10:19,295 - INFO - Signal received:
SPY    0.4
SHV    0.0
EFA    0.6
TLT    0.0
PFF    0.0
2025-06-18 20:10:19,295 - INFO - --- Processing Backtest Engine for date: 2021-02-08 ---
2025-06-18 20:10:19,295 - INFO - No orders from previous signals to execute for 2021-02-08
2025-06-18 20:10:19,296 - INFO - Generated 1 new orders based on today's signal for 2021-02-08
2025-06-18 20:10:19,296 - INFO - T+0 Execution: Processing 1 orders generated from today's signal.
2025-06-18 20:10:19,296 - INFO - Executed 1 T+0 trades from today's signal for 2021-02-08
2025-06-18 20:10:19,296 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine BEFORE mark_to_market: <class 'pandas.core.series.Series'>
2025-06-18 20:10:19,296 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine BEFORE direct get_weights call: <class 'pandas.core.series.Series'>
2025-06-18 20:10:19,297 - INFO - Final post-trade allocations for 2021-02-08:
EFA     0.599997
SPY     0.399724
Cash    0.000279
2025-06-18 20:10:19,297 - INFO - 
========== Tracing for Date: 2021-02-09 ==========
2025-06-18 20:10:19,297 - INFO - --- Calling Production Model with trace_mode=True ---
2025-06-18 20:10:19,308 - INFO - --- Step 1: Ratios (EMAXAvg) ---
2025-06-18 20:10:19,308 - INFO - Ratios received:
SPY    1.029898
SHV    1.000039
EFA    1.029979
TLT    0.982640
PFF    1.011457
2025-06-18 20:10:19,308 - INFO - --- Step 2: Ranks ---
2025-06-18 20:10:19,309 - INFO - Ranks received:
  Asset  EMAXAvg_Value  Rank_Ordinal
0   EFA       1.029979             1
1   SPY       1.029898             2
2   PFF       1.011457             3
3   SHV       1.000039             4
4   TLT       0.982640             5
2025-06-18 20:10:19,310 - INFO - --- Step 3: Signal (Allocation) ---
2025-06-18 20:10:19,310 - INFO - Signal received:
SPY    0.4
SHV    0.0
EFA    0.6
TLT    0.0
PFF    0.0
2025-06-18 20:10:19,311 - INFO - --- Processing Backtest Engine for date: 2021-02-09 ---
2025-06-18 20:10:19,311 - INFO - No orders from previous signals to execute for 2021-02-09
2025-06-18 20:10:19,311 - INFO - Generated 2 new orders based on today's signal for 2021-02-09
2025-06-18 20:10:19,311 - INFO - T+0 Execution: Processing 2 orders generated from today's signal.
2025-06-18 20:10:19,311 - INFO - Executed 2 T+0 trades from today's signal for 2021-02-09
2025-06-18 20:10:19,311 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine BEFORE mark_to_market: <class 'pandas.core.series.Series'>
2025-06-18 20:10:19,312 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine BEFORE direct get_weights call: <class 'pandas.core.series.Series'>
2025-06-18 20:10:19,313 - INFO - Final post-trade allocations for 2021-02-09:
EFA     0.599995
SPY     0.399723
Cash    0.000282
2025-06-18 20:10:19,313 - INFO - 
========== Tracing for Date: 2021-02-10 ==========
2025-06-18 20:10:19,313 - INFO - --- Calling Production Model with trace_mode=True ---
2025-06-18 20:10:19,321 - INFO - --- Step 1: Ratios (EMAXAvg) ---
2025-06-18 20:10:19,321 - INFO - Ratios received:
SPY    1.030300
SHV    1.000043
EFA    1.029994
TLT    0.982652
PFF    1.011425
2025-06-18 20:10:19,321 - INFO - --- Step 2: Ranks ---
2025-06-18 20:10:19,322 - INFO - Ranks received:
  Asset  EMAXAvg_Value  Rank_Ordinal
0   SPY       1.030300             1
1   EFA       1.029994             2
2   PFF       1.011425             3
3   SHV       1.000043             4
4   TLT       0.982652             5
2025-06-18 20:10:19,323 - INFO - --- Step 3: Signal (Allocation) ---
2025-06-18 20:10:19,323 - INFO - Signal received:
SPY    0.6
SHV    0.0
EFA    0.4
TLT    0.0
PFF    0.0
2025-06-18 20:10:19,323 - INFO - --- Processing Backtest Engine for date: 2021-02-10 ---
2025-06-18 20:10:19,324 - INFO - No orders from previous signals to execute for 2021-02-10
2025-06-18 20:10:19,324 - INFO - Generated 2 new orders based on today's signal for 2021-02-10
2025-06-18 20:10:19,324 - INFO - T+0 Execution: Processing 2 orders generated from today's signal.
2025-06-18 20:10:19,324 - INFO - Executed 2 T+0 trades from today's signal for 2021-02-10
2025-06-18 20:10:19,324 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine BEFORE mark_to_market: <class 'pandas.core.series.Series'>
2025-06-18 20:10:19,324 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine BEFORE direct get_weights call: <class 'pandas.core.series.Series'>
2025-06-18 20:10:19,325 - INFO - Final post-trade allocations for 2021-02-10:
EFA     0.400105
SPY     0.599139
Cash    0.000756
2025-06-18 20:10:19,325 - INFO - 
========== Tracing for Date: 2021-02-11 ==========
2025-06-18 20:10:19,325 - INFO - --- Calling Production Model with trace_mode=True ---
2025-06-18 20:10:19,334 - INFO - --- Step 1: Ratios (EMAXAvg) ---
2025-06-18 20:10:19,335 - INFO - Ratios received:
SPY    1.030653
SHV    1.000042
EFA    1.030297
TLT    0.982423
PFF    1.011242
2025-06-18 20:10:19,335 - INFO - --- Step 2: Ranks ---
2025-06-18 20:10:19,336 - INFO - Ranks received:
  Asset  EMAXAvg_Value  Rank_Ordinal
0   SPY       1.030653             1
1   EFA       1.030297             2
2   PFF       1.011242             3
3   SHV       1.000042             4
4   TLT       0.982423             5
2025-06-18 20:10:19,336 - INFO - --- Step 3: Signal (Allocation) ---
2025-06-18 20:10:19,337 - INFO - Signal received:
SPY    0.6
SHV    0.0
EFA    0.4
TLT    0.0
PFF    0.0
2025-06-18 20:10:19,337 - INFO - --- Processing Backtest Engine for date: 2021-02-11 ---
2025-06-18 20:10:19,337 - INFO - No orders from previous signals to execute for 2021-02-11
2025-06-18 20:10:19,338 - INFO - Generated 2 new orders based on today's signal for 2021-02-11
2025-06-18 20:10:19,338 - INFO - T+0 Execution: Processing 2 orders generated from today's signal.
2025-06-18 20:10:19,338 - INFO - Executed 2 T+0 trades from today's signal for 2021-02-11
2025-06-18 20:10:19,338 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine BEFORE mark_to_market: <class 'pandas.core.series.Series'>
2025-06-18 20:10:19,338 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine BEFORE direct get_weights call: <class 'pandas.core.series.Series'>
2025-06-18 20:10:19,339 - INFO - Final post-trade allocations for 2021-02-11:
EFA     0.400003
SPY     0.599736
Cash    0.000261
2025-06-18 20:10:19,339 - INFO - 
========== Tracing for Date: 2021-02-12 ==========
2025-06-18 20:10:19,339 - INFO - --- Calling Production Model with trace_mode=True ---
2025-06-18 20:10:19,347 - INFO - --- Step 1: Ratios (EMAXAvg) ---
2025-06-18 20:10:19,348 - INFO - Ratios received:
SPY    1.031144
SHV    1.000041
EFA    1.030778
TLT    0.981632
PFF    1.010905
2025-06-18 20:10:19,348 - INFO - --- Step 2: Ranks ---
2025-06-18 20:10:19,349 - INFO - Ranks received:
  Asset  EMAXAvg_Value  Rank_Ordinal
0   SPY       1.031144             1
1   EFA       1.030778             2
2   PFF       1.010905             3
3   SHV       1.000041             4
4   TLT       0.981632             5
2025-06-18 20:10:19,349 - INFO - --- Step 3: Signal (Allocation) ---
2025-06-18 20:10:19,350 - INFO - Signal received:
SPY    0.6
SHV    0.0
EFA    0.4
TLT    0.0
PFF    0.0
2025-06-18 20:10:19,350 - INFO - --- Processing Backtest Engine for date: 2021-02-12 ---
2025-06-18 20:10:19,350 - INFO - No orders from previous signals to execute for 2021-02-12
2025-06-18 20:10:19,351 - INFO - Generated 2 new orders based on today's signal for 2021-02-12
2025-06-18 20:10:19,351 - INFO - T+0 Execution: Processing 2 orders generated from today's signal.
2025-06-18 20:10:19,351 - INFO - Executed 2 T+0 trades from today's signal for 2021-02-12
2025-06-18 20:10:19,351 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine BEFORE mark_to_market: <class 'pandas.core.series.Series'>
2025-06-18 20:10:19,351 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine BEFORE direct get_weights call: <class 'pandas.core.series.Series'>
2025-06-18 20:10:19,352 - INFO - Final post-trade allocations for 2021-02-12:
EFA     0.400012
SPY     0.599960
Cash    0.000028
2025-06-18 20:10:19,352 - INFO - 
========== Tracing for Date: 2021-02-16 ==========
2025-06-18 20:10:19,352 - INFO - --- Calling Production Model with trace_mode=True ---
2025-06-18 20:10:19,360 - INFO - --- Step 1: Ratios (EMAXAvg) ---
2025-06-18 20:10:19,360 - INFO - Ratios received:
SPY    1.031433
SHV    1.000039
EFA    1.031503
TLT    0.980223
PFF    1.010323
2025-06-18 20:10:19,361 - INFO - --- Step 2: Ranks ---
2025-06-18 20:10:19,362 - INFO - Ranks received:
  Asset  EMAXAvg_Value  Rank_Ordinal
0   EFA       1.031503             1
1   SPY       1.031433             2
2   PFF       1.010323             3
3   SHV       1.000039             4
4   TLT       0.980223             5
2025-06-18 20:10:19,363 - INFO - --- Step 3: Signal (Allocation) ---
2025-06-18 20:10:19,363 - INFO - Signal received:
SPY    0.4
SHV    0.0
EFA    0.6
TLT    0.0
PFF    0.0
2025-06-18 20:10:19,364 - INFO - --- Processing Backtest Engine for date: 2021-02-16 ---
2025-06-18 20:10:19,364 - INFO - No orders from previous signals to execute for 2021-02-16
2025-06-18 20:10:19,364 - INFO - Generated 2 new orders based on today's signal for 2021-02-16
2025-06-18 20:10:19,364 - INFO - T+0 Execution: Processing 2 orders generated from today's signal.
2025-06-18 20:10:19,365 - INFO - Executed 2 T+0 trades from today's signal for 2021-02-16
2025-06-18 20:10:19,365 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine BEFORE mark_to_market: <class 'pandas.core.series.Series'>
2025-06-18 20:10:19,365 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine BEFORE direct get_weights call: <class 'pandas.core.series.Series'>
2025-06-18 20:10:19,366 - INFO - Final post-trade allocations for 2021-02-16:
EFA     0.599244
SPY     0.400219
Cash    0.000538
2025-06-18 20:10:19,366 - INFO - 
========== Tracing for Date: 2021-02-17 ==========
2025-06-18 20:10:19,366 - INFO - --- Calling Production Model with trace_mode=True ---
2025-06-18 20:10:19,374 - INFO - --- Step 1: Ratios (EMAXAvg) ---
2025-06-18 20:10:19,375 - INFO - Ratios received:
SPY    1.031610
SHV    1.000038
EFA    1.031716
TLT    0.979374
PFF    1.009770
2025-06-18 20:10:19,375 - INFO - --- Step 2: Ranks ---
2025-06-18 20:10:19,376 - INFO - Ranks received:
  Asset  EMAXAvg_Value  Rank_Ordinal
0   EFA       1.031716             1
1   SPY       1.031610             2
2   PFF       1.009770             3
3   SHV       1.000038             4
4   TLT       0.979374             5
2025-06-18 20:10:19,377 - INFO - --- Step 3: Signal (Allocation) ---
2025-06-18 20:10:19,377 - INFO - Signal received:
SPY    0.4
SHV    0.0
EFA    0.6
TLT    0.0
PFF    0.0
2025-06-18 20:10:19,378 - INFO - --- Processing Backtest Engine for date: 2021-02-17 ---
2025-06-18 20:10:19,378 - INFO - No orders from previous signals to execute for 2021-02-17
2025-06-18 20:10:19,378 - INFO - Generated 2 new orders based on today's signal for 2021-02-17
2025-06-18 20:10:19,378 - INFO - T+0 Execution: Processing 2 orders generated from today's signal.
2025-06-18 20:10:19,378 - INFO - Executed 2 T+0 trades from today's signal for 2021-02-17
2025-06-18 20:10:19,379 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine BEFORE mark_to_market: <class 'pandas.core.series.Series'>
2025-06-18 20:10:19,379 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine BEFORE direct get_weights call: <class 'pandas.core.series.Series'>
2025-06-18 20:10:19,380 - INFO - Final post-trade allocations for 2021-02-17:
EFA     0.599989
SPY     0.399858
Cash    0.000153
2025-06-18 20:10:19,380 - INFO - 
========== Tracing for Date: 2021-02-18 ==========
2025-06-18 20:10:19,380 - INFO - --- Calling Production Model with trace_mode=True ---
2025-06-18 20:10:19,388 - INFO - --- Step 1: Ratios (EMAXAvg) ---
2025-06-18 20:10:19,389 - INFO - Ratios received:
SPY    1.031444
SHV    1.000038
EFA    1.031542
TLT    0.978488
PFF    1.009272
2025-06-18 20:10:19,389 - INFO - --- Step 2: Ranks ---
2025-06-18 20:10:19,390 - INFO - Ranks received:
  Asset  EMAXAvg_Value  Rank_Ordinal
0   EFA       1.031542             1
1   SPY       1.031444             2
2   PFF       1.009272             3
3   SHV       1.000038             4
4   TLT       0.978488             5
2025-06-18 20:10:19,391 - INFO - --- Step 3: Signal (Allocation) ---
2025-06-18 20:10:19,391 - INFO - Signal received:
SPY    0.4
SHV    0.0
EFA    0.6
TLT    0.0
PFF    0.0
2025-06-18 20:10:19,392 - INFO - --- Processing Backtest Engine for date: 2021-02-18 ---
2025-06-18 20:10:19,392 - INFO - No orders from previous signals to execute for 2021-02-18
2025-06-18 20:10:19,392 - INFO - Generated 1 new orders based on today's signal for 2021-02-18
2025-06-18 20:10:19,392 - INFO - T+0 Execution: Processing 1 orders generated from today's signal.
2025-06-18 20:10:19,392 - INFO - Executed 1 T+0 trades from today's signal for 2021-02-18
2025-06-18 20:10:19,392 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine BEFORE mark_to_market: <class 'pandas.core.series.Series'>
2025-06-18 20:10:19,392 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine BEFORE direct get_weights call: <class 'pandas.core.series.Series'>
2025-06-18 20:10:19,393 - INFO - Final post-trade allocations for 2021-02-18:
EFA     0.599949
SPY     0.400031
Cash    0.000020
2025-06-18 20:10:19,393 - INFO - 
========== Tracing for Date: 2021-02-19 ==========
2025-06-18 20:10:19,394 - INFO - --- Calling Production Model with trace_mode=True ---
2025-06-18 20:10:19,402 - INFO - --- Step 1: Ratios (EMAXAvg) ---
2025-06-18 20:10:19,402 - INFO - Ratios received:
SPY    1.031123
SHV    1.000037
EFA    1.031468
TLT    0.977106
PFF    1.008752
2025-06-18 20:10:19,403 - INFO - --- Step 2: Ranks ---
2025-06-18 20:10:19,404 - INFO - Ranks received:
  Asset  EMAXAvg_Value  Rank_Ordinal
0   EFA       1.031468             1
1   SPY       1.031123             2
2   PFF       1.008752             3
3   SHV       1.000037             4
4   TLT       0.977106             5
2025-06-18 20:10:19,404 - INFO - --- Step 3: Signal (Allocation) ---
2025-06-18 20:10:19,405 - INFO - Signal received:
SPY    0.4
SHV    0.0
EFA    0.6
TLT    0.0
PFF    0.0
2025-06-18 20:10:19,405 - INFO - --- Processing Backtest Engine for date: 2021-02-19 ---
2025-06-18 20:10:19,405 - INFO - No orders from previous signals to execute for 2021-02-19
2025-06-18 20:10:19,406 - INFO - Generated 2 new orders based on today's signal for 2021-02-19
2025-06-18 20:10:19,406 - INFO - T+0 Execution: Processing 2 orders generated from today's signal.
2025-06-18 20:10:19,406 - INFO - Executed 2 T+0 trades from today's signal for 2021-02-19
2025-06-18 20:10:19,406 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine BEFORE mark_to_market: <class 'pandas.core.series.Series'>
2025-06-18 20:10:19,406 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine BEFORE direct get_weights call: <class 'pandas.core.series.Series'>
2025-06-18 20:10:19,407 - INFO - Final post-trade allocations for 2021-02-19:
EFA     0.599994
SPY     0.399643
Cash    0.000364
2025-06-18 20:10:19,407 - INFO - 
========== Tracing for Date: 2021-02-22 ==========
2025-06-18 20:10:19,407 - INFO - --- Calling Production Model with trace_mode=True ---
2025-06-18 20:10:19,415 - INFO - --- Step 1: Ratios (EMAXAvg) ---
2025-06-18 20:10:19,416 - INFO - Ratios received:
SPY    1.030350
SHV    1.000036
EFA    1.031186
TLT    0.975590
PFF    1.008061
2025-06-18 20:10:19,416 - INFO - --- Step 2: Ranks ---
2025-06-18 20:10:19,417 - INFO - Ranks received:
  Asset  EMAXAvg_Value  Rank_Ordinal
0   EFA       1.031186             1
1   SPY       1.030350             2
2   PFF       1.008061             3
3   SHV       1.000036             4
4   TLT       0.975590             5
2025-06-18 20:10:19,417 - INFO - --- Step 3: Signal (Allocation) ---
2025-06-18 20:10:19,418 - INFO - Signal received:
SPY    0.4
SHV    0.0
EFA    0.6
TLT    0.0
PFF    0.0
2025-06-18 20:10:19,418 - INFO - --- Processing Backtest Engine for date: 2021-02-22 ---
2025-06-18 20:10:19,418 - INFO - No orders from previous signals to execute for 2021-02-22
2025-06-18 20:10:19,419 - INFO - Generated 2 new orders based on today's signal for 2021-02-22
2025-06-18 20:10:19,419 - INFO - T+0 Execution: Processing 2 orders generated from today's signal.
2025-06-18 20:10:19,419 - INFO - Executed 2 T+0 trades from today's signal for 2021-02-22
2025-06-18 20:10:19,419 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine BEFORE mark_to_market: <class 'pandas.core.series.Series'>
2025-06-18 20:10:19,419 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine BEFORE direct get_weights call: <class 'pandas.core.series.Series'>
2025-06-18 20:10:19,420 - INFO - Final post-trade allocations for 2021-02-22:
EFA     0.599970
SPY     0.399842
Cash    0.000188
2025-06-18 20:10:19,420 - INFO - 
========== Tracing for Date: 2021-02-23 ==========
2025-06-18 20:10:19,421 - INFO - --- Calling Production Model with trace_mode=True ---
2025-06-18 20:10:19,429 - INFO - --- Step 1: Ratios (EMAXAvg) ---
2025-06-18 20:10:19,429 - INFO - Ratios received:
SPY    1.029675
SHV    1.000035
EFA    1.030913
TLT    0.974197
PFF    1.007406
2025-06-18 20:10:19,430 - INFO - --- Step 2: Ranks ---
2025-06-18 20:10:19,431 - INFO - Ranks received:
  Asset  EMAXAvg_Value  Rank_Ordinal
0   EFA       1.030913             1
1   SPY       1.029675             2
2   PFF       1.007406             3
3   SHV       1.000035             4
4   TLT       0.974197             5
2025-06-18 20:10:19,431 - INFO - --- Step 3: Signal (Allocation) ---
2025-06-18 20:10:19,432 - INFO - Signal received:
SPY    0.4
SHV    0.0
EFA    0.6
TLT    0.0
PFF    0.0
2025-06-18 20:10:19,432 - INFO - --- Processing Backtest Engine for date: 2021-02-23 ---
2025-06-18 20:10:19,432 - INFO - No orders from previous signals to execute for 2021-02-23
2025-06-18 20:10:19,432 - INFO - Generated 1 new orders based on today's signal for 2021-02-23
2025-06-18 20:10:19,433 - INFO - T+0 Execution: Processing 1 orders generated from today's signal.
2025-06-18 20:10:19,433 - INFO - Executed 1 T+0 trades from today's signal for 2021-02-23
2025-06-18 20:10:19,433 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine BEFORE mark_to_market: <class 'pandas.core.series.Series'>
2025-06-18 20:10:19,433 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine BEFORE direct get_weights call: <class 'pandas.core.series.Series'>
2025-06-18 20:10:19,435 - INFO - Final post-trade allocations for 2021-02-23:
EFA     0.599967
SPY     0.399913
Cash    0.000121
2025-06-18 20:10:19,435 - INFO - 
========== Tracing for Date: 2021-02-24 ==========
2025-06-18 20:10:19,435 - INFO - --- Calling Production Model with trace_mode=True ---
2025-06-18 20:10:19,444 - INFO - --- Step 1: Ratios (EMAXAvg) ---
2025-06-18 20:10:19,444 - INFO - Ratios received:
SPY    1.029613
SHV    1.000034
EFA    1.030769
TLT    0.972731
PFF    1.006756
2025-06-18 20:10:19,444 - INFO - --- Step 2: Ranks ---
2025-06-18 20:10:19,446 - INFO - Ranks received:
  Asset  EMAXAvg_Value  Rank_Ordinal
0   EFA       1.030769             1
1   SPY       1.029613             2
2   PFF       1.006756             3
3   SHV       1.000034             4
4   TLT       0.972731             5
2025-06-18 20:10:19,446 - INFO - --- Step 3: Signal (Allocation) ---
2025-06-18 20:10:19,447 - INFO - Signal received:
SPY    0.4
SHV    0.0
EFA    0.6
TLT    0.0
PFF    0.0
2025-06-18 20:10:19,447 - INFO - --- Processing Backtest Engine for date: 2021-02-24 ---
2025-06-18 20:10:19,447 - INFO - No orders from previous signals to execute for 2021-02-24
2025-06-18 20:10:19,448 - INFO - Generated 2 new orders based on today's signal for 2021-02-24
2025-06-18 20:10:19,448 - INFO - T+0 Execution: Processing 2 orders generated from today's signal.
2025-06-18 20:10:19,448 - INFO - Executed 2 T+0 trades from today's signal for 2021-02-24
2025-06-18 20:10:19,448 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine BEFORE mark_to_market: <class 'pandas.core.series.Series'>
2025-06-18 20:10:19,448 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine BEFORE direct get_weights call: <class 'pandas.core.series.Series'>
2025-06-18 20:10:19,449 - INFO - Final post-trade allocations for 2021-02-24:
EFA     0.599968
SPY     0.399965
Cash    0.000067
2025-06-18 20:10:19,449 - INFO - 
========== Tracing for Date: 2021-02-25 ==========
2025-06-18 20:10:19,449 - INFO - --- Calling Production Model with trace_mode=True ---
2025-06-18 20:10:19,461 - INFO - --- Step 1: Ratios (EMAXAvg) ---
2025-06-18 20:10:19,461 - INFO - Ratios received:
SPY    1.028172
SHV    1.000029
EFA    1.029640
TLT    0.970723
PFF    1.005763
2025-06-18 20:10:19,461 - INFO - --- Step 2: Ranks ---
2025-06-18 20:10:19,463 - INFO - Ranks received:
  Asset  EMAXAvg_Value  Rank_Ordinal
0   EFA       1.029640             1
1   SPY       1.028172             2
2   PFF       1.005763             3
3   SHV       1.000029             4
4   TLT       0.970723             5
2025-06-18 20:10:19,463 - INFO - --- Step 3: Signal (Allocation) ---
2025-06-18 20:10:19,464 - INFO - Signal received:
SPY    0.4
SHV    0.0
EFA    0.6
TLT    0.0
PFF    0.0
2025-06-18 20:10:19,464 - INFO - --- Processing Backtest Engine for date: 2021-02-25 ---
2025-06-18 20:10:19,464 - INFO - No orders from previous signals to execute for 2021-02-25
2025-06-18 20:10:19,465 - INFO - Generated 2 new orders based on today's signal for 2021-02-25
2025-06-18 20:10:19,465 - INFO - T+0 Execution: Processing 2 orders generated from today's signal.
2025-06-18 20:10:19,465 - INFO - Executed 2 T+0 trades from today's signal for 2021-02-25
2025-06-18 20:10:19,465 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine BEFORE mark_to_market: <class 'pandas.core.series.Series'>
2025-06-18 20:10:19,465 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine BEFORE direct get_weights call: <class 'pandas.core.series.Series'>
2025-06-18 20:10:19,466 - INFO - Final post-trade allocations for 2021-02-25:
EFA     0.600020
SPY     0.399687
Cash    0.000293
2025-06-18 20:10:19,466 - INFO - 
========== Tracing for Date: 2021-02-26 ==========
2025-06-18 20:10:19,466 - INFO - --- Calling Production Model with trace_mode=True ---
2025-06-18 20:10:19,476 - INFO - --- Step 1: Ratios (EMAXAvg) ---
2025-06-18 20:10:19,476 - INFO - Ratios received:
SPY    1.026591
SHV    1.000028
EFA    1.027841
TLT    0.970668
PFF    1.005204
2025-06-18 20:10:19,476 - INFO - --- Step 2: Ranks ---
2025-06-18 20:10:19,478 - INFO - Ranks received:
  Asset  EMAXAvg_Value  Rank_Ordinal
0   EFA       1.027841             1
1   SPY       1.026591             2
2   PFF       1.005204             3
3   SHV       1.000028             4
4   TLT       0.970668             5
2025-06-18 20:10:19,479 - INFO - --- Step 3: Signal (Allocation) ---
2025-06-18 20:10:19,480 - INFO - Signal received:
SPY    0.4
SHV    0.0
EFA    0.6
TLT    0.0
PFF    0.0
2025-06-18 20:10:19,480 - INFO - --- Processing Backtest Engine for date: 2021-02-26 ---
2025-06-18 20:10:19,480 - INFO - No orders from previous signals to execute for 2021-02-26
2025-06-18 20:10:19,481 - INFO - Generated 2 new orders based on today's signal for 2021-02-26
2025-06-18 20:10:19,481 - INFO - T+0 Execution: Processing 2 orders generated from today's signal.
2025-06-18 20:10:19,482 - INFO - Executed 2 T+0 trades from today's signal for 2021-02-26
2025-06-18 20:10:19,482 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine BEFORE mark_to_market: <class 'pandas.core.series.Series'>
2025-06-18 20:10:19,482 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine BEFORE direct get_weights call: <class 'pandas.core.series.Series'>
2025-06-18 20:10:19,484 - INFO - Final post-trade allocations for 2021-02-26:
EFA     0.599921
SPY     0.400034
Cash    0.000045
2025-06-18 20:10:19,513 - INFO - Full history ratios saved to: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\test_logs\trace_ratios_20210120_20210228_20250618_201018.csv
2025-06-18 20:10:19,525 - INFO - Ranks matrix saved to: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\test_logs\trace_ranks_20210120_20210228_20250618_201018.csv
2025-06-18 20:10:19,529 - INFO - Signal allocations saved to: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\test_logs\trace_signal_allocation_20210120_20210228_20250618_201018.csv
2025-06-18 20:10:19,533 - INFO - Post-trade allocations saved to: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\test_logs\trace_post_trade_allocations_20210120_20210228_20250618_201018.csv
2025-06-18 20:10:19,533 - INFO - Trace script finished.
2025-06-18 20:10:19,533 - INFO - EMA signal breakdown trace finished.
