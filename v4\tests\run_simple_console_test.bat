@echo on
echo =====================================================
echo SIMPLE CONSOLE TEST - %date% %time%
echo =====================================================

REM Set up virtual environment path
set VENV_PATH=F:\AI_Library\my_quant_env

REM Activate virtual environment
echo Activating virtual environment at %VENV_PATH%...
call %VENV_PATH%\Scripts\activate.bat
if %errorlevel% neq 0 (
    echo ERROR: Failed to activate virtual environment! Error code: %errorlevel%
    goto :error
)

echo.
echo Python environment diagnostics:
echo -------------------------------
echo PATH = %PATH%
echo.
where python
echo.
python --version
echo.
echo Testing basic Python print:
python -c "print('Hello from Python!')"

echo.
echo ------------------------------------------------------
echo Running simple console test script...
echo ------------------------------------------------------
echo.

REM Run the test script with ALL output to console
echo.
echo Running: python S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\tests\simple_console_test.py
echo.
python S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\tests\simple_console_test.py
echo.
echo Python script exit code: %errorlevel%
if %errorlevel% neq 0 (
    echo ERROR: Python script failed with exit code: %errorlevel%
    goto :error
)

echo.
echo ------------------------------------------------------
echo Test script execution complete!
echo ------------------------------------------------------

goto :end

:error
echo.
echo ** AN ERROR OCCURRED - See details above **

:end
echo.
echo Press any key to close this window...
pause > nul
