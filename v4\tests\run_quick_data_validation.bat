@echo off
REM ============================================================================
REM run_quick_data_validation.bat - Tiny data-flow sanity check
REM Activates virtual environment and runs quick_data_validation.py
REM ============================================================================

REM Activate virtual environment (path defined in global rules)
set VENV_PATH=F:\AI_Library\my_quant_env
call "%VENV_PATH%\Scripts\activate.bat"

REM Run the validation script with output capture and display output
python "%~dp0quick_data_validation.py" > "%~dp0quick_data_validation_output.txt" 2>&1
type "%~dp0quick_data_validation_output.txt"
REM pause
