"""
v4/utils/run_logger.py

Utility to log batch run information, including timestamps and files created.
This script is intended to be called from a batch file after the main process runs.
"""

import os
import sys
import argparse
from datetime import datetime, timedelta

LOG_FILE_NAME = 'run_history.log'

def log_run_and_output_files(scan_directory, log_directory, time_window_minutes=5):
    """
    Logs the start time of the run and scans a directory for recently created files,
    appending the findings to a central log file.

    Args:
        scan_directory (str): The directory to scan for new files (e.g., the output folder).
        log_directory (str): The directory where the run_history.log will be saved.
        time_window_minutes (int): The lookback window in minutes to find recent files.
    """
    run_timestamp = datetime.now()
    log_file_path = os.path.join(log_directory, LOG_FILE_NAME)

    try:
        # --- Find recently created files ---
        recent_files = []
        time_threshold = run_timestamp - timedelta(minutes=time_window_minutes)

        if not os.path.isdir(scan_directory):
            error_message = f"Error: Scan directory '{scan_directory}' does not exist."
            print(error_message)
            # Still log the run attempt, but with the error
            with open(log_file_path, 'a') as f:
                f.write(f"--- RUN AT: {run_timestamp.strftime('%Y-%m-%d %H:%M:%S')} ---\n")
                f.write(f"ERROR: {error_message}\n")
                f.write("--- END RUN ---\n\n")
            return

        for root, _, files in os.walk(scan_directory):
            for file in files:
                try:
                    file_path = os.path.join(root, file)
                    file_mtime = datetime.fromtimestamp(os.path.getmtime(file_path))
                    if file_mtime > time_threshold:
                        recent_files.append(file_path)
                except FileNotFoundError:
                    # File might be deleted between os.walk and os.path.getmtime
                    continue

        # --- Write to log file ---
        with open(log_file_path, 'a') as f:
            f.write(f"--- RUN AT: {run_timestamp.strftime('%Y-%m-%d %H:%M:%S')} ---\n")
            if recent_files:
                f.write("Files created in this run:\n")
                for file_path in sorted(recent_files):
                    f.write(f"- {file_path}\n")
            else:
                f.write("No new files detected in the last {time_window_minutes} minutes.\n")
            f.write("--- END RUN ---\n\n")
        
        print(f"Successfully logged run to '{log_file_path}'")

    except Exception as e:
        # Also log errors in the logger itself
        error_log_message = f"--- LOGGER ERROR at {run_timestamp.strftime('%Y-%m-%d %H:%M:%S')} ---\n"
        error_log_message += f"Failed to log run details: {e}\n"
        error_log_message += "--- END ERROR ---\n\n"
        with open(log_file_path, 'a') as f:
            f.write(error_log_message)
        print(f"Error in logging script: {e}")
        sys.exit(1)

def main():
    """Main function to parse arguments and run the logger."""
    parser = argparse.ArgumentParser(description='Log batch run details and output files.')
    parser.add_argument('--scan-dir', required=True, help='Directory to scan for newly created files.')
    parser.add_argument('--log-dir', required=True, help='Directory to save the log file.')
    parser.add_argument('--window', type=int, default=5, help='Time window in minutes to check for new files.')

    args = parser.parse_args()

    log_run_and_output_files(
        scan_directory=args.scan_dir,
        log_directory=args.log_dir,
        time_window_minutes=args.window
    )

if __name__ == '__main__':
    main()
