#!/usr/bin/env python
# -*- coding: utf-8 -*-
# fix_optimization_params.py
"""
Simple fix for optimization parameter passing issues.
This module identifies and fixes the parameter handling that causes "all zeros" in optimization loops.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

import pandas as pd
import numpy as np

def run_parameter_override_test():
    """
    Test parameter override behavior that occurs in optimization loops.
    This isolates the exact issue where optimization parameters override module-level params.
    """
    print("=== PARAMETER OVERRIDE TEST ===")
    
    # Create simple test data 
    dates = pd.date_range('2024-01-01', periods=100)
    symbols = ['SPY', 'QQQ', 'EFA']
    np.random.seed(42)
    price_data = pd.DataFrame(
        np.random.randn(100, 3).cumsum(axis=0) + 100,
        index=dates,
        columns=symbols
    )
    
    print(f"Test data shape: {price_data.shape}")
    
    # Test 1: Call with NO override parameters (should work)
    print("\n--- Test 1: NO parameter overrides ---")
    try:
        from v4.models.ema_allocation_model_v4 import ema_allocation_model_updated_single
        result1 = ema_allocation_model_updated_single(price_data, trace_mode=False)
        print(f"SUCCESS: Result type: {type(result1)}")
        if isinstance(result1, dict):
            for date, weights in result1.items():
                total = sum(weights.values())
                print(f"Date: {date}, Total weight: {total:.3f}")
                if total == 0:
                    print("ERROR: Zero weights found!")
                else:
                    print("SUCCESS: Non-zero weights")
                break
    except Exception as e:
        print(f"ERROR in Test 1: {e}")
    
    # Test 2: Call WITH override parameters (optimization scenario - likely fails)
    print("\n--- Test 2: WITH parameter overrides (optimization scenario) ---")
    try:
        result2 = ema_allocation_model_updated_single(
            price_data, 
            trace_mode=False,
            st_lookback=20,  # Override module param
            mt_lookback=60,  # Override module param  
            lt_lookback=120  # Override module param
        )
        print(f"SUCCESS: Result type: {type(result2)}")
        if isinstance(result2, dict):
            for date, weights in result2.items():
                total = sum(weights.values())
                print(f"Date: {date}, Total weight: {total:.3f}")
                if total == 0:
                    print("ERROR: Zero weights found! This is the optimization bug!")
                else:
                    print("SUCCESS: Non-zero weights")
                break
    except Exception as e:
        print(f"ERROR in Test 2: {e}")
        import traceback
        traceback.print_exc()
    
    return True

if __name__ == '__main__':
    run_parameter_override_test()
