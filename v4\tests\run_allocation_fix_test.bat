@echo off
echo =====================================================
echo Allocation History Fix Test - %date% %time%
echo =====================================================

REM Set up virtual environment path
set VENV_PATH=F:\AI_Library\my_quant_env

REM Activate virtual environment
echo Activating virtual environment at %VENV_PATH%...
call %VENV_PATH%\Scripts\activate.bat

REM Set output directory
set OUTPUT_DIR=s:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\tests\fix_test_output
echo Output directory path: %OUTPUT_DIR%
if not exist "%OUTPUT_DIR%" (
    echo Creating output directory...
    mkdir "%OUTPUT_DIR%"
    if exist "%OUTPUT_DIR%" (
        echo Output directory created successfully.
    ) else (
        echo ERROR: Failed to create output directory!
    )
) else (
    echo Output directory already exists.
)

REM Set log file
set LOG_FILE=%OUTPUT_DIR%\fix_test_%date:~-4,4%%date:~-7,2%%date:~-10,2%_%time:~0,2%%time:~3,2%%time:~6,2%.log
set LOG_FILE=%LOG_FILE: =0%

echo Starting allocation history fix test...
echo Results will be saved to: %LOG_FILE%

REM Create a test script that applies the fix and runs a test
echo Creating test script...
set TEST_SCRIPT=%OUTPUT_DIR%\run_fix_test.py

echo import sys, os, logging > %TEST_SCRIPT%
echo from pathlib import Path >> %TEST_SCRIPT%
echo logging.basicConfig(level=logging.INFO, format='%%%(asctime)s - %%%(levelname)s - %%%(message)s') >> %TEST_SCRIPT%
echo logger = logging.getLogger(__name__) >> %TEST_SCRIPT%
echo project_root = Path(__file__).parent.parent.parent.parent >> %TEST_SCRIPT%
echo if str(project_root) not in sys.path: >> %TEST_SCRIPT%
echo     sys.path.append(str(project_root)) >> %TEST_SCRIPT%
echo print("Running allocation history fix test...") >> %TEST_SCRIPT%
echo # Import the fix >> %TEST_SCRIPT%
echo from v4.tests.allocation_history_fix import apply_patch, restore_original >> %TEST_SCRIPT%
echo # Apply the patch >> %TEST_SCRIPT%
echo original_method = apply_patch() >> %TEST_SCRIPT%
echo print("Patch applied successfully") >> %TEST_SCRIPT%
echo # Import and run the test >> %TEST_SCRIPT%
echo print("Running test_backtest_v4.py with the patch...") >> %TEST_SCRIPT%
echo import v4.tests.test_backtest_v4 >> %TEST_SCRIPT%
echo print("Test completed") >> %TEST_SCRIPT%
echo # Restore original method >> %TEST_SCRIPT%
echo restore_original(original_method) >> %TEST_SCRIPT%
echo print("Original method restored") >> %TEST_SCRIPT%

REM Run the test script with output to log file
echo Running test script...
python %TEST_SCRIPT% > "%LOG_FILE%" 2>&1
echo Python script completed with exit code: %errorlevel%

REM Display log file contents
echo.
echo Log file contents:
echo ==================
if exist "%LOG_FILE%" (
    type "%LOG_FILE%"
) else (
    echo ERROR: Log file not created!
)

REM Check for success/failure in the log file
echo.
echo Checking for success/failure in the log...
findstr /C:"weights_history differs from signal_history" "%LOG_FILE%" > nul
if %errorlevel% equ 0 (
    echo SUCCESS: Allocation history is properly preserved!
) else (
    echo WARNING: Could not confirm allocation history preservation.
)

REM Display output directory contents
echo.
echo Output directory contents:
dir "%OUTPUT_DIR%"

echo.

REM Deactivate virtual environment
echo Deactivating virtual environment...
call %VENV_PATH%\Scripts\deactivate.bat

pause
