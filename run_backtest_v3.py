"""
V3 Backtest Runner

This script runs a backtest using the V3 parameter system and generates reports.
It integrates the V3 parameter system with the existing backtest engine and
uses the V3 reporting system to generate reports.
"""

import os
import sys
import logging
import datetime
from typing import Dict, Any, Optional

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Add project root to path if needed
project_root = os.path.abspath(os.path.dirname(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# Import V3 modules
from v3_engine.parameter_registry import get_registry
from v3_engine.gui_parameter_manager import GuiParameterManager
from v3_engine import reporting_parameters  # Import reporting parameters to ensure they're registered
from config.config_v3 import config_v3  # Import config_v3 for data parameters

# Import V3 reporting modules
from v3_reporting.v3_performance_report import generate_v3_performance_report
from v3_reporting.v3_allocation_report import generate_v3_allocation_report
from v3_reporting.v3_visualization import generate_v3_performance_charts

# Import backtest engine
from engine.backtest import BacktestEngine

def run_v3_backtest(parameters: Optional[Dict[str, Any]] = None):
    """
    Run a backtest using V3 parameters and generate reports.
    
    Args:
        parameters: Optional dictionary of parameters to use.
                   If None, parameters will be loaded from the registry.
    
    Returns:
        Dict: Backtest results
    """
    # Get parameters from registry if not provided
    registry = get_registry()
    if parameters is None:
        # Get parameters from registry
        parameters = {}
        for group in registry.get_groups():
            group_params = registry.get_parameter_values(group)
            parameters.update(group_params)
        
        logger.info(f"Using parameters from registry: {len(parameters)} parameters")
    
    # Special handling for execution_delay parameter to ensure proper type handling
    if 'execution_delay' in parameters:
        execution_delay = parameters['execution_delay']
        # Check if it's an optimization tuple ('Y', default, min, max, step)
        if isinstance(execution_delay, tuple) and len(execution_delay) >= 2:
            # Use the default value (second element) for the actual run
            parameters['execution_delay'] = execution_delay[1]  # Default value
            logger.info(f"Using execution_delay default value {execution_delay[1]} from optimization tuple")
        else:
            # Ensure it's an integer for the engine
            parameters['execution_delay'] = int(parameters['execution_delay'])
            logger.info(f"Using execution_delay: {parameters['execution_delay']}")
    else:
        logger.warning("execution_delay parameter not found, using engine default")
    
    # Use core output directory
    output_dir = os.path.join('output')
    os.makedirs(output_dir, exist_ok=True)
    
    # Generate timestamp for filenames
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # Create a config dictionary for data loading
    data_config = {
        'data_params': {
            'tickers': parameters.get('tickers', config_v3['data_params']['tickers']),
            'start_date': parameters.get('start_date', config_v3['data_params']['start_date']),
            'end_date': parameters.get('end_date', config_v3['data_params']['end_date']),
            'price_field': parameters.get('price_field', config_v3['data_params']['price_field']),
            'risk_free_ticker': parameters.get('risk_free_ticker', config_v3['data_params']['risk_free_ticker']),
            'data_storage_mode': parameters.get('data_storage_mode', config_v3['data_params']['data_storage_mode'])
        },
        'backtest_params': {
            'benchmark': parameters.get('benchmark_ticker', 'SPY')
        }
    }
    
    # Log the data configuration
    logger.info(f"Data config: {data_config['data_params']}")
    logger.info(f"Using data_storage_mode: {data_config['data_params']['data_storage_mode']}")
    
    # Load data using the data config
    from data.data_loader import load_data_for_backtest
    data_dict = load_data_for_backtest(data_config)
    price_data = data_dict['price_data']
    
    # Run backtest
    logger.info("Running backtest with V3 parameters")
    engine = BacktestEngine()
    
    # Create an EMA V3 adapter for signal generation
    from v3_engine.ema_v3_adapter import create_ema_v3_adapter
    ema_adapter = create_ema_v3_adapter(registry)
    
    # Run the backtest with the loaded data and EMA adapter
    results = engine.run_backtest(
        price_data=price_data,
        signal_generator=ema_adapter.generate_signal,
        rebalance_freq=parameters.get('rebalance_frequency', 'weekly'),
        execution_delay=parameters.get('execution_delay', 1)
    )
    
    # Generate performance report
    logger.info("Generating performance report")
    output_path = os.path.join(output_dir, f"{parameters.get('strategy', 'ema')}_performance_tables_{timestamp}.xlsx")
    
    performance_report_path = generate_v3_performance_report(
        backtest_results=results,
        output_path=output_path,
        strategy_name=parameters.get('strategy', 'ema'),
        parameters=parameters
    )
    
    logger.info(f"Performance report generated: {performance_report_path}")
    
    # Generate allocation report if signal history is available
    # Add detailed logging to diagnose signal_history issues
    if 'signal_history' not in results:
        logger.error("CRITICAL ERROR: signal_history missing from backtest results")
        logger.error("Available keys in results: " + ", ".join(results.keys()))
        logger.error("This indicates a problem in the backtest engine's _calculate_results method")
        logger.error("Check engine/backtest.py line ~519 where signal_history should be added to results")
        signal_history = None
    elif results['signal_history'] is None:
        logger.error("CRITICAL ERROR: signal_history is None in backtest results")
        logger.error("This indicates signal_history was created but set to None somewhere")
        logger.error("Check engine/backtest.py for where signal_history is initialized and updated")
        signal_history = None
    elif hasattr(results['signal_history'], 'empty') and results['signal_history'].empty:
        logger.error("CRITICAL ERROR: signal_history is empty (DataFrame exists but has no data)")
        logger.error("This indicates signal_history DataFrame was created but not populated")
        logger.error("Check engine/backtest.py line ~232-233 where signal_history should be updated")
        signal_history = None
    else:
        # Signal history exists and has data
        signal_history = results['signal_history']
        logger.info(f"Signal history found with shape: {signal_history.shape}")
        logger.info(f"Signal history columns: {signal_history.columns.tolist()}")
        logger.info(f"Signal history index range: {signal_history.index[0]} to {signal_history.index[-1]}")
        
        # Log a sample of the signal history for debugging
        if not signal_history.empty:
            logger.info("Sample of signal_history (first 3 rows):")
            sample_rows = min(3, len(signal_history))
            for i in range(sample_rows):
                idx = signal_history.index[i]
                values = ', '.join([f"{col}: {signal_history.loc[idx, col]:.4f}" for col in signal_history.columns])
                logger.info(f"  {idx}: {values}")
        
        # Check for NaN values which might cause problems
        nan_count = signal_history.isna().sum().sum()
        if nan_count > 0:
            logger.warning(f"WARNING: Signal history contains {nan_count} NaN values")
            logger.warning("This may cause problems in allocation reporting")
            
            # Show which columns/dates have NaNs
            nan_cols = signal_history.columns[signal_history.isna().any()].tolist()
            if nan_cols:
                logger.warning(f"Columns with NaNs: {nan_cols}")
                
            # Don't automatically fix - report the issue
            logger.warning("Fix the root cause in the backtest engine rather than applying a bandaid here")
        
    if signal_history is not None:
        # Add any allocation-specific parameters from the registry
        allocation_params = {}
        try:
            # Get chart formatting parameters
            registry = get_registry()
            allocation_params['chart_format'] = registry.get_parameter('chart_format', 'visualization').value
            allocation_params['chart_dpi'] = registry.get_parameter('chart_dpi', 'visualization').value
        except Exception as e:
            logger.warning(f"Could not get visualization parameters from registry: {e}")
        
        # Generate the allocation report
        allocation_report_path = generate_v3_allocation_report(
            signal_history=signal_history,
            output_dir=output_dir,
            ticker_group=parameters.get('ticker_group', 'default'),
            weights_df=results.get('weights_history'),
            **allocation_params
        )
        
        logger.info(f"Allocation report generated: {allocation_report_path}")
    
    # Generate charts
    logger.info("Generating performance charts")
    chart_paths = generate_v3_performance_charts(
        backtest_results=results,
        output_dir=output_dir,
        strategy_name=parameters.get('strategy', 'ema')
    )
    
    logger.info(f"Generated {len(chart_paths)} performance charts")
    
    # Return backtest results
    return results

if __name__ == "__main__":
    # Import reporting parameters to ensure they're registered
    import v3_engine.reporting_parameters
    
    # Run backtest
    results = run_v3_backtest()
    
    logger.info("V3 backtest completed successfully")
