# v4/utils/tracing_utils.py
"""
Utility functions for CSV tracing during backtest runs.

This module provides helpers to create timestamped output directories
and save pandas DataFrames to CSV files for debugging and verification.
"""

from datetime import datetime
from pathlib import Path
import pandas as pd
import logging

# Import centralized path configuration
from v4.config.paths_v4 import PROJECT_ROOT, V4_TRACE_OUTPUTS_DIR

logger = logging.getLogger(__name__)

# Stores the path to the specific directory for the current backtest/script run.
_run_specific_trace_dir: Path = None

def get_project_root() -> Path:
    """Returns the project root directory from centralized configuration."""
    return PROJECT_ROOT

def reset_trace_directory_for_run():
    """Resets the run-specific trace directory path.
    Call this if a new, distinct run needs its own new timestamped folder 
    within the same overall script execution (e.g., in loops or sequential tests).
    """
    global _run_specific_trace_dir
    _run_specific_trace_dir = None
    logger.debug("Run-specific trace directory reset.")

def setup_trace_directory(base_dir_name: str = None, sub_dir_prefix: str = "") -> Path:
    """Sets up and returns the path to a run-specific trace directory.

    If a run-specific directory has already been set up for the current execution context
    (and not reset by `reset_trace_directory_for_run`), this function returns that existing path.
    Otherwise, it creates a new timestamped subdirectory.

    Args:
        base_dir_name (str): The name of the main directory to store all trace outputs.
        sub_dir_prefix (str): A prefix for the timestamped subdirectory name.

    Returns:
        Path: The absolute path to the run-specific trace directory.
    """
    global _run_specific_trace_dir
    if _run_specific_trace_dir is not None:
        return _run_specific_trace_dir

    # Use centralized trace directory configuration
    if base_dir_name is None:
        main_trace_dir = V4_TRACE_OUTPUTS_DIR
    else:
        project_root_path = get_project_root()
        main_trace_dir = project_root_path / base_dir_name
    main_trace_dir.mkdir(parents=True, exist_ok=True)

    # No subdirectory, always use the flat v4_trace_outputs directory
    _run_specific_trace_dir = main_trace_dir
    logger.info(f"Trace directory for this run: {_run_specific_trace_dir}")
    print(f"INFO: Trace directory for this run: {_run_specific_trace_dir}")
    return _run_specific_trace_dir

def save_df_to_trace_dir(df: pd.DataFrame, filename: str, trace_dir_override: Path = None, step_description: str = ""):
    """Saves a DataFrame to a CSV file in the appropriate trace directory.

    Args:
        df (pd.DataFrame): The DataFrame to save.
        filename (str): The name for the CSV file (e.g., '01_initial_data.csv').
        trace_dir_override (Path, optional): Explicitly specify a directory to save to.
                                             If None, uses the run-specific directory.
        step_description (str, optional): A description of the data being saved, for logging.
    """
    if df is None:
        logger.warning(f"DataFrame for '{filename}' ('{step_description}') is None. Skipping save.")
        print(f"WARNING TRACE: DataFrame for '{filename}' ('{step_description}') is None. Skipping save.")
        return
    
    # CHECK FOR OPTIMIZATION MODE - Suppress CSV generation during optimization
    import os
    optimization_active = os.environ.get('CPS_V4_OPTIMIZATION_ACTIVE', '').lower() == 'true'
    if optimization_active:
        logger.info(f"CSV generation suppressed during optimization: '{filename}' ('{step_description}')")
        return

    target_dir = trace_dir_override
    if target_dir is None:
        if _run_specific_trace_dir is None:
            # Attempt to set up the default directory if not already done.
            setup_trace_directory()
        target_dir = _run_specific_trace_dir
    
    if target_dir is None:
        logger.error(f"Could not establish a trace directory. Skipping CSV save for '{filename}' ('{step_description}').")
        print(f"ERROR TRACE: Could not establish trace directory for '{filename}' ('{step_description}').")
        return

    # Add timestamp if not present in filename
    import re
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    if not re.search(r'\d{8}_\d{6}', filename):
        if filename.lower().endswith('.csv'):
            filename = filename[:-4] + f'_{timestamp}.csv'
        else:
            filename = filename + f'_{timestamp}.csv'
    file_path = target_dir / filename
    try:
        df.to_csv(file_path)
        logger.info(f"Saved {step_description} to {file_path}")
        print(f"TRACE: Saved {step_description} to {file_path}")
    except Exception as e:
        logger.error(f"Failed to save {step_description} to {file_path}: {e}")
        print(f"ERROR TRACE: Failed to save {step_description} to {file_path}: {e}")

if __name__ == '__main__':
    # Example usage / simple test
    print(f"Project root determined as: {get_project_root()}")
    
    reset_trace_directory_for_run()
    run_dir1 = setup_trace_directory(sub_dir_prefix="test1_")
    print(f"Run 1 directory: {run_dir1}")
    sample_df1 = pd.DataFrame({'A': [1, 2], 'B': [3, 4]})
    save_df_to_trace_dir(sample_df1, "sample1.csv", step_description="Sample DF1")
    save_df_to_trace_dir(sample_df1, "sample1_override.csv", trace_dir_override=run_dir1 / "subfolder", step_description="Sample DF1 in subfolder") # Test override with subfolder creation
    (run_dir1 / "subfolder").mkdir(exist_ok=True) # Manual mkdir for subfolder if save_df doesn't create it.
    save_df_to_trace_dir(sample_df1, "sample1_override.csv", trace_dir_override=run_dir1 / "subfolder", step_description="Sample DF1 in subfolder")


    reset_trace_directory_for_run()
    run_dir2 = setup_trace_directory(sub_dir_prefix="test2_")
    print(f"Run 2 directory: {run_dir2}")
    sample_df2 = pd.DataFrame({'X': [5, 6], 'Y': [7, 8]})
    save_df_to_trace_dir(sample_df2, "sample2.csv", step_description="Sample DF2")

    print("Tracing utility example finished. Check 'v4_trace_outputs' directory.")
