@echo off
setlocal enabledelayedexpansion

REM ============================================
REM Enhanced Verification Script with Logging
REM ============================================

REM Set timestamp for log files
set TIMESTAMP=%DATE:~-4%%DATE:~-7,2%%DATE:~-10,2%_%TIME:~0,2%%TIME:~3,2%%TIME:~6,2%
set LOG_FILE=verification_%TIMESTAMP%.log

REM Create logs directory if it doesn't exist
if not exist "logs" mkdir logs

REM Function to log messages
:log
    echo [%TIME%] %* >> "logs\%LOG_FILE%"
    echo [%TIME%] %*
    goto :eof

REM Start logging
call :log "=== Starting Verification Process ==="
call :log "Timestamp: %DATE% %TIME%"

REM Check Python version
python --version >nul 2>&1
if %ERRORLEVEL% neq 0 (
    call :log "ERROR: Python not found in PATH"
    exit /b 1
)

REM Check if virtual environment exists
if not exist "F:\AI_Library\my_quant_env\Scripts\python.exe" (
    call :log "ERROR: Virtual environment not found at F:\AI_Library\my_quant_env"
    exit /b 1
)

REM Activate virtual environment
call :log "Activating virtual environment..."
call "F:\AI_Library\my_quant_env\Scripts\activate.bat"
if %ERRORLEVEL% neq 0 (
    call :log "ERROR: Failed to activate virtual environment"
    exit /b 1
)

REM Set environment variables
set PYTHONPATH=%CD%;%CD%\tests;%CD%\v3_engine;%CD%\v3_reporting
set BACKTEST_LOG_LEVEL=DEBUG

REM Run the verification script with output to both console and log
call :log "Running verification script..."
python -c "import sys; print('Python version:', sys.version); print('Python paths:', sys.path)" 2>&1 | tee -a "logs\%LOG_FILE%"

python tests/verify_signal_history_reporting.py 2>&1 | tee -a "logs\%LOG_FILE%"

REM Check the result of the verification
if %ERRORLEVEL% equ 0 (
    call :log "Verification completed successfully!"
) else (
    call :log "Verification failed with errors. Check logs\%LOG_FILE% for details."
)

call :log "=== Verification Process Completed ==="

REM Keep the window open to see the results
pause
endlocal
