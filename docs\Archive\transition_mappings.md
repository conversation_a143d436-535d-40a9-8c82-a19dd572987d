# Transition Mappings

## V3 Reporting System Transition (2025-04-28)

| Old Component                  | New Component                     | Migration Notes |
|--------------------------------|-----------------------------------|-----------------|
| run_v3_backtest_with_metrics.py | v3_reporting/v3_performance_report.py | Core reporting functionality |
| (legacy reporting functions)     | v3_reporting/v3_allocation_report.py  | Allocation-specific reporting |
| (legacy visualization)          | v3_reporting/v3_visualization.py      | Chart generation |

## Reporting Component Transitions

| Old Component                  | New Component                     | Migration Notes |
|--------------------------------|-----------------------------------|-----------------|
| run_v3_backtest_with_metrics.py | v3_reporting/v3_performance_report.py | - New module handles all performance metrics<br>- Supports same Excel output format<br>- Added parameter validation |
| legacy_report_allocations()     | v3_reporting/v3_allocation_report.py  | - Improved allocation calculations<br>- Better visualization integration<br>- Supports multi-period analysis |
| generate_charts()              | v3_reporting/v3_visualization.py      | - Unified chart generation<br>- Added interactive options<br>- Better performance with large datasets |

## Key Changes
1. Reporting functions now modularized
2. Direct integration with V3 parameter system
3. Better separation of concerns
4. All new components support parameter optimization tracking

## Key Benefits

- **Modular Design**: Each report type is now independently maintainable
- **Standardized Interfaces**: Consistent parameter passing between components
- **Improved Documentation**: Each module has complete usage examples
- **Deprecation Warnings**: Old components show clear migration paths

## Required Updates
- GUI actions updated to use new modules
- All backtest calls should use new reporting modules
- Old backup files moved to temp_backups/
