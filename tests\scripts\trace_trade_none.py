"""
Trace script to diagnose execution_delay parameter handling issues between GUI and backtest engine.
Logs parameter flow and type conversions across component boundaries.
"""
import os
import csv
import inspect
import traceback
import datetime
from engine.backtest import BacktestEngine, get_param_value
from engine.orders import Trade

# Setup log directory and file
log_dir = r'S:/Dropbox/Scott Only Internal/Quant_Python_24/Backtest_FinAsset_Alloc_Template/logs'
os.makedirs(log_dir, exist_ok=True)
log_file = os.path.join(log_dir, 'execution_delay_trace.csv')

# Prepare CSV file
with open(log_file, 'w', newline='') as f:
    writer = csv.writer(f)
    writer.writerow(['timestamp', 'event_type', 'location', 'execution_delay_value', 'execution_delay_type', 'context', 'stack_trace'])

def log_event(event_type, location, execution_delay_value, context='', include_stack=False):
    """Log a parameter tracing event to the CSV file"""
    with open(log_file, 'a', newline='') as f:
        writer = csv.writer(f)
        stack_trace = ''.join(traceback.format_stack()) if include_stack else ''
        writer.writerow([
            datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f'),
            event_type,
            location,
            str(execution_delay_value),
            str(type(execution_delay_value)),
            context,
            stack_trace
        ])

# Patch get_param_value to trace execution_delay handling
orig_get_param_value = get_param_value
def traced_get_param_value(param_value):
    result = orig_get_param_value(param_value)
    caller_frame = inspect.currentframe().f_back
    caller_info = f"{caller_frame.f_code.co_filename}:{caller_frame.f_lineno}"
    
    # Trace all parameter conversions, but mark execution_delay specially
    if isinstance(param_value, tuple) and len(param_value) == 5 and param_value[0] in ['Y', 'N']:
        log_event(
            'parameter_conversion', 
            caller_info,
            param_value,
            f"Input tuple: {param_value}, Output: {result}",
            include_stack=True
        )
    
    return result

# Replace the function
get_param_value.__globals__['get_param_value'] = traced_get_param_value

# Patch BacktestEngine.run_backtest to trace execution_delay handling
orig_run_backtest = BacktestEngine.run_backtest
def traced_run_backtest(self, price_data, signal_generator, rebalance_freq='monthly', execution_delay=0, **signal_params):
    # Log before any processing
    log_event(
        'run_backtest_entry',
        'BacktestEngine.run_backtest',
        execution_delay,
        f"raw value at entry, rebalance_freq={rebalance_freq}",
        include_stack=True
    )
    
    # Call original method
    return orig_run_backtest(self, price_data, signal_generator, rebalance_freq, execution_delay, **signal_params)

# Replace the method
BacktestEngine.run_backtest = traced_run_backtest

# Also trace Trade creation to see if any None values appear during execution
orig_trade_init = Trade.__init__
def traced_trade_init(self, order, execution_date, execution_price, commission, amount):
    if execution_price is None or commission is None:
        log_event(
            'TRADE_WITH_NONE',
            'Trade.__init__',
            None,
            f"order={order}, execution_date={execution_date}, execution_price={execution_price}, commission={commission}, amount={amount}",
            include_stack=True
        )
    orig_trade_init(self, order, execution_date, execution_price, commission, amount)

# Replace the method
Trade.__init__ = traced_trade_init

# Now run the standard backtest (customize as needed)
if __name__ == '__main__':
    # Run the backtest with parameter tracing enabled
    import run_backtest_v2_with_metrics as runner
    from config.config_v2 import config_v2 as config
    
    # Update config to test optimization mode for execution_delay
    if isinstance(config['backtest_params']['execution_delay'], int):
        # Convert to optimization tuple format to test that path
        config['backtest_params']['execution_delay'] = ('Y', 1, 0, 5, 1)
        print(f"Set execution_delay to optimization mode: {config['backtest_params']['execution_delay']}")
    
    # Run the backtest
    print("Running backtest with traced execution_delay parameter...")
    runner.run_backtest_with_metrics(config)
    print(f"Trace complete. See {log_file}")
