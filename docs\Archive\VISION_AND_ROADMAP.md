# Vision & Roadmap for Flexible Backtesting Framework

## 1. Objectives & Principles

- **Strategic Guidance**: Build one capability at a time with clear vision; avoid aimless infrastructure work.
- **Docs-First**: Draft high-level spec before coding; update docs alongside implementation.
- **Tests-As-You-Go**: Write smoke tests for each new feature upon completion.
- **Modular Design**: Keep modules under 500 lines; mix grouping by feature and by function type (e.g. `reporting/`, `visualization/`, `engine/`).
- **Plug-and-Play Configuration**: Enable rapid swapping of variables and parameters via centralized, well-documented config.

## 2. Architecture Overview

```text
Backtest_FinAsset_Alloc_Template/
├── engine/                # Core backtest engine (signal, allocation, execution, portfolio)
├── models/                # Strategy implementations & registry
├── config/                # All configuration files (paths, params, reporting)
├── reporting/             # Excel/graph reporting utilities
├── visualization/         # Standalone plotting modules (optional for web dashboards)
├── docs/                  # Documentation & guides
│   ├── README.md
│   └── VISION_AND_ROADMAP.md
├── tests/                 # Smoke tests & feature tests
├── run_backtest_v2.py     # CLI/runner script
└── output/                # Generated reports and plots
```

## 3. Plug-and-Play Configuration Strategy

- **Central Config**: `config/config_v2.py` holds all `data_params`, `backtest_params`, `strategy_params`, `reporting_params`, and `visualization_params`.
- **Parameter Definitions**: Use `define_parameter(optimize, default, min, max, step)` for any tunable setting; fallback to raw values for fixed settings.
- **Single Source**: No hardcoded values in modules; import from `config_v2` via `BackwardCompatDict`.
- **Dynamic Loading**: Strategy registry loads modules by name (`models/strategy_registry.py`); new strategies plug in without engine changes.

## 4. Development Workflow

1. **Draft Spec** (docs-first): Write feature overview, API contract, config keys, and tests plan in docs.
2. **Scaffold**: Create module file(s), function/class signatures, and empty docstrings/tests.
3. **Implement**: Fill in logic; hook into runner/CLI; integrate config defaults.
4. **Document**: Update `docs/README.md` and inline docstrings to reflect actual implementation.
5. **Test**: Add smoke test in `tests/` to verify outputs (XLSX sheets exist, plots saved, parameter row present).
6. **Review & Iterate**: Confirm feature matches vision; adjust docs or code as needed.

## 5. Next Steps

- **Feature 1**: Allocation Report (signal & allocation list) – spec drafted, code scaffolded.
- **Feature 2**: Hook into CLI – enable `--report-allocation` flag and config support.
- **Feature 3**: Add performance loop only for EMA performance; all other reports remain base.
- **Feature 4**: Extend strategy registry to support multiple models dynamically.
- **Feature 5**: Automate CI with smoke tests and coverage checks.

*Use this document as the north star: before any new feature, revisit Vision & Roadmap to ensure alignment.*
