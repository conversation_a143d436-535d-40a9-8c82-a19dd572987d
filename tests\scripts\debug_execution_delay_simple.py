"""
Simple debug script to diagnose execution_delay parameter handling issues.
"""

import sys
import os
import logging
from pathlib import Path
from datetime import datetime
from config.paths import OUTPUT_DIR

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Define log file
log_file = OUTPUT_DIR / "debug" / f"execution_delay_simple_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
os.makedirs(log_file.parent, exist_ok=True)

logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Import necessary modules
from config.config_v2 import config_v2, _get_param_value
from config.local_parameter_optimization import define_parameter, validate_parameter, get_parameter_range

# Log the start of the test
logger.info("Starting execution_delay parameter debug test")
logger.info(f"Python version: {sys.version}")

# Test 1: Check raw parameter in config
try:
    raw_exec_delay = config_v2['backtest_params'].get('execution_delay')
    logger.info(f"Raw execution_delay from config: {raw_exec_delay} (type: {type(raw_exec_delay)})")
except Exception as e:
    logger.error(f"Error accessing config: {e}")

# Test 2: Try to reproduce the error
try:
    # Create a parameter tuple with optimization enabled (Y)
    optimizable_param = ('Y', 1, 0, 5, 1)
    logger.info(f"Created optimizable parameter: {optimizable_param}")
    
    # Try to use isinstance() with this parameter
    # This is where the error occurs in the backtest engine
    from datetime import date
    logger.info("Testing isinstance() with parameter tuple...")
    
    # The error occurs in a line like this:
    if isinstance(optimizable_param, date):
        logger.info("Parameter is a date (this should not happen)")
    else:
        logger.info("Parameter is not a date (expected)")
    
    logger.info("No error occurred with isinstance() check")
except Exception as e:
    logger.error(f"Error in isinstance() check: {e}")
    import traceback
    logger.error(f"Traceback: {traceback.format_exc()}")

# Test 3: Check the BacktestEngine.run_backtest method
try:
    from engine.backtest import BacktestEngine
    logger.info("Successfully imported BacktestEngine")
    
    # Create an instance
    engine = BacktestEngine()
    logger.info("Successfully created BacktestEngine instance")
    
    # Check the run_backtest method signature
    import inspect
    run_backtest_sig = inspect.signature(engine.run_backtest)
    logger.info(f"run_backtest signature: {run_backtest_sig}")
    
    # Check the docstring position
    run_backtest_source = inspect.getsource(engine.run_backtest)
    logger.info(f"run_backtest source code first 200 chars: {run_backtest_source[:200]}")
    
    # Check if get_param_value is called before or after docstring
    if "get_param_value" in run_backtest_source.split('"""')[0]:
        logger.info("get_param_value is called BEFORE docstring (potential issue)")
    else:
        logger.info("get_param_value is called AFTER docstring (correct)")
    
except Exception as e:
    logger.error(f"Error inspecting BacktestEngine: {e}")
    import traceback
    logger.error(f"Traceback: {traceback.format_exc()}")

logger.info(f"Debug information saved to: {log_file}")
print(f"Debug information saved to: {log_file}")
