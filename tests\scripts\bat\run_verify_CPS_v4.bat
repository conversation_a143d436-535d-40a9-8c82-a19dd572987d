@echo off
REM run_verify_CPS_v4.bat
REM Batch file to run production validation for CPS v4
REM Author: AI Assistant
REM Date: 2025-06-06

echo Running CPS v4 production validation...

REM Activate the virtual environment
call "F:\\AI_Library\\my_quant_env\\Scripts\\activate.bat"

REM Set environment to use production data
set USE_PRODUCTION_DATA=1
set SKIP_MOCK_DATA=1

REM Run the production validation
python CPS_v4\V4_transition\verify_CPS_v4.py --validate-production --verbose

REM Check the exit code
if %ERRORLEVEL% EQU 0 (
    echo.
    echo Production validation passed successfully!
) else (
    echo.
    echo Production validation failed. Please check the output above for details.
)
