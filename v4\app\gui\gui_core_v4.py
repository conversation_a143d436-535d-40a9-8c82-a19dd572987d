# filename: app/gui/gui_core_v4.py
"""
CPS_v4 Parameter System - GUI Core Components

Core components for the CPS_v4 GUI system, including the main window
and basic application setup. This module integrates with the global CPS_v4 settings.
"""

import sys
import os
import logging
from PySide6.QtWidgets import (
    QApplication,
    QMainWindow,
    QWidget,
    QLabel,
    QCheckBox,
    QPushButton,
    QMessageBox,
    QHBoxLayout,
    QVBoxLayout,
    QScrollArea,
    QGroupBox,
    QSpinBox,
    QDoubleSpinBox,
    QLineEdit,
    QComboBox
)
from PySide6.QtCore import Qt

# Add project root to path to ensure imports work properly
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# Import CPS_v4 settings system
try:
    from CPS_v4.settings_CPS_v4 import load_settings, settings
except ImportError as e:
    logging.critical(f"CRITICAL ERROR: Could not import settings_CPS_v4: {e}")
    # Attempt to provide a more helpful message if the file is missing or path is wrong
    try:
        import config
        logging.critical(f"'config' module found at {config.__file__}")
        settings_file_path = os.path.join(os.path.dirname(config.__file__), 'settings_CPS_v4.py')
        logging.critical(f"Expected settings_CPS_v4.py at: {settings_file_path}")
        if not os.path.exists(settings_file_path):
            logging.critical(f"File NOT FOUND at expected path: {settings_file_path}")
    except ImportError:
        logging.critical("'config' package not found in sys.path. Ensure project structure is correct.")
    sys.exit(f"Failed to import settings_CPS_v4. Application cannot start. {e}")


# Helper to convert string to appropriate type
def _parse_value(value_str, target_type):
    """
    Parses a string value to a target type.
    Handles basic conversion for int, float, bool.
    """
    target_type = str(target_type).lower()
    value_str = str(value_str) # Ensure value_str is a string before lower()
    
    if target_type == 'int':
        try:
            return int(float(value_str)) # Convert to float first to handle "1.0" as int
        except ValueError:
            # logger.warning(f"Could not convert '{value_str}' to int. Returning as string.")
            return value_str # Fallback to string if direct conversion fails
    elif target_type == 'float':
        try:
            return float(value_str)
        except ValueError:
            # logger.warning(f"Could not convert '{value_str}' to float. Returning as string.")
            return value_str # Fallback to string
    elif target_type == 'bool':
        return value_str.lower() in ['true', '1', 't', 'yes', 'y', 'on']
    # For 'string', 'categorical', or any other type, return as string
    return value_str

# Set up logging
logger = logging.getLogger(__name__)

# Import local CPS_v4 GUI modules
from app.gui.parameter_widgets_v4 import create_parameter_widgets_v4, update_parameters_from_widgets_v4
from app.gui.gui_actions_v4 import run_backtest_action_v4

class MainWindowV4(QMainWindow):
    """
    Main window for the CPS_v4 parameter system GUI.
    This integrates the CPS_v4 parameter system with the GUI framework.
    Parameters are sourced from the global 'settings' object.
    """
    def __init__(self):
        super().__init__()
        self.setWindowTitle("WTP Backtesting Engine - CPS_v4 Parameter System")
        self.resize(900, 650)
        central = QWidget()
        self.setCentralWidget(central)
        main_layout = QVBoxLayout(central)

        # Title and logo placeholder
        title_widget = QWidget()
        title_layout = QHBoxLayout(title_widget)
        title_label = QLabel("WTP Backtesting Engine - CPS_v4 Parameter System")
        font = title_label.font()
        font.setPointSize(20)
        font.setBold(True)
        title_label.setFont(font)
        title_label.setAlignment(Qt.AlignCenter)
        spacer = QWidget()
        spacer.setFixedSize(200, 50)  # reserved for a logo
        title_layout.addWidget(title_label, stretch=1)
        title_layout.addWidget(spacer)
        main_layout.addWidget(title_widget)

        # Instruction label
        info_label = QLabel("CPS_v4 Parameter System - Configure parameters for the backtest run.")
        info_font = info_label.font()
        info_font.setPointSize(10)
        info_label.setFont(info_font)
        main_layout.addWidget(info_label)

        # Create a scrollable area
        scroll = QScrollArea()
        scroll.setWidgetResizable(True)
        main_layout.addWidget(scroll, stretch=1)

        # The actual form widget
        self.form_widget = QWidget()
        self.form_layout = QVBoxLayout(self.form_widget)
        scroll.setWidget(self.form_widget)

        # Parameter groups
        self._add_parameter_groups()

        # Add report options
        self.track_ema_checkbox = QCheckBox("Track EMA calculations (creates detailed XLSX file)")
        self.form_layout.addWidget(self.track_ema_checkbox)

        # Add debug options
        debug_group = QGroupBox("Debug Options")
        debug_layout = QVBoxLayout(debug_group)
        self.debug_checkbox = QCheckBox("Debug mode (more logging)")
        self.debug_paramflow_checkbox = QCheckBox("Debug parameter flow")
        debug_layout.addWidget(self.debug_checkbox)
        debug_layout.addWidget(self.debug_paramflow_checkbox)
        self.form_layout.addWidget(debug_group)

        # Add run button at the bottom
        run_btn = QPushButton("Run Backtest with CPS_v4 Parameters")
        run_btn.setMinimumHeight(40)
        run_btn.clicked.connect(self._run_backtest)
        main_layout.addWidget(run_btn)

        # Initialize parameter widgets from configuration
        self._init_parameter_widgets()

    def _add_parameter_groups(self):
        """Add parameter groups to the form layout."""
        # These group names should match sections in the settings INI file
        # that define parameters for these GUI groups.
        self.core_backtest_group_name = 'Core Backtest Parameters'
        self.strategy_ema_group_name = 'EMA Strategy Parameters'

        self.backtest_group = QGroupBox(self.core_backtest_group_name)
        self.backtest_layout = QVBoxLayout(self.backtest_group)
        self.form_layout.addWidget(self.backtest_group)
        
        self.strategy_group = QGroupBox(self.strategy_ema_group_name)
        self.strategy_layout = QVBoxLayout(self.strategy_group)
        self.form_layout.addWidget(self.strategy_group)

    def _init_parameter_widgets(self):
        """Initialize parameter widgets from global CPS_v4 settings."""
        try:
            core_params_definitions = settings.get(self.core_backtest_group_name, {})
            if not core_params_definitions:
                logger.warning(f"No parameters found in settings for group: {self.core_backtest_group_name}")
            self.core_widgets = create_parameter_widgets_v4(
                core_params_definitions,
                self.backtest_layout
            )
            
            strategy_params_definitions = settings.get(self.strategy_ema_group_name, {})
            if not strategy_params_definitions:
                logger.warning(f"No parameters found in settings for group: {self.strategy_ema_group_name}")
            self.strategy_widgets = create_parameter_widgets_v4(
                strategy_params_definitions,
                self.strategy_layout
            )
        except Exception as e:
            logger.error(f"Error initializing parameter widgets: {e}", exc_info=True)
            QMessageBox.critical(self, "Error", f"Failed to initialize parameter widgets: {e}")
    
    def _run_backtest(self):
        """
        Collects parameters from GUI widgets, then initiates a backtest run
        using the CPS V4 compliant run_backtest_action_v4.
        """
        try:
            run_params = {}
            
            core_param_definitions = settings.get(self.core_backtest_group_name, {})
            strategy_param_definitions = settings.get(self.strategy_ema_group_name, {})

            widget_groups_to_process = [
                (self.core_widgets, core_param_definitions),
                (self.strategy_widgets, strategy_param_definitions)
            ]

            for widgets_dict, param_defs in widget_groups_to_process:
                if widgets_dict is None:
                    logger.warning(f"Widget dictionary for a group is None. Definitions: {list(param_defs.keys()) if param_defs else 'N/A'}. Skipping.")
                    continue
                for param_name, widget_obj in widgets_dict.items():
                    if param_name.startswith("optimize_"):
                        continue

                    if widget_obj is None:
                        logger.warning(f"Widget object for parameter '{param_name}' is None. Skipping.")
                        continue

                    raw_value = None
                    if isinstance(widget_obj, (QSpinBox, QDoubleSpinBox)):
                        raw_value = widget_obj.value()
                    elif isinstance(widget_obj, QLineEdit):
                        raw_value = widget_obj.text()
                    elif isinstance(widget_obj, QComboBox):
                        raw_value = widget_obj.currentText()
                    elif isinstance(widget_obj, QCheckBox):
                        raw_value = widget_obj.isChecked()
                    else:
                        logger.warning(f"Widget for parameter '{param_name}' is of an unhandled type: {type(widget_obj)}. Cannot retrieve value.")
                        continue
                    
                    param_detail = param_defs.get(param_name, {})
                    target_type = param_detail.get('type', 'string').lower()
                    
                    parsed_value = _parse_value(raw_value, target_type)
                    run_params[param_name] = parsed_value
                    logger.debug(f"Collected param: {param_name} = {parsed_value} (raw: {raw_value}, type: {target_type})")

            if hasattr(self, 'debug_checkbox'):
                run_params['debug_mode'] = self.debug_checkbox.isChecked()
            if hasattr(self, 'debug_paramflow_checkbox'):
                run_params['debug_parameter_flow'] = self.debug_paramflow_checkbox.isChecked()
            if hasattr(self, 'track_ema_checkbox'):
                run_params['track_ema_calculations'] = self.track_ema_checkbox.isChecked()
            
            logger.info(f"Running backtest with parameters: {run_params}")
            run_backtest_action_v4(self, run_params)

        except Exception as e:
            import traceback
            error_msg = f"Error running backtest: {str(e)}\n\n{traceback.format_exc()}"
            QMessageBox.critical(self, "Error", error_msg)
            logger.error(error_msg)


# Main entry point when run as a module
if __name__ == "__main__":
    # Print Python version and paths for debugging
    import sys
    print(f"Python version: {sys.version}")
    print("Python paths:")
    for path in sys.path:
        print(f"  {path}")
    
    # Create Qt application
    app = QApplication(sys.argv)
    
    # Create and show main window
    main_window = MainWindowV4()
    main_window.show()
    
    # Run the application event loop
    sys.exit(app.exec())
