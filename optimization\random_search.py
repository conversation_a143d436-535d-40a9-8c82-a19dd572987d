"""
Random search implementation for parameter optimization.
"""

import pandas as pd
import numpy as np
import random
import logging

logger = logging.getLogger(__name__)

def run_random_search(objective_func, param_ranges, n_iter=10, evaluation_metric='sharpe_ratio', maximize=True):
    """
    Run random search over parameter space.
    
    Args:
        objective_func (callable): Function to evaluate with parameters
        param_ranges (dict): Dictionary of parameter names and ranges (min, max) to search
        n_iter (int): Number of random combinations to try
        evaluation_metric (str): Metric to optimize
        maximize (bool): Whether to maximize or minimize the metric
        
    Returns:
        tuple: Best parameters and all results
    """
    logger.info(f"Running random search with {len(param_ranges)} parameters for {n_iter} iterations")
    
    results = []
    best_score = -np.inf if maximize else np.inf
    best_params = None
    
    # Generate and evaluate random parameter combinations
    for i in range(n_iter):
        # Generate random parameters
        params = {}
        for param_name, param_range in param_ranges.items():
            min_val, max_val = param_range
            
            # Handle different parameter types
            if isinstance(min_val, int) and isinstance(max_val, int):
                params[param_name] = random.randint(min_val, max_val)
            else:
                params[param_name] = min_val + random.random() * (max_val - min_val)
        
        try:
            # Run objective function with these parameters
            result = objective_func(**params)
            
            # Extract score from result
            if isinstance(result, dict) and evaluation_metric in result:
                score = result[evaluation_metric]
            elif isinstance(result, (int, float)):
                score = result
            else:
                logger.warning(f"Could not extract {evaluation_metric} from result")
                continue
            
            # Store result
            params_result = {**params, evaluation_metric: score}
            results.append(params_result)
            
            # Update best parameters if better
            if (maximize and score > best_score) or (not maximize and score < best_score):
                best_score = score
                best_params = params
                
            logger.info(f"Evaluated {i+1}/{n_iter}: {params}, score: {score:.4f}")
            
        except Exception as e:
            logger.error(f"Error evaluating parameters {params}: {e}")
    
    # Convert results to DataFrame
    results_df = pd.DataFrame(results)
    
    # Sort by evaluation metric
    if not results_df.empty:
        results_df = results_df.sort_values(by=evaluation_metric, ascending=not maximize)
    
    return best_params, results_df
