"""Parameter propagation sanity test.

Ensures that critical settings flow from CPS V4 into BacktestEngine
attributes and nested objects without raising errors.  The test does *not*
run a full simulation; it simply checks initialization wiring.
"""
from __future__ import annotations

import sys
import traceback
from pathlib import Path

# Add project root to path
PROJECT_ROOT = Path(__file__).resolve().parents[1]
if str(PROJECT_ROOT) not in sys.path:
    sys.path.insert(0, str(PROJECT_ROOT))

try:
    from v4.settings.settings_CPS_v4 import load_settings
    from v4.engine.backtest_v4 import BacktestEngine  # type: ignore
except Exception as exc:  # pragma: no cover
    traceback.print_exc()
    raise RuntimeError("Import failure in parameter flow test") from exc


def test_backtest_engine_initialization():
    """Create BacktestEngine and verify key parameters propagate correctly."""
    settings = load_settings()
    bt_params = settings["backtest"]

    engine = BacktestEngine()

    # Verify top-level attributes copied from settings
    assert engine.benchmark_rebalance_freq == bt_params["benchmark_rebalance_freq"]

    # Execution engine should have initial capital from settings.core or backtest
    # BacktestEngine sets this via its ExecutionEngine
    assert engine.execution_engine.initial_capital == bt_params["initial_capital"]

    # Commission/slippage rates propagate into models
    assert (
        engine.execution_engine.commission_model.rate
        == bt_params["commission_rate"]
    )
    assert engine.execution_engine.slippage_model.rate == bt_params["slippage_rate"]


def main() -> None:  # pragma: no cover
    try:
        test_backtest_engine_initialization()
        print("Parameter flow test passed.")
    except AssertionError as aerr:
        print(f"Parameter flow test failed: {aerr}")
        sys.exit(1)


if __name__ == "__main__":  # pragma: no cover
    main()
