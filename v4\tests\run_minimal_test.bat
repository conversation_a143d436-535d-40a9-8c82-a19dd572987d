@echo off
echo =====================================================
echo Minimal Backtest Engine Test - %date% %time%
echo =====================================================

REM Set up virtual environment path
set VENV_PATH=F:\AI_Library\my_quant_env

REM Activate virtual environment
echo Activating virtual environment at %VENV_PATH%...
call %VENV_PATH%\Scripts\activate.bat

REM Set output directory
set OUTPUT_DIR=S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\tests\debug_output
echo Output directory path: %OUTPUT_DIR%

REM Create output directory if it doesn't exist
if not exist "%OUTPUT_DIR%" (
    echo Creating output directory...
    mkdir "%OUTPUT_DIR%"
)

REM Set log file with timestamp
set LOG_FILE=%OUTPUT_DIR%\minimal_test_%date:~-4,4%%date:~-7,2%%date:~-10,2%_%time:~0,2%%time:~3,2%%time:~6,2%.log
set LOG_FILE=%LOG_FILE: =0%

echo Running minimal engine test...
echo Results will be saved to: %LOG_FILE%

REM Run the test script with output to log file
echo Command: python S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\tests\minimal_engine_test.py
python S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\tests\minimal_engine_test.py > "%LOG_FILE%" 2>&1
echo Python script completed with exit code: %errorlevel%

REM Display log file contents
echo.
echo Log file contents:
echo ==================
if exist "%LOG_FILE%" (
    type "%LOG_FILE%"
) else (
    echo ERROR: Log file not created!
)

echo.
echo Test complete!
echo Log saved to: %LOG_FILE%

REM Display output directory contents
echo.
echo Output directory contents:
dir "%OUTPUT_DIR%"

echo.
pause
