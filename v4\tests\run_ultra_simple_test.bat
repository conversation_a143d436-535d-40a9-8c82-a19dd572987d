@echo on
echo =====================================================
echo ULTRA SIMPLE CONSOLE TEST - %date% %time%
echo =====================================================

REM Set virtual environment path
set VENV_PATH=F:\AI_Library\my_quant_env

REM Activate virtual environment
echo Activating virtual environment at %VENV_PATH%...
call %VENV_PATH%\Scripts\activate.bat
if %errorlevel% neq 0 (
    echo ERROR: Failed to activate virtual environment! Error code: %errorlevel%
    goto :error
)

echo.
echo ------------------------------------------------------
echo Running ultra simple test script...
echo ------------------------------------------------------
echo.

REM Run the test script with ALL output to console
python S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\tests\ultra_simple_test.py
if %errorlevel% neq 0 (
    echo ERROR: Python script failed with exit code: %errorlevel%
    goto :error
)

echo.
echo ------------------------------------------------------
echo Test script execution complete!
echo ------------------------------------------------------

goto :end

:error
echo.
echo ERROR: Test script execution failed!
echo.

:end
echo.
echo Press any key to close this window...
pause > nul
