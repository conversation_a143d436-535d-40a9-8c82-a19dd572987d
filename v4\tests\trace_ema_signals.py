"""
trace_ema_signals.py
--------------------
Trace EMA signal generation step-by-step for the CPS-v4 back-test engine.

Creates a CSV file containing the allocation weights that the EMA strategy
would output for **every business day** between 2021-01-20 and 2021-02-28.
The script replicates the logic that exists in
`v4.engine.signal_generator.EMASignalGenerator.generate_signals` so that we
can observe the intermediate calculations (EMA values, trend strength, final
weights) without altering production code.

Location of output:
    v4/tests/output/ema_signal_trace_20210120_20210228.csv

The resulting CSV has these columns:
    Date, <symbol_1>, <symbol_2>, ...  – weights per symbol (weights sum to 1)

Additional diagnostic CSVs will also be written with the suffixes:
    *_trend_strength.csv  – raw trend-strength numbers per symbol
    *_ema_values.csv      – EMA values (short, medium, long) for each symbol

This file purposefully lives inside the `v4/tests` folder so that it is **not
imported by production code**.
"""

from __future__ import annotations

import logging
from pathlib import Path
from datetime import datetime

import numpy as np
import pandas as pd

# ------------------------------------------------------------------
# Ensure project root is on sys.path so that `import v4.*` works when
# this script is run directly from the `v4/tests` directory.
# ------------------------------------------------------------------
import sys
from pathlib import Path
project_root = Path(__file__).resolve().parents[2]
if str(project_root) not in sys.path:
    sys.path.append(str(project_root))

# Project imports – **do not** move these to production modules
from v4.engine.data_loader_v4 import load_data_for_backtest

# --------------------------------------------------------------------------------------
# Configuration – adjust here if alternative look-back windows are required.
# --------------------------------------------------------------------------------------
ST_LOOKBACK = 10
MT_LOOKBACK = 50
LT_LOOKBACK = 150
START_DATE = "2021-01-20"
END_DATE = "2021-02-28"

OUTPUT_DIR = Path(__file__).with_suffix("").parent / "output"
OUTPUT_DIR.mkdir(exist_ok=True, parents=True)

TRACE_CSV = OUTPUT_DIR / f"ema_signal_trace_{START_DATE.replace('-', '')}_{END_DATE.replace('-', '')}.csv"
TREND_CSV = OUTPUT_DIR / f"ema_trend_strength_{START_DATE.replace('-', '')}_{END_DATE.replace('-', '')}.csv"
EMA_CSV = OUTPUT_DIR / f"ema_values_{START_DATE.replace('-', '')}_{END_DATE.replace('-', '')}.csv"

logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")
logger = logging.getLogger("trace_ema_signals")

# --------------------------------------------------------------------------------------
# Helper functions
# --------------------------------------------------------------------------------------

def _compute_ema_series(price_data: pd.DataFrame, span: int) -> pd.DataFrame:
    """Simple wrapper around `DataFrame.ewm(span).mean()` that handles NaNs."""
    return price_data.ewm(span=span, adjust=False).mean()


def _compute_signals_for_slice(slice_df: pd.DataFrame) -> tuple[dict[str, float], dict[str, float]]:
    """Replicate EMASignalGenerator logic for a *single* date slice.

    Parameters
    ----------
    slice_df : pd.DataFrame
        Price data up to and **including** the current analysis date. Index must
        be datetime-like and columns the ticker symbols.

    Returns
    -------
    signals : dict[str, float]
        Allocation weights that sum to 1. Symbols with 0 weight are omitted.
    trend_strength : dict[str, float]
        Raw trend-strength numbers (≥0). Used for proportional weighting.
    """

    # Calculate EMAs using the available history up to the current date
    st_ema = _compute_ema_series(slice_df, ST_LOOKBACK)
    mt_ema = _compute_ema_series(slice_df, MT_LOOKBACK)
    lt_ema = _compute_ema_series(slice_df, LT_LOOKBACK)

    # Grab the **latest** row – that corresponds to the current date
    st_cur = st_ema.iloc[-1]
    mt_cur = mt_ema.iloc[-1]
    lt_cur = lt_ema.iloc[-1]

    # Trend-strength calculation (same as production code)
    trend_strength: dict[str, float] = {}
    for sym in slice_df.columns:
        if pd.isna(st_cur[sym]) or pd.isna(mt_cur[sym]) or pd.isna(lt_cur[sym]):
            trend_strength[sym] = 0.0
            continue
        st_mt_ratio = st_cur[sym] / mt_cur[sym] - 1.0
        mt_lt_ratio = mt_cur[sym] / lt_cur[sym] - 1.0
        strength = max(0.0, st_mt_ratio + mt_lt_ratio)
        trend_strength[sym] = strength

    # Convert strength → weights
    positive_trends = {s: v for s, v in trend_strength.items() if v > 0}
    if not positive_trends:
        # No positive trends – return empty dict (weights later normalised)
        return {}, trend_strength

    total_strength = sum(positive_trends.values())
    weights = {s: v / total_strength for s, v in positive_trends.items()}
    return weights, trend_strength


# --------------------------------------------------------------------------------------
# Main tracing routine
# --------------------------------------------------------------------------------------

def main() -> None:
    logger.info("Loading price data via CPS-v4 data loader …")
    data = load_data_for_backtest()
    price_df: pd.DataFrame = data["price_data"].copy()

    logger.info("Price data loaded: %d rows × %d columns", *price_df.shape)

    # Ensure the date range exists in the data
    price_df = price_df.sort_index()
    start_ts = pd.to_datetime(START_DATE)
    end_ts = pd.to_datetime(END_DATE)
    mask = (price_df.index >= start_ts) & (price_df.index <= end_ts)
    analysis_dates = price_df.index[mask]

    if analysis_dates.empty:
        raise ValueError("No price data available in the requested date range → check settings & data files.")

    logger.info("Tracing %d business days from %s to %s", len(analysis_dates), START_DATE, END_DATE)

    weights_records: list[dict[str, float]] = []
    trend_records: list[dict[str, float]] = []

    for dt in analysis_dates:
        # Slice up to *this* date (inclusive)
        slice_df = price_df.loc[:dt]
        weights, strength = _compute_signals_for_slice(slice_df)

        # Normalise weights to ensure Σ=1 even if empty
        if not weights:
            weights = {sym: 0.0 for sym in price_df.columns}
        else:
            # Fill missing symbols with 0 weight
            for sym in price_df.columns:
                weights.setdefault(sym, 0.0)

        # Store records with Date as string for easier CSV reading
        weight_rec = {"Date": dt.strftime("%Y-%m-%d"), **weights}
        strength_rec = {"Date": dt.strftime("%Y-%m-%d"), **strength}
        weights_records.append(weight_rec)
        trend_records.append(strength_rec)

    # Convert to DataFrames and save
    weights_df = pd.DataFrame(weights_records).set_index("Date")
    trend_df = pd.DataFrame(trend_records).set_index("Date")

    weights_df.to_csv(TRACE_CSV)
    trend_df.to_csv(TREND_CSV)

    # Additionally save the EMA values for the *last* date as reference
    last_slice = price_df.loc[:analysis_dates[-1]]
    st_ema_last = _compute_ema_series(last_slice, ST_LOOKBACK).iloc[-1]
    mt_ema_last = _compute_ema_series(last_slice, MT_LOOKBACK).iloc[-1]
    lt_ema_last = _compute_ema_series(last_slice, LT_LOOKBACK).iloc[-1]
    ema_df = pd.DataFrame({
        "short_ema": st_ema_last,
        "medium_ema": mt_ema_last,
        "long_ema": lt_ema_last,
    })
    ema_df.to_csv(EMA_CSV)

    logger.info("Signal trace written to %s", TRACE_CSV)
    logger.info("Trend-strength trace written to %s", TREND_CSV)
    logger.info("EMA values at %s written to %s", END_DATE, EMA_CSV)

    # Show a quick preview in console for verification
    print("\n=== Preview of signal weights ===")
    print(weights_df.head())
    print("\n=== Preview of trend strength ===")
    print(trend_df.head())


if __name__ == "__main__":
    main()
