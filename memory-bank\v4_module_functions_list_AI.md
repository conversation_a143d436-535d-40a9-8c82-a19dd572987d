# V4 Module and Functions List

## Detailed System Architecture (Updated 2025-06-21)

```mermaid
graph TD
    subgraph InputLayer[Input Layer]
        A1[Historical Price Data] --> B1[data_loader_v4.py]
        A2[Strategy Parameters] --> B2[settings_CPS_v4.py]
    end

    subgraph SignalGeneration[Signal Generation]
        B1 --> C1[run_signal_generation.py]
        B2 --> C1
        C1 -->|signal_history.csv| D1[File System]
        C1 -->|ranking.csv| D2[Validation]
    end

    subgraph BacktestLayer[Backtest Engine]
        D1 --> E1[backtest_v4.py]
        B1 --> E1
        E1 -->|Orders| F1[execution_v4.py]
        E1 -->|Portfolio Updates| F2[portfolio_v4.py]
        F1 -->|Filled Trades| F2
        F2 -->|Current Weights| E1
    end

    subgraph ReportLayer[Reporting]
        E1 --> G1[allocation_report_v4.py]
        E1 --> G2[performance_report_v4.py]
        G1 --> H1[Allocation Reports]
        G2 --> H2[Performance Reports]
    end

    classDef input fill:#D6EAF8,stroke:#5DADE2
    classDef process fill:#D1F2EB,stroke:#48C9B0
    classDef output fill:#FCF3CF,stroke:#F7DC6F
    classDef file fill:#F5B7B1,stroke:#E74C3C
    class A1,A2 input
    class B1,B2,C1,E1,F1,F2,G1,G2 process
    class D1,D2 file
    class H1,H2 output
```

## Complete Module and Function Matrix

| Module                | File                                      | Key Functions                          | Description |
|-----------------------|-------------------------------------------|----------------------------------------|-------------|
| Data Loading          | `v4/data/data_loader_v4.py`               | `load_market_data()`  
`validate_data()` | Loads and validates historical price data |
| Signal Generation     | `v4/run_signal_generation.py`             | `generate_signals()`  
`_validate_signals()` | Generates and validates signal files |
| Backtest Engine       | `v4/engine/backtest_v4.py`                | `run_backtest()`  
`_load_signal_file()` | Loads and validates pre-computed signals |
| Execution             | `v4/engine/execution_v4.py`               | `execute_orders()`  
`calculate_slippage()` | Trade execution simulation |
| Portfolio             | `v4/engine/portfolio_v4.py`               | `update_portfolio()`  
`get_weights()` | Portfolio state management |
| Reporting             | `v4/reporting/allocation_report_v4.py`    | `generate_allocation_report()` | Asset allocation visualization |
| Reporting             | `v4/reporting/performance_report_v4.py`   | `generate_performance_report()` | Performance metrics calculation |
| Configuration         | `CPS_v4/settings_CPS_v4.py`               | `load_settings()` | Parameter management |
| Utilities             | `v4/utils/date_utils_v4.py`               | `parse_date()` | Date handling functions |

## Supporting Documentation

- `docs/CPS_v4/signals_trading_flow.md` - Core components and flow of signal generation
- `docs/CPS_v4/How_signals_work.md` - Detailed EMA signal generation process
- `docs/CPS_v4/Parameter_Reference_CPS_v4.md` - Complete parameter reference
