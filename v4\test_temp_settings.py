#!/usr/bin/env python3
"""
Test script to verify temp settings file modification works correctly
"""

import sys
import os
import tempfile
import shutil
import re
from pathlib import Path

# Add v4 directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'v4'))

from reporting.performance_table_generator import PerformanceTableGenerator

def test_temp_settings_modification():
    """Test that temp settings files are being modified correctly"""
    print("Testing temp settings file modification...")
    
    # Initialize generator
    generator = PerformanceTableGenerator()
    
    # Test combination with different values
    test_combination = {
        'st_lookback': 5,  # Changed from default 15
        'mt_lookback': 30,  # Changed from default 70
        'lt_lookback': 100,  # Same as default
        'system_lookback': 25,  # Changed from default 60
        'execution_delay': 1,
        'top_n': 2
    }
    
    print(f"Test combination: {test_combination}")
    
    # Create temp settings file
    temp_path = generator._create_temp_settings_for_combination(test_combination)
    print(f"Created temp file: {temp_path}")
    
    # Read the temp file and check modifications
    with open(temp_path, 'r') as f:
        content = f.read()
    
    # Check for modified values
    print("\nChecking modified values in temp file:")
    
    # Check st_lookback
    st_match = re.search(r'st_lookback\s*=\s*\([^)]*default_value=(\d+)', content)
    if st_match:
        st_value = int(st_match.group(1))
        print(f"  st_lookback found: {st_value} (expected: 5) - {'✓' if st_value == 5 else '✗'}")
    else:
        print("  st_lookback NOT FOUND - ✗")
    
    # Check mt_lookback  
    mt_match = re.search(r'mt_lookback\s*=\s*\([^)]*default_value=(\d+)', content)
    if mt_match:
        mt_value = int(mt_match.group(1))
        print(f"  mt_lookback found: {mt_value} (expected: 30) - {'✓' if mt_value == 30 else '✗'}")
    else:
        print("  mt_lookback NOT FOUND - ✗")
    
    # Check system_lookback
    sys_match = re.search(r'system_lookback\s*=\s*\([^)]*default_value=(\d+)', content)
    if sys_match:
        sys_value = int(sys_match.group(1))
        print(f"  system_lookback found: {sys_value} (expected: 25) - {'✓' if sys_value == 25 else '✗'}")
    else:
        print("  system_lookback NOT FOUND - ✗")
    
    # Check lt_lookback (should be unchanged)
    lt_match = re.search(r'lt_lookback\s*=\s*\([^)]*default_value=(\d+)', content)
    if lt_match:
        lt_value = int(lt_match.group(1))
        print(f"  lt_lookback found: {lt_value} (expected: 100) - {'✓' if lt_value == 100 else '✗'}")
    else:
        print("  lt_lookback NOT FOUND - ✗")
    
    # Save a copy for inspection
    inspection_file = "temp_settings_inspection.ini"
    shutil.copy(temp_path, inspection_file)
    print(f"\nSaved copy for inspection: {inspection_file}")
    
    # Cleanup
    generator._cleanup_temp_settings(temp_path)
    
    return True

if __name__ == "__main__":
    test_temp_settings_modification()
