"""v4.engine.orders
Compatibility shim that re-exports symbols from :pymod:`v4.engine.orders_v4`.

Many modules were written to import ``v4.engine.orders`` (without the *_v4*
file suffix).  To avoid a sweeping refactor while we stabilise CPS V4 we add
this lightweight façade module.  Once all code has been updated the shim can
be removed.
"""
from importlib import import_module as _imp

_orders_mod = _imp("v4.engine.orders_v4")

# Re-export public names so that ``from v4.engine.orders import Order`` works.
__all__ = _orders_mod.__all__ if hasattr(_orders_mod, "__all__") else [
    name for name in _orders_mod.__dict__.keys() if not name.startswith("_")
]

globals().update({name: getattr(_orders_mod, name) for name in __all__})

# Ensure sub-modules resolve correctly (rarely needed but harmless)
import sys as _sys
_sys.modules[__name__ + ".orders_v4"] = _orders_mod
