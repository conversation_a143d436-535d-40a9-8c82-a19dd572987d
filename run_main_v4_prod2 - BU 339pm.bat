@echo off
REM ============================================
REM Script: run_main_v4_prod2.bat
REM Description: Super-stable launcher for V4 decoupled pipeline with full + filtered logs
REM ============================================

SETLOCAL

REM --- Paths ---
SET "PYTHON_EXE=F:\AI_Library\my_quant_env\Scripts\python.exe"
SET "SCRIPT_DIR=%~dp0"
SET "SIGNAL_SCRIPT=%SCRIPT_DIR%v4\run_signal_phase.py"
SET "TRADING_SCRIPT=%SCRIPT_DIR%v4\run_trading_phase.py"
SET "OUTPUT_DIR=%SCRIPT_DIR%v4_trace_outputs"

REM --- Ensure output directory exists ---
IF NOT EXIST "%OUTPUT_DIR%" (
    mkdir "%OUTPUT_DIR%"
)

REM --- Timestamp (YYYYMMDD_HHMMSS) ---
SET "TIMESTAMP=%DATE:~10,4%%DATE:~4,2%%DATE:~7,2%_%TIME:~0,2%%TIME:~3,2%%TIME:~6,2%"
SET "TIMESTAMP=%TIMESTAMP: =0%"

REM --- Log files ---
SET "FULL_LOG=%OUTPUT_DIR%\full_%TIMESTAMP%.txt"
SET "FILTERED_LOG=%OUTPUT_DIR%\filtered_%TIMESTAMP%.txt"

echo [%TIME%] Running V4 production pipeline (full log: "%FULL_LOG%" )

REM --- Run Python Orchestrator (Decoupled Architecture) ---
echo [%TIME%] Starting V4 production pipeline...

echo ===== V4 PRODUCTION PIPELINE ===== > "%FULL_LOG%"

echo [%TIME%] Phase 1: Running Signal Generation...
echo [%TIME%] Phase 1: Running Signal Generation... >> "%FULL_LOG%"

REM Run signal phase and capture output to both console and log
(
    "%PYTHON_EXE%" "%SIGNAL_SCRIPT%"
    SET "SIGNAL_EXIT_CODE=%ERRORLEVEL%"
    echo Signal phase completed with exit code %SIGNAL_EXIT_CODE%
) 2>&1 | findstr /V /B /C:"" > "%TEMP%\temp_output.txt"

type "%TEMP%\temp_output.txt"
type "%TEMP%\temp_output.txt" >> "%FULL_LOG%"

IF %SIGNAL_EXIT_CODE% EQU 0 (
    echo [%TIME%] Phase 2: Running Trading Phase...
    echo [%TIME%] Phase 2: Running Trading Phase... >> "%FULL_LOG%"
    
    (
        "%PYTHON_EXE%" "%TRADING_SCRIPT%"
        SET "TRADING_EXIT_CODE=%ERRORLEVEL%"
        echo Trading phase completed with exit code %TRADING_EXIT_CODE%
    ) 2>&1 | findstr /V /B /C:"" > "%TEMP%\temp_output.txt"
    
    type "%TEMP%\temp_output.txt"
    type "%TEMP%\temp_output.txt" >> "%FULL_LOG%"
    
    REM Set overall exit code
    IF %TRADING_EXIT_CODE% EQU 0 (
        SET "EXIT_CODE=0"
    ) ELSE (
        SET "EXIT_CODE=%TRADING_EXIT_CODE%"
    )
) ELSE (
    echo [ERROR] Signal phase failed, skipping trading phase
    echo [ERROR] Signal phase failed, skipping trading phase >> "%FULL_LOG%"
    SET "EXIT_CODE=%SIGNAL_EXIT_CODE%"
)

echo V4 production pipeline completed with exit code %EXIT_CODE%
echo V4 production pipeline completed with exit code %EXIT_CODE% >> "%FULL_LOG%"

REM --- Create filtered log ---
echo ===== FILTERED OUTPUT ===== > "%FILTERED_LOG%"
echo Run Time: %DATE% %TIME% >> "%FILTERED_LOG%"
echo. >> "%FILTERED_LOG%"
echo ===== MILESTONES ===== >> "%FILTERED_LOG%"
findstr /C:"[MILESTONE]" "%FULL_LOG%" >> "%FILTERED_LOG%" 2>nul
echo. >> "%FILTERED_LOG%"
echo ===== WARNINGS ^& ERRORS ===== >> "%FILTERED_LOG%"
findstr /C:"[ERROR]" /C:"[WARNING]" "%FULL_LOG%" >> "%FILTERED_LOG%" 2>nul
findstr /i "error warning exception traceback failed fatal" "%FULL_LOG%" >> "%FILTERED_LOG%" 2>nul

echo [%TIME%] V4 production pipeline finished with exit code %EXIT_CODE%
IF %EXIT_CODE% EQU 0 (
    echo Pipeline completed successfully.
) ELSE (
    echo Pipeline encountered errors. Check the logs for details.
)
echo Full log: "%FULL_LOG%"
echo Filtered log: "%FILTERED_LOG%"

ENDLOCAL
exit /b %EXIT_CODE%
