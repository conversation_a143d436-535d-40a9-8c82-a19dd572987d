"""
test_allocation_flow.py
Simple test script to run a backtest and analyze allocation data flow.
"""

import os
import pandas as pd
import numpy as np
from datetime import datetime
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, 
                   format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Enable allocation export
os.environ["EXPORT_ALLOCATIONS"] = "true"

# Import backtest engine
from v4.engine.backtest_v4 import BacktestEngine
from v4.models.ema_allocation_model_v4 import ema_allocation_model_single

def main():
    """Run a simple backtest to test allocation flow."""
    logger.info("Starting allocation flow test")
    
    # Create sample price data
    # Use a small dataset for quick testing
    tickers = ['SPY', 'AGG', 'GLD', 'QQQ', 'VWO']
    dates = pd.date_range(start='2023-01-01', end='2023-03-01', freq='B')
    
    # Create random price data with realistic trends
    np.random.seed(42)  # For reproducibility
    price_data = pd.DataFrame(index=dates)
    
    for ticker in tickers:
        # Start with a base price
        base_price = np.random.uniform(50, 200)
        # Generate random daily returns with a slight upward bias
        returns = np.random.normal(0.0003, 0.01, len(dates))
        # Calculate cumulative returns
        cum_returns = (1 + returns).cumprod()
        # Generate prices
        prices = base_price * cum_returns
        price_data[ticker] = prices
    
    logger.info(f"Created sample price data with shape {price_data.shape}")
    
    # Initialize backtest engine
    engine = BacktestEngine()
    
    # Run backtest
    logger.info("Running backtest with EMA allocation model")
    results = engine.run_backtest(
        price_data=price_data,
        signal_generator=ema_allocation_model_single,
        st_lookback=5,
        mt_lookback=10,
        lt_lookback=20
    )
    
    # Log results summary
    logger.info(f"Backtest completed with final value: ${results['final_value']:,.2f}")
    logger.info(f"Total return: {results['total_return']:.2%}")
    
    # Check if weights_history is present and valid
    if 'weights_history' in results:
        weights = results['weights_history']
        logger.info(f"Weights history shape: {weights.shape}")
        logger.info(f"Weights history sample:\n{weights.iloc[:5]}")
    else:
        logger.error("No weights_history found in results")
    
    # Check if signal_history is present and valid
    if 'signal_history' in results:
        signals = results['signal_history']
        logger.info(f"Signal history shape: {signals.shape}")
        logger.info(f"Signal history sample:\n{signals.iloc[:5]}")
    else:
        logger.error("No signal_history found in results")
    
    logger.info("Allocation flow test completed")
    logger.info("Check debug_allocations directory for detailed output")

if __name__ == "__main__":
    main()
