"""
CPS V4 Parameter System - GUI Integration

This module provides a GUI interface for the CPS V4 parameter system.
It adapts the V3 GUI structure to use CPS V4 settings directly.
"""

import sys
import os
from pathlib import Path
import logging
from PySide6.QtWidgets import (
    Q<PERSON><PERSON>lication,
    QMainWindow,
    QWidget,
    QLabel,
    QCheckBox,
    QLineEdit,
    QComboBox,
    QPushButton,
    QMessageBox,
    QHBoxLayout,
    QVBoxLayout,
    QScrollArea,
    QSizePolicy,
    QGroupBox
)
from PySide6.QtCore import Qt

# Initialize logger
logger = logging.getLogger(__name__)

# Add project root to path to ensure imports work properly
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# Import configuration and necessary modules
from v4.config.paths_v4 import *
from CPS_v4.settings_CPS_v4 import load_settings, save_settings
from data.data_loader import load_data_for_backtest
from models.ema_allocation_model import ema_allocation_model, get_ema_parameters

from CPS_v4.settings_CPS_v4 import load_settings

# Set up logging configuration
log_level_str = os.environ.get('BACKTEST_LOG_LEVEL', 'INFO')
log_level_map = {
    'DEBUG': logging.DEBUG,
    'INFO': logging.INFO,
    'WARNING': logging.WARNING,
    'ERROR': logging.ERROR,
    'CRITICAL': logging.CRITICAL
}

log_level = log_level_map.get(log_level_str.upper(), logging.INFO)

# Set up logging
logging.basicConfig(level=log_level, 
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)
logger.setLevel(log_level)


class MainWindowV4(QMainWindow):
    """
    Main window for the CPS V4 parameter system GUI.
    This integrates CPS V4 settings with the GUI framework.
    """
    def __init__(self):
        super().__init__()
        self.setWindowTitle("WTP Backtesting Engine - CPS V4 System")
        self.resize(900, 650)
        central = QWidget()
        self.setCentralWidget(central)
        main_layout = QVBoxLayout(central)

        # Load CPS V4 settings
        self.settings = load_settings()
        self._transform_gui_settings()  # Convert flat GUI params to nested structure
        
        # Title and logo placeholder
        title_widget = QWidget()
        title_layout = QHBoxLayout(title_widget)
        title_label = QLabel("WTP Backtesting Engine - CPS V4 System")
        font = title_label.font()
        font.setPointSize(20)
        font.setBold(True)
        title_label.setFont(font)
        title_label.setAlignment(Qt.AlignCenter)
        spacer = QWidget()
        spacer.setFixedSize(200, 50)  # reserved for a logo
        title_layout.addWidget(title_label, stretch=1)
        title_layout.addWidget(spacer)
        main_layout.addWidget(title_widget)

        # Instruction label
        info_label = QLabel("CPS V4 Parameter System - Check box to optimize/loop on variable")
        info_font = info_label.font()
        info_font.setPointSize(10)
        info_label.setFont(info_font)
        main_layout.addWidget(info_label)

        # Create a scrollable area
        scroll = QScrollArea()
        scroll.setWidgetResizable(True)
        main_layout.addWidget(scroll, stretch=1)

        # The actual form widget
        self.form_widget = QWidget()
        self.form_layout = QVBoxLayout(self.form_widget)
        scroll.setWidget(self.form_widget)

        # Parameter groups
        self._add_parameter_groups()

        # Add report options
        self.track_ema_checkbox = QCheckBox("Track EMA calculations (creates detailed XLSX file)")
        self.form_layout.addWidget(self.track_ema_checkbox)

        # Add debug options
        debug_group = QGroupBox("Debug Options")
        debug_layout = QVBoxLayout(debug_group)
        self.debug_checkbox = QCheckBox("Debug mode (more logging)")
        self.debug_paramflow_checkbox = QCheckBox("Debug parameter flow")
        debug_layout.addWidget(self.debug_checkbox)
        debug_layout.addWidget(self.debug_paramflow_checkbox)
        self.form_layout.addWidget(debug_group)

        # Add run button at the bottom
        run_btn = QPushButton("Run Backtest with CPS V4 System")
        run_btn.setMinimumHeight(40)
        run_btn.clicked.connect(self.run_backtest)
        main_layout.addWidget(run_btn)

        # Initialize parameter widgets from configuration
        self._init_parameters()

    
    def _add_parameter_groups(self):
        """Add parameter groups to the form layout."""
        # Backtest Parameters Group
        self.backtest_group = QGroupBox("Core Backtest Parameters")
        self.backtest_layout = QVBoxLayout(self.backtest_group)
        self.form_layout.addWidget(self.backtest_group)
        
        # Strategy Parameters Group
        self.strategy_group = QGroupBox("EMA Strategy Parameters")
        self.strategy_layout = QVBoxLayout(self.strategy_group)
        self.form_layout.addWidget(self.strategy_group)


    def _transform_gui_settings(self):
        """
        Transforms flat GUI parameter settings (from INI) into a nested dictionary
        under self.settings['gui_params'] as expected by _init_parameters.
        Example flat key: 'guiparam_core_initial_cash_type'
        Becomes: self.settings['gui_params']['core']['initial_cash']['type']
        """
        gui_params_nested = {}
        keys_to_remove = []

        for key, value in list(self.settings.items()): # Iterate over a copy for safe removal
            if key.startswith('guiparam_'):
                parts = key.split('_', 3) # Split into 'guiparam', group, param_name, attribute
                if len(parts) == 4:
                    _, group_raw, param_name_raw, attribute = parts
                    
                    group = group_raw
                    if group == 'strategyema': # Map 'strategyema' from INI to 'strategy_ema' for consistency
                        group = 'strategy_ema'
                    # Add other specific group mappings here if needed in the future

                    param_name = param_name_raw

                    if group not in gui_params_nested:
                        gui_params_nested[group] = {}
                    if param_name not in gui_params_nested[group]:
                        gui_params_nested[group][param_name] = {}
                    
                    # Type conversion for boolean 'optimize' should happen here if not handled by _convert_value
                    # However, _convert_value in settings_CPS_v4.py already handles 'True'/'False' to bool.
                    gui_params_nested[group][param_name][attribute] = value
                    keys_to_remove.append(key)
                else:
                    logger.warning(f"Could not parse GUI parameter key: {key}. Expected 4 parts, got {len(parts)}.")

        if gui_params_nested:
            if 'gui_params' not in self.settings:
                self.settings['gui_params'] = {}
            self.settings['gui_params'].update(gui_params_nested) # Merge, in case 'gui_params' already exists with other data
            
            for key_to_remove in keys_to_remove:
                if key_to_remove in self.settings:
                    del self.settings[key_to_remove]
            logger.info("Transformed flat GUI settings into nested 'gui_params' structure.")
        else:
            logger.info("No flat GUI settings found with 'guiparam_' prefix to transform.")

    def _init_parameters(self):
        """Initialize parameter widgets from CPS V4 settings."""
    # Create widgets for core parameters
    core_params_config = self.settings.get('gui_params', {}).get('core', {})
    self.core_widgets = {}
    
    for name, param_config in core_params_config.items():
        widget_row = QWidget()
        layout = QHBoxLayout(widget_row)
        layout.setContentsMargins(0, 0, 0, 0)
        
        description = param_config.get('description', name)
        checkbox = QCheckBox(f"{description}:")
        checkbox.setChecked(param_config.get('optimize', False))
        layout.addWidget(checkbox, 1)
        
        param_type = param_config.get('type', 'numeric') # Default to numeric if type not specified
        
        if param_type == 'numeric':
            value_field = QLineEdit(str(param_config.get('default', '')))
            layout.addWidget(value_field, 1)
            
            min_field = QLineEdit(str(param_config.get('min_value', '')))
            min_field.setPlaceholderText("Min")
            layout.addWidget(min_field, 1)
            
            max_field = QLineEdit(str(param_config.get('max_value', '')))
            max_field.setPlaceholderText("Max")
            layout.addWidget(max_field, 1)
            
            step_field = QLineEdit(str(param_config.get('step', '')))
            step_field.setPlaceholderText("Step")
            layout.addWidget(step_field, 1)
            
            self.core_widgets[name] = {
                'checkbox': checkbox,
                'value': value_field,
                'min': min_field,
                'max': max_field,
                'step': step_field,
                'type': 'numeric',
                'description_label': checkbox # Re-using checkbox as it holds the description
            }
        elif param_type == 'categorical':
            combo = QComboBox()
            combo.addItems(param_config.get('choices', []))
            combo.setCurrentText(str(param_config.get('default', '')))
            layout.addWidget(combo, 3) # Give more space to combo box
            
            self.core_widgets[name] = {
                'checkbox': checkbox,
                'value': combo,
                'type': 'categorical',
                'description_label': checkbox
            }
        
        self.backtest_layout.addWidget(widget_row)
    
    # Create widgets for strategy parameters
    strategy_params_config = self.settings.get('gui_params', {}).get('strategy_ema', {})
    self.strategy_widgets = {}
    
    for name, param_config in strategy_params_config.items():
        widget_row = QWidget()
        layout = QHBoxLayout(widget_row)
        layout.setContentsMargins(0, 0, 0, 0)
        
        description = param_config.get('description', name)
        checkbox = QCheckBox(f"{description}:")
        checkbox.setChecked(param_config.get('optimize', False))
        layout.addWidget(checkbox, 1)
        
        param_type = param_config.get('type', 'numeric')
        
        if param_type == 'numeric':
            value_field = QLineEdit(str(param_config.get('default', '')))
            layout.addWidget(value_field, 1)
            
            min_field = QLineEdit(str(param_config.get('min_value', '')))
            min_field.setPlaceholderText("Min")
            layout.addWidget(min_field, 1)
            
            max_field = QLineEdit(str(param_config.get('max_value', '')))
            max_field.setPlaceholderText("Max")
            layout.addWidget(max_field, 1)
            
            step_field = QLineEdit(str(param_config.get('step', '')))
            step_field.setPlaceholderText("Step")
            layout.addWidget(step_field, 1)
            
            self.strategy_widgets[name] = {
                'checkbox': checkbox,
                'value': value_field,
                'min': min_field,
                'max': max_field,
                'step': step_field,
                'type': 'numeric',
                'description_label': checkbox
            }
        elif param_type == 'categorical':
            combo = QComboBox()
            combo.addItems(param_config.get('choices', []))
            combo.setCurrentText(str(param_config.get('default', '')))
            layout.addWidget(combo, 3)
            
            self.strategy_widgets[name] = {
                'checkbox': checkbox,
                'value': combo,
                'type': 'categorical',
                'description_label': checkbox
            }
        
        self.strategy_layout.addWidget(widget_row)

    def _update_parameters_from_widgets(self):
        """Update CPS V4 settings from GUI widgets."""
        # TODO: Implement logic to update self.settings from GUI widgets
        # TODO: Implement or call a function to save self.settings (e.g., save_settings(self.settings))
        # For now, log that this functionality is pending
        # Ensure logger is imported if not already: import logging; logger = logging.getLogger(__name__)
        logger.info("GUI changes are not yet saved to CPS V4 settings. _update_parameters_from_widgets needs full V4 implementation.")
        pass
    
    def run_backtest(self):
        """Run backtest with V3 parameter system."""
        try:
            # Update parameters from widgets
            self._update_parameters_from_widgets()
            
            # Create output directories
            output_dir = Path(project_root) / 'output' / 'v3_test'
            os.makedirs(output_dir, exist_ok=True)
            
            # Get debug options
            debug = self.debug_checkbox.isChecked()
            debug_paramflow = self.debug_paramflow_checkbox.isChecked()
            track_ema_calcs = self.track_ema_checkbox.isChecked()
            
            # Show a message that the backtest is running
            QMessageBox.information(self, "Backtest Started", 
                "Running backtest with V3 parameter system. This may take a few minutes. Check the console for progress.")
            
            # Import the test_ema_v3 module and run backtest
            import importlib.util
            spec = importlib.util.spec_from_file_location(
                "test_ema_v3", 
                os.path.join(project_root, "test_ema_v3.py")
            )
            test_module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(test_module)
            
            # Run the V3 backtest with GUI parameters
            from run_v3_backtest_with_metrics import run_v3_backtest_with_metrics
            
            results = run_v3_backtest_with_metrics(
                debug=debug, 
                debug_paramflow=debug_paramflow, 
                track_ema_calcs=track_ema_calcs
                # v3_registry=self.v3_registry # Removed: V3 registry no longer used
            )
            
            # Show completion message with results path
            QMessageBox.information(self, "Backtest Complete", 
                f"Backtest completed successfully. Results saved to {output_dir}")
            
        except Exception as e:
            # Show error message
            import traceback
            error_msg = f"Error running backtest: {str(e)}\n\n{traceback.format_exc()}"
            QMessageBox.critical(self, "Error", error_msg)
            logger.error(error_msg)


if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = MainWindowV4()
    window.show()
    sys.exit(app.exec())
