@echo off
echo Starting phase 2 of project reorganization...
echo.

REM Run as administrator check
NET SESSION >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo This script requires administrator privileges.
    echo Please right-click and select "Run as administrator"
    pause
    exit /b 1
)

echo Creating remaining directory structure...
mkdir archive\special_tests 2>nul
mkdir archive\combined_docs 2>nul
mkdir archive\legacy_files\batch_backup 2>nul
mkdir archive\legacy_files\temp_backups 2>nul

mkdir data\v3 2>nul
mkdir data\v4 2>nul
mkdir output\v3 2>nul
mkdir output\v4 2>nul

mkdir docs\v3 2>nul
mkdir docs\v4 2>nul

mkdir v3\optimization 2>nul
mkdir v4\optimization 2>nul
mkdir v3\tools 2>nul
mkdir v4\tools 2>nul

echo.
echo Moving special_test files...
move /Y special_test\*.* archive\special_tests\ >nul 2>&1

echo Moving combined_docs files...
move /Y combined_docs\*.* archive\combined_docs\ >nul 2>&1

echo Moving batch_backup files...
move /Y batch_backup\*.* archive\legacy_files\batch_backup\ >nul 2>&1

echo Moving temp_backups files...
move /Y temp_backups\*.* archive\legacy_files\temp_backups\ >nul 2>&1

echo Moving data files...
move /Y data\*.v4.* data\v4\ >nul 2>&1
for %%f in (data\*.*) do (
    if not "%%~xf"==".v4" move /Y "%%f" data\v3\ >nul 2>&1
)

echo Moving output files...
move /Y output\*.v4.* output\v4\ >nul 2>&1
for %%f in (output\*.*) do (
    if not "%%~xf"==".v4" move /Y "%%f" output\v3\ >nul 2>&1
)

echo Moving documentation files...
move /Y docs\v3_*.* docs\v3\ >nul 2>&1
move /Y docs\v4_*.* docs\v4\ >nul 2>&1

echo Moving optimization files...
move /Y optimization\*.v4.* v4\optimization\ >nul 2>&1
for %%f in (optimization\*.*) do (
    if not "%%~xf"==".v4" move /Y "%%f" v3\optimization\ >nul 2>&1
)

echo Moving tools files...
move /Y tools\*.v4.* v4\tools\ >nul 2>&1
for %%f in (tools\*.*) do (
    if not "%%~xf"==".v4" move /Y "%%f" v3\tools\ >nul 2>&1
)

echo Cleaning up empty directories...
rmdir /S /Q combined_docs 2>nul
rmdir /S /Q batch_backup 2>nul
rmdir /S /Q temp_backups 2>nul
rmdir /S /Q special_test 2>nul
rmdir /S /Q v3_engine 2>nul
rmdir /S /Q v3_reporting 2>nul
rmdir /S /Q app 2>nul
rmdir /S /Q engine 2>nul
rmdir /S /Q models 2>nul
rmdir /S /Q reporting 2>nul
rmdir /S /Q visualization 2>nul
rmdir /S /Q utils 2>nul
rmdir /S /Q config 2>nul
rmdir /S /Q CPS_v4 2>nul

echo.
echo Phase 2 of project reorganization complete!
echo.
pause
