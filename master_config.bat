@echo off
REM ============================================================================
REM  MASTER CONFIGURATION - SET ACTIVE PC AND ENVIRONMENT PATHS
REM ============================================================================
REM
REM  Instructions:
REM  - Set the ACTIVE_PC variable to either "PC1" or "LAPTOP".
REM  - PC1 is the default.
REM
REM ============================================================================

SET ACTIVE_PC=PC1

REM --- Do not edit below this line ---

ECHO Active PC: %ACTIVE_PC%

IF /I "%ACTIVE_PC%"=="PC1" (
    ECHO Setting up environment for PC1...
    SET "VENV_PATH=F:\AI_Library\my_quant_env"
    SET "DROPBOX_ROOT=S:\Dropbox\Scott Only Internal"
) ELSE IF /I "%ACTIVE_PC%"=="LAPTOP" (
    ECHO Setting up environment for LAPTOP...
    SET "VENV_PATH=C:\my_qant_env_LT"
    SET "DROPBOX_ROOT=C:\Dropbox\Scott Only Internal"
) ELSE (
    ECHO ERROR: Unknown ACTIVE_PC value. Please set it to "PC1" or "LAPTOP".
    GOTO :EOF
)

REM Activate the virtual environment
ECHO Activating venv: %VENV_PATH%\Scripts\activate
CALL %VENV_PATH%\Scripts\activate

REM Set the project root and add it to the Python Path
SET "PROJECT_ROOT=%DROPBOX_ROOT%\Quant_Python_24\Backtest_FinAsset_Alloc_Template"
SET "PYTHONPATH=%PROJECT_ROOT%;%PYTHONPATH%"
ECHO Project Root: %PROJECT_ROOT%

ECHO Environment setup complete.
