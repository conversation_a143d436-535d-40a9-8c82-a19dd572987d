#!/usr/bin/env python
"""
Test Lean Matrix Optimization with Milestone Logging
"""

import sys
import os
sys.path.append('v4')

import pandas as pd
import numpy as np
from pathlib import Path
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s:%(name)s:%(message)s')
logger = logging.getLogger(__name__)

def test_lean_optimization():
    """Test the lean matrix optimization approach."""
    try:
        from reporting.performance_table_generator import PerformanceTableGenerator
        
        # Create generator
        gen = PerformanceTableGenerator('v4/settings/settings_parameters_v4.ini')
        
        # Test if method exists
        if hasattr(gen, 'get_optimization_combinations'):
            logger.info("✅ get_optimization_combinations method found")
            
            # Get combinations
            combinations = gen.get_optimization_combinations()
            logger.info(f"✅ Generated {len(combinations)} combinations")
            
            # Show parameter ranges being optimized
            if len(combinations) > 1:
                st_values = sorted(set(c.get('st_lookback', 0) for c in combinations))
                mt_values = sorted(set(c.get('mt_lookback', 0) for c in combinations))
                logger.info(f"[PARAMETERS] st_lookback: {st_values}")
                logger.info(f"[PARAMETERS] mt_lookback: {mt_values}")
            
            # Test matrix optimization with lean logging
            logger.info("=" * 60)
            logger.info("TESTING LEAN MATRIX OPTIMIZATION")
            logger.info("=" * 60)
            
            # Simulate the matrix optimization process
            test_matrix_optimization(gen, combinations)
            
            return True
            
        else:
            logger.error("❌ get_optimization_combinations method NOT found")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error testing optimization: {e}")
        return False

def test_matrix_optimization(gen, combinations):
    """Test matrix optimization with lean logging."""
    import time
    
    # Create sample date range
    date_range = pd.date_range('2020-01-01', '2024-12-31', freq='D')
    
    # Pre-allocate matrix
    combo_names = [f"combo_{i}" for i in range(len(combinations))]
    equity_matrix = pd.DataFrame(index=date_range, columns=combo_names, dtype=float)
    
    # MILESTONE: Start optimization
    logger.info(f"[OPTIMIZATION] Starting {len(combinations)} combinations...")
    
    start_time = time.time()
    completed = 0
    failed = 0
    progress_interval = max(1, len(combinations) // 10)  # Report every 10%
    
    # Simulate processing each combination
    for i, combo in enumerate(combinations):
        combo_name = combo_names[i]
        
        # Simulate pipeline execution time
        time.sleep(0.1)  # Simulate 0.1s per combination
        
        try:
            # Create unique equity curve for each combination
            base_value = 1000000
            # Use combination parameters to create different curves
            st_factor = combo.get('st_lookback', 15) / 15.0
            mt_factor = combo.get('mt_lookback', 70) / 70.0
            
            # Generate different returns based on parameters
            np.random.seed(i)  # Different seed for each combination
            daily_returns = np.random.normal(0.0005 * st_factor, 0.01 * mt_factor, len(date_range))
            equity_curve = base_value * (1 + daily_returns).cumprod()
            
            equity_matrix.iloc[:, i] = equity_curve
            completed += 1
            
        except Exception as e:
            equity_matrix.iloc[:, i] = np.nan
            failed += 1
        
        # MILESTONE: Progress reporting (lean)
        if (i + 1) % progress_interval == 0 or (i + 1) == len(combinations):
            elapsed = time.time() - start_time
            pct = ((i + 1) / len(combinations)) * 100
            
            if i + 1 < len(combinations):
                eta_seconds = (elapsed / (i + 1)) * (len(combinations) - (i + 1))
                eta = f"{eta_seconds/60:.0f}min" if eta_seconds > 60 else f"{eta_seconds:.0f}s"
                logger.info(f"[PROGRESS] {i+1}/{len(combinations)} ({pct:.0f}%) | Success: {completed} | Failed: {failed} | ETA: {eta}")
            else:
                duration = f"{elapsed/60:.0f}min" if elapsed > 60 else f"{elapsed:.0f}s"
                logger.info(f"[PROGRESS] {i+1}/{len(combinations)} (100%) | Success: {completed} | Failed: {failed} | Duration: {duration}")
    
    # MILESTONE: Save matrix
    matrix_file = Path("test_optimization_matrix.csv")
    equity_matrix.to_csv(matrix_file)
    
    # MILESTONE: Complete
    matrix_size = f"{len(combinations)} combinations × {len(date_range)} days"
    logger.info(f"[MATRIX] Saved equity matrix: {matrix_size} → {matrix_file}")
    
    # Verify results are different
    final_values = []
    for i in range(len(combinations)):
        final_value = equity_matrix.iloc[-1, i]
        final_values.append(final_value)
    
    unique_values = len(set(final_values))
    if unique_values == len(combinations):
        logger.info(f"[VALIDATION] ✅ All {len(combinations)} combinations produced different results")
    else:
        logger.warning(f"[VALIDATION] ❌ Only {unique_values}/{len(combinations)} unique results")
    
    # Cleanup
    matrix_file.unlink()
    logger.info("[CLEANUP] Test files removed")

if __name__ == "__main__":
    logger.info("=" * 60)
    logger.info("LEAN MATRIX OPTIMIZATION TEST")
    logger.info("=" * 60)
    
    success = test_lean_optimization()
    
    if success:
        logger.info("=" * 60)
        logger.info("✅ LEAN OPTIMIZATION TEST PASSED")
        logger.info("=" * 60)
    else:
        logger.info("=" * 60)
        logger.info("❌ LEAN OPTIMIZATION TEST FAILED")
        logger.info("=" * 60)
