"""
V3 Engine to Reporting Test Script

This script tests the data flow from the V3 engine to the reporting system,
with a focus on ensuring signal history and allocation data are properly
captured and passed to the reporting modules.

It creates detailed CSV and Excel files showing exactly what data is being
generated by the engine and passed to the reporting system.

FileName: test_v3_engine_reporting.py
"""

import os
import sys
import pandas as pd
import numpy as np
from pathlib import Path
import datetime
import json
import traceback
import logging
from unittest.mock import Mock

# Add project root to path
project_root = Path(__file__).parent
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

# Import centralized logging configuration
from v3_engine.logging_config import get_logger, LOG_LEVEL, TIMESTAMP

# Configure module logger
logger = get_logger(__name__)

# Store timestamp for file naming
timestamp = TIMESTAMP

# Import necessary modules
from config.paths import *
from config.config import config
from v3_engine.parameter_registry import ParameterRegistry, get_registry
from v3_engine.ema_v3_adapter import EMAV3Adapter
from engine.backtest import BacktestEngine
from utils.date_utils import standardize_dataframe_index
from v3_engine.debug_output import save_engine_output, analyze_engine_output
from v3_reporting.v3_allocation_report import generate_v3_allocation_report
from v3_engine.performance_reporter_adapter import PerformanceReporterAdapter

# Import data loading function
from app.gui.v3_gui_actions import load_data_for_backtest

from config.config_v3 import config_v3

class TestRegistry:
    """Full registry mock that implements required interface"""
    def __init__(self):
        self.parameters = {
            'st_lookback': 10,
            'mt_lookback': 50,
            'lt_lookback': 150,
            'top_n': 2,
            'actual_execution_delay': 1
        }
        # Add _registry attribute to fix PerformanceReporterAdapter compatibility
        self._registry = {
            'log_level': logging.INFO
        }
    
    def get_parameter_value(self, name):
        return self.parameters.get(name)
    
    def get(self, name):
        return self.parameters.get(name)
    
    def register_parameter(self, parameter):
        pass

def setup_v3_registry():
    """Initialize test registry"""
    registry = TestRegistry()
    logger.info("Initialized test parameter registry")
    return registry

class TestEMAAdapter(EMAV3Adapter):
    """Test adapter with working parameter access"""
    def generate_signal(self, price_data):
        # Access parameters through proper registry interface
        st = self.registry.get_parameter_value('st_lookback')
        mt = self.registry.get_parameter_value('mt_lookback')
        lt = self.registry.get_parameter_value('lt_lookback')
        top_n = self.registry.get_parameter_value('top_n')
        
        logger.info(f"Generating signals with params: {st}/{mt}/{lt}, top {top_n}")
        
        # Directly call the EMA allocation model to ensure we get proper signals
        from models.ema_allocation_model import ema_allocation_model_single
        
        # Validate price data before passing to model
        if price_data is None or price_data.empty:
            logger.error("Price data is None or empty in TestEMAAdapter")
            return {}
        
        logger.info(f"Price data shape in TestEMAAdapter: {price_data.shape}")
        logger.info(f"Price data columns: {price_data.columns.tolist()}")
        
        # Call the model directly with the parameters from registry
        weights = ema_allocation_model_single(
            price_data=price_data,
            st_lookback=st,
            mt_lookback=mt,
            lt_lookback=lt,
            top_n=top_n
        )
        
        # Log the generated weights
        logger.info(f"Generated weights in TestEMAAdapter: {weights}")
        
        # Validate weights
        if not weights:
            logger.error("EMA model returned empty weights in TestEMAAdapter")
        else:
            non_zero_weights = {k: v for k, v in weights.items() if v > 0}
            logger.info(f"Non-zero weights in TestEMAAdapter: {non_zero_weights}")
            logger.info(f"Total weight allocated: {sum(weights.values()):.4f}")
        
        return weights

def run_test():
    """Run the V3 engine reporting test."""
    try:
        # Get timestamp
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Create output directory
        output_dir = OUTPUT_DIR / f"v3_test_{timestamp}"
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # Setup V3 registry
        registry = setup_v3_registry()
        
        # Load data
        logger.info("Loading data for backtest")
        try:
            try:
                # Try to load real data first
                data_dict = load_data_for_backtest(config)
                price_data = standardize_dataframe_index(data_dict['price_data'])
                logger.info("Successfully loaded real price data")
            except FileNotFoundError as e:
                # Generate sample data if real data not found
                logger.warning(f"Real data file not found: {str(e)}")
                logger.info("Generating sample price data for testing")
                
                # Create sample date range for 2 years of daily data
                dates = pd.date_range(start='2023-01-01', end='2025-01-01', freq='B')
                
                # Create sample price data for 5 assets
                assets = ['SPY', 'TLT', 'GLD', 'QQQ', 'VTI']
                price_data = pd.DataFrame(index=dates)
                
                # Generate realistic price series with some trends
                np.random.seed(42)  # For reproducibility
                for asset in assets:
                    # Start with a base price
                    base_price = np.random.uniform(50, 200)
                    # Generate daily returns with some autocorrelation
                    daily_returns = np.random.normal(0.0003, 0.01, len(dates))
                    # Add some trend
                    trend = np.linspace(0, 0.2, len(dates)) if asset in ['SPY', 'QQQ', 'VTI'] else np.linspace(0, 0.1, len(dates))
                    daily_returns += trend
                    # Calculate cumulative returns
                    cum_returns = np.cumprod(1 + daily_returns)
                    # Calculate price series
                    price_data[asset] = base_price * cum_returns
                
                # Standardize the index
                price_data = standardize_dataframe_index(price_data)
                
                # Create a mock data_dict to match expected format
                data_dict = {
                    'price_data': price_data,
                    'returns_data': price_data.pct_change().dropna()
                }
                
                logger.info("Successfully generated sample price data")
            
            # Log data information
            logger.info(f"Price data shape: {price_data.shape}")
            logger.info(f"Price data columns: {price_data.columns.tolist()}")
            logger.info(f"Price data index range: {price_data.index[0]} to {price_data.index[-1]}")
            
            # Save price data for reference
            price_data.to_csv(output_dir / "price_data.csv")
            price_data.to_excel(output_dir / "price_data.xlsx")
            
            # Verify data quality
            if price_data.empty:
                error_msg = "Price data is empty"
                logger.error(error_msg)
                test_status["success"] = False
                test_status["errors"].append(error_msg)
                raise ValueError(error_msg)
                
            if price_data.isna().any().any():
                warning_msg = "Price data contains NaN values"
                logger.warning(warning_msg)
                test_status["warnings"].append(warning_msg)
        
        except Exception as e:
            if not isinstance(e, FileNotFoundError):  # We already handle FileNotFoundError above
                error_msg = f"Error loading data: {str(e)}"
                logger.error(error_msg)
                logger.error(traceback.format_exc())
                test_status["success"] = False
                test_status["errors"].append(error_msg)
                raise
        
        # Create test adapter
        logger.info("Creating test adapter")
        try:
            ema_adapter = TestEMAAdapter(registry)
            logger.info("Initialized EMA V3 Adapter with parameter registry")
        except Exception as e:
            error_msg = f"Error creating adapter: {str(e)}"
            logger.error(error_msg)
            logger.error(traceback.format_exc())
            test_status["success"] = False
            test_status["errors"].append(error_msg)
            raise
        
        # Initialize backtest engine
        logger.info("Initializing backtest engine")
        try:
            engine = BacktestEngine(
                initial_capital=100000.0,
                commission_rate=0.001,
                slippage_rate=0.001,
                config=config
            )
            logger.info(f"Initialized backtest engine with ${100000.0:,.2f} capital")
            logger.info(f"Commission rate: {0.001:.2%}")
            logger.info(f"Slippage rate: {0.001:.2%}")
        except Exception as e:
            error_msg = f"Error initializing backtest engine: {str(e)}"
            logger.error(error_msg)
            logger.error(traceback.format_exc())
            test_status["success"] = False
            test_status["errors"].append(error_msg)
            raise
        
        # Run backtest
        logger.info("Running backtest")
        try:
            logger.info("Starting backtest with monthly rebalancing")
            logger.info(f"Execution delay: {registry.get_parameter_value('actual_execution_delay')} days")
            
            results = engine.run_backtest(
                price_data=price_data,
                signal_generator=ema_adapter.generate_signal,
                rebalance_freq='monthly',
                execution_delay=registry.get_parameter_value('actual_execution_delay')
            )
            
            # Verify results structure
            required_keys = ['signal_history', 'weights_history', 'portfolio_values', 'trade_log']
            missing_keys = [key for key in required_keys if key not in results]
            
            if missing_keys:
                warning_msg = f"Results missing expected keys: {missing_keys}"
                logger.warning(warning_msg)
                test_status["warnings"].append(warning_msg)
        
        except Exception as e:
            error_msg = f"Error running backtest: {str(e)}"
            logger.error(error_msg)
            logger.error(traceback.format_exc())
            test_status["success"] = False
            test_status["errors"].append(error_msg)
            raise
        
        # Save and analyze engine output
        logger.info("Saving engine output")
        try:
            output_paths = save_engine_output(
                results=results,
                output_dir=output_dir,
                strategy_name="EMA_V3_Test",
                parameters=registry.parameters
            )
            
            # Verify output files were created
            for file_type, file_path in output_paths.items():
                path = Path(file_path)
                if not path.exists():
                    warning_msg = f"Output file not created: {file_type} at {file_path}"
                    logger.warning(warning_msg)
                    test_status["warnings"].append(warning_msg)
                else:
                    file_size = path.stat().st_size
                    if file_size < 100:  # Arbitrary small size threshold
                        warning_msg = f"Output file suspiciously small: {file_type} is {file_size} bytes"
                        logger.warning(warning_msg)
                        test_status["warnings"].append(warning_msg)
                    else:
                        logger.info(f"Created output file: {file_type} ({file_size} bytes)")
        
        except Exception as e:
            error_msg = f"Error saving engine output: {str(e)}"
            logger.error(error_msg)
            logger.error(traceback.format_exc())
            test_status["warnings"].append(error_msg)  # Warning not error, to continue test
        
        # Analyze engine output
        logger.info("Analyzing engine output")
        try:
            analysis = analyze_engine_output(results)
            
            # Save analysis to JSON file
            analysis_path = output_dir / "analysis_results.json"
            with open(analysis_path, 'w') as f:
                json.dump(analysis, f, indent=4, default=str)
            
            # Log analysis results
            logger.info(f"Analysis results saved to {analysis_path}")
            logger.info(f"Analysis found {len(analysis['issues'])} issues")
            
            for issue in analysis['issues']:
                logger.warning(f"Issue detected: {issue}")
                test_status["warnings"].append(issue)
            
            # Store analysis in verification results
            test_status["verification_results"]["engine_analysis"] = analysis
            
        except Exception as e:
            error_msg = f"Error analyzing engine output: {str(e)}"
            logger.error(error_msg)
            logger.error(traceback.format_exc())
            test_status["warnings"].append(error_msg)  # Warning not error, to continue test
        
        # Generate allocation report
        logger.info("Generating allocation report")
        try:
            signal_df = results.get('signal_history')
            weights_df = results.get('weights_history')
            
            # Verify signal history
            if signal_df is not None and not signal_df.empty:
                logger.info(f"Signal history shape: {signal_df.shape}")
                logger.info(f"Signal history sample (first 5 rows):\n{signal_df.head().to_string()}")
                test_status["verification_results"]["signal_history"] = {
                    "shape": signal_df.shape,
                    "columns": signal_df.columns.tolist(),
                    "index_range": [str(signal_df.index[0]), str(signal_df.index[-1])],
                    "status": "OK"
                }
            else:
                error_msg = "Signal history is None or empty"
                logger.error(error_msg)
                test_status["errors"].append(error_msg)
                test_status["verification_results"]["signal_history"] = {
                    "status": "MISSING"
                }
            
            # Verify weights history
            if weights_df is not None and not weights_df.empty:
                logger.info(f"Weights history shape: {weights_df.shape}")
                logger.info(f"Weights history sample (first 5 rows):\n{weights_df.head().to_string()}")
                test_status["verification_results"]["weights_history"] = {
                    "shape": weights_df.shape,
                    "columns": weights_df.columns.tolist(),
                    "index_range": [str(weights_df.index[0]), str(weights_df.index[-1])],
                    "status": "OK"
                }
            else:
                error_msg = "Weights history is None or empty"
                logger.error(error_msg)
                test_status["errors"].append(error_msg)
                test_status["verification_results"]["weights_history"] = {
                    "status": "MISSING"
                }
            
            # Only attempt to generate report if we have the required data
            if (signal_df is not None and not signal_df.empty) or \
               (weights_df is not None and not weights_df.empty):
                
                report_paths = generate_v3_allocation_report(
                    signal_df=signal_df,
                    allocation_df=weights_df,
                    output_dir=str(output_dir),
                    strategy_name='EMA_V3_Test',
                    portfolio_values=results.get('portfolio_values'),
                    include_cash=True,
                    parameters=registry.parameters
                )
                
                logger.info(f"Allocation report generated successfully")
                logger.info(f"Report paths: {report_paths}")
                
                # Verify report files were created
                for report_type, report_path in report_paths.items():
                    path = Path(report_path)
                    if not path.exists():
                        warning_msg = f"Report file not created: {report_type} at {report_path}"
                        logger.warning(warning_msg)
                        test_status["warnings"].append(warning_msg)
                    else:
                        file_size = path.stat().st_size
                        if file_size < 1000:  # Arbitrary small size threshold for reports
                            warning_msg = f"Report file suspiciously small: {report_type} is {file_size} bytes"
                            logger.warning(warning_msg)
                            test_status["warnings"].append(warning_msg)
                        else:
                            logger.info(f"Created report file: {report_type} ({file_size} bytes)")
                
                test_status["verification_results"]["allocation_report"] = {
                    "status": "GENERATED",
                    "paths": report_paths
                }
            else:
                error_msg = "Skipping allocation report generation due to missing data"
                logger.error(error_msg)
                test_status["errors"].append(error_msg)
                test_status["verification_results"]["allocation_report"] = {
                    "status": "SKIPPED"
                }
        
        except Exception as e:
            error_msg = f"Error generating allocation report: {str(e)}"
            logger.error(error_msg)
            logger.error(traceback.format_exc())
            test_status["errors"].append(error_msg)
            test_status["verification_results"]["allocation_report"] = {
                "status": "ERROR",
                "error": str(e)
            }
            
        # Generate performance report
        logger.info("Generating performance report")
        try:
            # Create performance reporter adapter
            perf_reporter = PerformanceReporterAdapter(registry=registry)
            
            # Generate the performance report
            report_path = perf_reporter.generate_performance_report(
                backtest_results=results,
                output_path=str(output_dir / "ema_performance_report.xlsx"),
                strategy_name="EMA_V3_Test",
                is_new_file=True
            )
            
            # Verify report file was created
            path = Path(report_path)
            if not path.exists():
                warning_msg = f"Performance report file not created at {report_path}"
                logger.warning(warning_msg)
                test_status["warnings"].append(warning_msg)
                test_status["verification_results"]["performance_report"] = {
                    "status": "MISSING"
                }
            else:
                file_size = path.stat().st_size
                if file_size < 1000:  # Arbitrary small size threshold for reports
                    warning_msg = f"Performance report file suspiciously small: {file_size} bytes"
                    logger.warning(warning_msg)
                    test_status["warnings"].append(warning_msg)
                else:
                    logger.info(f"Created performance report: {report_path} ({file_size} bytes)")
                
                test_status["verification_results"]["performance_report"] = {
                    "status": "GENERATED",
                    "path": report_path
                }
        
        except Exception as e:
            error_msg = f"Error generating performance report: {str(e)}"
            logger.error(error_msg)
            logger.error(traceback.format_exc())
            test_status["errors"].append(error_msg)
            test_status["verification_results"]["performance_report"] = {
                "status": "ERROR",
                "error": str(e)
            }
        
        # Save test status to JSON file
        status_path = output_dir / "test_status.json"
        with open(status_path, 'w') as f:
            json.dump(test_status, f, indent=4, default=str)
        
        # Log completion status
        if test_status["success"] and not test_status["errors"]:
            logger.info("Test completed successfully")
        else:
            logger.warning(f"Test completed with {len(test_status['errors'])} errors and {len(test_status['warnings'])} warnings")
        
        logger.info(f"All output files are in: {output_dir}")
        logger.info(f"Test status saved to: {status_path}")
    
    except Exception as e:
        # Catch any unhandled exceptions
        error_msg = f"Unhandled error in test: {str(e)}"
        logger.critical(error_msg)
        logger.critical(traceback.format_exc())
        test_status["success"] = False
        test_status["errors"].append(error_msg)
    
    # Return results dictionary
    return {
        "output_dir": str(output_dir) if 'output_dir' in locals() else None,
        "test_status": test_status,
        "output_paths": output_paths if 'output_paths' in locals() else None,
        "analysis": analysis if 'analysis' in locals() else None
    }

if __name__ == "__main__":
    try:
        results = run_test()
        
        # Safely access output directory
        output_dir = results.get('output_dir', 'Unknown location')
        print(f"\nTest completed. Results saved to: {output_dir}")
        
        # Create log file path for output
        log_file_path = os.path.join('logs', f'v3_engine_reporting_test_{timestamp}.log')
        print(f"Log file: {log_file_path}")
        
        # Safely check for analysis results
        analysis = results.get('analysis', {})
        issues = analysis.get('issues', [])
        
        if issues:
            print("\nAnalysis summary:")
            for issue in issues:
                print(f"- {issue}")
        else:
            print("\nNo analysis issues found")
            
        # Print test status summary
        test_status = results.get('test_status', {})
        if test_status:
            errors = test_status.get('errors', [])
            warnings = test_status.get('warnings', [])
            
            if errors:
                print("\nErrors:")
                for error in errors:
                    print(f"- {error}")
                    
            if warnings:
                print("\nWarnings:")
                for warning in warnings:
                    print(f"- {warning}")
    except Exception as e:
        print(f"\nError running test: {str(e)}")
        import traceback
        traceback.print_exc()
