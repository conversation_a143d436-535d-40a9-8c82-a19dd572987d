# Handoff 616 – CPS V4 Backtest & Data-Flow Debugging

_Time stamp: 2025-06-16 11:38 EDT_

## 1. Current Objective
Fix the V4 back-test engine so it can run end-to-end on ETF data, using CPS V4 settings as **single source of truth**. Confirm that:
* Price data can be **downloaded** (mode `Save`) and later **read** (mode `Read`).
* `BacktestEngine` instantiates successfully and produces `weights_history` vs `signal_history`.

## 2. Work Completed This Session
| Area | Actions | Result |
|------|---------|--------|
| **Blocking import errors** | • Added aggressive `sys.path` prepend in `simple_console_test.py`.<br>• Defined `INFO` marker.<br>• Moved logger init in `data_loader_v4.py`. | ✅ Resolved initial `ModuleNotFoundError`, `NameError` for `logger` / `INFO`. |
| **Settings – missing parameters** | Added `st_lookback`, then `mt_lookback` into `[ema_model]` section to unblock `ema_allocation_model_v4` import. | ✅ Import proceeds further. |
| **INI parsing bug** | Removed inline comment from `data_storage_mode` so it is parsed as simple string. | ✅ Eliminated `AttributeError: list has no attribute lower`. |
| **Parameter-duplication debate** | ⚠️ Introduced fallback logic in `ema_allocation_model_v4` so `st_lookback / mt_lookback / lt_lookback` are fetched from `[ema_model]` **or** `[Strategy]`. *This may conflict with original CPS V4 policy – needs confirmation.* |
| **Ultra-small data check** | Added `quick_data_validation.py` + `.bat` to validate early data-flow. | ⚠️ Still exits with error 1 – stack trace truncated; needs triage. |

## 3. Open Issues / Blockers
1. **Parameter Source-of-Truth** – Clarify whether lookback parameters should live solely in `[Strategy]` or `[ema_model]`. Current code path in `ema_allocation_model_v4` changed; may violate design.
2. **quick_data_validation failure** – The script exits early (likely import path or missing util module `utils.date_utils`). Needs log capture & fix.
3. **Backtest still not running** – `BacktestEngine` not created due to previous param issues; after clarification above, must retest full flow.
4. **Test script verbosity** – Batch + py logs often truncated; consider redirecting full output to file for easier debugging.

## 4. Suggested Next Steps
1. **Decision on lookback parameter location** – Revert duplicate removal **or** keep fallback code; align with CPS V4 documentation.
2. **Fix `quick_data_validation.py`** – Ensure all helper modules (`utils.date_utils`, `market_data`) are on `PYTHONPATH` or replace with inline logic.
3. **Run `simple_console_test.bat` again** after parameter decision to verify import chain.
4. **Switch `data_storage_mode` to `Read`** after initial successful download to speed iterative runs.
5. **Add explicit tests** for `settings_CPS_v4.load_settings()` to guarantee correct type conversion and parameter availability across sections.

## 5. Environment / Paths
* Project root: `S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template`
* Virtual env: `F:\AI_Library\my_quant_env`
* Key scripts: `v4/tests/simple_console_test.py`, `v4/tests/quick_data_validation.py`

## 6. Pending Questions
1. Confirm single-source-of-truth rule: where **exactly** should model-specific optimization parameters reside?
2. Should `ema_allocation_model_v4` read ComplexN dicts directly (`default_value`) or rely on pre-flattened numeric values?

---
**End of handoff 616**
