@echo off
echo Verifying Graphviz installation...
echo ================================

echo [1/3] Checking Graphviz in PATH...
where dot 2>nul
if %ERRORLEVEL% EQU 0 (
    echo ✓ Graphviz found in PATH
    echo.
    echo [2/3] Checking Graphviz version...
    dot -V || echo ❌ Could not get Graphviz version
) else (
    echo ❌ Graphviz not found in PATH
    echo Please make sure to check "Add to PATH" during installation
    pause
    exit /b 1
)

echo.
echo [3/3] Testing Python graphviz package...
call F:\AI_Library\my_quant_env\Scripts\activate.bat
python -c "import graphviz; print('✓ Python graphviz package is working')" 2>nul || (
    echo ❌ Python graphviz package not found
    echo Installing...
    pip install graphviz
)

echo.
echo ================================
echo Verification complete!
pause
