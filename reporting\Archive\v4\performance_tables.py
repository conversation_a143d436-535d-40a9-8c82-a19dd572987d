"""
Performance table generation module for CPS v4.
Contains functions for creating performance tables and formatting Excel output.
"""

import pandas as pd
import numpy as np
from typing import Optional, Dict, Union, List
from datetime import datetime
from .performance_metrics import (
    calculate_performance_metrics,
    get_calendar_year_returns
)

def _col_idx_to_letter(idx: int) -> str:
    """Convert a column index to Excel column letter."""
    result = ""
    while idx > 0:
        idx, rem = divmod(idx-1, 26)
        result = chr(65 + rem) + result
    return result

def create_performance_table(
    returns: pd.Series,
    benchmark_returns: Optional[pd.Series] = None,
    strategy_params: Optional[Dict] = None,
    benchmark_only: bool = False
) -> pd.DataFrame:
    """
    Create a performance table with strategy parameters and performance metrics.
    
    Layout:
    Left side: Strategy parameters (st_lookback, mt_lookback, lt_lookback, top_n positions, execution delay)
    Right side: Performance metrics (CAGR, Sharpe, Sortino, Max Drawdown, etc.)
    
    Args:
        returns: Series of strategy returns
        benchmark_returns: Series of benchmark returns
        strategy_params: Strategy parameters
        benchmark_only: If True, only include benchmark metrics
        
    Returns:
        DataFrame: Performance table
    """
    # Calculate performance metrics
    metrics = calculate_performance_metrics(returns, benchmark_returns=benchmark_returns)
    
    # Create table structure
    table_data = []
    
    # Add strategy parameters if provided
    if strategy_params and not benchmark_only:
        for param, value in strategy_params.items():
            if param in ['st_lookback', 'mt_lookback', 'lt_lookback', 'top_n', 'execution_delay']:
                table_data.append(['Parameter', param, 'Value', value])
        table_data.append(['', '', '', ''])  # Empty row for spacing
    
    # Add performance metrics
    if not benchmark_only:
        table_data.extend([
            ['Strategy Metrics', '', '', ''],
            ['CAGR', f"{metrics['cagr']*100:.2f}%", '', ''],
            ['Sharpe Ratio', f"{metrics['sharpe']:.2f}", '', ''],
            ['Sortino Ratio', f"{metrics['sortino']:.2f}", '', ''],
            ['Max Drawdown', f"{metrics['max_drawdown']*100:.2f}%", '', ''],
            ['Volatility', f"{metrics['volatility']*100:.2f}%", '', ''],
            ['Total Return', f"{metrics['total_return']*100:.2f}%", '', ''],
            ['YTD Return', f"{metrics.get('ytd', 0)*100:.2f}%", '', '']
        ])
    
    # Add benchmark metrics if available
    if benchmark_returns is not None:
        table_data.extend([
            ['', '', '', ''],  # Empty row for spacing
            ['Benchmark Metrics', '', '', ''],
            ['CAGR', f"{metrics['benchmark_cagr']*100:.2f}%", '', ''],
            ['Sharpe Ratio', f"{metrics['benchmark_sharpe']:.2f}", '', ''],
            ['Sortino Ratio', f"{metrics['benchmark_sortino']:.2f}", '', ''],
            ['Max Drawdown', f"{metrics['benchmark_max_drawdown']*100:.2f}%", '', ''],
            ['Volatility', f"{metrics['benchmark_volatility']*100:.2f}%", '', ''],
            ['Total Return', f"{metrics['benchmark_total_return']*100:.2f}%", '', ''],
            ['YTD Return', f"{metrics.get('benchmark_ytd', 0)*100:.2f}%", '', '']
        ])
        
        # Add relative metrics
        if not benchmark_only:
            table_data.extend([
                ['', '', '', ''],  # Empty row for spacing
                ['Relative Metrics', '', '', ''],
                ['Tracking Error', f"{metrics['tracking_error']*100:.2f}%", '', ''],
                ['Information Ratio', f"{metrics['information_ratio']:.2f}", '', '']
            ])
    
    # Create DataFrame
    df = pd.DataFrame(table_data, columns=['Metric', 'Value', 'Param', 'ParamValue'])
    
    return df
