#!/usr/bin/env python
# super_simple_config_reader.py
"""
Super simple config reader: loads CPS v4 settings and prints them.
"""
import sys
from pathlib import Path

# Step 1: add project root to sys.path
project_root = Path(__file__).resolve().parent.parent.parent  # Goes up to project root (Backtest_FinAsset_Alloc_Template)
print(f"super_simple_config_reader.py: PROJECT_ROOT set to: {project_root}")
sys.path.insert(0, str(project_root))

# Step 2: import and load settings
from v4.settings.settings_CPS_v4 import load_settings
settings = load_settings()

# Step 3: print settings dictionary
print(settings)
