"""
Parameter Groups Utility for CPS v4

This module provides utilities for working with parameter groups defined in the Lists section
of settings_parameters_v4.ini. It enables organized parameter access by functional groups
rather than relying on INI section organization.

Usage:
    from v4.settings.parameter_groups import ParameterGroups
    
    groups = ParameterGroups()
    optimization_params = groups.get_group('OptimizationParams')
    core_params = groups.get_group('CoreParams')
"""

import os
from typing import List, Dict, Any, Optional
from v4.settings.config_helper import ConfigHelper


class ParameterGroups:
    """Utility class for working with parameter groups defined in the Lists section."""
    
    def __init__(self, config_path: Optional[str] = None):
        """
        Initialize parameter groups utility.
        
        Args:
            config_path: Optional path to settings file. If None, uses default.
        """
        if config_path is None:
            config_path = os.path.join(
                os.path.dirname(__file__), 
                'settings_parameters_v4.ini'
            )
        
        self.helper = ConfigHelper(config_path)
        self._groups_cache = {}
        self._load_groups()
    
    def _load_groups(self):
        """Load all parameter groups from the Lists section."""
        try:
            # Get all parameter group definitions (Config<PERSON><PERSON><PERSON> uses lowercase keys)
            group_definitions = {
                'OptimizationParams': self.helper.get('optimizationparams', fallback=''),
                'CoreParams': self.helper.get('coreparams', fallback=''),
                'StrategyParams': self.helper.get('strategyparams', fallback=''),
                'BacktestParams': self.helper.get('backtestparams', fallback=''),
                'PerformanceParams': self.helper.get('performanceparams', fallback=''),
                'SystemParams': self.helper.get('systemparams', fallback=''),
                'DataParams': self.helper.get('dataparams', fallback=''),
            }
            
            # Parse each group definition
            for group_name, definition in group_definitions.items():
                if definition:
                    # Parse comma-separated parameter names
                    params = [param.strip() for param in definition.split(',')]
                    self._groups_cache[group_name] = params
                    
        except Exception as e:
            print(f"Warning: Could not load parameter groups: {e}")
    
    def get_group(self, group_name: str) -> List[str]:
        """
        Get list of parameters in a specific group.
        
        Args:
            group_name: Name of the parameter group
            
        Returns:
            List of parameter names in the group
        """
        return self._groups_cache.get(group_name, [])
    
    def get_all_groups(self) -> Dict[str, List[str]]:
        """
        Get all parameter groups.
        
        Returns:
            Dictionary mapping group names to parameter lists
        """
        return self._groups_cache.copy()
    
    def get_group_for_parameter(self, param_name: str) -> Optional[str]:
        """
        Find which group a parameter belongs to.
        
        Args:
            param_name: Name of the parameter to find
            
        Returns:
            Group name if found, None otherwise
        """
        for group_name, params in self._groups_cache.items():
            if param_name in params:
                return group_name
        return None
    
    def get_parameters_by_groups(self, group_names: List[str]) -> Dict[str, Any]:
        """
        Get parameter values for multiple groups.
        
        Args:
            group_names: List of group names to retrieve
            
        Returns:
            Dictionary mapping parameter names to their values
        """
        result = {}
        
        for group_name in group_names:
            params = self.get_group(group_name)
            for param_name in params:
                try:
                    # Try to get the parameter value
                    value = self.helper.get(param_name, fallback=None)
                    if value is not None:
                        result[param_name] = value
                except Exception:
                    # Skip parameters that can't be retrieved
                    continue
                    
        return result
    
    def validate_groups(self) -> Dict[str, List[str]]:
        """
        Validate that all parameters in groups actually exist in the configuration.
        
        Returns:
            Dictionary mapping group names to lists of missing parameters
        """
        missing = {}
        
        for group_name, params in self._groups_cache.items():
            missing_params = []
            for param_name in params:
                try:
                    value = self.helper.get(param_name, fallback=None)
                    if value is None:
                        missing_params.append(param_name)
                except Exception:
                    missing_params.append(param_name)
            
            if missing_params:
                missing[group_name] = missing_params
                
        return missing


# Convenience functions for common use cases
def get_optimization_parameters() -> List[str]:
    """Get list of optimization parameters."""
    groups = ParameterGroups()
    return groups.get_group('OptimizationParams')


def get_core_parameters() -> List[str]:
    """Get list of core parameters."""
    groups = ParameterGroups()
    return groups.get_group('CoreParams')


def get_strategy_parameters() -> List[str]:
    """Get list of strategy parameters."""
    groups = ParameterGroups()
    return groups.get_group('StrategyParams')


def validate_parameter_groups() -> Dict[str, List[str]]:
    """Validate all parameter groups and return any missing parameters."""
    groups = ParameterGroups()
    return groups.validate_groups()


if __name__ == "__main__":
    # Example usage and validation
    groups = ParameterGroups()
    
    print("Available Parameter Groups:")
    for group_name, params in groups.get_all_groups().items():
        print(f"  {group_name}: {params}")
    
    print("\nValidation Results:")
    missing = groups.validate_groups()
    if missing:
        for group_name, missing_params in missing.items():
            print(f"  {group_name}: Missing {missing_params}")
    else:
        print("  All parameter groups are valid!")
