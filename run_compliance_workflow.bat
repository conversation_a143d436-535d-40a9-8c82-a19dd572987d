@echo off
echo Running Compliance Workflow for CPS V4 Reporting
echo ==============================================

:: Activate virtual environment
call F:\AI_Library\my_quant_env\Scripts\activate.bat

:: Set project root
set PROJECT_ROOT=s:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template

:: Set Python path
set PYTHONPATH=%PROJECT_ROOT%

:: Run the compliance workflow
python %PROJECT_ROOT%\v4_reporting\run_compliance_workflow.py

echo.
echo Compliance workflow complete. See results in v4_reporting\compliance_workflow folder.
echo.
