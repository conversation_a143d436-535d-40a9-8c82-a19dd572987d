"""test_all_parameters_loaded.py

Validates that every parameter defined in the CPS V4 settings file is successfully
loaded, type-converted, and available for downstream modules.  This is an early
sanity-check before deeper integration tests.

Outputs a simple text results file in v4_reporting/test_output so the existing
batch script can append the results to its consolidated log.
"""
from __future__ import annotations

"""Parameter-loader sanity check.

IMPORTANT:  We manipulate `sys.path` **only** for this test so that it runs
stand-alone regardless of how the batch file launches Python (sometimes the
working directory is not auto-added on Windows when using `Start-Process`).
This avoids brittle environment tweaks in the main code.
"""

import json
import sys
import traceback
from pathlib import Path
from typing import Any, Dict

# Ensure project root is on import path (idempotent)
PROJECT_ROOT = Path(__file__).resolve().parents[1]
if str(PROJECT_ROOT) not in sys.path:
    sys.path.insert(0, str(PROJECT_ROOT))

# Import after path fix
try:
    from v4.settings.settings_CPS_v4 import load_settings, get_parameter_type  # type: ignore
except Exception as exc:  # pragma: no cover – critical path
    print(f"ERROR: failed to import settings loader: {exc}")
    traceback.print_exc()
    sys.exit(1)

ROOT_DIR = Path(__file__).resolve().parents[1]
OUTPUT_DIR = ROOT_DIR / "v4_reporting" / "test_output"
OUTPUT_DIR.mkdir(parents=True, exist_ok=True)
RESULTS_FILE = OUTPUT_DIR / "test_all_params_results.txt"
ERROR_FILE = OUTPUT_DIR / "error_log.txt"


def validate_settings() -> Dict[str, Any]:
    """Load settings and perform validation. Returns a dict with counts."""
    settings = load_settings()

    # Track counts and failures
    total_params = 0
    failures: list[str] = []

    for section, params in settings.items():
        # Skip [lists] pseudo-section which holds named lists
        if section.lower() == "lists":
            continue

        for key, value in params.items():
            total_params += 1
            # Ensure no empty strings / None
            if value in (None, ""):
                failures.append(f"{section}.{key} is empty or None")
                continue

            # Use helper to detect type – must not raise
            try:
                _ = get_parameter_type(value)
            except Exception as exc:  # type: ignore[broad-except]
                failures.append(f"{section}.{key} type detection error: {exc}")

    return {
        "total": total_params,
        "failures": failures,
    }


def main() -> None:  # pragma: no cover
    summary = validate_settings()
    failures = summary["failures"]

    with RESULTS_FILE.open("w", encoding="utf-8") as fh:
        fh.write("V4 Parameter Validation Results\n")
        fh.write("================================\n")
        fh.write(f"Total parameters checked: {summary['total']}\n\n")

        if not failures:
            fh.write("ALL PARAMETERS LOADED SUCCESSFULLY\n")
            print("All parameters loaded successfully.")
        else:
            fh.write("Failures detected:\n")
            for item in failures:
                fh.write(f" - {item}\n")
            print(f"Parameter validation failed – {len(failures)} issue(s).")

    if failures:
        # Append traceback to shared error file for batch log to pick up
        with ERROR_FILE.open("a", encoding="utf-8") as ef:
            ef.write("Parameter validation failures:\n")
            for item in failures:
                ef.write(f"{item}\n")
            ef.write("\n")
        sys.exit(1)


if __name__ == "__main__":
    main()
