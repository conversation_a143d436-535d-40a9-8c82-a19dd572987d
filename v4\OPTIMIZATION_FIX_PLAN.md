# OPTIMIZATION FIX PLAN

## Problem Identified
The EMA allocation model ignores the optimization flags in settings and uses fallback logic instead of flag-based parameter selection.

## Current WRONG Structure:
```python
# Module loads defaults once at import
st_lookback = _initialize_module_parameter('st_lookback', int)

# Function uses fallback logic
effective_st_lookback = st_lookback if st_lookback is not None else globals()['st_lookback']
```

## Required CORRECT Structure:

### Step 1: Load Parameter Metadata (not just defaults)
```python
def load_parameter_metadata():
    """Load complete parameter metadata including optimization flags"""
    settings = load_settings()
    param_metadata = {}
    
    for param_name in ['st_lookback', 'mt_lookback', 'lt_lookback']:
        raw_param = settings['strategy'][param_name]  # ComplexN dict
        param_metadata[param_name] = {
            'optimize': raw_param['optimize'],         # True/False flag
            'default_value': raw_param['default_value'] # Default value
        }
    return param_metadata
```

### Step 2: Flag-Based Parameter Selection 
```python
def get_effective_parameter(param_name, override_value, param_metadata):
    """Get effective parameter based on optimization flag"""
    metadata = param_metadata[param_name]
    
    if metadata['optimize']:
        # optimize=True: MUST use override (fail if None)
        if override_value is None:
            raise ValueError(f"Parameter {param_name} has optimize=True but no override provided")
        return override_value
    else:
        # optimize=False: ALWAYS use default_value (ignore override)
        return metadata['default_value']
```

### Step 3: Clean Model Function
```python
def ema_allocation_model(price_data, st_lookback=None, mt_lookback=None, lt_lookback=None, **params):
    # Load parameter metadata once per call
    param_metadata = load_parameter_metadata()
    
    # Use flag-based selection (NO FALLBACKS)
    effective_st = get_effective_parameter('st_lookback', st_lookback, param_metadata)
    effective_mt = get_effective_parameter('mt_lookback', mt_lookback, param_metadata) 
    effective_lt = get_effective_parameter('lt_lookback', lt_lookback, param_metadata)
    
    # Continue with model logic using effective parameters
```

## Implementation Steps:
1. Replace fallback logic with flag-based parameter selection
2. Add parameter metadata loading function
3. Add clean parameter selection function  
4. Update main model function to use new pattern
5. Test with optimization loop

## Key Principles:
- **Optimization flag is the SINGLE source of truth**
- **NO fallback chains or backup logic**
- **Direct flag-based parameter selection only**
- **Hard fail on missing required parameters**
