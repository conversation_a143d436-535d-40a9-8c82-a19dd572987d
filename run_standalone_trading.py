"""
run_standalone_trading.py
Standalone trading phase execution script for the CPS V4 system.

This script takes a pre-computed signal file path as input and runs only the trading phase
without requiring signal generation. It's designed for testing and validation scenarios
where signal files already exist.

Usage:
    python run_standalone_trading.py [signals_file_path]
    
    If signals_file_path is not provided, it will look for the default
    v4_trace_outputs/signals_output.parquet file.
"""

import os
import sys
from pathlib import Path
import logging
from datetime import datetime
import argparse

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# Add project root to path
_script_path = Path(__file__).resolve()
_project_root = _script_path.parent
if str(_project_root) not in sys.path:
    sys.path.insert(0, str(_project_root))

def log_message(message, level="INFO"):
    """Centralized logging function to standardize output format.
    Levels: INFO, WARNING, ERROR, CR<PERSON><PERSON><PERSON>, MILESTONE
    """
    timestamp = datetime.now().strftime("%H:%M:%S")
    prefix = f"[{level}] {timestamp} -"
    print(f"{prefix} {message}")
    sys.stdout.flush()  # Ensure output is immediately written

def run_standalone_trading(signals_file=None):
    """
    Run only the trading phase using a pre-computed signal file.
    
    Args:
        signals_file (str, optional): Path to signals file. If not provided,
                                     will use the default file.
    
    Returns:
        int: Exit code. 0 for success, non-zero for failure.
    """
    log_message("--- Starting Standalone Trading Phase ---", "MILESTONE")
    
    # Import trading phase function
    try:
        from v4.run_trading_phase import run_trading_phase
        log_message("Imported trading phase module successfully.")
    except ImportError as e:
        log_message(f"Failed to import trading phase module: {e}", "CRITICAL")
        return 1
    
    # Verify signals file exists
    if signals_file is None:
        # Use the correct CSV file with proper allocations summing to 100%
        signals_file = os.path.join(_project_root, "v4_trace_outputs", "signals_output_20250622_192844.csv")
        log_message(f"No signals file provided. Using correct CSV signals: {signals_file}")
    else:
        signals_file = os.path.abspath(signals_file)
        log_message(f"Using provided signals file: {signals_file}")
    
    signals_path = Path(signals_file)
    if not signals_path.exists():
        log_message(f"Signals file does not exist: {signals_file}", "CRITICAL")
        return 1
    
    # Log file type and check extension
    file_ext = signals_path.suffix.lower()
    log_message(f"Signal file type: {file_ext}")
    
    if file_ext not in ['.parquet', '.csv']:
        log_message(f"Warning: Unexpected file extension {file_ext}. Expected .parquet or .csv", "WARNING")
    
    # Run trading phase
    try:
        log_message("Executing trading phase...", "MILESTONE")
        results = run_trading_phase(signals_file)
        
        if results:
            log_message("Trading phase completed successfully.", "MILESTONE")
            return 0
        else:
            log_message("Trading phase returned no results.", "ERROR")
            return 1
            
    except Exception as e:
        log_message(f"Trading phase failed with error: {e}", "CRITICAL")
        # Print full traceback
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Run standalone trading phase')
    parser.add_argument('signals_file', nargs='?', default=None, 
                        help='Path to the signals file (CSV or Parquet)')
    args = parser.parse_args()
    
    # Run trading phase
    exit_code = run_standalone_trading(args.signals_file)
    sys.exit(exit_code)
