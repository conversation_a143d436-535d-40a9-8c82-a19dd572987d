# V3 Module Mapping Matrix

This document provides a detailed mapping between V2 and V3 modules, showing how each V2 component is represented, enhanced, or replaced in the V3 architecture. The mapping helps ensure that all existing capabilities are maintained in the V3 transition.

## Implementation Status

This document distinguishes between modules that are currently implemented and those that are planned for future implementation:

- ✓ Fully implemented and in use
- ⚠️ Partially implemented or in progress
- 🔄 Planned for future implementation

## Module Mapping Table

| V2 Module                              | V3 Module                                                 | Status | Key Functions                                                      | Description of Changes                                                         |
| -------------------------------------- | --------------------------------------------------------- | ------ | ------------------------------------------------------------------ | ------------------------------------------------------------------------------ |
| `config/config_v2.py`                  | `v3_engine/parameter_registry.py`<br/>`config/config_v3.py` | ✓      | `register_parameter()`, `get_parameter()`, `get_parameter_values()` | Parameter tuples replaced by type-safe parameter objects in a central registry |
| `config/parameter_optimization.py`     | `v3_engine/parameter_optimizer.py`                        | ✓      | `get_optimization_combinations()`, `optimize_parameters()`          | Enhanced with support for categorical parameters and execution_delay fixes     |
| `engine/backtest.py`                   | (unchanged)                                               | ✓      | `run_backtest()`, `calculate_metrics()`                            | No direct changes, becomes a consumer of parameters from registry              |
| `engine/portfolio.py`                  | (unchanged)                                               | ✓      | `update_portfolio()`, `calculate_portfolio_value()`                | No direct changes, becomes a consumer of parameters from registry              |
| `engine/orders.py`                     | (unchanged)                                               | ✓      | `create_order()`, `execute_order()`                               | No direct changes, becomes a consumer of parameters from registry              |
| `engine/execution.py`                  | (unchanged)                                               | ✓      | `execute_trades()`, `apply_slippage()`                            | No direct changes, becomes a consumer of parameters from registry              |
| `engine/allocation.py`                 | (unchanged)                                               | ✓      | `compare_positions()`, `generate_orders()`                        | No direct changes, becomes a consumer of parameters from registry              |
| `models/ema_allocation_model.py`       | `v3_engine/ema_v3_adapter.py`                             | ✓      | `generate_signal()`, `calculate_ema_metrics()`                    | Adapter pattern for EMA model with V3 parameters                              |
| `performance/performance_reporting.py` | `v3_engine/performance_reporter_adapter.py`               | ✓      | `adapt_parameters()`, `convert_metrics()`                         | Adapter pattern maintains existing output format                               |
| `config_interface.py`                  | `v3_engine/gui_parameter_manager.py`                      | ✓      | `create_parameter_widgets()`, `update_parameters_from_widgets()`   | Enhanced GUI integration                                                       |
| (New)                                  | `app/gui/v3_gui_core.py`                                  | ✓      | `MainWindowV3.__init__()`, `_run_backtest()`                      | Main GUI window implementation for V3                                          |

## Module Function Reference

### Core Engine Modules (Implemented)

| Module                                      | Key Functions                                                  | Status | Description                               |
| ------------------------------------------- | -------------------------------------------------------------- | ------ | ----------------------------------------- |
| `v3_engine/parameter_registry.py`           | `register_parameter()`, `get_parameter()`, `get_parameter_values()` |       | Central parameter registry                |
| `v3_engine/parameter_optimizer.py`          | `get_optimization_combinations()`, `optimize_parameters()`     |       | Parameter optimization                    |
| `v3_engine/performance_reporter_adapter.py` | `generate_performance_report()` |       | Reporting adapter |
| `v3_engine/V3_perf_repadapt_legacybridge.py` | `convert_legacy_parameters()` |       | Legacy bridge functions |
| `v3_engine/V3_perf_repadapt_paramconvert.py` | `convert_parameter_tuple()` |       | Parameter conversion utilities |       | Bridges V3 parameters to legacy reporting |
| `v3_engine/data_validator.py`               | `validate_data()`, `check_missing()`                           |       | Data quality validation                   |
| `v3_engine/ema_v3_adapter.py`               | `generate_signal()`, `calculate_ema_metrics()`                 |       | EMA strategy adapter for V3               |
| `v3_engine/strategy_parameter_set.py`       | `register_parameters()`, `get_parameter_values()`              |       | Strategy parameter container              |
| `v3_engine/parameters.py`                   | `NumericParameter.__init__()`, `CategoricalParameter.validate()` |       | Parameter class definitions              |
| `v3_engine/gui_parameter_manager.py`        | `create_parameter_widgets()`, `update_parameters_from_widgets()` |       | GUI parameter management                 |

### Core Engine Modules (Planned)

| Module                                      | Key Functions                                                  | Status | Description                               |
| ------------------------------------------- | -------------------------------------------------------------- | ------ | ----------------------------------------- |
| `v3_engine/data_quality_metrics.py`         | `calculate_quality_score()`, `generate_quality_report()`       |       | Data quality scoring                      |
| `v3_engine/exception_handler.py`            | `handle_error()`, `log_exception()`, `recover_from_error()`    |       | Unified error handling                    |

### Reporting Modules (Implemented)

| Module                                  | Key Functions                                             | Status | Description                   |
| --------------------------------------- | --------------------------------------------------------- | ------ | ----------------------------- |
| `v3_reporting/v3_performance_report.py` | `generate_v3_performance_report()`                        |       | Performance report generation |
| `v3_reporting/v3_allocation_report.py`  | `generate_v3_allocation_report()`                         |       | Allocation report generation  |
| `v3_reporting/reporting_parameters.py` | `register_reporting_parameters()` |       | Reporting parameter definitions |
| `v3_reporting/visualization_parameters.py` | `register_visualization_parameters()` |       | Visualization parameter definitions |
| `v3_reporting/parameter_registry_integration.py` | `register_all_reporting_parameters()`, `get_reporting_parameters()` |       | Parameter registry integration |
| `v3_reporting/v3_performance_charts.py` | `create_return_chart()`, `create_drawdown_chart()` |       | Performance chart generation |
| `v3_reporting/v3_trade_log.py`          | `format_trade_log()`, `summarize_trades()`                |       | Trade log formatting and export |

### Strategy Modules

#### Implemented

| Module                                | Key Functions                                                       | Status | Description                       |
| ------------------------------------- | ------------------------------------------------------------------- | ------ | --------------------------------- |
| `v3_engine/ema_v3_adapter.py`         | `generate_signal()`, `calculate_ema_metrics()`                      |       | EMA strategy adapter for V3        |

#### Planned

| Module                                | Key Functions                                                       | Status | Description                       |
| ------------------------------------- | ------------------------------------------------------------------- | ------ | --------------------------------- |
| `v3_strategies/ema_strategy.py`       | `generate_signals()`, `rebalance()`, `calculate_weights()`          |       | EMA strategy implementation       |
| `v3_strategies/strategy_discovery.py` | `load_strategies()`, `validate_strategy()`, `get_strategy_params()` |       | Dynamic strategy loading          |
| `v3_strategies/strategy_validator.py` | `validate_config()`, `check_parameters()`, `verify_compatibility()` |       | Strategy configuration validation |

## Verification System

| Module | Key Functions | Status | Description |
|--------|---------------|:------:|-------------|
| `tests/verify_v3_reporting.py` | `setup_test_environment()`, `run_all_tests()` |       | Main verification script |
| `tests/test_v3_reporting.py` | `test_parameter_registration()`, `test_performance_report_generation()` |       | End-to-end test script |
| `verify_v3_reporting.py` (legacy) | `verify_file_size()`, `verify_excel_file()` |       | Outdated verification script |

### Verification Process

The verification system has been refactored to run in an isolated test environment using the following process:

1. `run_v3_verification.bat` sets environment variables and activates the Python environment
2. `tests/verify_v3_reporting.py` performs verification in an isolated test environment
3. Verification results are logged and a report is generated

## Recent Changes (June 2025)

1. **Parameter System**:
   - Added validation for execution_delay parameter
   - Fixed optimization handling for categorical parameters
   - Identified parameter_registry.py as exceeding 450-line limit

2. **Reporting**:
   - Refactored verification system into tests directory
   - Added parameter registry integration module
   - Split reporting and visualization parameters into separate modules
   - Enhanced adapter pattern with legacy bridge components

3. **GUI**:
   - Added parameter group visibility controls
   - Improved parameter widget layout
   - Fixed stalling issues in backtest execution

## Implementation Plans

### Phase 1: Core Parameter System

- Implement parameter classes and registry 
- Create strategy parameter set framework 
- Design GUI parameter manager 

### Phase 2: Integration Layer

- Develop performance reporter adapter 
- Create configuration conversion utilities
- Implement ticker list configuration 

### Phase 3: Strategy Refactoring

- Refactor EMA strategy to use StrategyParameterSet
- Create new strategy implementations
- Test plug-and-play capability

### Phase 4: GUI Integration

- Integrate GUI parameter manager with existing interface
- Implement strategy selector in GUI
- Add parameter optimization controls

### Phase 5: Engine Integration

- Update backtest engine to use registry for parameters
- Ensure full backward compatibility
- Comprehensive testing

## Testing Strategy

1. Unit tests for each V3 component
2. Integration tests for parameter flow
3. Specific tests for execution_delay parameter optimization
4. Backward compatibility tests with V2 configurations
5. Performance comparison between V2 and V3 implementations
