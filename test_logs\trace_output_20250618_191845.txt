2025-06-18 19:18:50,458 - INFO - Successfully loaded settings for ema_allocation_model_v4.
Initial PYTHONPATH: None
Initial sys.path: ['S:\\Dropbox\\Scott Only Internal\\Quant_Python_24\\Backtest_FinAsset_Alloc_Template', 'C:\\Python313\\python313.zip', 'C:\\Python313\\DLLs', 'C:\\Python313\\Lib', 'C:\\Python313', 'F:\\AI_Library\\my_quant_env', 'F:\\AI_Library\\my_quant_env\\Lib\\site-packages', 'F:\\GitHub_Clone\\ai-sdlc', 'F:\\AI_Library\\my_quant_env\\Lib\\site-packages\\win32', 'F:\\AI_Library\\my_quant_env\\Lib\\site-packages\\win32\\lib', 'F:\\AI_Library\\my_quant_env\\Lib\\site-packages\\Pythonwin']
sys.path after ensuring PROJECT_ROOT: ['S:\\Dropbox\\Scott Only Internal\\Quant_Python_24\\Backtest_FinAsset_Alloc_Template', 'C:\\Python313\\python313.zip', 'C:\\Python313\\DLLs', 'C:\\Python313\\Lib', 'C:\\Python313', 'F:\\AI_Library\\my_quant_env', 'F:\\AI_Library\\my_quant_env\\Lib\\site-packages', 'F:\\GitHub_Clone\\ai-sdlc', 'F:\\AI_Library\\my_quant_env\\Lib\\site-packages\\win32', 'F:\\AI_Library\\my_quant_env\\Lib\\site-packages\\win32\\lib', 'F:\\AI_Library\\my_quant_env\\Lib\\site-packages\\Pythonwin']
Successfully imported config.paths. PROJECT_ROOT according to paths.py: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template
Loaded settings from: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\settings\settings_parameters_v4.ini
Found 11 parameter sections
Found 55 named lists for parameter references
Successfully loaded settings.
Python version: 3.13.1 (tags/v3.13.1:0671451, Dec  3 2024, 19:06:28) [MSC v.1942 64 bit (AMD64)]
Python paths:
  S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4
  S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template
  S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\CPS_V4
  S:\Dropbox\Scott Only Internal\Quant_Python_24\Custom Function Library
  C:\Python313\python313.zip
  C:\Python313\DLLs
  C:\Python313\Lib
  C:\Python313
  F:\AI_Library\my_quant_env
  F:\AI_Library\my_quant_env\Lib\site-packages
Loaded settings from: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\settings\settings_parameters_v4.ini
Found 11 parameter sections
Found 55 named lists for parameter references
Loaded settings from: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\settings\settings_parameters_v4.ini
Found 11 parameter sections
Found 55 named lists for parameter references
Loaded settings from: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\settings\settings_parameters_v4.ini
Found 11 parameter sections
Found 55 named lists for parameter references
Loaded settings from: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\settings\settings_parameters_v4.ini
Found 11 parameter sections
Found 55 named lists for parameter references
Loaded settings from: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\settings\settings_parameters_v4.ini
Found 11 parameter sections
Found 55 named lists for parameter references
Loaded settings from: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\settings\settings_parameters_v4.ini
Found 11 parameter sections
Found 55 named lists for parameter references
2025-06-18 19:18:50,541 - INFO - Script setup complete. Settings loaded and loggers configured.
2025-06-18 19:18:50,541 - INFO - Script setup complete. Settings loaded and loggers configured.
2025-06-18 19:18:50,541 - INFO - Script setup complete. Settings loaded and loggers configured.
2025-06-18 19:18:50,541 - INFO - Starting EMA Signal Breakdown trace.
2025-06-18 19:18:50,541 - INFO - Starting EMA Signal Breakdown trace.
2025-06-18 19:18:50,541 - INFO - EMA Signal Breakdown Trace Log - 20250618_191850
2025-06-18 19:18:50,542 - INFO - Period: 2021-01-20 to 2021-02-28
2025-06-18 19:18:50,542 - INFO - Initializing Backtest Engine components...
2025-06-18 19:18:50,542 - INFO - Initializing Backtest Engine components...
2025-06-18 19:18:50,542 - INFO - Initializing Backtest Engine components...
2025-06-18 19:18:50,543 - ERROR - An unexpected error occurred: cannot access local variable 'settings' where it is not associated with a value
Traceback (most recent call last):
  File "S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\trace_ema_signal_breakdown.py", line 142, in main
    backtest_settings = settings.get('backtest', {})
                        ^^^^^^^^
UnboundLocalError: cannot access local variable 'settings' where it is not associated with a value
2025-06-18 19:18:50,543 - ERROR - An unexpected error occurred: cannot access local variable 'settings' where it is not associated with a value
Traceback (most recent call last):
  File "S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\trace_ema_signal_breakdown.py", line 142, in main
    backtest_settings = settings.get('backtest', {})
                        ^^^^^^^^
UnboundLocalError: cannot access local variable 'settings' where it is not associated with a value
2025-06-18 19:18:50,544 - ERROR - An unexpected error occurred: cannot access local variable 'settings' where it is not associated with a value
Traceback (most recent call last):
  File "S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\trace_ema_signal_breakdown.py", line 142, in main
    backtest_settings = settings.get('backtest', {})
                        ^^^^^^^^
UnboundLocalError: cannot access local variable 'settings' where it is not associated with a value
2025-06-18 19:18:50,545 - INFO - EMA signal breakdown trace finished.
2025-06-18 19:18:50,545 - INFO - EMA signal breakdown trace finished.
2025-06-18 19:18:50,545 - INFO - EMA signal breakdown trace finished.
