# Single vs Optimization Function Matrix

## Overview
This document provides a comprehensive matrix of all key functions in the CPS V4 financial backtesting system, showing the clear separation between single mode and optimization mode execution paths.

## Function Differences: ema_allocation_model vs ema_allocation_model_updated

### `ema_allocation_model()` → `ema_allocation_model_single()`
- **Purpose**: Core EMA allocation logic with parameter optimization support
- **Return Format**: Simple weights dictionary `{symbol: weight}`
- **Parameter Handling**: Uses `get_effective_parameter()` with optimization flag checking
- **Optimization Support**: Checks parameter metadata to determine if override values should be used
- **Trace Mode**: Returns tuple with detailed trace data when `trace_mode=True`
- **Usage**: Called by other functions, not directly by pipeline

### `ema_allocation_model_updated()` → `ema_allocation_model_updated_single()`
- **Purpose**: Wrapper function that provides timestamp-keyed output format
- **Return Format**: Dictionary with timestamp keys `{pd.Timestamp: {symbol: weight}}`
- **Parameter Handling**: Loads fresh parameters via `_load_fresh_parameters()`
- **Optimization Support**: None - always uses default values from settings
- **Trace Mode**: Returns tuple with historical data when `trace_mode=True`
- **Usage**: Main entry point called by signal bridge functions

## Function Matrix

| **Component** | **Single Mode Function** | **Optimization Mode Function** | **Role & Responsibilities** |
|---------------|---------------------------|--------------------------------|----------------------------|
| **Core Model Functions** | | | |
| EMA Allocation Core | `ema_allocation_model_single()` | `ema_allocation_model_optimization()` | Core EMA calculation logic with parameter optimization support vs direct settings acceptance |
| EMA Allocation Wrapper | `ema_allocation_model_updated_single()` | `ema_allocation_model_updated_optimization()` | Timestamp-formatted wrapper with environment variable loading vs direct settings passing |
| **Bridge Functions** | | | |
| Signal Bridge Entry | `run_ema_model_with_tracing_single()` | `run_ema_model_with_tracing_optimization()` | Main entry point for EMA signal generation with trace file output |
| Bridge Generate Signals | `_bridge_generate_signals_single()` | `_bridge_generate_signals_optimization()` | Alias function for backward compatibility with backtest engine |
| Bridge Validate Signals | `_bridge_validate_signals_single()` | `_bridge_validate_signals_optimization()` | Signal validation placeholder (passthrough) |
| **Pipeline Functions** | | | |
| Pipeline Entry | `run_single_pipeline()` | `run_optimization_combination()` | Top-level pipeline orchestration for single backtest vs optimization combination |
| **Shared Utility Functions** | | | |
| EMA Calculations | `calculate_ema_metrics()` | `calculate_ema_metrics()` | Pure calculation functions - no mode dependency |
| EMA Ratios | `calculate_ema_ratios()` | `calculate_ema_ratios()` | Pure calculation functions - no mode dependency |
| Allocation Rules | `get_allocation_weights()` | `get_allocation_weights()` | Pure lookup functions - no mode dependency |
| Parameter Extraction | `_extract_value_from_complexn_dict()` | `_extract_value_from_complexn_dict()` | Pure utility functions - no mode dependency |

## Key Architectural Differences

### Single Mode Characteristics
- **Environment Variable Dependencies**: Uses `CPS_V4_OPTIMIZATION_ACTIVE` and `CPS_V4_OPTIMIZATION_SETTINGS_FILE`
- **Settings Loading**: Calls `load_settings()` and `_load_fresh_parameters()`
- **Parameter Resolution**: Uses optimization flags to determine effective parameters
- **File I/O**: Generates CSV trace files when enabled
- **Error Handling**: Comprehensive validation with fallbacks

### Optimization Mode Characteristics
- **Direct Settings**: Accepts settings dictionary as parameter
- **No Environment Variables**: Completely eliminates subprocess communication
- **Parameter Resolution**: Uses provided settings directly without optimization flag checking
- **Minimal I/O**: No CSV generation, minimal logging
- **Streamlined Logic**: Pure optimization execution without single-mode concerns

## File Locations

### Single Mode Files
- `v4/models/ema_allocation_model_v4.py` - Core model functions
- `v4/models/ema_signal_bridge.py` - Signal bridge functions
- `v4/pipeline/modes.py` - Contains `run_single_pipeline()`

### Optimization Mode Files
- `v4/models/ema_signal_bridge_optimization.py` - Optimization-specific bridge and model functions
- `v4/pipeline/modes.py` - Contains `run_optimization_combination()`

### Shared Utility Files
- `v4/models/ema_allocation_model_v4.py` - Calculation functions
- `v4/config/allocation_rules_v4.py` - Allocation rules
- `v4/utils/tracing_utils.py` - Trace utilities

## Import Dependencies

### Single Mode Imports
```python
from v4.models.ema_signal_bridge import run_ema_model_with_tracing_single
from v4.models.ema_allocation_model_v4 import ema_allocation_model_single, ema_allocation_model_updated_single
```

### Optimization Mode Imports
```python
from v4.models.ema_signal_bridge_optimization import run_ema_model_with_tracing_optimization
from v4.models.ema_signal_bridge_optimization import ema_allocation_model_optimization, ema_allocation_model_updated_optimization
```

## Usage Patterns

### Single Mode Flow
1. `run_single_pipeline()` → 
2. `run_ema_model_with_tracing_single()` → 
3. `ema_allocation_model_updated_single()` → 
4. `ema_allocation_model_single()`

### Optimization Mode Flow
1. `run_optimization_combination()` → 
2. `run_ema_model_with_tracing_optimization()` → 
3. `ema_allocation_model_updated_optimization()` → 
4. `ema_allocation_model_optimization()`

## Status
- ✅ **Optimization Functions**: Fully implemented and tested
- 🔄 **Single Functions**: Need renaming to include "_single" suffix
- 🔄 **Import Updates**: Need to update all import statements
- 🔄 **Function Calls**: Need to update all function calls

## Next Steps
1. Rename all single mode functions to include "_single" suffix
2. Update all import statements across the codebase
3. Update all function calls to use new names
4. Test both single and optimization flows
5. Update documentation and comments
