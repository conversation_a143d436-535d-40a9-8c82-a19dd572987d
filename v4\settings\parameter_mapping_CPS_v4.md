# Parameter Migration Mapping

This document maps legacy V3 parameters to the new CPS v4 parameters.

## Parameter Mapping Table

| Legacy Parameter | CPS v4 Parameter | Type | Default | Files |
|-----------------|------------------|------|---------|-------|
| core | system_core | string | None | parameter_registry.py, v3_register_parameters.py |
| execution_delay | system_execution_delay | integer | 1 | config_v3.py, parameter_registry.py |
| lookback | system_lookback | integer | 60 | config_v3.py |
| lt_lookback | system_lt_lookback | integer | 100 | config_v3.py |
| mt_lookback | system_mt_lookback | integer | 70 | config_v3.py |
| reporting | report_reporting | string | None | reporting_parameters.py |
| st_lookback | system_st_lookback | integer | 15 | config_v3.py |
| strategy_ema | system_strategy_ema | string | None | parameter_registry.py |
| top_n | system_top_n | integer | 2 | config_v3.py |
| visualization | system_visualization | string | None | visualization_parameters.py |
