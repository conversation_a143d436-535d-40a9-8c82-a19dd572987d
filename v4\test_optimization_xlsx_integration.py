#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Test script to verify optimization data flows into XLSX Performance Table

This script tests the integration between optimization equity curves and 
the Performance Table XLSX generation with ComboID labeling.
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from pathlib import Path
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def create_test_optimization_data():
    """Create test optimization equity curves matrix and metadata."""
    logger.info("Creating test optimization data...")
    
    # Create date range (1 year of daily data)
    start_date = datetime(2024, 1, 1)
    dates = pd.date_range(start_date, periods=252, freq='D')
    
    # Create 12 test combinations (3 st_lookback × 4 mt_lookback)
    st_lookbacks = [5, 15, 25]
    mt_lookbacks = [30, 50, 70, 90]
    
    # Initialize equity matrix
    equity_matrix = pd.DataFrame(index=dates)
    combination_metadata = {}
    
    initial_value = 1000000
    combo_id = 1
    
    for st in st_lookbacks:
        for mt in mt_lookbacks:
            # Generate ComboID
            combo_name = f"S{st}_M{mt}_L100_E1_T2"
            
            # Generate unique equity curve for this combination
            # Use different random seeds based on parameters to ensure uniqueness
            np.random.seed(st * 100 + mt)
            
            # Create different performance characteristics for each combo
            daily_return_mean = 0.0003 + (st * 0.00001) + (mt * 0.000005)  # Slight variation
            daily_return_std = 0.008 + (st * 0.0001) + (mt * 0.00005)     # Slight variation
            
            daily_returns = np.random.normal(daily_return_mean, daily_return_std, len(dates))
            
            # Calculate cumulative equity curve
            equity_values = [initial_value]
            for ret in daily_returns:
                equity_values.append(equity_values[-1] * (1 + ret))
            
            # Store in matrix (remove initial value)
            equity_matrix[combo_name] = equity_values[1:]
            
            # Store metadata
            combination_metadata[combo_name] = {
                'st_lookback': st,
                'mt_lookback': mt,
                'lt_lookback': 100,
                'execution_delay': 1,
                'top_n': 2,
                'combo_id': combo_id
            }
            
            combo_id += 1
    
    logger.info(f"Created optimization matrix with {len(equity_matrix.columns)} combinations")
    logger.info(f"Final values range: ${equity_matrix.iloc[-1].min():,.2f} to ${equity_matrix.iloc[-1].max():,.2f}")
    
    return equity_matrix, combination_metadata

def create_test_base_case_data():
    """Create test base case data for XLSX generation."""
    logger.info("Creating test base case data...")
    
    # Create simple allocation and trade data
    dates = pd.date_range('2024-01-01', periods=252, freq='D')
    
    # Allocation history
    allocation_df = pd.DataFrame({
        'Date': dates,
        'SPY': np.random.uniform(0.2, 0.4, len(dates)),
        'SHV': np.random.uniform(0.1, 0.3, len(dates)),
        'EFA': np.random.uniform(0.1, 0.3, len(dates)),
        'TLT': np.random.uniform(0.1, 0.3, len(dates)),
        'PFF': np.random.uniform(0.1, 0.3, len(dates))
    })
    
    # Normalize allocations to sum to 1
    allocation_cols = ['SPY', 'SHV', 'EFA', 'TLT', 'PFF']
    allocation_df[allocation_cols] = allocation_df[allocation_cols].div(
        allocation_df[allocation_cols].sum(axis=1), axis=0
    )
    
    # Trade log
    trade_df = pd.DataFrame({
        'Date': dates[:10],  # Just a few trades
        'Ticker': ['SPY', 'SHV', 'EFA', 'TLT', 'PFF'] * 2,
        'Action': ['BUY'] * 5 + ['SELL'] * 5,
        'Quantity': np.random.randint(100, 1000, 10),
        'Price': np.random.uniform(50, 200, 10),
        'Amount': np.random.uniform(5000, 50000, 10)
    })
    
    # Signal history
    signal_df = pd.DataFrame({
        'Date': dates,
        'SPY_Signal': np.random.uniform(0.2, 0.4, len(dates)),
        'SHV_Signal': np.random.uniform(0.1, 0.3, len(dates)),
        'EFA_Signal': np.random.uniform(0.1, 0.3, len(dates)),
        'TLT_Signal': np.random.uniform(0.1, 0.3, len(dates)),
        'PFF_Signal': np.random.uniform(0.1, 0.3, len(dates))
    })
    
    return allocation_df, trade_df, signal_df

def test_optimization_xlsx_integration():
    """Test the optimization XLSX integration."""
    logger.info("Testing optimization XLSX integration...")
    
    try:
        # Import the pipeline Excel generator
        sys.path.insert(0, str(project_root.parent))  # Add parent directory to path
        from v4.py_reporting.report_modules.report_pipeline_excel import generate_performance_table_from_pipeline_results
        
        # Create test data
        equity_matrix, combination_metadata = create_test_optimization_data()
        allocation_df, trade_df, signal_df = create_test_base_case_data()
        
        # Create results dictionary with optimization data
        results = {
            'allocation_history': allocation_df,
            'trade_log': trade_df,
            'signal_history': signal_df,
            'optimization_equity_matrix': equity_matrix,
            'optimization_metadata': combination_metadata
        }
        
        # Generate XLSX report using the configured OUTPUT_DIR
        from v4.config.paths_v4 import OUTPUT_DIR

        logger.info("Generating Performance Table XLSX with optimization data...")
        xlsx_filepath = generate_performance_table_from_pipeline_results(
            results=results,
            signals_df=signal_df,
            output_dir=str(OUTPUT_DIR)
        )
        
        if xlsx_filepath and xlsx_filepath.exists():
            logger.info(f"✓ SUCCESS: XLSX file generated at {xlsx_filepath}")
            logger.info(f"  File size: {xlsx_filepath.stat().st_size:,} bytes")
            
            # Verify the file can be opened
            try:
                import openpyxl
                workbook = openpyxl.load_workbook(xlsx_filepath)
                sheet_names = workbook.sheetnames
                logger.info(f"  Sheets: {sheet_names}")
                
                # Check Performance tab specifically
                if 'Performance' in sheet_names:
                    perf_sheet = workbook['Performance']
                    max_row = perf_sheet.max_row
                    max_col = perf_sheet.max_column
                    logger.info(f"  Performance tab: {max_row} rows × {max_col} columns")
                    
                    # Check for ComboID labels in column A
                    combo_ids_found = []
                    for row in range(3, min(max_row + 1, 15)):  # Check first 12 strategy rows
                        cell_value = perf_sheet.cell(row=row, column=1).value
                        if cell_value and 'S' in str(cell_value) and 'M' in str(cell_value):
                            combo_ids_found.append(cell_value)
                    
                    if combo_ids_found:
                        logger.info(f"  ✓ Found ComboID labels: {combo_ids_found[:3]}... ({len(combo_ids_found)} total)")
                    else:
                        logger.warning("  ✗ No ComboID labels found in Performance tab")
                
                workbook.close()
                return True
                
            except Exception as e:
                logger.error(f"  ✗ Error verifying XLSX file: {e}")
                return False
        else:
            logger.error("✗ FAILED: XLSX file not generated")
            return False
            
    except Exception as e:
        logger.error(f"✗ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function."""
    logger.info("=" * 60)
    logger.info("OPTIMIZATION XLSX INTEGRATION TEST")
    logger.info("=" * 60)
    
    success = test_optimization_xlsx_integration()
    
    logger.info("=" * 60)
    if success:
        logger.info("✓ ALL TESTS PASSED")
        logger.info("Optimization data successfully flows into XLSX Performance Table")
        logger.info("ComboID labeling is working correctly")
    else:
        logger.error("✗ TESTS FAILED")
        logger.error("Check error messages above for details")
    logger.info("=" * 60)
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
