# New conversation - Grok

# V3 Reporting System Product Requirements Document (PRD)

## 1\. Objective

Develop a robust, compliant, and automated reporting system for the V3 backtest engine that generates performance reports, allocation reports, trade logs, and visualizations as specified in v3\_performance\_reporting\_standards\_a.md. The system must integrate with the V3 parameter registry, resolve known issues from reporting\_system\_AI.md, and support scalable AI-driven development with Windsurf and Cline in a Python-based environment on Windows 11.

## 2\. Scope

This PRD covers the implementation of the V3 reporting system, including:

* Excel-based performance tables (Performance Table Report with tabs: Signal History, Allocation History, Trade Log).

* PNG visualizations (Allocation Weights, Monthly & Annual Returns, Combined Cumulative Returns & Drawdown).

* Integration with the V3 parameter registry for consistent parameter flow.

* Systematic debugging and testing to address issues like signal history population and adapter integration.

* Verification scripts to ensure compliance with reporting standards.

## 3\. Requirements

### 3.1 Functional Requirements

1. **Performance Table Report (XLSX)**
   
   * **Filename**: EMA\_V3\_1\_performance\_tables\_YYYYMMDD\_HHMMSS.xlsx
   
   * **Tabs**:
     
     * **Signal History**:
       
       * Rows: Daily dates (YYYY-MM-DD, no timestamps).
       
       * Columns: Tickers + Cash, with % allocation signals summing to 100% per row.
       
       * Format: 0.00%.
     
     * **Allocation History**:
       
       * Matches Signal History structure.
       
       * Reflects actual capital allocations from executed trades, held constant until the next trade, with a 1-day lag from signals.
       
       * Each row sums to 100%, no gaps in dates.
       
       * Format: 0.00%.
       
       * Includes a PNG visualization (docs/allocation\_history\_example.png) as a stacked area or bar chart showing allocation evolution.
     
     * **Trade Log**:
       
       * Columns: trade\_num (integer), symbol (ticker), quantity (signed integer), execution\_date (YYYY-MM-DD), execution\_price (float, 3 decimals), commission+slippage (float, 2 decimals), amount (float, 2 decimals), pnl (float, 2 decimals).
       
       * Sorted by execution\_date and trade\_num.
       
       * Includes all executed trades, including final position closures.
   
   * **Header**: Cell A1 in each tab displays main/default parameters (e.g., st\_lookback=30, mt\_lookback=90, lt\_lookback=180, execution\_delay=1, top\_n=5).
   
   * **Performance Tab**:
     
     * Columns: Parameters (Strategy, st\_lookback, mt\_lookback, lt\_lookback, execution\_delay, top\_n, etc.) on the left; Metrics (CAGR, Sharpe, Sortino, Max Drawdown, Win Rate, Final Value, YTD Returns, Annual Returns) on the right.
     
     * One row per parameter combination, plus a benchmark row at the top.
     
     * Formatting: Percentages (0.00%), Ratios (0.00), Currency ($#,##0.00).

2. **Monthly & Annual Returns Graphic (PNG)**
   
   * **Filename**: EMA\_V3\_1\_monthly\_returns\_YYYYMMDD\_HHMMSS.png
   
   * **Content**:
     
     * Heatmap of monthly returns (years as rows, months as columns, color-coded red to green).
     
     * Annual returns in a single column next to the heatmap.
     
     * Color bar/legend for return scale (e.g., -5% to +8%).
   
   * **Formatting**:
     
     * Title: "Monthly Returns".
     
     * Axis labels: Year (rows), Month (columns: Jan–Dec).
     
     * Values: Percentages (0.00%), no missing months/years.
     
     * High DPI (≥300, ideally 600).
   
   * **Audit**: Matches realized returns from Allocation History and Trade Log.

3. **Combined Cumulative Returns & Drawdown Graphic (PNG)**
   
   * **Filename**: EMA\_V3\_1\_cumulative\_returns\_drawdown\_YYYYMMDD\_HHMMSS.png
   
   * **Content**:
     
     * Top panel: Cumulative returns for strategy and benchmark.
     
     * Bottom panel: Drawdowns over the same period.
     
     * Shared x-axis (Date) for the full backtest period.
     
     * Overlay arrows/markers on cumulative returns to indicate allocation changes.
   
   * **Parameter Display**:
     
     * Sub-title: Backtest period (e.g., "2020-01-01 to 2025-05-01").
     
     * Main/default parameters displayed at the bottom in readable font.
   
   * **Audit**: Matches realized returns and drawdowns from Allocation History and Trade Log.

4. **Parameter Integration**
   
   * All reporting parameters (e.g., create\_excel, create\_charts, metrics) must be defined as V3 parameter classes in app/gui/v3\_register\_parameters.py.
   
   * Parameters must flow from GUI → Parameter Registry → Backtest Engine → Performance Reporter Adapter → Reporting Modules.
   
   * Header rows in Excel outputs must dynamically reflect all parameter values.

5. **Error Handling and Logging**
   
   * Log signal history creation and usage in engine/backtest.py and reporting/allocation\_report.py.
   
   * Handle cases where signal\_history is None or empty with clear error messages and recovery logic.
   
   * Generate debug logs (logs/v3\_debug\_{timestamp}.txt) with signal generation and report entries.

### 3.2 Non-Functional Requirements

1. **Compliance**:
   
   * Adhere to v3\_performance\_reporting\_standards\_a.md for column ordering, formatting, and content.
   
   * No changes to existing functionality, column ordering, or core formatting without explicit approval.

2. **Performance**:
   
   * Generate reports within 5 minutes for a 5-year backtest with 10 tickers.
   
   * Handle up to 100 parameter combinations in the Performance Tab.

3. **Scalability**:
   
   * Support AI-driven development with Windsurf, ensuring modular code for iterative updates.
   
   * Compatible with Python virtual environments on Windows 11.

4. **Reliability**:
   
   * Ensure 100% data integrity for trade logs and allocation history.
   
   * Validate that all rows in Signal History and Allocation History sum to 100%.

5. **Maintainability**:
   
   * Modular code structure with clear separation of concerns (parameter handling, backtest logic, reporting, visualization).
   
   * Comprehensive docstrings and comments for AI-generated code.

## 4\. Implementation Plan

### 4.1 Phase 1: Parameter Registration and Flow

* **Objective**: Ensure all reporting parameters are integrated into the V3 parameter registry.

* **Tasks**:
  
  * Define V3 parameter classes for create\_excel, create\_charts, metrics, chart\_types, etc., in app/gui/v3\_register\_parameters.py.
    
    python
    
    CollapseCopy
    
    ```
    from v3_parameters import ConfigParameter
    
    create_excel = ConfigParameter(
        name='create_excel',
        default=True,
        show_in_gui=True,
        optimizable=False
    )
    create_charts = ConfigParameter(
        name='create_charts',
        default=True,
        show_in_gui=True,
        optimizable=False
    )
    # Add other parameters (metrics, chart_types, etc.)
    ```
  
  * Register parameters in v3\_register\_parameters.py.
  
  * Update v3\_engine/performance\_reporter\_adapter.py to retrieve and pass these parameters to reporting modules.
  
  * Modify reporting/performance\_reporting.py and reporting/allocation\_report.py to use parameters from the adapter.

* **AI Guidance**:
  
  * Use Windsurf to generate parameter class definitions and registration logic.
  
  * Prompt: “Generate V3 parameter classes for reporting parameters (create\_excel, create\_charts, metrics, chart\_types) and register them in v3\_register\_parameters.py, ensuring GUI visibility and non-optimizable settings.”

### 4.2 Phase 2: Signal History and Allocation Report Fixes

* **Objective**: Resolve issues with signal history population and allocation report generation.

* **Tasks**:
  
  * Review and improve logging in engine/backtest.py to track signal\_history creation:
    
    python
    
    CollapseCopy
    
    ```
    import logging
    logging.basicConfig(level=logging.DEBUG, filename=f'logs/v3_debug_{timestamp}.txt')
    
    def generate_signals(self, parameters):
        logging.debug("Generating signal history...")
        signal_history = {}
        # Signal generation logic
        if not signal_history:
            logging.error("Signal history is empty or None")
        return signal_history
    ```
  
  * Implement validation checks in reporting/allocation\_report.py:
    
    python
    
    CollapseCopy
    
    ```
    def generate_allocation_report(signal_history, trades, parameters):
        if signal_history is None or not signal_history:
            logging.error("Signal history is None or empty, cannot generate allocation report")
            return None
        # Allocation report logic
    ```
  
  * Ensure signal\_history is preserved and passed correctly from backtest.py to performance\_reporter\_adapter.py.

* **AI Guidance**:
  
  * Prompt: “Add debug logging to track signal\_history creation in engine/backtest.py and validation checks in reporting/allocation\_report.py to handle None or empty signal\_history cases.”

### 4.3 Phase 3: Report Generation

* **Objective**: Implement Excel and PNG report generation per standards.

* **Tasks**:
  
  * Update reporting/performance\_reporting.py to generate EMA\_V3\_1\_performance\_tables\_YYYYMMDD\_HHMMSS.xlsx:
    
    * Use pandas and openpyxl for Excel output.
    
    * Ensure header row includes all parameters.
    
    * Format columns per standards (e.g., 0.00% for percentages).
  
  * Update visualization/performance\_charts.py to generate PNGs:
    
    * Use matplotlib for Monthly & Annual Returns (heatmap) and Combined Cumulative Returns & Drawdown (dual-panel plot).
    
    * Save PNGs with high DPI (≥300) and correct filenames.
  
  * Implement allocation weights graphic as a stacked area chart.

* **AI Guidance**:
  
  * Prompt: “Generate code in reporting/performance\_reporting.py to create an Excel file with Signal History, Allocation History, Trade Log, and Performance tabs, following the formatting and structure in v3\_performance\_reporting\_standards\_a.md.”
  
  * Prompt: “Generate code in visualization/performance\_charts.py to create Monthly & Annual Returns heatmap and Combined Cumulative Returns & Drawdown plot, ensuring high DPI and parameter display.”

### 4.4 Phase 4: Verification and Testing

* **Objective**: Ensure reports meet standards and resolve all issues.

* **Tasks**:
  
  * Enhance verify\_v3\_reporting.py to check:
    
    * File existence and size (e.g., Excel ≥50KB, PNGs ≥100KB).
    
    * Content structure (e.g., correct tabs, column headers, formatting).
    
    * Data integrity (e.g., rows sum to 100%, no missing trades).
  
  * Run run\_v3\_test\_with\_verification.bat to automate testing.
  
  * Generate a verification report with pass/fail status and error details.

* **AI Guidance**:
  
  * Prompt: “Update verify\_v3\_reporting.py to validate Excel and PNG outputs against v3\_performance\_reporting\_standards\_a.md, checking file sizes, content structure, and data integrity.”

## 5\. Testing Strategy

### 5.1 Unit Tests

* Test parameter registration and retrieval in v3\_register\_parameters.py and performance\_reporter\_adapter.py.

* Test signal history generation in backtest.py.

* Test report generation functions in performance\_reporting.py and allocation\_report.py.

### 5.2 Integration Tests

* Run end-to-end backtest with run\_ema\_v3\_gui\_test.bat to verify parameter flow and report generation.

* Check output directory for all required files (Excel, PNGs, CSVs).

* Validate report content against sample tables and graphics in v3\_performance\_reporting\_standards\_a.md.

### 5.3 Verification Tests

* Use verify\_v3\_reporting.py to automate checks for:
  
  * File existence and minimum sizes.
  
  * Log file entries (e.g., “Starting V3 engine”, “Completed successfully”).
  
  * Excel tab structure and formatting.
  
  * PNG image dimensions and content (e.g., heatmap, dual-panel plot).

### 5.4 AI-Assisted Testing

* Use Windsurf to generate test cases:
  
  * Prompt: “Generate unit tests for v3\_register\_parameters.py to verify parameter registration and retrieval.”
  
  * Prompt: “Generate integration tests for run\_ema\_v3\_gui\_test.bat to validate report generation and file output.”

## 6\. Success Criteria

* All reports (Excel, PNGs) are generated with correct filenames, formats, and content as per v3\_performance\_reporting\_standards\_a.md.

* Signal history is consistently populated and used in allocation reports.

* Parameters flow correctly from GUI to reports via the V3 registry and adapter.

* Verification script reports 100% pass rate for file existence, size, and content.

* No errors in debug logs; all expected log entries present.

## 7\. Risks and Mitigation

* **Risk**: Signal history remains None or empty.
  
  * **Mitigation**: Add robust logging and validation in backtest.py; implement fallback logic in allocation\_report.py.

* **Risk**: Parameter mismatches between GUI and reports.
  
  * **Mitigation**: Centralize parameter handling in v3\_register\_parameters.py and verify in adapter.

* **Risk**: Formatting errors in Excel/PNG outputs.
  
  * **Mitigation**: Use templates and unit tests to enforce formatting standards.

## 8\. Augment Testing based on User Feedback

* Test, package up and report, and only validate completion after user confirmation.

## 9\. AI Development Guidance

* **Prompt Structure**: Use specific, standards-referenced prompts for Windsurf to generate code, tests, and documentation.

* **Code Reviews**: Use VS Code to review AI-generated code for compliance with standards and modularity.

* **Version Control**: Create checkpoints periodically with descriptive messages (e.g., “Implemented Signal History logging in backtest.py”);  Remind user to save at these points.

* **Iterative Development**: Break tasks into small, testable chunks for AI to handle, validating each before proceeding.