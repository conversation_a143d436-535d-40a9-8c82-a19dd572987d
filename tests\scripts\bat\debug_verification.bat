@echo off
setlocal

echo === Starting Debug Verification ===
echo Date: %DATE%
echo Time: %TIME%

REM Check Python
python --version
if %ERRORLEVEL% neq 0 (
    echo ERROR: Python not found in PATH
    pause
    exit /b 1
)

REM Run a simple Python command
echo.
echo === Running Python Test ===
python -c "import sys; print('Python version:', sys.version); print('Python paths:', sys.path)"

REM Try to run the verification script
echo.
echo === Running Verification Script ===
python tests/verify_signal_history_reporting.py

if %ERRORLEVEL% equ 0 (
    echo.
    echo === Verification completed successfully! ===
) else (
    echo.
    echo === Verification failed with errors ===
)

pause
