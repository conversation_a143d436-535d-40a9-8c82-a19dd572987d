# V4 Backtest Engine Testing - Handoff Documentation

## Current State (2025-06-17)

- **Test Scripts**: 
  - `quick_engine_load_test.py` - Main test for backtest engine loading and execution
  - `run_quick_engine_load_test.bat` - Batch file to run the test
  - `run_quick_data_validation.bat` - Data validation test
- **Key Components Validated**:
  - Settings loading from `settings_CPS_v4.py`
  - Data loading via `data_loader_v4.py`
  - Backtest execution through `backtest_v4.py`
  - Signal generation and portfolio management
- **Status**: Core backtest pipeline is functional with comprehensive assertions

- **Test Script**: `simple_console_test.py` operational with exit code 0
- **Output Handling**: Robust encoding solution in place (ASCII console + UTF-8 logs)
- **Data Flow**: Core pipeline working (settings → data → signals → backtest → results)
- **Known Issues**: See Outstanding Issues section

## Key Test Files

### 1. quick_engine_load_test.py
- **Purpose**: Validates the complete backtest engine loading and execution
- **Key Validations**:
  - Settings loading with required sections
  - Data loading (prices, returns, risk-free rate)
  - Backtest execution and result validation
  - Portfolio value and returns verification

### 2. run_quick_engine_load_test.bat
```batch
@echo off
set BACKTEST_LOG_LEVEL=INFO
set PYTHONUNBUFFERED=1
call F:\AI_Library\my_quant_env\Scripts\activate
python quick_engine_load_test.py
pause
```

### 3. run_quick_data_validation.bat
```batch
@echo off
set BACKTEST_LOG_LEVEL=INFO
set PYTHONUNBUFFERED=1
call F:\AI_Library\my_quant_env\Scripts\activate
python quick_data_validation.py
pause
```

## Environment Setup

```bat
:: Required environment variables
set BACKTEST_LOG_LEVEL=INFO
set PYTHONUNBUFFERED=1

:: Virtual environment activation
call F:\AI_Library\my_quant_env\Scripts\activate

:: Python execution
python simple_console_test.py
```

## Recent Fixes and Improvements

### Backtest Engine (backtest_v4.py)
- Fixed `_calculate_results` method to handle trade data correctly
- Added missing `get_trades()` method to `TradeLog` class
- Added `get_value_history` method to `Portfolio` class
- Fixed imports and removed duplicates
- Added `_calculate_benchmark_returns` method
- Ensured `run_backtest` properly returns results dictionary

### Test Suite
- Added comprehensive assertions to validate:
  - Settings loading
  - Data integrity
  - Backtest results structure
  - Portfolio value and returns
- Reduced verbose logging in backtest loop
- Fixed linting and indentation issues

### Output Encoding

- Low-level stream wrapping (`EncodedStream` class)
- Dual ASCII/UTF-8 handling
- Comprehensive print override

### Path Handling

- Automatic project root detection
- Guaranteed module import paths

### Configuration

- Added required `[ema_model]` section to settings
- Validated all parameter sections

### Syntax Errors

- Fixed unterminated strings in signal_generator.py

## Current Focus Areas

### 1. Backtest Validation
- [x] Basic settings and data loading
- [x] Backtest execution flow
- [x] Portfolio value calculations
- [ ] Performance metrics validation
- [ ] Benchmark comparison

### 2. Code Quality
- [x] Fixed critical import issues
- [x] Improved error handling
- [ ] Add docstrings and type hints
- [ ] Increase test coverage

### 3. Performance
- [ ] Optimize data loading
- [ ] Profile backtest execution
- [ ] Memory usage monitoring

| Issue                        | Location                   | Status                  |
| ---------------------------- | -------------------------- | ----------------------- |
| ModuleNotFoundError: v4.data | data_loader.py             | Needs path adjustment   |
| EMA config validation        | ema_allocation_model_v4.py | Strict checking enabled |
| Signal history generation    | signal_generator.py        | Needs validation        |

## Test Execution Protocol

### Running Tests

#### Quick Engine Load Test
```batch
:: Run with debug logging
set BACKTEST_LOG_LEVEL=DEBUG
python quick_engine_load_test.py
```

#### Data Validation Test
```batch
:: Run data validation
python quick_data_validation.py
```

### Expected Outputs
- Console output showing test progress
- Log files in `logs/` directory
- Validation results for each test case
- Assertion failures with detailed error messages

### Verification Steps
1. Check console output for test results
2. Review log files for warnings/errors
3. Verify output files in `output/` directory
4. Check test coverage report (if available)

### Run via batch file

```bat
simple_console_test.bat
```

### - Settings =  // Turbo-all and safetorunauto All

### Expected outputs

- `terminal_log.txt` (UTF-8 encoded)
- `signal_history_head.csv`
- `weights_history_head.csv`
- `portfolio_history_head.csv`

## Data Loading Testing

### Test Components

- **Test Script**: `v4/data/download_test.py`
- **Batch File**: `v4/data/download_test.bat`
- **Key Parameters**:
  - `data_storage_mode`: Controls data loading behavior (`Save`, `Read`, `New`)
  - Default tickers: `('SPY', 'QQQ', 'IWM', 'GLD', 'TLT')`

### Test Execution

```bat
:: Force fresh data download with saving
set BACKTEST_LOG_LEVEL=DEBUG
python v4/data/download_test.py
```

### Verification Steps

1. Check console output for success message
2. Verify new data files in `v4/data/backtest_data/`
3. Confirm proper logging in `logs/dataloader.log`

### Configuration Settings

Required `[data_params]` section in `settings_parameters_v4.ini`:

```ini
[data_params]
tickers = ('SPY', 'QQQ', 'IWM', 'GLD', 'TLT')
start_date = 20200101
end_date = 20250101
price_field = Close
data_storage_mode = Save
```

### Current Status

| Issue                        | Location                   | Status                  |
| ---------------------------- | -------------------------- | ----------------------- |
| ModuleNotFoundError: v4.data | data_loader.py             | Resolved via path fixes |
| Missing data_params          | settings_parameters_v4.ini | Resolved with defaults  |

## Troubleshooting Guide

### Common Issues

#### Data Loading Failures
- **Symptom**: Missing data or shape mismatches
- **Check**:
  - Data files exist in expected locations
  - Date ranges in settings match available data
  - Required columns are present in price data

#### Backtest Failures
- **Symptom**: Backtest fails with errors
- **Check**:
  - All required parameters are set in settings
  - Signal generator returns expected format
  - Portfolio has sufficient initial capital

### Debugging

#### Logging Levels
```python
# Set in environment before running tests
set BACKTEST_LOG_LEVEL=DEBUG  # Most verbose
set BACKTEST_LOG_LEVEL=INFO   # Standard output
set BACKTEST_LOG_LEVEL=ERROR  # Errors only
```

#### Data Inspection
```python
# In quick_engine_load_test.py
print("Data columns:", data['price_data'].columns)
print("Date range:", data['price_data'].index.min(), "to", data['price_data'].index.max())
print("Initial capital:", results.get('initial_capital'))
print("Final value:", results.get('final_value'))
```

```python
# Debugging Levels:
# BACKTEST_LOG_LEVEL=DEBUG - Full trace
# BACKTEST_LOG_LEVEL=INFO - Standard output
# BACKTEST_LOG_ALERT=ERROR - Critical only
```

### Common Fixes

1. Encoding issues:
   
   - Verify EncodedStream class active
   - Check log file opened with UTF-8

2. Import errors:
   
   - Confirm sys.path includes project root
   - Check virtual environment packages

3. Configuration issues:
   
   - Validate settings_parameters_v4.ini
   - Check required sections exist

## Next Steps

### Immediate (Current Sprint)
1. [ ] Add detailed performance metrics validation
2. [ ] Implement comprehensive test cases for edge cases
3. [ ] Add input validation for signal generator
4. [ ] Document API and usage examples

### Short-term (Next Sprint)
1. [ ] Add more asset classes to test data
2. [ ] Implement transaction cost modeling
3. [ ] Add slippage modeling
4. [ ] Create performance visualization

### Long-term
1. [ ] Parallel backtest execution
2. [ ] Walk-forward optimization
3. [ ] Monte Carlo simulation
4. [ ] Integration with reporting system

1. Expand test coverage for:
   
   - Allocation and signal history details
   - Portfolio rebalancing logic
   - Performance metric calculations
   - Production - non-testing - generation bat and main py setup
   - Full reporting suite process

2. Add validation checks for:
   
   - Signal/weights consistency
   - Portfolio value monotonicity
