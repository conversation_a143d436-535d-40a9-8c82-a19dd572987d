# enhanced_engine_test.py
"""
Enhanced engine test with improved tracking and multi-problem detection
"""

import sys
from pathlib import Path
import time
import pandas as pd
import logging
import traceback
import os
from datetime import datetime

# Add project root to sys.path
project_root = Path(__file__).parent.parent.parent
if str(project_root) not in sys.path:
    sys.path.append(str(project_root))

# Set up logging
log_dir = Path(f"{project_root}/logs")
log_dir.mkdir(exist_ok=True)

# Create both log file and direct output file
log_file = f"{log_dir}/enhanced_test_{time.strftime('%Y%m%d_%H%M%S')}.log"
output_file = f"{project_root}/v4/tests/output/test_output_{time.strftime('%Y%m%d_%H%M%S')}.txt"

# Ensure output directory exists
output_dir = Path(f"{project_root}/v4/tests/output")
output_dir.mkdir(exist_ok=True, parents=True)

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("enhanced_test")

# Set up direct file output for terminal messages
class TeeOutput:
    def __init__(self, file):
        self.file = file
        self.stdout = sys.stdout
        
    def write(self, message):
        self.file.write(message)
        self.stdout.write(message)
        
    def flush(self):
        self.file.flush()
        self.stdout.flush()

# Redirect stdout to both console and file
output_stream = open(output_file, 'w', encoding='utf-8')
sys.stdout = TeeOutput(output_stream)

# Import required modules
from v4.settings.settings_CPS_v4 import load_settings
from v4.engine.data_loader_v4 import load_data_for_backtest
from v4.engine.backtest_v4 import BacktestEngine
from v4.engine.signal_generator_v4 import generate_signals


def check_dataframe_properties(df, expected_properties, name="DataFrame"):
    """
    Verify dataframe properties with detailed logging
    Returns list of issues found
    """
    issues = []
    
    # Check if df is None or empty
    if df is None:
        issues.append(f"{name} is None")
        return issues
    if df.empty:
        issues.append(f"{name} is empty")
        return issues
    
    # Check columns
    if 'columns' in expected_properties:
        missing_cols = set(expected_properties['columns']) - set(df.columns)
        if missing_cols:
            issues.append(f"{name} missing columns: {missing_cols}")
    
    # Check min rows
    if 'min_rows' in expected_properties:
        if len(df) < expected_properties['min_rows']:
            issues.append(f"{name} has insufficient rows: {len(df)} (expected >= {expected_properties['min_rows']})")
    
    # Check index type
    if 'index_type' in expected_properties:
        if not isinstance(df.index, expected_properties['index_type']):
            issues.append(f"{name} has wrong index type: {type(df.index)} (expected {expected_properties['index_type']})")
    
    # Check for NaN values
    if expected_properties.get('no_nans', False):
        nan_counts = df.isna().sum().sum()
        if nan_counts > 0:
            issues.append(f"{name} contains {nan_counts} NaN values")
    
    return issues


def verify_module(module_name, verification_func, *args, **kwargs):
    """
    Verify a module's functionality with detailed logging
    Returns (success, issues)
    """
    logger.info(f"Verifying module: {module_name}")
    start_time = time.time()
    issues = []
    result = None
    
    try:
        result = verification_func(*args, **kwargs)
        logger.info(f"Module {module_name} verification completed in {time.time() - start_time:.2f}s")
    except Exception as e:
        issues.append(f"Error in {module_name}: {str(e)}")
        logger.error(f"Module {module_name} verification failed: {e}")
        logger.error(traceback.format_exc())
    
    return result, issues


def verify_settings():
    """Verify settings loading"""
    settings = load_settings()
    issues = []
    
    # Check required sections
    required_sections = ['backtest', 'data_params', 'ema_model', 'portfolio']
    for section in required_sections:
        if section not in settings:
            issues.append(f"Missing required settings section: {section}")
    
    # Check specific parameters
    if 'data_params' in settings:
        if 'tickers' not in settings['data_params']:
            issues.append("Missing 'tickers' in data_params")
    
    if 'backtest' in settings:
        if 'initial_capital' not in settings['backtest']:
            issues.append("Missing 'initial_capital' in backtest settings")
    
    logger.info(f"Settings verified with {len(issues)} issues found")
    return settings, issues


def verify_data_loading():
    """Verify data loading"""
    data = load_data_for_backtest()
    issues = []
    
    # Check required data components
    required_components = ['price_data', 'returns_data', 'risk_free_rate']
    for component in required_components:
        if component not in data:
            issues.append(f"Missing required data component: {component}")
    
    # Check price data
    if 'price_data' in data:
        price_issues = check_dataframe_properties(
            data['price_data'],
            {
                'min_rows': 10,
                'no_nans': True
            },
            "price_data"
        )
        issues.extend(price_issues)
    
    # Check returns data
    if 'returns_data' in data:
        returns_issues = check_dataframe_properties(
            data['returns_data'],
            {
                'min_rows': 10,
                'no_nans': True
            },
            "returns_data"
        )
        issues.extend(returns_issues)
    
    # Check risk-free rate
    if 'risk_free_rate' in data and not isinstance(data['risk_free_rate'], (float, int, list, pd.Series)):
        issues.append(f"risk_free_rate has unexpected type: {type(data['risk_free_rate'])}")
    
    logger.info(f"Data verified with {len(issues)} issues found")
    return data, issues


def verify_backtest(data):
    """Verify backtest engine"""
    engine = BacktestEngine()
    issues = []
    
    try:
        results = engine.run_backtest(
            price_data=data['price_data'],
            signal_generator=generate_signals
        )
    except Exception as e:
        issues.append(f"Backtest execution failed: {str(e)}")
        logger.error(f"Backtest failed: {e}")
        logger.error(traceback.format_exc())
        return None, issues
    
    # Check required result components
    required_components = [
        'initial_capital', 'final_value', 'total_return', 
        'strategy_returns', 'benchmark_returns'
    ]
    for component in required_components:
        if component not in results:
            issues.append(f"Missing required results component: {component}")
    
    # Check value constraints
    if 'final_value' in results and results['final_value'] <= 0:
        issues.append(f"Invalid final_value: {results['final_value']} (should be positive)")
    
    # Check returns data
    if 'strategy_returns' in results:
        strategy_issues = check_dataframe_properties(
            results['strategy_returns'],
            {'min_rows': 1},
            "strategy_returns"
        )
        issues.extend(strategy_issues)
    
    # Check benchmark returns
    if 'benchmark_returns' in results:
        benchmark_issues = check_dataframe_properties(
            results['benchmark_returns'],
            {'min_rows': 1},
            "benchmark_returns"
        )
        issues.extend(benchmark_issues)
    
    # Check for allocation history
    if 'allocation_history' not in results:
        issues.append("Missing allocation_history in results")
    elif results['allocation_history'] is None or (
        isinstance(results['allocation_history'], pd.DataFrame) and results['allocation_history'].empty
    ):
        issues.append("allocation_history is empty or None")
    
    logger.info(f"Backtest verified with {len(issues)} issues found")
    return results, issues


def main():
    """Main test execution function"""
    logger.info("=== ENHANCED ENGINE TEST STARTED ===")
    all_issues = []
    
    # Step 1: Verify settings
    settings, settings_issues = verify_module("Settings", verify_settings)
    all_issues.extend(settings_issues)
    
    # Step 2: Verify data loading
    data, data_issues = verify_module("Data Loading", verify_data_loading)
    all_issues.extend(data_issues)
    
    # Fail early if critical components are missing
    if not settings or not data:
        logger.error("Critical failure: Missing settings or data. Aborting test.")
        return False
    
    # Step 3: Verify backtest
    results, backtest_issues = verify_module("Backtest Engine", verify_backtest, data)
    all_issues.extend(backtest_issues)
    
    # Final report
    if all_issues:
        logger.error(f"=== TEST FAILED: {len(all_issues)} issues found ===")
        for i, issue in enumerate(all_issues, 1):
            logger.error(f"Issue {i}: {issue}")
        return False
    else:
        logger.info("=== TEST PASSED: No issues found ===")
        # Save results if successful for further analysis
        if results:
            if 'allocation_history' in results and isinstance(results['allocation_history'], pd.DataFrame):
                results['allocation_history'].head().to_csv(
                    f"{project_root}/v4/tests/output/allocation_history_head.csv"
                )
            if 'portfolio_history' in results and isinstance(results['portfolio_history'], pd.DataFrame):
                results['portfolio_history'].head().to_csv(
                    f"{project_root}/v4/tests/output/portfolio_history_head.csv"
                )
        return True


if __name__ == "__main__":
    print(f"=== ENHANCED ENGINE TEST STARTED AT {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} ===\n")
    success = main()
    print(f"\n=== ENHANCED ENGINE TEST COMPLETED AT {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} ===\n")
    print(f"Test result: {'SUCCESS' if success else 'FAILURE'}")
    
    # Restore stdout and close file
    if isinstance(sys.stdout, TeeOutput):
        output_stream.close()
        sys.stdout = sys.stdout.stdout
        
    sys.exit(0 if success else 1)  # Return exit code based on success
