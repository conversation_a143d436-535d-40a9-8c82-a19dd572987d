#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
v4/py_reporting/report_modules/__init__.py

Package initialization for CPS V4 Performance Reporting Modules

This package contains the refactored modules from v4_performance_report.py:
- report_excel: Excel report generation and verification
- report_metrics: Performance metrics calculation
- report_validation: Validation file export functionality
- report_optimization: Optimization reporting functionality

All modules maintain the exact same functionality as the original v4_performance_report.py
but are organized into focused, maintainable modules.

Author: AI Assistant
Date: 2025-07-26 (Refactored from v4_performance_report.py)
"""

# Import all functions from the refactored modules for easy access
from .report_excel import (
    _generate_excel_report,
    _generate_optimization_excel,
    verify_excel_report,
    _write_simple_output,
    REQUIRED_SHEETS
)

from .report_metrics import (
    _calculate_performance_metrics
)

from .report_validation import (
    export_validation_files,
    _export_signal_history,
    _export_allocation_history,
    _export_performance_metrics,
    _export_trade_log,
    _export_portfolio_values
)

from .report_optimization import (
    generate_optimization_report
)

from .report_pipeline_excel import (
    generate_performance_table_from_pipeline_results
)

__all__ = [
    # Excel functions
    '_generate_excel_report',
    '_generate_optimization_excel', 
    'verify_excel_report',
    '_write_simple_output',
    'REQUIRED_SHEETS',
    
    # Metrics functions
    '_calculate_performance_metrics',
    
    # Validation functions
    'export_validation_files',
    '_export_signal_history',
    '_export_allocation_history',
    '_export_performance_metrics',
    '_export_trade_log',
    '_export_portfolio_values',
    
    # Optimization functions
    'generate_optimization_report',
    
    # Pipeline functions
    'generate_performance_table_from_pipeline_results'
]
