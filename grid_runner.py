"""
grid_runner.py
Orchestrator for single-scenario and grid backtest runs from project root.
"""
import os
import pandas as pd
from datetime import datetime

from v4.settings.settings_CPS_v4 import load_settings
from v4.engine.data_loader_v4 import load_data_for_backtest
from v4.engine.signal_generator_v4 import create_signal_generator
from v4.engine.backtest_v4 import BacktestEngine


def run_one_scenario(settings, scenario: dict, output_dir: str) -> dict:
    """
    Run backtest for one scenario.
    scenario: overrides for settings['strategy'] (must include 'strategy_name')
    """
    # Merge default strategy settings with scenario overrides
    strat_cfg = settings.get('strategy', {}).copy()
    strat_cfg.update(scenario)
    strat_name = strat_cfg.pop('strategy_name', None)

    # Load data
    data = load_data_for_backtest(current_settings=settings)
    price_data = data['price_data']

    # Create strategy and engine
    signal_gen = create_signal_generator(strat_name, **strat_cfg) if strat_name else create_signal_generator(settings['strategy']['strategy_name'], **settings['strategy'])
    engine = BacktestEngine()

    # Run backtest
    results = engine.run_backtest(price_data=price_data,
                                  signal_generator=signal_gen,
                                  **strat_cfg)

    # Ensure output directory
    os.makedirs(output_dir, exist_ok=True)
    ts = datetime.now().strftime("%Y%m%d_%H%M%S")

    # Save core outputs
    if 'weights_history' in results:
        results['weights_history'].to_csv(os.path.join(output_dir, f"weights_{ts}.csv"))
    if 'trade_log' in results:
        results['trade_log'].to_csv(os.path.join(output_dir, f"trades_{ts}.csv"))
    if 'performance' in results:
        pd.Series(results['performance']).to_csv(os.path.join(output_dir, f"perf_{ts}.csv"), header=['value'])

    # Return results for further inspection
    return results


def main():
    settings = load_settings()
    try:
        # Default single scenario
        output_dir = os.path.join(os.getcwd(), 'v4_trace_outputs')
        res = run_one_scenario(settings, {}, output_dir)
        print("Performance metrics:", res.get('performance', {}))
    except Exception as e:
        print("Grid runner failed:", e)
        raise


if __name__ == '__main__':
    main()
