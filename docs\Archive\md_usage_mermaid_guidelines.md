# Markdown Usage Guidelines for This Project

## Diagrams and Documentation (Marktext/Windows 11)

- Use fenced code blocks (```) with the correct language specifier (e.g., `mermaid` for diagrams).
- For Mermaid diagrams, use the simplest syntax possible for compatibility.
- Place diagrams at the top of the file or after a clear heading.
- For advanced diagrams, you CAN use colors, classes, and custom arrows—see template below.
- Always verify diagram rendering in your target Markdown editor (Marktext, VS Code, etc.).
- If a diagram does not render, test with a minimal example to confirm editor support.
- For complex diagrams, export as PNG/SVG and embed the image in the Markdown file.
- Keep documentation up to date as part of your workflow.

---

## **Copy/Paste Template: Advanced Mermaid Diagram with Colors**

```mermaid
flowchart TD
    %% Example nodes and colored classes
    A[Start] --> B[GUI Layer]
    B --> C[Engine]
    C --> D[Reporting]
    D --> E[End]
    
    %% Bidirectional and feedback arrows
    C <--> B
    D -->|Feedback| B
    
    %% Custom styling
    classDef gui fill:#BBDEFB,stroke:#1976D2,color:#0D47A1;
    classDef engine fill:#C8E6C9,stroke:#388E3C,color:#1B5E20;
    classDef report fill:#F3E5F5,stroke:#8E24AA,color:#4A148C;
    class B gui;
    class C engine;
    class D report;
```

---

## **How to Use Colors and Classes in Mermaid**
- Use `classDef` to define a class (color, border, text color).
- Use `class <NodeID> <className>;` to apply a class to a node.
- You can use `-->` for one-way, `<-->` for two-way, and `-.->` for dashed arrows.
- Comments (`%%`) are ignored by Mermaid.

---

## **Reference: Full V3 Process Flow Example**

```mermaid
flowchart TD
    %% Start of Flow
    Start[Start] --> GUI[v3_gui_core.py\nMainWindowV3]
    GUI --> |Initialize GUI| ParamWidgets[v3_parameter_widgets.py\nParameter Widgets]
    GUI --> |Handle Actions| GUIActions[v3_gui_actions.py\nGUI Actions]
    ParamWidgets --> |Manage Controls| GuiParamMgr[v3_engine/gui_parameter_manager.py\nGuiParameterManager]
    GuiParamMgr --> |Sync Parameters| ParamReg[v3_engine/parameter_registry.py\nParameterRegistry]
    ParamReg --> |Register/Lookup| StratParamSet[v3_engine/strategy_parameter_set.py\nStrategyParameterSet]
    ParamReg --> |Define Types| Params[v3_engine/parameters.py\nNumeric/Categorical Parameters]
    Params --> |Optimize| ParamOpt[v3_engine/parameter_optimizer.py\nParameterOptimizer]
    StratParamSet --> |Apply to Strategy| Strategy[Strategy Classes\n e.g., EMA]
    ParamOpt --> |Generate Combinations| Strategy
    Strategy --> |Load Data| DataLoader[data/data_loader.py\nDataLoader]
    Strategy --> |Execute Backtest| Backtest[Backtest Engine]
    Backtest --> |Generate Reports| PerfReport[reporting/performance_reporting.py\nPerformance Reporting]
    Backtest --> |Generate Reports| AllocReport[reporting/allocation_report.py\nAllocation Report]
    Backtest --> |Generate Visuals| PerfCharts[visualization/performance_charts.py\nPerformance Charts]
    PerfReport --> |Integrate Data| PerfReportAdapter[v3_engine/performance_reporter_adapter.py\nPerformance Reporter Adapter]
    Backtest --> |Use Utilities| DateUtils[utils/date_utils.py\nDate Utilities]
    Backtest --> |Log Trades| TradeLog[utils/trade_log.py\nTrade Log]
    PerfReport --> End[End]
    AllocReport --> End
    PerfCharts --> End
    classDef gui fill:#BBDEFB,stroke:#1976D2,color:#0D47A1;
    classDef param fill:#C8E6C9,stroke:#388E3C,color:#1B5E20;
    classDef strategy fill:#FFECB3,stroke:#F57F17,color:#E65100;
    classDef data fill:#F5F5F5,stroke:#616161,color:#212121;
    classDef report fill:#F3E5F5,stroke:#8E24AA,color:#4A148C;
    classDef util fill:#EFEBE9,stroke:#5D4037,color:#3E2723;
    class GUI,ParamWidgets,GUIActions,GuiParamMgr gui;
    class ParamReg,Params,ParamOpt,StratParamSet param;
    class Strategy,Backtest strategy;
    class DataLoader data;
    class PerfReport,AllocReport,PerfCharts,PerfReportAdapter report;
    class DateUtils,TradeLog util;
```

---

*This file is maintained as the single source of truth for Markdown usage in this project. Update as project evolves.*
