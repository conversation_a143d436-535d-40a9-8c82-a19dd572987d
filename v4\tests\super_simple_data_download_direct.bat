@echo on
REM ==============================================================================
REM super_simple_data_download_direct.bat - Run the direct data download Python test
REM ==============================================================================

echo Activating virtual environment...
call "F:\AI_Library\my_quant_env\Scripts\activate.bat"
if %errorlevel% neq 0 (
    echo ERROR: Failed to activate virtual environment
    pause
    goto :eof
)

echo.
echo Running direct data download test...
python "%~dp0super_simple_data_download_direct.py"
echo Exit code: %errorlevel%

echo.
echo Test complete. Press any key to exit.
pause
