# Requirements Document

## Introduction

This feature completes the refactoring of `performance_table_generator.py` by migrating the missing complex optimization functionality to the existing modular system. A new `report_modules/report_matrix_optimization.py` module will be created to handle matrix optimization, validation framework, and EquityCurvesManager integration. The module will maintain identical function signatures to ensure `v4_performance_report.py` becomes a complete drop-in replacement for `performance_table_generator.py`, enabling the optimization validation to work through the refactored system.

## Requirements

### Requirement 1

**User Story:** As a system developer, I want a new `report_modules/report_matrix_optimization.py` module that contains all the missing optimization functionality from `performance_table_generator.py`, so that the refactored system can handle complex optimization workflows.

#### Acceptance Criteria

1. WHEN the new module is created THEN it SHALL contain `get_optimization_combinations()`, `_run_matrix_optimization()`, `_validate_single_combination()`, and `_run_pipeline_for_combination()` functions
2. WHEN these functions are called THEN they SHALL maintain identical signatures and behavior to the original `performance_table_generator.py` methods
3. WHEN the module imports EquityCurvesManager THEN it SHALL use it directly without any wrapper complexity
4. WHEN `v4_performance_report.py` imports this module THEN it SHALL expose these functions as if they were part of the original class

### Requirement 2

**User Story:** As a system developer, I want the pipeline mode detection in `v4/pipeline/config.py` to be fixed so that individual parameter combinations route to single mode, enabling the new optimization module to work correctly.

#### Acceptance Criteria

1. WHEN a subprocess is called with CPS_V4_COMBO_ID environment variable THEN the `determine_pipeline_mode()` function SHALL route to single mode regardless of global optimization_active flag
2. WHEN `determine_pipeline_mode()` executes THEN it SHALL check environment variables before INI file settings
3. WHEN individual combinations are processed THEN the subprocess SHALL NOT attempt to run full matrix optimization
4. IF global optimization is active AND combo_id is present THEN the pipeline SHALL force single mode execution to avoid infinite loops

### Requirement 3

**User Story:** As a quantitative analyst, I want the new optimization module to generate correct output files with proper naming conventions, so that equity curves can be loaded and verified.

#### Acceptance Criteria

1. WHEN `run_single_pipeline()` executes with CPS_V4_COMBO_ID set THEN it SHALL create files named `unified_portfolio_combo_{combo_id}_{timestamp}.csv`
2. WHEN `_load_unified_portfolio_for_combination()` searches for files THEN it SHALL find matching combination-specific files in the reporting directory
3. WHEN equity curves are generated THEN they SHALL contain the Portfolio_Value column with valid numerical data from the backtest engine
4. IF combo_id is not present THEN the system SHALL create standard `unified_portfolio_{timestamp}.csv` files for non-optimization runs

### Requirement 4

**User Story:** As a system developer, I want temporary settings files and subprocess execution to work correctly in the new optimization module, so that each parameter combination uses the correct values for signal generation.

#### Acceptance Criteria

1. WHEN `_create_temp_settings_for_combination()` creates a temporary settings file THEN it SHALL contain the specific parameter combination values in the correct INI format
2. WHEN the subprocess loads the custom settings file via `--settings` argument THEN it SHALL apply those parameters to the EMA signal generation phase
3. WHEN `_run_pipeline_for_combination()` executes THEN it SHALL log the exact command and capture both stdout and stderr output
4. IF subprocess fails THEN the system SHALL report the return code, command details, and stderr content to identify the failure cause

### Requirement 5

**User Story:** As a quantitative analyst, I want the 10-step validation framework to work through the new optimization module, so that I can verify all 12 parameter combinations are processed correctly by the refactored system.

#### Acceptance Criteria

1. WHEN Step 3 (Single Combination Test) executes via the new module's `_validate_single_combination()` THEN it SHALL complete within reasonable time limits without hanging
2. WHEN single combination processing succeeds THEN the validation SHALL proceed to Step 4 matrix optimization using the new module's `_run_matrix_optimization()`
3. WHEN `_run_matrix_optimization()` runs THEN it SHALL process all 12 parameter combinations and generate unique equity curves using the EquityCurvesManager
4. WHEN `test_optimization_validation.py` calls the refactored system THEN it SHALL work identically to the original `performance_table_generator.py` system