# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
dev-debug.log

# Dependency directories
node_modules/

# Environment variables
.env

# Editor directories and files
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# OS specific
.DS_Store

# Task files
tasks.json
tasks/

# API Keys and sensitive configuration
# Explicitly include essential .ini files that are part of the core project configuration
!CPS_v4/default_settings_CPS_v4.ini
!CPS_v4/V4_transition/default_settings_CPS_v4.ini
!CPS_v4/archive/default_settings_CPS_v4.ini
!v4/settings/settings_parameters_v4.ini
!tests/v4/test_settings_small_range.ini
!v4/settings/trace_outputs_section.ini

# General exclusion for other .ini files (e.g., user-specific or sensitive configs)
*.ini
# Explicitly include essential .json files that are part of the core project configuration
!system_structure.json

# General exclusion for other .json files (e.g., user-specific or sensitive configs)
*.json

# Exclude generated output directories containing JSON files
optimization_validation/
output/
*.yml
*.yaml
keys/
secrets/
api_keys.py
credentials.py
 