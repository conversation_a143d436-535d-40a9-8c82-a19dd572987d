@echo on
echo =====================================================
echo MINIMAL PRINT TEST - %date% %time%
echo =====================================================

REM Set virtual environment path
set VENV_PATH=F:\AI_Library\my_quant_env

REM Activate virtual environment
echo Activating virtual environment at %VENV_PATH%...
call %VENV_PATH%\Scripts\activate.bat
if %errorlevel% neq 0 (
    echo ERROR: Failed to activate virtual environment! Error code: %errorlevel%
    goto :error
)

echo.
echo ------------------------------------------------------
echo Running minimal print test...
echo ------------------------------------------------------
echo.

REM Run the minimal test script with direct console output
python -u S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\tests\minimal_print_test.py
echo Python script exit code: %errorlevel%

echo.
echo ------------------------------------------------------
echo Test script execution complete!
echo ------------------------------------------------------

goto :end

:error
echo.
echo ERROR: Test script execution failed!
echo.

:end
echo.
echo Press any key to close this window...
pause > nul
