"""
Verify that the execution delay parameter optimization fix works correctly.
This test simulates optimizing the execution_delay parameter in the GUI.
"""

import sys
import os
from pathlib import Path
import logging

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Import necessary modules
from config.config_v2 import config_v2
from config.local_parameter_optimization import define_parameter, get_parameter_combinations
from engine.backtest import BacktestEngine

# Create test data
import pandas as pd
import numpy as np
from datetime import datetime

# Create minimal price data
dates = pd.date_range(start='2025-01-01', periods=10)
price_data = pd.DataFrame({
    'SPY': np.linspace(100, 110, 10),
    'TLT': np.linspace(90, 100, 10)
}, index=dates)

# Define a simple signal generator
def dummy_signal(price_data, **params):
    return {'SPY': 0.6, 'TLT': 0.4}

# Create a modified config with execution_delay set to be optimized
modified_config = config_v2.copy()
modified_config['backtest_params'] = config_v2['backtest_params'].copy()
modified_config['backtest_params']['execution_delay'] = ('Y', 1, 0, 3, 1)  # optimize=True, default=1, min=0, max=3, step=1

# Extract execution_delay parameter
exec_delay_param = modified_config['backtest_params']['execution_delay']

# Create parameter dictionary
param_dict = {'execution_delay': exec_delay_param}

# Get parameter combinations
param_combinations = get_parameter_combinations(param_dict)

print(f"Testing {len(param_combinations)} execution delay values: {[p['execution_delay'] for p in param_combinations]}")

# Test each execution delay value
for i, params in enumerate(param_combinations):
    delay_value = params['execution_delay']
    print(f"\nTesting execution_delay = {delay_value}")
    
    try:
        # Create engine
        engine = BacktestEngine()
        
        # Run backtest with the current execution_delay value
        result = engine.run_backtest(
            price_data=price_data,
            signal_generator=dummy_signal,
            execution_delay=delay_value
        )
        
        print(f"✓ Success: Backtest ran successfully with execution_delay = {delay_value}")
    except Exception as e:
        print(f"✗ Error: Backtest failed with execution_delay = {delay_value}: {e}")
        import traceback
        print(traceback.format_exc())

print("\nTest complete. If all tests passed, the fix was successful.")
