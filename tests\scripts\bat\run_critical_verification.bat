@echo off
REM =====================================================================
REM Critical Issues Verification Script
REM =====================================================================
REM This batch file runs targeted verification for critical issues:
REM 1. Signal history population
REM 2. Benchmark calculation using price_data not signal_history
REM 3. Logging levels for trade messages
REM 4. Execution delay parameter optimization
REM 5. Parameter registration with StrategyOptimizeParameter
REM
REM Created: June 2, 2025
REM =====================================================================

echo.
echo === Critical Issues Verification ===
echo.

REM Set environment variables for testing
set BACKTEST_LOG_LEVEL=DEBUG
set PYTHONPATH=%~dp0;%~dp0tests

REM Create verification directories if they don't exist
if not exist "%~dp0tests" mkdir "%~dp0tests"
if not exist "%~dp0tests\critical_verification" mkdir "%~dp0tests\critical_verification"

REM Set Python executable path
SET PYTHON_EXE=F:\AI_Library\my_quant_env\Scripts\python.exe

REM Verify Python exists
if not exist "%PYTHON_EXE%" (
    echo [X] ERROR: Python not found at %PYTHON_EXE%
    pause
    exit /b 1
)

echo [✓] Using Python at: %PYTHON_EXE%

echo.
echo Running critical issues verification...
echo.

REM Run the verification script
"%PYTHON_EXE%" "%~dp0tests\verify_critical_issues.py"

REM Check the exit code
if %ERRORLEVEL% EQU 0 (
    echo.
    echo === Critical Issues Verification PASSED ===
    echo.
    echo All critical issues have been fixed.
    echo Check the verification summary in tests\critical_verification
) else (
    echo.
    echo === Critical Issues Verification FAILED ===
    echo.
    echo Some critical issues still need attention.
    echo Check the verification log in tests\critical_issues.log for details
)

echo.
echo Verification process complete.
echo.

pause
