2025-06-17 19:54:39,427 - INFO - [TickerData] Mode=Read, file=S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\data\tickerdata_SPY_SHV_EFA_TLT_PFF_20200101_20250615.xlsx

=== ALLOCATION DEBUG STARTED ===

Python version: 3.13.1 (tags/v3.13.1:0671451, Dec  3 2024, 19:06:28) [MSC v.1942 64 bit (AMD64)]
Python paths:
  S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4
  S:\Dropbox\Scott Only Internal\Quant_Python_24\Custom Function Library
  S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\tests
  C:\Python313\python313.zip
  C:\Python313\DLLs
  C:\Python313\Lib
  C:\Python313
  F:\AI_Library\my_quant_env
  F:\AI_Library\my_quant_env\Lib\site-packages
  F:\GitHub_Clone\ai-sdlc
Loaded settings from: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\settings\settings_parameters_v4.ini
Found 11 parameter sections
Found 55 named lists for parameter references
Loaded settings from: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\settings\settings_parameters_v4.ini
Found 11 parameter sections
Found 55 named lists for parameter references
Loaded settings from: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\settings\settings_parameters_v4.ini
Found 11 parameter sections
Found 55 named lists for parameter references
Loaded settings from: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\settings\settings_parameters_v4.ini
Found 11 parameter sections
Found 55 named lists for parameter references
Loaded settings from: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\settings\settings_parameters_v4.ini
Found 11 parameter sections
Found 55 named lists for parameter references
Loaded settings from: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\settings\settings_parameters_v4.ini
Found 11 parameter sections
Found 55 named lists for parameter references
Starting allocation debug test

=== ALLOCATION HISTORY DEBUG STARTED ===
Loaded settings from: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\settings\settings_parameters_v4.ini
Found 11 parameter sections
Found 55 named lists for parameter references
Loaded settings: {'tickers': {'default': ('SPY', 'SHV', 'EFA', 'TLT', 'PFF'), 'picklist': ['Group1ETFBase', 'Group2ETF', 'Group3ETF', 'AllETF']}, 'start_date': 20200101, 'end_date': 20250615, 'price_field': 'Close', 'data_storage_mode': 'Read'}
Loaded data keys: ['price_data', 'returns_data', 'risk_free_rate']
price_data: (1370, 5)

=== DEBUG: SIGNAL GENERATION ===

--- Testing Equal Weight Strategy ---

=== EqualWeightSignalGenerator.generate_signals ===
Price data shape: (1370, 5)
Price data columns: ['SPY', 'SHV', 'EFA', 'TLT', 'PFF']
Price data head:
                   SPY        SHV        EFA         TLT        PFF
Date                                                               
2020-01-02  300.291565  96.425652  60.733517  118.468452  28.087305
2020-01-03  298.017792  96.425652  59.980179  120.292892  28.146736
2020-01-06  299.154633  96.416901  60.213978  119.609749  28.124453
2020-01-07  298.313446  96.434341  60.049454  119.021828  28.087305
2020-01-08  299.903381  96.443108  60.222630  118.234962  28.117022
Additional params: {}
Generated equal weight signals: {'SPY': 0.2, 'SHV': 0.2, 'EFA': 0.2, 'TLT': 0.2, 'PFF': 0.2}
Validated signals: {'SPY': 0.2, 'SHV': 0.2, 'EFA': 0.2, 'TLT': 0.2, 'PFF': 0.2}
=== EqualWeightSignalGenerator.generate_signals completed ===

Equal weight signals: {'SPY': 0.2, 'SHV': 0.2, 'EFA': 0.2, 'TLT': 0.2, 'PFF': 0.2}

--- Testing EMA Strategy ---

=== EMASignalGenerator.generate_signals ===
Price data shape: (1370, 5)
Price data columns: ['SPY', 'SHV', 'EFA', 'TLT', 'PFF']
Price data head:
                   SPY        SHV        EFA         TLT        PFF
Date                                                               
2020-01-02  300.291565  96.425652  60.733517  118.468452  28.087305
2020-01-03  298.017792  96.425652  59.980179  120.292892  28.146736
2020-01-06  299.154633  96.416901  60.213978  119.609749  28.124453
2020-01-07  298.313446  96.434341  60.049454  119.021828  28.087305
2020-01-08  299.903381  96.443108  60.222630  118.234962  28.117022
EMA parameters - st_lookback: 10, mt_lookback: 50, lt_lookback: 150
Additional params: {}
Calculating EMAs from price data...
Calculated EMAs - shapes: st_ema: (1370, 5), mt_ema: (1370, 5), lt_ema: (1370, 5)
Most recent EMA values (sample):
st_ema: {'SPY': 597.6428616311468, 'SHV': 110.14341105191042, 'EFA': 89.48092008523804}...
mt_ema: {'SPY': 579.4633287835247, 'SHV': 109.78875236086435, 'EFA': 86.37978256144793}...
lt_ema: {'SPY': 572.0831896200584, 'SHV': 108.84976057713072, 'EFA': 82.67839540289802}...
Calculating trend strength for each symbol...
  SPY: st_mt_ratio=0.0314, mt_lt_ratio=0.0129, strength=0.0443, final=0.0443
  SHV: st_mt_ratio=0.0032, mt_lt_ratio=0.0086, strength=0.0119, final=0.0119
  EFA: st_mt_ratio=0.0359, mt_lt_ratio=0.0448, strength=0.0807, final=0.0807
Positive trends: 3/5 assets
Total trend strength: 0.1368
Allocated weights proportionally to trend strength
Sample allocations (first 3): {'SPY': np.float64(0.3236366435817675), 'SHV': np.float64(0.08667296121667865), 'EFA': np.float64(0.5896903952015539)}...
Validated signals sample (first 3): {'SPY': np.float64(0.3236366435817675), 'SHV': np.float64(0.08667296121667865), 'EFA': np.float64(0.5896903952015539)}...
=== EMASignalGenerator.generate_signals completed ===

EMA signals: {'SPY': np.float64(0.3236366435817675), 'SHV': np.float64(0.08667296121667865), 'EFA': np.float64(0.5896903952015539)}

--- Testing generate_signals wrapper with equal_weight ---

=== generate_signals wrapper ===
Strategy: equal_weight
Price data shape: (1370, 5)
Additional params: {}

=== create_signal_generator ===
Strategy: equal_weight
Params: {}
Created signal generator: EqualWeightSignalGenerator
=== create_signal_generator completed ===


=== EqualWeightSignalGenerator.generate_signals ===
Price data shape: (1370, 5)
Price data columns: ['SPY', 'SHV', 'EFA', 'TLT', 'PFF']
Price data head:
                   SPY        SHV        EFA         TLT        PFF
Date                                                               
2020-01-02  300.291565  96.425652  60.733517  118.468452  28.087305
2020-01-03  298.017792  96.425652  59.980179  120.292892  28.146736
2020-01-06  299.154633  96.416901  60.213978  119.609749  28.124453
2020-01-07  298.313446  96.434341  60.049454  119.021828  28.087305
2020-01-08  299.903381  96.443108  60.222630  118.234962  28.117022
Additional params: {}
Generated equal weight signals: {'SPY': 0.2, 'SHV': 0.2, 'EFA': 0.2, 'TLT': 0.2, 'PFF': 0.2}
Validated signals: {'SPY': 0.2, 'SHV': 0.2, 'EFA': 0.2, 'TLT': 0.2, 'PFF': 0.2}
=== EqualWeightSignalGenerator.generate_signals completed ===

Signals returned from generator: <class 'dict'>
Number of symbols with allocations: 5
Sample allocations (first 3): {'SPY': 0.2, 'SHV': 0.2, 'EFA': 0.2}...
=== generate_signals wrapper completed ===

Wrapper equal_weight signals: {'SPY': 0.2, 'SHV': 0.2, 'EFA': 0.2, 'TLT': 0.2, 'PFF': 0.2}

--- Testing generate_signals wrapper with ema ---

=== generate_signals wrapper ===
Strategy: ema
Price data shape: (1370, 5)
Additional params: {}

=== create_signal_generator ===
Strategy: ema
Params: {}
Created signal generator: EMASignalGenerator
=== create_signal_generator completed ===


=== EMASignalGenerator.generate_signals ===
Price data shape: (1370, 5)
Price data columns: ['SPY', 'SHV', 'EFA', 'TLT', 'PFF']
Price data head:
                   SPY        SHV        EFA         TLT        PFF
Date                                                               
2020-01-02  300.291565  96.425652  60.733517  118.468452  28.087305
2020-01-03  298.017792  96.425652  59.980179  120.292892  28.146736
2020-01-06  299.154633  96.416901  60.213978  119.609749  28.124453
2020-01-07  298.313446  96.434341  60.049454  119.021828  28.087305
2020-01-08  299.903381  96.443108  60.222630  118.234962  28.117022
EMA parameters - st_lookback: 10, mt_lookback: 50, lt_lookback: 1502025-06-17 19:54:39,923 - INFO - Initialized backtest engine with $1,000,000.00 capital
2025-06-17 19:54:39,923 - INFO - Commission rate: 0.10%
2025-06-17 19:54:39,924 - INFO - Slippage rate: 0.05%
2025-06-17 19:54:39,929 - INFO - Starting backtest with monthly rebalancing
2025-06-17 19:54:39,930 - INFO - Execution delay: 0 days
2025-06-17 19:54:39,938 - INFO - Initialized signal_history with shape (1370, 5)
2025-06-17 19:54:39,938 - INFO - Initialized weights_history with shape (1370, 5)
2025-06-17 19:54:40,620 - INFO - Final signal_history shape: (1370, 5)
2025-06-17 19:54:40,620 - INFO - Final weights_history shape: (1370, 5)
2025-06-17 19:54:40,626 - INFO - Signal history sample (first 5 rows):
           SPY SHV EFA TLT PFF
Date                          
2020-01-02   0   0   0   0   0
2020-01-03   0   0   0   0   0
2020-01-06   0   0   0   0   0
2020-01-07   0   0   0   0   0
2020-01-08   0   0   0   0   0
2025-06-17 19:54:40,630 - INFO - Weights history sample (first 5 rows):
            SPY  SHV  EFA  TLT  PFF
Date                               
2020-01-02  0.0  0.0  0.0  0.0  0.0
2020-01-03  0.0  0.0  0.0  0.0  0.0
2020-01-06  0.0  0.0  0.0  0.0  0.0
2020-01-07  0.0  0.0  0.0  0.0  0.0
2020-01-08  0.0  0.0  0.0  0.0  0.0
S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\engine\backtest_v4.py:456: FutureWarning: 'M' is deprecated and will be removed in a future version, please use 'ME' instead.
  monthly_returns = strategy_returns.resample('M').apply(lambda x: (1 + x).prod() - 1)
S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\engine\backtest_v4.py:462: FutureWarning: 'Y' is deprecated and will be removed in a future version, please use 'YE' instead.
  yearly_returns = strategy_returns.resample('Y').apply(lambda x: (1 + x).prod() - 1)
2025-06-17 19:54:40,655 - INFO - Using provided weights_history for results
2025-06-17 19:54:40,667 - INFO - Initialized backtest engine with $1,000,000.00 capital
2025-06-17 19:54:40,667 - INFO - Commission rate: 0.10%
2025-06-17 19:54:40,667 - INFO - Slippage rate: 0.05%
2025-06-17 19:54:40,672 - INFO - Starting backtest with monthly rebalancing
2025-06-17 19:54:40,672 - INFO - Execution delay: 0 days
2025-06-17 19:54:40,676 - INFO - Initialized signal_history with shape (1370, 5)
2025-06-17 19:54:40,676 - INFO - Initialized weights_history with shape (1370, 5)

Additional params: {}
Calculating EMAs from price data...
Calculated EMAs - shapes: st_ema: (1370, 5), mt_ema: (1370, 5), lt_ema: (1370, 5)
Most recent EMA values (sample):
st_ema: {'SPY': 597.6428616311468, 'SHV': 110.14341105191042, 'EFA': 89.48092008523804}...
mt_ema: {'SPY': 579.4633287835247, 'SHV': 109.78875236086435, 'EFA': 86.37978256144793}...
lt_ema: {'SPY': 572.0831896200584, 'SHV': 108.84976057713072, 'EFA': 82.67839540289802}...
Calculating trend strength for each symbol...
  SPY: st_mt_ratio=0.0314, mt_lt_ratio=0.0129, strength=0.0443, final=0.0443
  SHV: st_mt_ratio=0.0032, mt_lt_ratio=0.0086, strength=0.0119, final=0.0119
  EFA: st_mt_ratio=0.0359, mt_lt_ratio=0.0448, strength=0.0807, final=0.0807
Positive trends: 3/5 assets
Total trend strength: 0.1368
Allocated weights proportionally to trend strength
Sample allocations (first 3): {'SPY': np.float64(0.3236366435817675), 'SHV': np.float64(0.08667296121667865), 'EFA': np.float64(0.5896903952015539)}...
Validated signals sample (first 3): {'SPY': np.float64(0.3236366435817675), 'SHV': np.float64(0.08667296121667865), 'EFA': np.float64(0.5896903952015539)}...
=== EMASignalGenerator.generate_signals completed ===

Signals returned from generator: <class 'dict'>
Number of symbols with allocations: 3
Sample allocations (first 3): {'SPY': np.float64(0.3236366435817675), 'SHV': np.float64(0.08667296121667865), 'EFA': np.float64(0.5896903952015539)}...
=== generate_signals wrapper completed ===

Wrapper ema signals: {'SPY': np.float64(0.3236366435817675), 'SHV': np.float64(0.08667296121667865), 'EFA': np.float64(0.5896903952015539)}

=== DEBUG: BACKTEST WITH EQUAL_WEIGHT STRATEGY ===

===== BACKTEST ENGINE: run_backtest STARTED =====
Price data shape: (1370, 5)
Price data head:
                   SPY        SHV        EFA         TLT        PFF
Date                                                               
2020-01-02  300.291565  96.425652  60.733517  118.468452  28.087305
2020-01-03  298.017792  96.425652  59.980179  120.292892  28.146736
2020-01-06  299.154633  96.416901  60.213978  119.609749  28.124453
2020-01-07  298.313446  96.434341  60.049454  119.021828  28.087305
2020-01-08  299.903381  96.443108  60.222630  118.234962  28.117022
Signal generator: signal_wrapper
Signal params: {}
Rebalance frequency: {'default': "'monthly'", 'picklist': ['Rebalance_Daily', 'Rebalance_Weekly', 'Rebalance_Monthly', 'Rebalance_Quarterly', 'Rebalance_Yearly']}
Execution delay: 0
Stored price data in engine, shape: (1370, 5)
Initialized signal_history with shape: (1370, 5)

===== Starting date iteration =====

Processing date 1/1370: 2020-01-02 00:00:00
  Current prices: {'SPY': 300.2915649414062, 'SHV': 96.42565155029297, 'EFA': 60.73351669311523, 'TLT': 118.4684524536133, 'PFF': 28.08730506896973}
  Portfolio value: $1,000,000.00
  Should rebalance: False

Processing date 2/1370: 2020-01-03 00:00:00
  Current prices: {'SPY': 298.0177917480469, 'SHV': 96.42565155029297, 'EFA': 59.98017883300781, 'TLT': 120.2928924560547, 'PFF': 28.14673614501953}
  Portfolio value: $1,000,000.00
  Should rebalance: False

Processing date 3/1370: 2020-01-06 00:00:00
  Current prices: {'SPY': 299.1546325683594, 'SHV': 96.41690063476562, 'EFA': 60.2139778137207, 'TLT': 119.609748840332, 'PFF': 28.12445259094238}
  Portfolio value: $1,000,000.00
  Should rebalance: False

Processing date 4/1370: 2020-01-07 00:00:00
  Current prices: {'SPY': 298.3134460449219, 'SHV': 96.43434143066406, 'EFA': 60.04945373535156, 'TLT': 119.0218276977539, 'PFF': 28.08730506896973}
  Portfolio value: $1,000,000.00
  Should rebalance: False

Processing date 5/1370: 2020-01-08 00:00:00
  Current prices: {'SPY': 299.9033813476562, 'SHV': 96.44310760498047, 'EFA': 60.22262954711914, 'TLT': 118.2349624633789, 'PFF': 28.11702156066895}
  Portfolio value: $1,000,000.00
  Should rebalance: False

Processing date 1366/1370: 2025-06-09 00:00:00
  Current prices: {'SPY': 599.6799926757812, 'SHV': 110.1600036621094, 'EFA': 89.7300033569336, 'TLT': 85.44000244140625, 'PFF': 30.35000038146973}
  Portfolio value: $1,000,000.00
  Should rebalance: False

Processing date 1367/1370: 2025-06-10 00:00:00
  Current prices: {'SPY': 603.0800170898438, 'SHV': 110.1699981689453, 'EFA': 89.8499984741211, 'TLT': 85.87999725341797, 'PFF': 30.43000030517578}
  Portfolio value: $1,000,000.00
  Should rebalance: False

Processing date 1368/1370: 2025-06-11 00:00:00
  Current prices: {'SPY': 601.3599853515625, 'SHV': 110.1800003051758, 'EFA': 89.69999694824219, 'TLT': 86.13999938964844, 'PFF': 30.40999984741211}
  Portfolio value: $1,000,000.00
  Should rebalance: False

Processing date 1369/1370: 2025-06-12 00:00:00
  Current prices: {'SPY': 603.75, 'SHV': 110.1999969482422, 'EFA': 90.38999938964844, 'TLT': 87.16999816894531, 'PFF': 30.34000015258789}
  Portfolio value: $1,000,000.00
  Should rebalance: False

Processing date 1370/1370: 2025-06-13 00:00:00
  Current prices: {'SPY': 597.0, 'SHV': 110.2399978637695, 'EFA': 89.2300033569336, 'TLT': 86.33000183105469, 'PFF': 30.15999984741211}
  Portfolio value: $1,000,000.00
  Should rebalance: False

===== _calculate_results STARTED =====
Using provided weights_history for results
Weights history shape: (1370, 5)
Weights history sample:
            SPY  SHV  EFA  TLT  PFF
Date                               
2020-01-02  0.0  0.0  0.0  0.0  0.0
2020-01-03  0.0  0.0  0.0  0.0  0.0
2020-01-06  0.0  0.0  0.0  0.0  0.0
2020-01-07  0.0  0.0  0.0  0.0  0.0
2020-01-08  0.0  0.0  0.0  0.0  0.0
===== _calculate_results COMPLETED =====


===== Results summary =====
Initial capital: $1,000,000.00
Final value: $1,000,000.00
Total return: 0.00%
CAGR: 0.00%
Sharpe: 0.00
Max drawdown: 0.00%
Weights history shape: (1370, 5)
Signal history shape: (1370, 5)
===== BACKTEST ENGINE: run_backtest COMPLETED =====


--- Allocation History Analysis ---
Weights history shape: (1370, 5)
Weights history sample:
            SPY  SHV  EFA  TLT  PFF
Date                               
2020-01-02  0.0  0.0  0.0  0.0  0.0
2020-01-03  0.0  0.0  0.0  0.0  0.0
2020-01-06  0.0  0.0  0.0  0.0  0.0
2020-01-07  0.0  0.0  0.0  0.0  0.0
2020-01-08  0.0  0.0  0.0  0.0  0.0
Non-zero weights: 0
Max weight: 0.0

=== TESTING WITH PATCHED SIGNAL GENERATOR ===

=== ATTEMPTING TO FIX SIGNAL GENERATION ISSUE ===

===== BACKTEST ENGINE: run_backtest STARTED =====
Price data shape: (1370, 5)
Price data head:
                   SPY        SHV        EFA         TLT        PFF
Date                                                               
2020-01-02  300.291565  96.425652  60.733517  118.468452  28.087305
2020-01-03  298.017792  96.425652  59.980179  120.292892  28.146736
2020-01-06  299.154633  96.416901  60.213978  119.609749  28.124453
2020-01-07  298.313446  96.434341  60.049454  119.021828  28.087305
2020-01-08  299.903381  96.443108  60.222630  118.234962  28.117022
Signal generator: patched_signal_generator
Signal params: {}
Rebalance frequency: {'default': "'monthly'", 'picklist': ['Rebalance_Daily', 'Rebalance_Weekly', 'Rebalance_Monthly', 'Rebalance_Quarterly', 'Rebalance_Yearly']}
Execution delay: 0
Stored price data in engine, shape: (1370, 5)
Initialized signal_history with shape: (1370, 5)

===== Starting date iteration =====

Processing date 1/1370: 2020-01-02 00:00:00
  Current prices: {'SPY': 300.2915649414062, 'SHV': 96.42565155029297, 'EFA': 60.73351669311523, 'TLT': 118.4684524536133, 'PFF': 28.08730506896973}
  Portfolio value: $1,000,000.00
  Should rebalance: False

Processing date 2/1370: 2020-01-03 00:00:00
  Current prices: {'SPY': 298.0177917480469, 'SHV': 96.42565155029297, 'EFA': 59.98017883300781, 'TLT': 120.2928924560547, 'PFF': 28.14673614501953}
  Portfolio value: $1,000,000.00
  Should rebalance: False

Processing date 3/1370: 2020-01-06 00:00:00
  Current prices: {'SPY': 299.1546325683594, 'SHV': 96.41690063476562, 'EFA': 60.2139778137207, 'TLT': 119.609748840332, 'PFF': 28.12445259094238}
  Portfolio value: $1,000,000.00
  Should rebalance: False

Processing date 4/1370: 2020-01-07 00:00:002025-06-17 19:54:41,339 - INFO - Final signal_history shape: (1370, 5)
2025-06-17 19:54:41,339 - INFO - Final weights_history shape: (1370, 5)
2025-06-17 19:54:41,344 - INFO - Signal history sample (first 5 rows):
           SPY SHV EFA TLT PFF
Date                          
2020-01-02   0   0   0   0   0
2020-01-03   0   0   0   0   0
2020-01-06   0   0   0   0   0
2020-01-07   0   0   0   0   0
2020-01-08   0   0   0   0   0
2025-06-17 19:54:41,352 - INFO - Weights history sample (first 5 rows):
            SPY  SHV  EFA  TLT  PFF
Date                               
2020-01-02  0.0  0.0  0.0  0.0  0.0
2020-01-03  0.0  0.0  0.0  0.0  0.0
2020-01-06  0.0  0.0  0.0  0.0  0.0
2020-01-07  0.0  0.0  0.0  0.0  0.0
2020-01-08  0.0  0.0  0.0  0.0  0.0
S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\engine\backtest_v4.py:456: FutureWarning: 'M' is deprecated and will be removed in a future version, please use 'ME' instead.
  monthly_returns = strategy_returns.resample('M').apply(lambda x: (1 + x).prod() - 1)
S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\engine\backtest_v4.py:462: FutureWarning: 'Y' is deprecated and will be removed in a future version, please use 'YE' instead.
  yearly_returns = strategy_returns.resample('Y').apply(lambda x: (1 + x).prod() - 1)
2025-06-17 19:54:41,377 - INFO - Using provided weights_history for results

  Current prices: {'SPY': 298.3134460449219, 'SHV': 96.43434143066406, 'EFA': 60.04945373535156, 'TLT': 119.0218276977539, 'PFF': 28.08730506896973}
  Portfolio value: $1,000,000.00
  Should rebalance: False

Processing date 5/1370: 2020-01-08 00:00:00
  Current prices: {'SPY': 299.9033813476562, 'SHV': 96.44310760498047, 'EFA': 60.22262954711914, 'TLT': 118.2349624633789, 'PFF': 28.11702156066895}
  Portfolio value: $1,000,000.00
  Should rebalance: False

Processing date 1366/1370: 2025-06-09 00:00:00
  Current prices: {'SPY': 599.6799926757812, 'SHV': 110.1600036621094, 'EFA': 89.7300033569336, 'TLT': 85.44000244140625, 'PFF': 30.35000038146973}
  Portfolio value: $1,000,000.00
  Should rebalance: False

Processing date 1367/1370: 2025-06-10 00:00:00
  Current prices: {'SPY': 603.0800170898438, 'SHV': 110.1699981689453, 'EFA': 89.8499984741211, 'TLT': 85.87999725341797, 'PFF': 30.43000030517578}
  Portfolio value: $1,000,000.00
  Should rebalance: False

Processing date 1368/1370: 2025-06-11 00:00:00
  Current prices: {'SPY': 601.3599853515625, 'SHV': 110.1800003051758, 'EFA': 89.69999694824219, 'TLT': 86.13999938964844, 'PFF': 30.40999984741211}
  Portfolio value: $1,000,000.00
  Should rebalance: False

Processing date 1369/1370: 2025-06-12 00:00:00
  Current prices: {'SPY': 603.75, 'SHV': 110.1999969482422, 'EFA': 90.38999938964844, 'TLT': 87.16999816894531, 'PFF': 30.34000015258789}
  Portfolio value: $1,000,000.00
  Should rebalance: False

Processing date 1370/1370: 2025-06-13 00:00:00
  Current prices: {'SPY': 597.0, 'SHV': 110.2399978637695, 'EFA': 89.2300033569336, 'TLT': 86.33000183105469, 'PFF': 30.15999984741211}
  Portfolio value: $1,000,000.00
  Should rebalance: False

===== _calculate_results STARTED =====
Using provided weights_history for results
Weights history shape: (1370, 5)
Weights history sample:
            SPY  SHV  EFA  TLT  PFF
Date                               
2020-01-02  0.0  0.0  0.0  0.0  0.0
2020-01-03  0.0  0.0  0.0  0.0  0.0
2020-01-06  0.0  0.0  0.0  0.0  0.0
2020-01-07  0.0  0.0  0.0  0.0  0.0
2020-01-08  0.0  0.0  0.0  0.0  0.0
===== _calculate_results COMPLETED =====


===== Results summary =====
Initial capital: $1,000,000.00
Final value: $1,000,000.00
Total return: 0.00%
CAGR: 0.00%
Sharpe: 0.00
Max drawdown: 0.00%
Weights history shape: (1370, 5)
Signal history shape: (1370, 5)
===== BACKTEST ENGINE: run_backtest COMPLETED =====


--- Patched Allocation History Analysis ---
Weights history shape: (1370, 5)
Weights history sample:
            SPY  SHV  EFA  TLT  PFF
Date                               
2020-01-02  0.0  0.0  0.0  0.0  0.0
2020-01-03  0.0  0.0  0.0  0.0  0.0
2020-01-06  0.0  0.0  0.0  0.0  0.0
2020-01-07  0.0  0.0  0.0  0.0  0.0
2020-01-08  0.0  0.0  0.0  0.0  0.0
Non-zero weights: 0
Max weight: 0.0

=== COMPARISON OF RESULTS ===
Equal weight strategy - final value: $1,000,000.00
Patched generator - final value: $1,000,000.00

Exported allocation history to S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\tests\output/allocation_history_debug.csv

=== ALLOCATION HISTORY DEBUG COMPLETED ===

=== DEBUG COMPLETED SUCCESSFULLY ===
Final value: $1,000,000.00
Total return: 0.00%
