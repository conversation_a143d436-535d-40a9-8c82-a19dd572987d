# reporting/allocation_report.py
"""
reporting/allocation_report.py

Generate rebalance period signal and allocation list reports:
- XLSX workbook with two tabs
- Weekly high-quality image plots

Note: This report uses only base/default parameter settings and does not iterate through parameter combinations (no looping).
"""
import pandas as pd
import matplotlib.pyplot as plt
from pathlib import Path
from datetime import datetime


def _write_default_parameters(writer: pd.ExcelWriter, params: dict, sheet_name: str = 'performance') -> None:
    """
    Writes a single-row sheet listing parameter names and their values.
    """
    df = pd.DataFrame([params])
    df.to_excel(writer, sheet_name=sheet_name, index=False)


import sys

def generate_rebalance_report(
    signal_df: pd.DataFrame,
    allocation_df: pd.DataFrame,
    out_dir: Path,
    freq: str,
    strategy_name: str,
    portfolio_values=None,
    include_cash=False
) -> None:
    """
    Writes signal/allocation reports and weekly allocation image with clear warnings and output confirmation.
    """
    out_dir = Path(out_dir)
    out_dir.mkdir(parents=True, exist_ok=True)
    timestamp = datetime.now().strftime("%Y-%m-%d_%H%M%S")
    base_name = f"signal_alloc_history_{timestamp}"
    excel_path = out_dir / f"{base_name}.xlsx"
    image_path = out_dir / f"{base_name}_weekly.png"

    # Check DataFrame validity
    if signal_df.empty or allocation_df.empty:
        print("WARNING: signal_df or allocation_df is empty. No report/image will be created.")
        return
    if not isinstance(allocation_df.index, pd.DatetimeIndex):
        print("WARNING: allocation_df index is not a DatetimeIndex. No image will be created.")

    # Convert DatetimeIndex to date-only for Excel output
    if isinstance(signal_df.index, pd.DatetimeIndex):
        signal_df_excel = signal_df.copy()
        signal_df_excel.index = signal_df_excel.index.date
    else:
        signal_df_excel = signal_df
        
    if isinstance(allocation_df.index, pd.DatetimeIndex):
        allocation_df_excel = allocation_df.copy()
        allocation_df_excel.index = allocation_df_excel.index.date
    else:
        allocation_df_excel = allocation_df
    
    # Calculate actual dollar allocations and percentage allocations including cash
    if portfolio_values is not None:
        try:
            # Create dollar allocation dataframe
            dollar_allocation_df = pd.DataFrame(index=allocation_df.index, columns=allocation_df.columns)
            
            # Calculate dollar allocation based on weights and portfolio value
            for date in allocation_df.index:
                if date in portfolio_values.index:
                    total_value = portfolio_values.loc[date, 'total_value']
                    for asset in allocation_df.columns:
                        weight = allocation_df.loc[date, asset]
                        if pd.notna(weight):
                            dollar_allocation_df.loc[date, asset] = total_value * weight
            
            # Forward fill to handle missing dates
            dollar_allocation_df = dollar_allocation_df.ffill()
            
            # Calculate percentage allocations (rounded to 1 decimal) including cash
            percentage_allocation_df = pd.DataFrame(index=dollar_allocation_df.index, columns=list(dollar_allocation_df.columns))
            
            if include_cash:
                percentage_allocation_df['Cash'] = 0.0  # Add cash column
            
            # Calculate percentages including cash allocation
            for date in dollar_allocation_df.index:
                if date in portfolio_values.index:
                    total_value = portfolio_values.loc[date, 'total_value']
                    allocated_value = 0.0
                    
                    # Sum allocated values
                    for asset in dollar_allocation_df.columns:
                        value = dollar_allocation_df.loc[date, asset]
                        if pd.notna(value):
                            allocated_value += value
                            percentage_allocation_df.loc[date, asset] = round((value / total_value) * 100, 1)  # as percentage with 1 decimal
                    
                    # Calculate cash as remainder
                    if include_cash:
                        cash_value = total_value - allocated_value
                        percentage_allocation_df.loc[date, 'Cash'] = round((cash_value / total_value) * 100, 1)  # as percentage with 1 decimal
            
            # Convert dollar values to match Excel formatting
            dollar_allocation_df = dollar_allocation_df.round(2)
            
            # Prepare dollar allocation for Excel (convert DatetimeIndex to date)
            if isinstance(dollar_allocation_df.index, pd.DatetimeIndex):
                dollar_allocation_excel = dollar_allocation_df.copy()
                dollar_allocation_excel.index = dollar_allocation_excel.index.date
            else:
                dollar_allocation_excel = dollar_allocation_df
            
            # Prepare percentage allocation for Excel (convert DatetimeIndex to date)
            if isinstance(percentage_allocation_df.index, pd.DatetimeIndex):
                percentage_allocation_excel = percentage_allocation_df.copy()
                percentage_allocation_excel.index = percentage_allocation_excel.index.date
            else:
                percentage_allocation_excel = percentage_allocation_df
                
        except Exception as e:
            print(f"WARNING: Failed to calculate dollar/percentage allocations: {e}")
            dollar_allocation_excel = None
            percentage_allocation_excel = None
    else:
        dollar_allocation_excel = None
        percentage_allocation_excel = None
    
    # Write Excel file with date-only index
    with pd.ExcelWriter(excel_path, engine='openpyxl') as writer:
        signal_df_excel.to_excel(writer, sheet_name='signal_history')
        allocation_df_excel.to_excel(writer, sheet_name='allocation_history')
        
        # Add dollar allocation sheet if available
        if dollar_allocation_excel is not None:
            dollar_allocation_excel.to_excel(writer, sheet_name='dollar_allocation')
            
        # Add percentage allocation sheet if available
        if percentage_allocation_excel is not None:
            percentage_allocation_excel.to_excel(writer, sheet_name='percentage_allocation')
            
    print(f"Signal/allocation Excel report saved: {excel_path}")

    # Write image if possible
    if isinstance(allocation_df.index, pd.DatetimeIndex) and not allocation_df.empty:
        try:
            # Resample to weekly frequency
            resampled = allocation_df.resample(freq).last()
            
            # Set a professional style
            plt.style.use('seaborn-v0_8-whitegrid')
            
            # Create the stacked bar chart with improved colors
            fig, ax = plt.subplots(figsize=(12, 8), dpi=300)  # Higher DPI for crisper image
            
            # Use a professional color palette
            colors = plt.cm.tab10.colors
            
            # If we have percentage allocations with cash, use those for the plot instead
            if 'percentage_allocation_df' in locals() and percentage_allocation_df is not None:
                plot_df = percentage_allocation_df.resample(freq).last()
            else:
                # Otherwise use the original allocations (as percentage)
                plot_df = resampled * 100  # Convert to percentages
                
            plot_df.plot(kind='bar', stacked=True, ax=ax, color=colors, width=0.8)
            
            # Add grid lines for better readability
            ax.grid(axis='y', linestyle='--', alpha=0.7)
            
            # Set chart title and labels with improved fonts
            ax.set_title(f"{strategy_name} Allocation ({freq} frequency)", fontsize=14, fontweight='bold')
            ax.set_xlabel("Date", fontsize=12)
            ax.set_ylabel("Allocation %", fontsize=12)
            
            # Format y-axis to show percentages without % symbol (already in the values)
            ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, _: f'{x:.0f}'))
            
            # Format x-axis to show only years
            # Get current tick positions and labels
            locs, labels = plt.xticks()
            
            # Create new labels showing only the year
            new_labels = []
            prev_year = None
            
            for i, loc in enumerate(locs):
                if i < len(resampled.index):
                    current_year = resampled.index[i].year
                    # Only show year when it changes
                    if current_year != prev_year:
                        new_labels.append(str(current_year))
                        prev_year = current_year
                    else:
                        new_labels.append('')
                else:
                    new_labels.append('')
            
            # Apply the new labels
            plt.xticks(locs, new_labels, rotation=45, ha="right")
            
            # Adjust layout
            plt.tight_layout()
            # Add a legend with better positioning and formatting
            legend = ax.legend(loc='upper center', bbox_to_anchor=(0.5, -0.15), 
                       ncol=min(5, len(resampled.columns)), frameon=True, fontsize=10)
            
            # Ensure the figure has enough space for the legend
            plt.tight_layout(rect=[0, 0.1, 1, 0.95])
            
            # Save with higher DPI for crisper image
            fig.savefig(image_path, dpi=600, bbox_inches='tight')
            plt.close(fig)
            print(f"Allocation image saved: {image_path}")
        except Exception as e:
            print(f"WARNING: Failed to create allocation image: {e}")
    else:
        print("WARNING: Allocation image not created due to missing/invalid index.")
