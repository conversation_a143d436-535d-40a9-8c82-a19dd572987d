#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
v4/py_reporting/report_optimization.py

Optimization Reporting Module for CPS V4 Performance Reporting

This module contains all optimization-specific reporting functionality extracted from v4_performance_report.py:
- Optimization report generation for multiple parameter combinations
- Optimization results aggregation and analysis
- Optimization Excel report generation
- Parameter combination summary and ranking

Functions included:
- generate_optimization_report(): Main optimization report generation function
- _generate_optimization_excel(): Excel report generation for optimization results
- _analyze_optimization_results(): Analysis and ranking of optimization results
- _format_optimization_summary(): Format optimization results for display

Author: AI Assistant
Date: 2025-07-26 (Refactored from v4_performance_report.py)
"""

import os
import logging
import pandas as pd
from pathlib import Path
from typing import Dict, List, Tuple, Any, Optional

# Set up logging
logger = logging.getLogger(__name__)


def generate_optimization_report(
    optimization_results: List[Tuple[Dict[str, Any], Dict[str, Any]]],
    output_dir: str,
    strategy_name: str,
    settings: Dict[str, Any],
    **kwargs
) -> str:
    """
    Generate optimization report from multiple backtest results.
    
    Args:
        optimization_results: List of (parameters, results) tuples from optimization runs
        output_dir: Directory to save the report
        strategy_name: Name of the strategy
        settings: Settings dictionary
        **kwargs: Additional parameters
        
    Returns:
        str: Path to the generated optimization report
    """
    try:
        logger.info(f"Generating optimization report for {len(optimization_results)} parameter combinations")
        
        # Create output directory
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        # Generate timestamp for filename
        import datetime
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Generate Excel report
        excel_filename = f"{strategy_name}_optimization_report_{timestamp}.xlsx"
        excel_path = output_path / excel_filename
        
        # Import Excel generation function
        from .report_excel import _generate_optimization_excel
        
        excel_report_path = _generate_optimization_excel(
            optimization_results=optimization_results,
            output_path=excel_path,
            strategy_name=strategy_name,
            settings=settings
        )
        
        # Generate summary analysis
        summary_path = _generate_optimization_summary(
            optimization_results=optimization_results,
            output_path=output_path,
            strategy_name=strategy_name,
            timestamp=timestamp
        )
        
        # Export validation files if requested
        export_validation = kwargs.get('export_simple_validation_files', False)
        if export_validation:
            logger.info("Exporting optimization validation files")
            validation_dir = _export_optimization_validation(
                optimization_results=optimization_results,
                output_dir=str(output_path),
                prefix=f"{strategy_name}_optimization_"
            )
            logger.info(f"Optimization validation files exported to: {validation_dir}")
        
        logger.info(f"Optimization report generated: {excel_report_path}")
        return excel_report_path
        
    except Exception as e:
        logger.error(f"Error generating optimization report: {e}")
        raise


def _generate_optimization_summary(
    optimization_results: List[Tuple[Dict[str, Any], Dict[str, Any]]],
    output_path: Path,
    strategy_name: str,
    timestamp: str
) -> str:
    """
    Generate text summary of optimization results.
    
    Args:
        optimization_results: List of (parameters, results) tuples
        output_path: Directory to save summary
        strategy_name: Name of the strategy
        timestamp: Timestamp for filename
        
    Returns:
        str: Path to the generated summary file
    """
    try:
        summary_filename = f"{strategy_name}_optimization_summary_{timestamp}.txt"
        summary_path = output_path / summary_filename
        
        # Analyze results
        analysis = _analyze_optimization_results(optimization_results)
        
        # Write summary
        with open(summary_path, 'w') as f:
            f.write(f"Optimization Report Summary - {strategy_name}\n")
            f.write("=" * 60 + "\n\n")
            
            f.write(f"Total Parameter Combinations: {len(optimization_results)}\n")
            f.write(f"Generated: {timestamp}\n\n")
            
            # Best performing combinations
            f.write("Top 5 Performing Combinations (by Sharpe Ratio):\n")
            f.write("-" * 50 + "\n")
            
            for i, result in enumerate(analysis['top_sharpe'][:5], 1):
                params, metrics = result
                f.write(f"{i}. Sharpe: {metrics.get('sharpe_ratio', 'N/A'):.4f}, ")
                f.write(f"Return: {metrics.get('total_return', 'N/A'):.2%}, ")
                f.write(f"Params: {params}\n")
            
            f.write("\n")
            
            # Summary statistics
            f.write("Summary Statistics:\n")
            f.write("-" * 20 + "\n")
            f.write(f"Best Sharpe Ratio: {analysis['stats']['best_sharpe']:.4f}\n")
            f.write(f"Worst Sharpe Ratio: {analysis['stats']['worst_sharpe']:.4f}\n")
            f.write(f"Average Sharpe Ratio: {analysis['stats']['avg_sharpe']:.4f}\n")
            f.write(f"Best Total Return: {analysis['stats']['best_return']:.2%}\n")
            f.write(f"Worst Total Return: {analysis['stats']['worst_return']:.2%}\n")
            f.write(f"Average Total Return: {analysis['stats']['avg_return']:.2%}\n")
        
        logger.debug(f"Optimization summary saved to: {summary_path}")
        return str(summary_path)
        
    except Exception as e:
        logger.error(f"Error generating optimization summary: {e}")
        return ""


def _analyze_optimization_results(optimization_results: List[Tuple[Dict[str, Any], Dict[str, Any]]]) -> Dict[str, Any]:
    """
    Analyze optimization results and provide rankings and statistics.
    
    Args:
        optimization_results: List of (parameters, results) tuples
        
    Returns:
        Dict containing analysis results
    """
    try:
        if not optimization_results:
            return _get_empty_analysis()
        
        # Extract metrics
        sharpe_ratios = []
        total_returns = []
        max_drawdowns = []
        
        for params, results in optimization_results:
            sharpe_ratios.append(results.get('sharpe_ratio', 0.0))
            total_returns.append(results.get('total_return', 0.0))
            max_drawdowns.append(results.get('max_drawdown', 0.0))
        
        # Sort by different metrics
        top_sharpe = sorted(optimization_results, 
                          key=lambda x: x[1].get('sharpe_ratio', 0.0), 
                          reverse=True)
        
        top_return = sorted(optimization_results, 
                          key=lambda x: x[1].get('total_return', 0.0), 
                          reverse=True)
        
        best_drawdown = sorted(optimization_results, 
                             key=lambda x: abs(x[1].get('max_drawdown', 0.0)))
        
        # Calculate statistics
        stats = {
            'best_sharpe': max(sharpe_ratios) if sharpe_ratios else 0.0,
            'worst_sharpe': min(sharpe_ratios) if sharpe_ratios else 0.0,
            'avg_sharpe': sum(sharpe_ratios) / len(sharpe_ratios) if sharpe_ratios else 0.0,
            'best_return': max(total_returns) if total_returns else 0.0,
            'worst_return': min(total_returns) if total_returns else 0.0,
            'avg_return': sum(total_returns) / len(total_returns) if total_returns else 0.0,
            'best_drawdown': min(max_drawdowns) if max_drawdowns else 0.0,
            'worst_drawdown': max(max_drawdowns) if max_drawdowns else 0.0,
            'avg_drawdown': sum(max_drawdowns) / len(max_drawdowns) if max_drawdowns else 0.0
        }
        
        return {
            'top_sharpe': top_sharpe,
            'top_return': top_return,
            'best_drawdown': best_drawdown,
            'stats': stats
        }
        
    except Exception as e:
        logger.error(f"Error analyzing optimization results: {e}")
        return _get_empty_analysis()


def _get_empty_analysis() -> Dict[str, Any]:
    """Return empty analysis structure."""
    return {
        'top_sharpe': [],
        'top_return': [],
        'best_drawdown': [],
        'stats': {
            'best_sharpe': 0.0,
            'worst_sharpe': 0.0,
            'avg_sharpe': 0.0,
            'best_return': 0.0,
            'worst_return': 0.0,
            'avg_return': 0.0,
            'best_drawdown': 0.0,
            'worst_drawdown': 0.0,
            'avg_drawdown': 0.0
        }
    }


def _export_optimization_validation(
    optimization_results: List[Tuple[Dict[str, Any], Dict[str, Any]]],
    output_dir: str,
    prefix: str = ''
) -> str:
    """
    Export optimization validation files.
    
    Args:
        optimization_results: List of (parameters, results) tuples
        output_dir: Directory to save validation files
        prefix: Optional filename prefix
        
    Returns:
        str: Path to validation directory
    """
    try:
        # Create validation directory
        validation_dir = Path(output_dir) / "optimization_validation"
        validation_dir.mkdir(parents=True, exist_ok=True)
        
        # Export parameter combinations
        params_file = validation_dir / f"{prefix}parameter_combinations.csv"
        params_data = []
        
        for i, (params, results) in enumerate(optimization_results):
            row = {'combination_id': i + 1}
            row.update(params)
            row.update(results)
            params_data.append(row)
        
        if params_data:
            df = pd.DataFrame(params_data)
            df.to_csv(params_file, index=False)
        
        # Export summary statistics
        summary_file = validation_dir / f"{prefix}optimization_summary.txt"
        analysis = _analyze_optimization_results(optimization_results)
        
        with open(summary_file, 'w') as f:
            f.write("Optimization Validation Summary\n")
            f.write("=" * 35 + "\n\n")
            f.write(f"Total Combinations: {len(optimization_results)}\n")
            f.write(f"Parameter File: {params_file.name}\n\n")
            
            f.write("Performance Statistics:\n")
            for key, value in analysis['stats'].items():
                if 'return' in key or 'drawdown' in key:
                    f.write(f"{key}: {value:.2%}\n")
                else:
                    f.write(f"{key}: {value:.4f}\n")
        
        logger.debug(f"Optimization validation files exported to: {validation_dir}")
        return str(validation_dir)
        
    except Exception as e:
        logger.error(f"Error exporting optimization validation: {e}")
        return ""
