#!/usr/bin/env python
# -*- coding: utf-8 -*-
# v4_performance_report.py
"""
V4 Performance Reporting Module - Main Shell

This is the main shell file for CPS V4 performance reporting functionality.
The actual implementation has been refactored into focused modules in the report_modules/ directory:

- report_modules/report_excel.py: Excel report generation and verification
- report_modules/report_metrics.py: Performance metrics calculation  
- report_modules/report_validation.py: Validation file export functionality
- report_modules/report_optimization.py: Optimization reporting functionality

This shell file maintains the exact same public API as the original v4_performance_report.py
to ensure complete backward compatibility with all existing code.

## Main Functions:
- generate_performance_report(): Main performance report generation function
- generate_optimization_report(): Optimization report generation function
- verify_excel_report(): Excel report verification function
- export_validation_files(): Validation file export function

## Validation Output Flag (2025-06-12)

Set `export_simple_validation_files = True` in the [report] section of your settings_parameters_v4.ini
or pass as a keyword argument to generate_performance_report().
When enabled, the reporting module will output simple CSV/TXT validation files for all key report data
(allocation history, signal history, trades, metrics, etc.) to a validation_data/ subdirectory, using ONLY real production backtest results.

Author: AI Assistant
Date: 2025-07-26 (Refactored from original 883-line monolithic file)
"""

import os
import sys
import logging
import datetime
import traceback
from pathlib import Path
from typing import Dict, List, Tuple, Any, Optional

# Set up logging
logger = logging.getLogger(__name__)

# Add the project root to the path if needed to ensure imports work properly
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# Import V4 settings module
try:
    from v4.settings.settings_CPS_v4 import load_settings
except ImportError as e:
    logger.error(f"Error importing V4 settings module: {e}")
    raise

# Import all functions from the refactored modules
try:
    from .report_modules import (
        _generate_excel_report,
        _generate_optimization_excel,
        verify_excel_report,
        _write_simple_output,
        REQUIRED_SHEETS,
        _calculate_performance_metrics,
        export_validation_files,
        _export_signal_history,
        _export_allocation_history,
        _export_performance_metrics,
        _export_trade_log,
        _export_portfolio_values,
        generate_optimization_report
    )
except ImportError as e:
    logger.error(f"Error importing refactored modules: {e}")
    raise

# Import matrix optimization functions from the new module
try:
    from .report_modules.report_matrix_optimization import (
        get_optimization_combinations,
        _run_matrix_optimization,
        _validate_single_combination,
        _run_pipeline_for_combination,
        _create_temp_settings_for_combination,
        _load_unified_portfolio_for_combination,
        _setup_validation_directories,
        _log_validation_step,
        generate_combo_id,
        _cleanup_temp_settings,
        _load_existing_equity_curve,
        _load_price_data
    )
except ImportError as e:
    logger.error(f"Error importing matrix optimization module: {e}")
    raise


def generate_performance_report(
    backtest_results: Dict[str, Any],
    settings: Dict[str, Any],
    strategy_name: str,
    output_dir: Optional[str] = None,
    is_new_file: bool = True,
    **kwargs
) -> str:
    """
    Generate a performance report using CPS V4 parameters.
    
    Args:
        backtest_results: Results dictionary from backtest (must be real production output)
        settings: Settings dictionary containing configuration parameters
        strategy_name: Name of the strategy
        output_dir: Optional directory to save the report (defaults to settings value)
        is_new_file: Whether to create a new file
        **kwargs: Additional parameters
            - export_simple_validation_files (bool): If True, always export simple CSV/TXT validation files to validation_data/ using only real production data.
    Returns:
        str: Path to the generated report file
    Raises:
        ValueError: If required parameters are missing
        FileNotFoundError: If output directory doesn't exist
    """
    try:
        # Validate settings passed as an argument
        if not settings:
            raise ValueError("Settings dictionary is empty or None")

        # Validate backtest results
        if not backtest_results:
            raise ValueError("Backtest results are empty or None")

        # Get reporting parameters directly from settings
        reporting_settings = settings.get('Report', {})
        create_excel = reporting_settings.get('create_excel', False)
        
        # --- Validation Output: Always real data, never synthetic ---
        export_validation = (
            kwargs.get('export_simple_validation_files') is True
            or reporting_settings.get('export_simple_validation_files', False) is True
        )
        debug_mode = kwargs.get('debug_mode', False)

        # If no reporting is needed, exit early
        if not create_excel and not (export_validation or debug_mode):
            logger.info("No report generation (Excel or CSV) is enabled in settings.")
            return "No report generated."

        # Determine output directory
        if output_dir is None:
            output_dir = reporting_settings.get('output_directory', 'output')
        output_path = Path(output_dir)
        if not output_path.exists():
            output_path.mkdir(parents=True, exist_ok=True)
            logger.info(f"Created output directory: {output_path}")

        report_path = "" # Will hold the path of the generated excel file

        # Generate Excel report if enabled
        if create_excel:
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            report_filename = f"performance_report_{strategy_name}_{timestamp}.xlsx"
            excel_report_path = output_path / report_filename
            logger.info(f"Generating V4 performance report for {strategy_name} at {excel_report_path}")

            _generate_excel_report(
                backtest_results=backtest_results,
                output_path=excel_report_path,
                strategy_name=strategy_name,
                settings=settings
            )
            report_path = str(excel_report_path)
            logger.info(f"Generated V4 performance report: {report_path}")

        # Export validation files if enabled
        if export_validation or debug_mode:
            logger.info("Validation output enabled - exporting validation files using real backtest data")
            validation_dir = export_validation_files(
                backtest_results=backtest_results,
                output_dir=output_dir,
                prefix=f"{strategy_name}_"
            )
            logger.info(f"Validation files exported to: {validation_dir}")

        return report_path if report_path else "CSV validation files generated."

    except Exception as e:
        logger.error(f"Error in generate_performance_report: {str(e)}")
        logger.error(traceback.format_exc())
        return f"Error generating report: {e}"
        raise


# Test function for backward compatibility
def test_main():
    """Test function to verify module functionality."""
    try:
        print("Testing v4_performance_report module...")
        
        # Test settings loading
        settings = load_settings()
        print(f"Successfully loaded {len(settings)} settings sections")
    except Exception as e:
        print(f"Error loading settings: {e}")


class PerformanceTableGenerator:
    """
    Performance Table Generator class that delegates to the matrix optimization module.
    
    This class provides backward compatibility with the original performance_table_generator.py
    by exposing the same interface while using the refactored modular implementation.
    """
    
    def __init__(self, config_path=None, csv_flag_use=False):
        """Initialize with configuration file path."""
        from v4.config.paths_v4 import V4_SETTINGS_FILE
        
        self.config_path = config_path or str(V4_SETTINGS_FILE)
        self.csv_flag_use = csv_flag_use
        self.timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Validation mode for step-by-step optimization validation
        self.validation_mode = False
        self.validation_dir = None
        
        logger.info(f"Initialized PerformanceTableGenerator with timestamp: {self.timestamp}, CSV generation: {self.csv_flag_use}")
    
    def get_optimization_combinations(self):
        """Generate all combinations of optimization parameters - PUBLIC METHOD."""
        return get_optimization_combinations(
            config_path=self.config_path,
            validation_mode=self.validation_mode,
            validation_dir=self.validation_dir
        )
    
    def _validate_single_combination(self, combination):
        """Minimal validation - run single real backtest for validation."""
        return _validate_single_combination(
            combination=combination,
            config_path=self.config_path,
            validation_mode=self.validation_mode,
            validation_dir=self.validation_dir
        )
    
    def _run_matrix_optimization(self, combinations):
        """Run matrix-based optimization using EquityCurvesManager for robust data handling."""
        return _run_matrix_optimization(
            combinations=combinations,
            validation_mode=self.validation_mode,
            validation_dir=self.validation_dir,
            csv_flag_use=self.csv_flag_use
        )
    
    def _run_pipeline_for_combination(self, combination):
        """Run unified pipeline for a specific parameter combination and return equity curve."""
        return _run_pipeline_for_combination(combination)
    
    def _create_temp_settings_for_combination(self, combination):
        """Create a temporary settings file for this optimization combination."""
        return _create_temp_settings_for_combination(
            combination=combination,
            validation_mode=self.validation_mode,
            validation_dir=self.validation_dir
        )
    
    def _load_unified_portfolio_for_combination(self, combo_id, timestamp):
        """Load unified portfolio file for specific optimization combination."""
        return _load_unified_portfolio_for_combination(combo_id, timestamp)
    
    def _setup_validation_directories(self):
        """Set up directories for validation artifacts with timestamps."""
        self.validation_dir = _setup_validation_directories(self.validation_dir)
    
    def _log_validation_step(self, step_number, step_name, status="RUNNING", details=None):
        """Log validation step information and update status."""
        _log_validation_step(
            step_number=step_number,
            step_name=step_name,
            status=status,
            details=details,
            validation_dir=self.validation_dir
        )

    def generate_performance_table(self, output_dir=None):
        """Generate complete Performance Table XLSX report - BACKWARD COMPATIBILITY METHOD."""
        try:
            logger.info("Starting Performance Table generation via refactored system...")

            from v4.config.paths_v4 import OUTPUT_DIR
            from .report_modules.report_pipeline_excel import generate_performance_table_from_pipeline_results
            import pandas as pd
            import glob
            import json

            output_path = Path(output_dir) if output_dir else OUTPUT_DIR

            # Check if optimization data exists (look for recent optimization files)
            optimization_equity_files = list(output_path.glob("optimization_equity_curves_*.csv"))

            if optimization_equity_files:
                # OPTIMIZATION MODE: Load optimization data and generate full XLSX
                logger.info("Optimization data detected - generating full Performance Table XLSX with optimization results")

                # Get the most recent optimization file
                latest_equity_file = max(optimization_equity_files, key=lambda f: f.stat().st_mtime)
                logger.info(f"Using optimization equity file: {latest_equity_file}")

                # Load optimization equity matrix
                equity_matrix = pd.read_csv(latest_equity_file, index_col=0, parse_dates=True)
                logger.info(f"Loaded optimization equity matrix: {equity_matrix.shape}")

                # Load metadata if available
                metadata_file = latest_equity_file.with_name(latest_equity_file.name.replace("equity_curves", "equity_curves_metadata")).with_suffix(".json")
                combination_metadata = {}
                if metadata_file.exists():
                    with open(metadata_file, 'r') as f:
                        combination_metadata = json.load(f)
                    logger.info(f"Loaded optimization metadata: {len(combination_metadata)} combinations")

                # Create mock pipeline results structure for the XLSX generator
                # We need to load base case data for the other tabs (Signal History, Allocation History, Trade Log)
                base_case_results = self._load_base_case_pipeline_data()

                # Add optimization equity matrix to results
                base_case_results['optimization_equity_matrix'] = equity_matrix
                base_case_results['optimization_metadata'] = combination_metadata

                # Generate the full XLSX using the pipeline Excel generator
                xlsx_filepath = generate_performance_table_from_pipeline_results(
                    results=base_case_results,
                    signals_df=base_case_results.get('signal_history'),
                    output_dir=str(output_path)
                )

                logger.info(f"Performance Table XLSX generated with optimization data: {xlsx_filepath}")
                return xlsx_filepath

            else:
                # SINGLE MODE: No optimization data, generate basic XLSX
                logger.info("No optimization data found - generating basic Performance Table XLSX")

                # Load base case data
                base_case_results = self._load_base_case_pipeline_data()

                # Generate basic XLSX
                xlsx_filepath = generate_performance_table_from_pipeline_results(
                    results=base_case_results,
                    signals_df=base_case_results.get('signal_history'),
                    output_dir=str(output_path)
                )

                logger.info(f"Basic Performance Table XLSX generated: {xlsx_filepath}")
                return xlsx_filepath

        except Exception as e:
            logger.error(f"Error generating Performance Table via refactored system: {e}")
            raise

    def _load_base_case_pipeline_data(self):
        """Load base case pipeline data for XLSX generation."""
        try:
            from v4.config.paths_v4 import OUTPUT_DIR
            import pandas as pd

            logger.info("Loading base case pipeline data for XLSX generation...")

            # Load the most recent data files from reporting directory
            results = {}

            # Load signal history
            signal_files = list(OUTPUT_DIR.glob("signal_history_*.csv"))
            if signal_files:
                latest_signal_file = max(signal_files, key=lambda f: f.stat().st_mtime)
                results['signal_history'] = pd.read_csv(latest_signal_file, index_col=0, parse_dates=True)
                logger.info(f"Loaded signal history: {results['signal_history'].shape}")
            else:
                logger.warning("No signal history files found")
                results['signal_history'] = pd.DataFrame()

            # Load allocation history
            allocation_files = list(OUTPUT_DIR.glob("allocation_history_*.csv"))
            if allocation_files:
                latest_allocation_file = max(allocation_files, key=lambda f: f.stat().st_mtime)
                results['allocation_history'] = pd.read_csv(latest_allocation_file, index_col=0, parse_dates=True)
                logger.info(f"Loaded allocation history: {results['allocation_history'].shape}")
            else:
                logger.warning("No allocation history files found")
                results['allocation_history'] = pd.DataFrame()

            # Load trade log
            trade_files = list(OUTPUT_DIR.glob("trade_log_*.csv"))
            if trade_files:
                latest_trade_file = max(trade_files, key=lambda f: f.stat().st_mtime)
                results['trade_log'] = pd.read_csv(latest_trade_file)
                logger.info(f"Loaded trade log: {results['trade_log'].shape}")
            else:
                logger.warning("No trade log files found")
                results['trade_log'] = pd.DataFrame()

            return results

        except Exception as e:
            logger.error(f"Error loading base case pipeline data: {e}")
            # Return empty structure if loading fails
            return {
                'signal_history': pd.DataFrame(),
                'allocation_history': pd.DataFrame(),
                'trade_log': pd.DataFrame()
            }

    def generate_combo_id(self, combination_params):
        """Generate unique, readable combination identifier."""
        return generate_combo_id(combination_params)
    
    def _cleanup_temp_settings(self, temp_settings_path):
        """Clean up temporary settings file with proper Windows file lock handling."""
        return _cleanup_temp_settings(temp_settings_path)
    
    def _load_existing_equity_curve(self, combination):
        """Load equity curve from existing pipeline results (single mode)."""
        return _load_existing_equity_curve(combination)
    
    def _load_price_data(self, data_dir=None):
        """Load the most recent price data file, preferring main data directory."""
        return _load_price_data(data_dir)


if __name__ == "__main__":
    test_main()
