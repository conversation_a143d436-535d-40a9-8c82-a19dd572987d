"""
v4/models/ema_signal_bridge.py
Bridge module to connect EMASignalGenerator with ema_allocation_model for tracing.

This module provides a function to run the EMA allocation model in trace mode
and save the detailed signal history data to CSV files.
"""

import pandas as pd
import logging
from pathlib import Path
import os

from v4.models.ema_allocation_model_v4 import ema_allocation_model_updated_single
from v4.utils.tracing_utils import save_df_to_trace_dir

logger = logging.getLogger(__name__)

def run_ema_model_with_tracing_single(price_data, **params):
    """
    Run the EMA allocation model in trace mode and save the detailed signal history data.

    Args:
        price_data (DataFrame): Historical price data
        **params: Additional parameters for the EMA model

    Returns:
        dict: Asset allocations with timestamp keys {pd.Timestamp: {symbol: weight}}
    """
    logger.info("Running EMA model with tracing enabled")

    # Run the EMA model in trace mode
    trace_results = ema_allocation_model_updated_single(
        price_data=price_data,
        trace_mode=True,
        **params
    )
    
    # Unpack the results
    (dict_weights, ratios_output, ranks_df, signal_output, 
     short_ema_df, med_ema_df, long_ema_df,
     stmtemax_hist_df, mtltemax_hist_df, emaxavg_hist_df) = trace_results
    
    # Save the detailed signal history data to CSV files
    try:
        # Save EMA averages with current timestamp
        current_timestamp = pd.Timestamp.now().strftime("%Y%m%d_%H%M%S")
        
        # Save individual EMA dataframes to match expected filenames
        # Save short EMA
        save_df_to_trace_dir(short_ema_df, f"ema_short_{current_timestamp}.csv",
                            step_description="Short-term EMA History")
        
        # Save medium EMA
        save_df_to_trace_dir(med_ema_df, f"ema_medium_{current_timestamp}.csv",
                            step_description="Medium-term EMA History")
        
        # Save long EMA
        save_df_to_trace_dir(long_ema_df, f"ema_long_{current_timestamp}.csv",
                            step_description="Long-term EMA History")
        
        # WORKING - ONLY CHANGE WITH PERMISSION
        # Format ranking data as a matrix with multi-level columns
        # Convert ranks_df to a pivoted format with Date as index and multi-level columns
        if 'Date' in ranks_df.columns and 'Asset' in ranks_df.columns:
            # Create a copy to avoid modifying the original
            ranks_pivot_df = ranks_df.copy()
            
            # Ensure Date is datetime
            ranks_pivot_df['Date'] = pd.to_datetime(ranks_pivot_df['Date'])
            
            # Create separate DataFrames for EMAXAvg_Value and Rank_Ordinal
            emaxavg_df = ranks_pivot_df.pivot(index='Date', columns='Asset', values='EMAXAvg_Value')
            rank_df = ranks_pivot_df.pivot(index='Date', columns='Asset', values='Rank_Ordinal')
            
            # Create multi-level columns
            emaxavg_df.columns = pd.MultiIndex.from_product([['EMAXAvg_Value'], emaxavg_df.columns])
            rank_df.columns = pd.MultiIndex.from_product([['Rank_Ordinal'], rank_df.columns])
            
            # Combine into a single DataFrame with multi-level columns
            matrix_ranks_df = pd.concat([emaxavg_df, rank_df], axis=1)
            
            # Save the matrix-formatted ranking data
            save_df_to_trace_dir(matrix_ranks_df, f"ranking_{current_timestamp}.csv",
                                step_description="Asset Ranking History (Matrix Format)")
        else:
            # Fallback to original format if expected columns are missing
            logger.warning("Cannot create matrix format for ranking data - missing required columns")
            save_df_to_trace_dir(ranks_df, f"ranking_{current_timestamp}.csv",
                                step_description="Asset Ranking History")
        
        # Signal history full CSV generation removed as requested
        
        # Also keep the original combined files for backward compatibility
        # Save combined EMA averages
        ema_averages = pd.concat([short_ema_df, med_ema_df, long_ema_df], axis=1)
        ema_averages.columns = [f"{col}_ST" for col in short_ema_df.columns] + \
                              [f"{col}_MT" for col in med_ema_df.columns] + \
                              [f"{col}_LT" for col in long_ema_df.columns]
        save_df_to_trace_dir(ema_averages, f"02_ema_average_history_{current_timestamp}.csv", 
                            step_description="Combined EMA Averages History")
        
        # Save raw signal history (EMA ratios)
        raw_signals = pd.concat([stmtemax_hist_df, mtltemax_hist_df, emaxavg_hist_df], axis=1)
        raw_signals.columns = [f"{col}_STMTEMAX" for col in stmtemax_hist_df.columns] + \
                              [f"{col}_MTLTEMAX" for col in mtltemax_hist_df.columns] + \
                              [f"{col}_EMAXAvg" for col in emaxavg_hist_df.columns]
        save_df_to_trace_dir(raw_signals, f"04_raw_algocalc_history_{current_timestamp}.csv", 
                            step_description="Raw Algorithm Calculation History")
        
        logger.info("Successfully saved detailed signal history data to CSV files including individual EMA files")
    except Exception as e:
        logger.error(f"Failed to save detailed signal history data: {e}")
    
    # Create a proper date matrix DataFrame for the full signal history
    # Initialize with zeros, using price_data's dates and tickers
    signal_history = pd.DataFrame(0.0, index=price_data.index, columns=price_data.columns)
    signal_history.index.name = 'Date'
    
    logger.info(f"Initialized signal_history DataFrame with shape {signal_history.shape}")
    logger.info(f"Signal history date range: {signal_history.index[0]} to {signal_history.index[-1]}")
    logger.info(f"Ranks DataFrame shape: {ranks_df.shape}")
    logger.info(f"Ranks date range: {ranks_df.index[0]} to {ranks_df.index[-1]}")
    
    # Log initial ranks_df info
    logger.info(f"Initial ranks_df info:")
    logger.info(f"Index type: {type(ranks_df.index)}")
    logger.info(f"Columns: {ranks_df.columns.tolist()}")
    logger.info(f"Sample data:\n{ranks_df.head(2)}")
    
    # The ranks_df has columns: ['Asset', 'EMAXAvg_Value', 'Rank_Ordinal']
    # We need to ensure we have a proper date index from price_data
    allocation_count = 0
    signal_history = pd.DataFrame(0.0, index=price_data.index, columns=price_data.columns)
    signal_history.index.name = 'Date'
    
    try:
        # First, ensure we have a date column in ranks_df
        if 'Date' not in ranks_df.columns:
            # If not, we'll assume ranks_df is for the last date in price_data
            last_date = price_data.index[-1]
            ranks_df['Date'] = last_date
        
        # Convert Date to datetime if it's not already
        ranks_df['Date'] = pd.to_datetime(ranks_df['Date'])
        
        # Pivot the ranks DataFrame to get assets as columns
        ranks_pivot = ranks_df.pivot(index='Date', columns='Asset', values='Rank_Ordinal')
        logger.info(f"Pivoted ranks DataFrame shape: {ranks_pivot.shape}")
        logger.info(f"Pivoted columns: {ranks_pivot.columns.tolist()}")
        
        # For each date in price data, get the most recent ranks as of that date
        for i, date in enumerate(price_data.index):
            try:
                # Get all ranks up to and including the current date
                current_ranks = ranks_pivot[ranks_pivot.index <= date]
                
                if len(current_ranks) == 0:
                    logger.warning(f"No ranks available for {date}")
                    continue
                    
                # Get the most recent ranks
                latest_ranks = current_ranks.iloc[-1].dropna()
                
                if len(latest_ranks) >= 2:
                    # Sort by rank (ascending) and get top 2 assets
                    top_assets = latest_ranks.sort_values().head(2).index.tolist()
                    
                    if len(top_assets) >= 2:
                        # Allocate 60% to top asset, 40% to second
                        signal_history.loc[date, top_assets[0]] = 0.6
                        signal_history.loc[date, top_assets[1]] = 0.4
                        allocation_count += 1
                        
                        # Log only occasionally to avoid flooding the logs
                        if i % 50 == 0 or i == len(price_data) - 1:
                            logger.info(f"{date}: Allocated 60% to {top_assets[0]}, 40% to {top_assets[1]}")
                
            except Exception as e:
                logger.error(f"Error processing date {date}: {e}", exc_info=True)
                continue
                
    except Exception as e:
        logger.error(f"Error in signal generation: {e}", exc_info=True)
        # Fall back to empty signal history
        signal_history = pd.DataFrame(0.0, index=price_data.index, columns=price_data.columns)
    
    logger.info(f"Signal generation complete. Made {allocation_count} allocations.")
    logger.info(f"Final signal_history shape: {signal_history.shape}")
    logger.info(f"Non-zero allocations: {(signal_history != 0).sum().sum()}")
    
    return signal_history
            
# ---------------------------------------------------------------------------
# Attach class-like interface so backtest_v4.py can treat this function as an
# object with generate_signals/validate_signals methods (backward compatibility)
# ---------------------------------------------------------------------------

def _bridge_generate_signals_single(price_data, **params):
    """Alias for run_ema_model_with_tracing_single so backtest expects .generate_signals."""
    return run_ema_model_with_tracing_single(price_data, **params)

def _bridge_validate_signals_single(self, signals):
    """Placeholder validator – passthrough for now."""
    return signals

# Monkey-patch the function to have expected attributes
run_ema_model_with_tracing_single.generate_signals = _bridge_generate_signals_single  # type: ignore
run_ema_model_with_tracing_single.validate_signals = _bridge_validate_signals_single  # type: ignore

