# Signal Generation Handoff Documentation

## Current Status

We're debugging the EMA signal generation process in the CPS V4 backtest engine. The key issue is that signal-related CSVs (`signal_history_debug.csv`, `equal_signal_history_debug.csv`) show all zeros, indicating a fundamental issue with signal generation or processing.

## Key Files and Paths

- **Debug Script**: `v4/tests/allocation_debug.py`
- **Run Script**: `v4/tests/run_allocation_debug.bat`
- **Text Output**: `v4/tests/output/allocation_debug_output.txt`
- **CSV Outputs**: 
  - `v4/tests/output/allocation_history_debug.csv` (weights)
  - `v4/tests/output/signal_history_debug.csv` (patched signals)
  - `v4/tests/output/equal_signal_history_debug.csv` (equal weight signals)
- **Signal Logic**: `v4/engine/signal_generator.py` (focus on `EMASignalGenerator`)
- **Backtest Engine**: `v4/engine/backtest_v4.py`

## Next Steps - EMA Signal Tracing

We'll focus on tracing the EMA signal generation process in detail for the period **Jan 20, 2021 to Feb 28, 2021**.

### Planned Analysis

1. **Document Signal Generation Flow**:
   - Input data requirements and validation
   - EMA calculation steps (short/medium/long-term)
   - Trend strength calculation
   - Weight allocation logic

2. **Daily Calculation Logging**:
   - Modify `allocation_debug.py` to output intermediate calculation steps
   - Capture daily values for:
     - Price data inputs
     - EMA values (ST/MT/LT)
     - Trend strength calculations
     - Final signal weights

3. **Validation Checks**:
   - Verify price data inputs are valid
   - Check EMA calculations match expected values
   - Confirm trend strength logic
   - Validate signal normalization

4. **Backtest Integration**:
   - Trace how signals flow into `backtest_v4.py`
   - Verify signal storage in `signal_history`
   - Check rebalance event handling

## Action Items

- [ ] Create detailed documentation of EMA signal formulas
- [ ] Implement daily calculation logging in debug script
- [ ] Analyze output for the target date range
- [ ] Identify where/why signals are being zeroed out
- [ ] Implement fixes and verify with test runs
