# CRITICAL MEMORY RULES

## NEVER FALLBACKS
- NEVER implement fallback logic in parameter handling
- Code must use optimization flags as the PRIMARY control mechanism
- Parameters have optimization flag (true/false) that determines default vs override usage
- NO fallback from passed parameters to module defaults using globals()
- The optimization flag is the single source of truth for parameter selection

## Code Structure Requirements
- 1st: Read the optimization flag for each parameter
- 2nd: Use flag to determine: default parameter OR override parameter
- NO complex fallback chains or backup logic
- Clean, direct flag-based parameter selection only
