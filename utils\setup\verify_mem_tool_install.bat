@echo off
echo Verifying Memory Tool Installation...
echo ===================================

:: 1. Activate virtual environment
call F:\AI_Library\my_quant_env\Scripts\activate.bat

echo.
echo === Python Environment ===
python --version
where python

echo.
echo === Checking Package Installations ===
for %%p in (graphviz astunparse) do (
    echo Checking %%p...
    python -c "import %%p; print(f'  - {%%p.__name__}: {%%p.__file__}')" 2>nul || echo   - %%p: NOT FOUND
)

echo.
echo === Checking Graphviz Installation ===
where dot 2>nul
if %ERRORLEVEL% EQU 0 (
    echo Graphviz dot command is available in PATH
    dot -V
) else (
    echo WARNING: Graphviz dot command not found in PATH
    echo Please install Graphviz from https://graphviz.org/download/
)

echo.
echo ===================================
echo Verification complete. Press any key to exit...
pause > nul
