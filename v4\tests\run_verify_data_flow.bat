@echo off
echo =====================================================
echo Backtest Engine Data Flow Verification - %date% %time%
echo =====================================================

REM Set up virtual environment path
set VENV_PATH=F:\AI_Library\my_quant_env

REM Activate virtual environment
echo Activating virtual environment at %VENV_PATH%...
call %VENV_PATH%\Scripts\activate.bat

REM Set output directory
set OUTPUT_DIR=S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\tests\debug_output
echo Output directory path: %OUTPUT_DIR%

REM Set log file
set LOG_FILE=%OUTPUT_DIR%\data_flow_verification_%date:~-4,4%%date:~-7,2%%date:~-10,2%_%time:~0,2%%time:~3,2%%time:~6,2%.log
set LOG_FILE=%LOG_FILE: =0%

echo Starting data flow verification...
echo Results will be saved to: %LOG_FILE%

REM Run the verification script with output to log file
echo Running verification script...
echo Command: python S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\tests\verify_data_flow.py
python S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\tests\verify_data_flow.py > "%LOG_FILE%" 2>&1
echo Python script completed with exit code: %errorlevel%

REM Display log file contents
echo.
echo Log file contents:
echo ==================
if exist "%LOG_FILE%" (
    type "%LOG_FILE%"
) else (
    echo ERROR: Log file not created!
)

echo.
echo Verification complete!
echo Full log saved to: %LOG_FILE%

REM Display output directory contents
echo.
echo Output directory contents:
dir "%OUTPUT_DIR%"

echo.
pause
