"""v4.engine.execution
Compatibility shim so that legacy imports like:

    from v4.engine.execution import ExecutionEngine

continue to work.  All symbols are re-exported from
:pymod:`v4.engine.execution_v4`.

Remove this shim once the entire code-base is updated to reference the
*_v4* modules directly.
"""
from importlib import import_module as _imp
import sys as _sys

_src = _imp("v4.engine.execution_v4")

__all__ = _src.__all__ if hasattr(_src, "__all__") else [
    n for n in _src.__dict__.keys() if not n.startswith("_")
]

globals().update({n: getattr(_src, n) for n in __all__})

# Also expose submodule path so ``import v4.engine.execution.execution_v4`` works
_sys.modules[__name__ + ".execution_v4"] = _src
