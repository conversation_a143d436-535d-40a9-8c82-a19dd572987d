@echo on
REM ==============================================================================
REM super_simple_config_reader.bat - Baby-step runner for super_simple_config_reader.py
REM ==============================================================================

echo ===========================================================================
echo STEP 1: Activating virtual environment
echo ===========================================================================
call "F:\AI_Library\my_quant_env\Scripts\activate.bat"
if %errorlevel% neq 0 (
    echo ERROR: Failed to activate virtual environment
    pause
    goto :eof
)

echo.
echo ===========================================================================
echo STEP 2: Running Python config reader script
echo =============================================================================
python "%~dp0super_simple_config_reader.py"
echo Exit code from Python: %errorlevel%

echo.
echo ===========================================================================
echo SCRIPT COMPLETE - Press any key to exit
pause
