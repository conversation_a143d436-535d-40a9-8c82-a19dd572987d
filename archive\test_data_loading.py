"""
Test script to debug data loading issues
"""

import sys
import logging
import pandas as pd
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Add paths
sys.path.append(str(Path(__file__).parent))
from config.paths import CUSTOM_LIB_PATH
sys.path.append(str(CUSTOM_LIB_PATH))

# Import configuration
from config.config import config

def test_data_loading():
    """Test data loading functionality with detailed error reporting"""
    try:
        logger.info("Testing data loading...")
        
        # Import data loader
        from data.data_loader import get_adjusted_close_data
        
        # Get configuration parameters
        tickers = config['data_params']['tickers']
        start_date = config['data_params']['start_date']
        end_date = config['data_params']['end_date']
        price_field = config['data_params']['price_field']
        
        logger.info(f"Loading data for tickers: {tickers}")
        logger.info(f"Date range: {start_date} to {end_date}")
        
        # Try to load data for each ticker individually
        for ticker in tickers:
            try:
                logger.info(f"Loading data for {ticker}...")
                
                # Import directly from the Custom Function Library
                try:
                    from market_data import data_fetch_stock_data
                    logger.info(f"Successfully imported data_fetch_stock_data")
                    
                    # Test with 'max' period
                    data = data_fetch_stock_data(
                        ticker=ticker,
                        period="max",
                        interval="1d"
                    )
                    
                    logger.info(f"Data for {ticker} retrieved successfully")
                    logger.info(f"Data shape: {data.shape}")
                    logger.info(f"Data columns: {data.columns}")
                    logger.info(f"Data index type: {type(data.index)}")
                    
                    if len(data) > 0:
                        logger.info(f"First date: {data.index[0]}")
                        logger.info(f"Last date: {data.index[-1]}")
                    
                except ImportError as e:
                    logger.error(f"Failed to import data_fetch_stock_data: {e}")
                except Exception as e:
                    logger.error(f"Error retrieving data for {ticker}: {e}")
                    logger.error(f"Error type: {type(e).__name__}")
            
            except Exception as e:
                logger.error(f"Error processing ticker {ticker}: {e}")
        
        logger.info("Data loading test completed")
        
    except Exception as e:
        logger.error(f"Test failed: {e}")
        import traceback
        logger.error(traceback.format_exc())

if __name__ == "__main__":
    test_data_loading()
