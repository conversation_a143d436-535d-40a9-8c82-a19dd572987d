# V3 Parameter System Documentation

## Key Parameter Registry Methods

| Method                                       | Description                                   | Usage                                                    |
| -------------------------------------------- | --------------------------------------------- | -------------------------------------------------------- |
| `register_parameter(group, parameter)`       | Register a single parameter with the registry | For registering individual parameters                    |
| `register_parameter_list(group, parameters)` | Register multiple parameters at once          | **Preferred method** for registering multiple parameters |
| `get_parameter(name, group=None)`            | Get a parameter by name                       | Retrieve a parameter for use                             |
| `get_parameters(group)`                      | Get all parameters in a group                 | Retrieve all parameters in a specific group              |
| `get_parameter_values(group)`                | Get parameter values as a dictionary          | Get parameter values for use in backtest                 |
| `get_core_parameters()`                      | Get all core parameters                       | Get engine parameters that are always present            |
| `get_strategy_parameters()`                  | Get all strategy parameters                   | Get parameters from groups starting with 'strategy_'     |
| `get_all_parameters()`                       | Get all parameters                            | Get all parameters from all groups                       |

## Parameter Class Hierarchy

The V3 parameter system uses a class hierarchy to represent different types of parameters with varying behaviors:

```markdown
BaseParameter
├── NumericParameter
├── CategoricalParameter
│└── CategoricalListParameter
├── ConfigParameter
└── StrategyOptimizeParameter
```

## Parameter Classes and Their Uses

| Class                         | Description                                         | GUI Visible | Optimizable | Reportable | Use Cases                                              |
| ----------------------------- | --------------------------------------------------- |:-----------:|:-----------:|:----------:| ------------------------------------------------------ |
| **BaseParameter**             | Base class for all parameters                       | Yes         | No          | No         | Default for any non-defined variables                  |
| **NumericParameter**          | For numeric values with min/max/step                | Yes         | Yes         | No         | Lookback periods, thresholds, execution delay          |
| **CategoricalParameter**      | For selection from fixed options                    | Yes         | Yes         | No         | Rebalance frequency, strategy selection                |
| **CategoricalListParameter**  | For user-defined groups in config files             | Yes         | Yes         | No         | Ticker groups, asset classes                           |
| **ConfigParameter**           | For config-only values, not in GUI                  | No          | No          | No         | Initial capital, commission rate, slippage rate        |
| **StrategyOptimizeParameter** | Enhanced strategy parameters with reporting control | Yes         | Yes         | Yes        | Strategy-specific parameters needing reporting control |

## Parameter Matrix

Below is a comprehensive list of all parameters in the system, their types, and uses:

### Core Engine Parameters

| Parameter             | Class                     | Group | Description                     | Default  | GUI Visible | Optimizable |
| --------------------- | ------------------------- | ----- | ------------------------------- | -------- |:-----------:|:-----------:|
| `initial_capital`     | ConfigParameter           | core  | Initial capital for portfolio   | 1000000  | No          | No          |
| `commission_rate`     | ConfigParameter           | core  | Commission rate for trades      | 0.001    | No          | No          |
| `slippage_rate`       | ConfigParameter           | core  | Slippage rate for trades        | 0.001    | No          | No          |
| `execution_delay`     | StrategyOptimizeParameter | core  | Trade execution delay in days   | 1        | Yes         | Yes         |
| `rebalance_frequency` | StrategyOptimizeParameter | core  | Portfolio rebalancing frequency | 'weekly' | Yes         | Yes         |

### Data Parameters

| Parameter           | Class         | Group | Description                        | Default      | GUI Visible | Optimizable |
| ------------------- | ------------- | ----- | ---------------------------------- | ------------ |:-----------:|:-----------:|
| `start_date`        | BaseParameter | data  | Start date for backtest            | '2020-01-01' | No*         | No          |
| `end_date`          | BaseParameter | data  | End date for backtest              | '2025-04-23' | No*         | No          |
| `data_storage_mode` | BaseParameter | data  | How to handle data (Save/Read/New) | 'Read'       | No*         | No          |
| `price_field`       | BaseParameter | data  | Price field for calculations       | 'Close'      | No*         | No          |
| `risk_free_ticker`  | BaseParameter | data  | Ticker for risk-free rate          | '^IRX'       | No*         | No          |

*These parameters are currently defined in config but not yet converted to parameter classes

### Benchmark Parameters

| Parameter                  | Class         | Group     | Description                      | Default  | GUI Visible | Optimizable |
| -------------------------- | ------------- | --------- | -------------------------------- | -------- |:-----------:|:-----------:|
| `benchmark_ticker`         | BaseParameter | benchmark | Ticker for benchmark comparison  | 'SPY'    | No*         | No          |
| `benchmark_rebalance_freq` | BaseParameter | benchmark | Benchmark rebalancing frequency  | 'yearly' | No*         | No          |
| `debug_benchmark`          | BaseParameter | benchmark | Whether to log benchmark details | True     | No*         | No          |

*These parameters are currently defined in config but not yet converted to parameter classes

### EMA Strategy Parameters

| Parameter     | Class                     | Group        | Description                     | Default     | GUI Visible | Optimizable |
| ------------- | ------------------------- | ------------ | ------------------------------- | ----------- |:-----------:|:-----------:|
| `st_lookback` | StrategyOptimizeParameter | strategy_ema | Short-term EMA lookback period  | 15          | Yes         | Yes         |
| `mt_lookback` | StrategyOptimizeParameter | strategy_ema | Medium-term EMA lookback period | 70          | Yes         | Yes         |
| `lt_lookback` | StrategyOptimizeParameter | strategy_ema | Long-term EMA lookback period   | 100         | Yes         | Yes         |
| `top_n`       | StrategyOptimizeParameter | strategy_ema | Number of top assets to hold    | 2           | Yes         | Yes         |
| `signal_algo` | StrategyOptimizeParameter | strategy_ema | Signal generation algorithm     | 'ema_cross' | Yes         | Yes         |
| `tickers`     | StrategyOptimizeParameter | strategy_ema | Ticker groups to use            | 'sp500'     | Yes         | Yes         |

### Reporting Parameters

| Parameter        | Class         | Group     | Description                     | Default               | GUI Visible | Optimizable |
| ---------------- | ------------- | --------- | ------------------------------- | --------------------- |:-----------:|:-----------:|
| `create_excel`   | BaseParameter | reporting | Whether to create Excel reports | True                  | No*         | No          |
| `save_trade_log` | BaseParameter | reporting | Whether to save trade logs      | True                  | No*         | No          |
| `metrics`        | BaseParameter | reporting | Performance metrics to include  | ['total_return', ...] | No*         | No          |

### Visualization Parameters

| Parameter       | Class         | Group         | Description                 | Default                     | GUI Visible | Optimizable |
| --------------- | ------------- | ------------- | --------------------------- | --------------------------- |:-----------:|:-----------:|
| `create_charts` | BaseParameter | visualization | Whether to create charts    | True                        | No*         | No          |
| `chart_types`   | BaseParameter | visualization | Types of charts to create   | ['cumulative_returns', ...] | No*         | No          |
| `chart_format`  | BaseParameter | visualization | Format for chart output     | 'png'                       | No*         | No          |
| `chart_dpi`     | BaseParameter | visualization | Resolution for chart output | 300                         | No*         | No          |

*These parameters are currently defined in config but not yet converted to parameter classes

## V3 Reporting System Components (Updated May 2025)

### Core Modules

1. **Parameter Registration** (`v3_engine/reporting_parameters.py`)
   
   - Central registry for all reporting parameters
   - Handles parameter types, defaults and validation
   - Integrated with main parameter registry

2. **Reporting Adapter** (`v3_engine/performance_reporter_adapter.py`)
   
   - Bridges V3 parameters to legacy reporting code
   - Handles parameter conversion and validation
   - Maintains backward compatibility
   - **Function updated:** `_convert_parameters_to_config` (improved version for robust V3-to-reporting config conversion)

3. **Reporting Modules** (`v3_reporting/`)
   
   - `v3_performance_report.py`: Performance reporting wrapper
   - `v3_allocation_report.py`: Allocation reporting wrapper
   - `v3_visualization.py`: Chart generation wrapper
   - **Function added:** `generate_allocation_chart` (weekly area plot PNG, high-DPI, for allocation reporting)

4. **Validation Utilities** (`v3_engine/data_validator.py`)
   
   - **Function added:** `validate_signal_allocation` (checks for presence of signal and allocation history in results)

### Key Features

- **Parameter Flow**:

```mermaid
graph TD
  GUI -->|Parameters| Engine
  Engine -->|Results + Parameters| V3_Reporting
  V3_Reporting -->|Formatted Output| Reports
```

- **Error Handling**:
  
  - Automatic parameter type conversion
  - Fallback logic for missing signal history
  - Graceful degradation when legacy features are unavailable

- **Optimization Support**:
  
  - Proper handling of parameter optimization tuples
  - Preservation of optimization context through reporting chain
  - Clear labeling of optimized parameters in output

## V3 Reporting Modules

### Performance Charts (v3_performance_charts.py)

#### Functions

- `generate_monthly_returns_chart`: Creates monthly returns bar chart
- `generate_annual_returns_chart`: Creates annual returns bar chart
- `generate_cumulative_returns_chart`: Creates cumulative returns line chart
- `generate_drawdown_chart`: Creates drawdown visualization

### Trade Log (v3_trade_log.py)

#### Functions

- `format_trade_log`: Formats trade log data for Excel output
- `write_trade_log_header`: Writes parameter settings to Excel header
- `generate_trade_log`: Main function to create complete trade log output

## Parameter Flow

Parameters flow through the system in the following way:

1. **Definition**: Parameters are defined in their respective registration functions
2. **Registration**: Parameters are registered with the V3 registry
3. **GUI Integration**: Parameters marked with `show_in_gui=True` appear in the GUI
4. **Value Setting**: Parameter values are set via GUI or directly in code
5. **Engine Usage**: Parameters are passed to the backtest engine
6. **Reporting**: Parameter values are included in performance reports

## Parameter Conversion Guidelines

When converting config variables to parameter classes:

1. Use **ConfigParameter** for variables that:
   
   - Should not appear in the GUI
   - Should not be optimized
   - Are loaded from config
   - Should appear in performance reports

2. Use **NumericParameter** for variables that:
   
   - Have numeric values with min/max bounds
   - May need optimization
   - Should appear in the GUI

3. Use **CategoricalParameter** for variables that:
   
   - Have a fixed set of options
   - May need optimization over those options
   - Should appear in the GUI

4. Use **BaseParameter** as a fallback for any other variables

5. Use **StrategyOptimizeParameter** for strategy-specific parameters that need reporting control, are always reportable, always optimizable, and always GUI visible.
