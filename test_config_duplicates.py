#!/usr/bin/env python3
"""
Test script to identify duplicate parameter names in the INI file using ConfigHelper.
This will help us identify and fix duplicates before implementing section-agnostic approach.
"""

import sys
from pathlib import Path

# Add the project root to the path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from v4.settings.config_helper import <PERSON>fi<PERSON><PERSON><PERSON><PERSON>

def test_config_duplicates():
    """Test the ConfigHelper to identify duplicate parameter names."""
    
    config_file = "v4/settings/settings_parameters_v4.ini"
    
    print("=" * 60)
    print("TESTING CONFIG HELPER FOR DUPLICATE PARAMETERS")
    print("=" * 60)
    print(f"Config file: {config_file}")
    print()
    
    try:
        # Initialize ConfigHelper - this will print warnings for duplicates
        print("Initializing ConfigHelper (will show duplicate warnings):")
        print("-" * 50)
        helper = ConfigHelper(config_file)
        print("-" * 50)
        print()
        
        # List all parameters for review
        print("ALL PARAMETERS IN CONFIG FILE:")
        print("-" * 50)
        helper.list_all_params()
        print("-" * 50)
        print()
        
        # Test some specific parameters that we suspect are duplicated
        test_params = ['tickers', 'start_date', 'end_date', 'optimization_active', 'csv_flag_use']
        
        print("TESTING SPECIFIC PARAMETERS:")
        print("-" * 50)
        for param in test_params:
            value = helper.get(param)
            section = helper.find_param_section(param)
            print(f"{param}: '{value}' (found in section: {section})")
        print("-" * 50)
        print()
        
        print("Test completed successfully!")
        
    except Exception as e:
        print(f"ERROR: {e}")
        return False
    
    return True

if __name__ == "__main__":
    success = test_config_duplicates()
    if success:
        print("\n✅ ConfigHelper test completed - check output above for duplicate warnings")
    else:
        print("\n❌ ConfigHelper test failed")
        sys.exit(1)
