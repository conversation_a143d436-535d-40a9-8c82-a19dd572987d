"""
Performance reporting package for CPS v4.
Provides functions for calculating performance metrics, generating reports and visualizations.
"""

from .performance_metrics import (
    calculate_cagr,
    calculate_sharpe_ratio,
    calculate_sortino_ratio,
    calculate_max_drawdown,
    get_calendar_year_returns,
    get_ytd_returns,
    calculate_performance_metrics
)

from .performance_tables import (
    create_performance_table
)

from .report_generator import (
    create_backtest_report,
    generate_performance_report_local
)

__all__ = [
    'calculate_cagr',
    'calculate_sharpe_ratio',
    'calculate_sortino_ratio',
    'calculate_max_drawdown',
    'get_calendar_year_returns',
    'get_ytd_returns',
    'calculate_performance_metrics',
    'create_performance_table',
    'create_backtest_report',
    'generate_performance_report_local'
]
