# Combo ID Visibility and Tracking Requirements

## Introduction

This specification defines requirements for comprehensive visibility and tracking of combo IDs throughout the optimization testing process. The goal is to ensure that users, developers, and AI can easily track which combo ID is being processed at any given time, with clear logging, reporting, and status updates across all phases of execution.

## Requirements

### Requirement 1: Real-Time Combo ID Status Display

**User Story:** As a user running optimization tests, I want to see which combo ID is currently being processed in real-time, so that I can monitor progress and identify any issues quickly.

#### Acceptance Criteria

1. WHEN an optimization test starts THEN the system SHALL display the total number of combo IDs to be processed
2. WHEN each combo ID begins processing THEN the system SHALL display the combo ID, its description, and progress indicator (e.g., "Processing 3/25: S5_M30_L100_E1_T2")
3. WHEN a combo ID completes successfully THEN the system SHALL display completion status with timing information
4. WHEN a combo ID fails THEN the system SHALL display failure status with clear error indication
5. WHEN the optimization completes THEN the system SHALL display a summary of all combo IDs processed with their final status

### Requirement 2: Comprehensive Combo ID Logging

**User Story:** As a developer debugging optimization issues, I want detailed logs that track combo ID processing at every step, so that I can identify exactly where problems occur.

#### Acceptance Criteria

1. WHEN a combo ID enters any processing phase THEN the system SHALL log the combo ID, phase name, and timestamp
2. WHEN settings files are created THEN the system SHALL log the combo ID and settings file path
3. WHEN subprocess execution begins THEN the system SHALL log the combo ID and command being executed
4. WHEN subprocess execution completes THEN the system SHALL log the combo ID, duration, and exit status
5. WHEN files are created or loaded THEN the system SHALL log the combo ID and file paths
6. WHEN errors occur THEN the system SHALL log the combo ID and detailed error context
7. WHEN validation steps execute THEN the system SHALL log the combo ID and validation results

### Requirement 3: Combo ID File Naming Consistency

**User Story:** As a user analyzing optimization results, I want all generated files to use consistent combo ID naming, so that I can easily correlate files across different outputs.

#### Acceptance Criteria

1. WHEN settings files are created THEN they SHALL include the combo ID in the filename
2. WHEN subprocess log files are created THEN they SHALL include the combo ID in the filename
3. WHEN unified portfolio files are created THEN they SHALL include the combo ID in the filename
4. WHEN validation artifacts are created THEN they SHALL include the combo ID in the filename
5. WHEN equity curve files are created THEN they SHALL use combo ID as column headers
6. WHEN lookup tables are created THEN they SHALL reference combo IDs consistently

### Requirement 4: Progress Tracking and Reporting

**User Story:** As a user monitoring long-running optimizations, I want to see detailed progress information including estimated completion times, so that I can plan accordingly.

#### Acceptance Criteria

1. WHEN optimization starts THEN the system SHALL display estimated total runtime based on historical data
2. WHEN each combo ID completes THEN the system SHALL update progress percentage and estimated time remaining
3. WHEN combo IDs are processed THEN the system SHALL track and display average processing time per combo
4. WHEN failures occur THEN the system SHALL track failure rate and display it in progress updates
5. WHEN optimization completes THEN the system SHALL display total runtime and performance statistics

### Requirement 5: Combo ID Validation and Error Handling

**User Story:** As a developer ensuring system reliability, I want comprehensive validation of combo ID generation and usage, so that inconsistencies are caught early.

#### Acceptance Criteria

1. WHEN combo IDs are generated THEN the system SHALL validate uniqueness within the current run
2. WHEN combo IDs are used in file operations THEN the system SHALL validate the combo ID format
3. WHEN combo IDs are referenced in logs THEN the system SHALL validate they exist in the lookup table
4. WHEN duplicate combo IDs are detected THEN the system SHALL log a warning and handle gracefully
5. WHEN invalid combo ID formats are detected THEN the system SHALL raise clear error messages

### Requirement 6: Interactive Combo ID Status Dashboard

**User Story:** As a user running optimization tests, I want an interactive status display that shows current combo ID processing status, so that I can monitor progress without scrolling through logs.

#### Acceptance Criteria

1. WHEN optimization starts THEN the system SHALL display a status dashboard with combo ID progress
2. WHEN combo IDs are processing THEN the dashboard SHALL show current combo ID, elapsed time, and estimated remaining time
3. WHEN combo IDs complete THEN the dashboard SHALL update with success/failure indicators
4. WHEN users request detailed status THEN the dashboard SHALL show recent combo ID processing history
5. WHEN optimization completes THEN the dashboard SHALL show final summary with all combo ID results

### Requirement 7: Combo ID Lookup Integration

**User Story:** As a user analyzing results, I want easy access to combo ID lookup information during processing, so that I can understand what parameters are being tested.

#### Acceptance Criteria

1. WHEN combo IDs are displayed THEN the system SHALL provide option to show parameter details
2. WHEN lookup tables are created THEN they SHALL be immediately accessible for reference
3. WHEN combo IDs are logged THEN the system SHALL include parameter descriptions in verbose mode
4. WHEN errors occur THEN the system SHALL include combo ID parameter details in error messages
5. WHEN validation runs THEN the system SHALL reference combo ID lookup data for context

### Requirement 8: Historical Combo ID Tracking

**User Story:** As a user comparing optimization runs, I want historical tracking of combo ID performance across multiple runs, so that I can identify patterns and improvements.

#### Acceptance Criteria

1. WHEN optimization runs complete THEN the system SHALL save combo ID performance data to historical database
2. WHEN new optimizations start THEN the system SHALL reference historical data for time estimates
3. WHEN combo IDs are processed THEN the system SHALL compare current performance to historical averages
4. WHEN optimization completes THEN the system SHALL generate historical comparison reports
5. WHEN users request analysis THEN the system SHALL provide combo ID performance trends over time

### Requirement 9: Combo ID Environment Integration

**User Story:** As a developer integrating combo ID tracking, I want environment variables and context to be properly set and tracked, so that all subprocess operations have proper combo ID context.

#### Acceptance Criteria

1. WHEN combo ID processing begins THEN the system SHALL set CPS_V4_COMBO_ID environment variable
2. WHEN subprocesses are launched THEN they SHALL inherit the combo ID environment context
3. WHEN subprocess logs are created THEN they SHALL include the combo ID from environment
4. WHEN validation steps run THEN they SHALL access combo ID from environment for context
5. WHEN errors occur in subprocesses THEN they SHALL report the combo ID from environment

### Requirement 10: Combo ID Testing and Validation Framework

**User Story:** As a developer ensuring combo ID system reliability, I want comprehensive testing that validates all combo ID functionality, so that regressions are caught early.

#### Acceptance Criteria

1. WHEN combo ID functions are modified THEN automated tests SHALL validate generation, uniqueness, and format
2. WHEN file naming functions are changed THEN tests SHALL validate consistent combo ID usage across all file types
3. WHEN logging functions are updated THEN tests SHALL validate combo ID appears in all expected log entries
4. WHEN lookup table functions are modified THEN tests SHALL validate table creation and data integrity
5. WHEN integration tests run THEN they SHALL validate end-to-end combo ID tracking through complete optimization cycles