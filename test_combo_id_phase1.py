#!/usr/bin/env python
"""
Test script for Combo ID Phase 1 implementation

This script tests the core combo ID functions:
1. generate_combo_id()
2. create_combo_lookup_table()
3. create_simple_settings_file()

Author: AI Assistant
Date: 2025-07-28
"""

import sys
import os
from pathlib import Path
import tempfile
import json

# Add the project root to Python path
sys.path.insert(0, str(Path(__file__).parent))

# Import the functions we want to test
from v4.py_reporting.report_modules.report_matrix_optimization import (
    generate_combo_id,
    create_combo_lookup_table,
    create_simple_settings_file
)

def test_generate_combo_id():
    """Test the combo ID generation function."""
    print("=" * 60)
    print("TESTING: generate_combo_id()")
    print("=" * 60)
    
    # Test case 1: Basic combination
    combo1 = {
        'st_lookback': 5,
        'mt_lookback': 30,
        'lt_lookback': 100,
        'execution_delay': 1,
        'top_n': 2
    }
    
    combo_id1 = generate_combo_id(combo1)
    expected1 = 'S5_M30_L100_E1_T2'
    
    print(f"Test 1:")
    print(f"  Input: {combo1}")
    print(f"  Output: {combo_id1}")
    print(f"  Expected: {expected1}")
    print(f"  Result: {'✅ PASS' if combo_id1 == expected1 else '❌ FAIL'}")
    
    # Test case 2: Different values
    combo2 = {
        'st_lookback': 15,
        'mt_lookback': 70,
        'lt_lookback': 100,
        'execution_delay': 1,
        'top_n': 2
    }
    
    combo_id2 = generate_combo_id(combo2)
    expected2 = 'S15_M70_L100_E1_T2'
    
    print(f"\nTest 2:")
    print(f"  Input: {combo2}")
    print(f"  Output: {combo_id2}")
    print(f"  Expected: {expected2}")
    print(f"  Result: {'✅ PASS' if combo_id2 == expected2 else '❌ FAIL'}")
    
    return combo_id1 == expected1 and combo_id2 == expected2

def test_create_simple_settings_file():
    """Test the simple settings file creation."""
    print("\n" + "=" * 60)
    print("TESTING: create_simple_settings_file()")
    print("=" * 60)
    
    combo = {
        'st_lookback': 5,
        'mt_lookback': 30,
        'lt_lookback': 100,
        'execution_delay': 1,
        'system_lookback': 60,
        'top_n': 2
    }
    
    combo_id = 'S5_M30_L100_E1_T2'
    
    try:
        # Create settings file
        settings_file = create_simple_settings_file(combo, combo_id)
        
        print(f"Created settings file: {settings_file}")
        
        # Read and verify content
        with open(settings_file, 'r') as f:
            content = f.read()
        
        print(f"\nSettings file content:")
        print("-" * 40)
        print(content)
        print("-" * 40)
        
        # Verify key content
        checks = [
            ('st_lookback = 5' in content, 'st_lookback parameter'),
            ('mt_lookback = 30' in content, 'mt_lookback parameter'),
            ('lt_lookback = 100' in content, 'lt_lookback parameter'),
            ('execution_delay = 1' in content, 'execution_delay parameter'),
            ('system_lookback = 60' in content, 'system_lookback parameter'),
            ('top_n = 2' in content, 'top_n parameter'),
            (combo_id in content, 'combo_id in comments'),
            ('[Strategy]' in content, 'Strategy section'),
            ('[System]' in content, 'System section')
        ]
        
        all_passed = True
        for check, description in checks:
            status = '✅ PASS' if check else '❌ FAIL'
            print(f"  {description}: {status}")
            if not check:
                all_passed = False
        
        # Clean up
        os.unlink(settings_file)
        
        return all_passed
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        return False

def test_create_combo_lookup_table():
    """Test the combo lookup table creation."""
    print("\n" + "=" * 60)
    print("TESTING: create_combo_lookup_table()")
    print("=" * 60)
    
    # Create test combinations
    combinations = [
        {
            'st_lookback': 5,
            'mt_lookback': 30,
            'lt_lookback': 100,
            'execution_delay': 1,
            'system_lookback': 60,
            'top_n': 2
        },
        {
            'st_lookback': 15,
            'mt_lookback': 70,
            'lt_lookback': 100,
            'execution_delay': 1,
            'system_lookback': 60,
            'top_n': 2
        },
        {
            'st_lookback': 25,
            'mt_lookback': 90,
            'lt_lookback': 100,
            'execution_delay': 1,
            'system_lookback': 60,
            'top_n': 2
        }
    ]
    
    try:
        # Create temporary directory
        with tempfile.TemporaryDirectory() as temp_dir:
            output_dir = Path(temp_dir)
            
            # Create lookup tables
            json_file, csv_file, txt_file, lookup_data = create_combo_lookup_table(combinations, output_dir)
            
            print(f"Created files:")
            print(f"  JSON: {json_file}")
            print(f"  CSV: {csv_file}")
            print(f"  TXT: {txt_file}")
            
            # Verify files exist
            files_exist = all(f.exists() for f in [json_file, csv_file, txt_file])
            print(f"\nFiles exist: {'✅ PASS' if files_exist else '❌ FAIL'}")
            
            # Verify lookup data
            expected_combo_ids = ['S5_M30_L100_E1_T2', 'S15_M70_L100_E1_T2', 'S25_M90_L100_E1_T2']
            actual_combo_ids = [entry['combo_id'] for entry in lookup_data]
            
            combo_ids_correct = actual_combo_ids == expected_combo_ids
            print(f"Combo IDs correct: {'✅ PASS' if combo_ids_correct else '❌ FAIL'}")
            print(f"  Expected: {expected_combo_ids}")
            print(f"  Actual: {actual_combo_ids}")
            
            # Verify JSON content
            with open(json_file, 'r') as f:
                json_data = json.load(f)
            
            json_valid = len(json_data) == 3 and all('combo_id' in entry for entry in json_data)
            print(f"JSON structure valid: {'✅ PASS' if json_valid else '❌ FAIL'}")
            
            # Show sample TXT content
            with open(txt_file, 'r') as f:
                txt_content = f.read()
            
            print(f"\nSample TXT content (first 500 chars):")
            print("-" * 40)
            print(txt_content[:500] + "..." if len(txt_content) > 500 else txt_content)
            print("-" * 40)
            
            return files_exist and combo_ids_correct and json_valid
            
    except Exception as e:
        print(f"❌ ERROR: {e}")
        return False

def main():
    """Run all Phase 1 tests."""
    print("🚀 COMBO ID PHASE 1 TESTING")
    print("Testing core combo ID functions...")
    
    results = []
    
    # Test 1: Combo ID generation
    results.append(test_generate_combo_id())
    
    # Test 2: Simple settings file creation
    results.append(test_create_simple_settings_file())
    
    # Test 3: Lookup table creation
    results.append(test_create_combo_lookup_table())
    
    # Summary
    print("\n" + "=" * 60)
    print("PHASE 1 TEST SUMMARY")
    print("=" * 60)
    
    test_names = [
        "generate_combo_id()",
        "create_simple_settings_file()",
        "create_combo_lookup_table()"
    ]
    
    for i, (name, result) in enumerate(zip(test_names, results)):
        status = '✅ PASS' if result else '❌ FAIL'
        print(f"{i+1}. {name}: {status}")
    
    all_passed = all(results)
    overall_status = '✅ ALL TESTS PASSED' if all_passed else '❌ SOME TESTS FAILED'
    
    print(f"\nOverall Result: {overall_status}")
    
    if all_passed:
        print("\n🎉 Phase 1 implementation is working correctly!")
        print("✅ Combo ID generation works")
        print("✅ Simple settings file creation works (no regex)")
        print("✅ Lookup table creation works")
        print("\nReady to proceed to Phase 2!")
    else:
        print("\n⚠️  Some tests failed. Please check the implementation.")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)