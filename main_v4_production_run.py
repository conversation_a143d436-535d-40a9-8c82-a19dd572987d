# main_v4_production_run.py
# This script is designed for a full end-to-end production run of the V4 backtest engine.

import sys
import os
from pathlib import Path
from datetime import datetime
import pandas as pd

# --- Logging Function ---
def log_message(message, level="INFO"):
    """Centralized logging function to standardize output format.
    Levels: INFO, WARNING, ERROR, CRITICAL, MILESTONE
    """
    timestamp = datetime.now().strftime("%H:%M:%S")
    prefix = f"[{level}] {timestamp} -"
    print(f"{prefix} {message}")
    sys.stdout.flush()  # Ensure output is immediately written

# --- Path Setup (adopted from trace_ema_signal_breakdown.py for robustness) ---
_script_path = Path(__file__).resolve()
_project_root = _script_path.parent
if str(_project_root) not in sys.path:
    sys.path.insert(0, str(_project_root))
    print(f"PROJECT_ROOT '{_project_root}' added to sys.path.")

# --- Import core V4 components ---
try:
    from v4.settings.settings_CPS_v4 import load_settings
    from v4.engine.data_loader_v4 import load_data_for_backtest
    from v4.engine.backtest_v4 import BacktestEngine
    from v4.engine.signal_generator_v4 import generate_signals

    print("Successfully imported V4 modules.")
except ImportError as e:
    print(f"Error importing V4 modules: {e}")
    print("Please ensure the V4 structure is correct and paths are set up properly.")
    sys.exit(1)

def main():
    """Main function to run the V4 backtest and reporting pipeline."""

    log_message("--- Starting V4 Production Backtest Run ---", "MILESTONE")

    # 1. Load Settings
    log_message("\nStep 1: Loading settings from settings_CPS_v4.ini...", "MILESTONE")
    try:
        settings = load_settings()
        log_message("Settings loaded successfully.")
        # print(f"Settings content: {settings}") # Uncomment for deep debug
    except Exception as e:
        log_message(f"Could not load settings. Error: {e}", "CRITICAL")
        return

    # 2. Load Data
    log_message("\nStep 2: Loading market data...", "MILESTONE")
    try:
        all_data = load_data_for_backtest(current_settings=settings)
        price_data = all_data['price_data']
        # benchmark_data is not directly returned; it's calculated inside the engine
        log_message(f"Price data loaded for {len(price_data.columns)} assets.")
    except Exception as e:
        log_message(f"Could not load data. Error: {e}", "CRITICAL")
        sys.exit(1)

    # 3. Run Backtest
    log_message("\nStep 3: Running V4 backtest engine...", "MILESTONE")
    try:
        engine = BacktestEngine()
        # The signal generator (and the engine itself) will use the globally loaded settings.
        # No need to pass signal_params if the model is designed to fetch them from settings.
        # Get strategy parameters from settings
        strategy_params = settings.get('strategy', {})
        # The signal factory `generate_signals` expects a 'strategy' argument.
        # We get this from the 'strategy_name' field in the settings.
        strategy_to_run = strategy_params.get('strategy_name', 'equal_weight')

        log_message(f"   - Using strategy: '{strategy_to_run}' with params: {strategy_params}")
        
        # Create a signal generator instance instead of passing the function directly
        from v4.engine.signal_generator_v4 import create_signal_generator
        signal_generator_instance = create_signal_generator(strategy_to_run)
        
        backtest_results = engine.run_backtest(
            price_data=price_data, 
            signal_generator=signal_generator_instance, # Pass the generator instance
            **strategy_params # Pass all other strategy params
        )
        log_message("Backtest engine finished.")
    except Exception as e:
        log_message(f"Backtest engine failed. Error: {e}", "ERROR")
        # Log the full traceback for debugging
        import traceback
        traceback.print_exc()
        sys.exit(1)

    # 4. Verify Backtest Results
    log_message("\nStep 4: Verifying backtest results...", "MILESTONE")
    if 'weights_history' not in backtest_results or backtest_results['weights_history'].empty:
        log_message("'weights_history' not found in backtest results or is empty.", "CRITICAL")
        # Potentially log more details or save what is available
        sys.exit(1)
    else:
        weights_history = backtest_results['weights_history']
        log_message(f"'weights_history' found with shape: {weights_history.shape}")
        # The check for empty is now part of the primary if condition.
        log_message("Basic 'weights_history' validation passed.")

        # Save weights_history to a csv for inspection
        try:
            # Write directly to v4_trace_outputs, using the global _project_root
            trace_csv_dir = os.path.join(_project_root, 'v4_trace_outputs')
            os.makedirs(trace_csv_dir, exist_ok=True)
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            weights_filename = f"weights_history_output_{timestamp}.csv"
            weights_filepath = os.path.join(trace_csv_dir, weights_filename)
            weights_history.to_csv(weights_filepath)
            log_message(f"Successfully saved weights history to {weights_filepath}")
        except Exception as e:
            log_message(f"Could not save weights_history to CSV. Error: {e}", "ERROR")

    # 5. Generate Reports
    log_message("\nStep 5: Generating reports...")
    try:
        # Get strategy name from settings, provide a default if not found
        strategy_name = settings.get('strategy', {}).get('strategy_name', 'default_strategy')
        log_message(f"   - Generating performance report for strategy: {strategy_name}...")
        
        # Import the reporting module
        from v4.py_reporting import v4_performance_report
        
        # Determine if any reporting output is needed
        report_settings = settings.get('Report', {})
        create_excel = report_settings.get('create_excel', False)
        export_csv = report_settings.get('export_simple_validation_files', False)

        if create_excel or export_csv:
            log_message("Generating reports and/or validation files...")
            report_path = v4_performance_report.generate_performance_report(
                backtest_results=backtest_results,
                settings=settings,
                strategy_name=strategy_name
            )
            
            if report_path:
                log_message(f"   - Performance report successfully generated at: {report_path}")
            else:
                log_message("   - Performance report generation was skipped (as per settings).")

    except Exception as e:
        log_message(f"ERROR: Report generation failed. Error: {e}")
        # Log the full traceback for debugging
        import traceback
        traceback.print_exc()
        sys.stdout.flush()

    log_message("\n--- V4 Production Backtest Run Finished ---", "MILESTONE")

if __name__ == "__main__":
    main()
