2025-06-06 12:35:34,418 - parameter_discovery - INFO - Starting parameter discovery
2025-06-06 12:35:34,418 - parameter_discovery - INFO - Scanning directory: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_engine
2025-06-06 12:35:34,418 - parameter_discovery - INFO - Scanning directory: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_engine\BU_Code
2025-06-06 12:35:34,418 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_engine\BU_Code\parameter_registry.py
2025-06-06 12:35:34,419 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_engine\BU_Code\performance_reporter_adapter - BU 0504.py
2025-06-06 12:35:34,423 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_engine\BU_Code\performance_reporter_adapter_buerror.py
2025-06-06 12:35:34,423 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_engine\BU_Code\performance_reporter_adapter_original.py
2025-06-06 12:35:34,423 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_engine\BU_Code\temp_v3_performance_reporter_adapter.py
2025-06-06 12:35:34,423 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_engine\config.py
2025-06-06 12:35:34,424 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_engine\config_adapter.py
2025-06-06 12:35:34,424 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_engine\config_integration.py
2025-06-06 12:35:34,424 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_engine\data_validator.py
2025-06-06 12:35:34,424 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_engine\debug_output.py
2025-06-06 12:35:34,424 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_engine\ema_v3_adapter.py
2025-06-06 12:35:34,424 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_engine\gui_parameter_manager.py
2025-06-06 12:35:34,424 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_engine\logging_config.py
2025-06-06 12:35:34,425 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_engine\parameters.py
2025-06-06 12:35:34,425 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_engine\parameter_gui_access.py
2025-06-06 12:35:34,425 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_engine\parameter_gui_controls.py
2025-06-06 12:35:34,425 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_engine\parameter_gui_sync.py
2025-06-06 12:35:34,425 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_engine\parameter_optimizer.py
2025-06-06 12:35:34,425 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_engine\parameter_registry.py
2025-06-06 12:35:34,425 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_engine\performance_reporter_adapter.py
2025-06-06 12:35:34,426 - parameter_discovery - INFO - Scanning directory: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_engine\reference
2025-06-06 12:35:34,426 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_engine\reference\strategy_optimize_example.py
2025-06-06 12:35:34,426 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_engine\reference\strategy_reporting_integration.py
2025-06-06 12:35:34,426 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_engine\reporting_parameters.py
2025-06-06 12:35:34,426 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_engine\signal_history_tracker.py
2025-06-06 12:35:34,426 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_engine\strategy_optimize_parameter.py
2025-06-06 12:35:34,427 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_engine\strategy_parameter_set.py
2025-06-06 12:35:34,427 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_engine\strategy_params.py
2025-06-06 12:35:34,427 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_engine\V3_perf_repadapt_core.py
2025-06-06 12:35:34,427 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_engine\V3_perf_repadapt_legacybridge.py
2025-06-06 12:35:34,427 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_engine\V3_perf_repadapt_metrics.py
2025-06-06 12:35:34,427 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_engine\V3_perf_repadapt_optimization.py
2025-06-06 12:35:34,427 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_engine\V3_perf_repadapt_paramconvert.py
2025-06-06 12:35:34,428 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_engine\V3_perf_repadapt_utils.py
2025-06-06 12:35:34,428 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_engine\__init__.py
2025-06-06 12:35:34,428 - parameter_discovery - INFO - Scanning directory: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_engine\__pycache__
2025-06-06 12:35:34,429 - parameter_discovery - INFO - Scanning directory: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_reporting
2025-06-06 12:35:34,429 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_reporting\parameter_registry_integration.py
2025-06-06 12:35:34,429 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_reporting\reporting_parameters.py
2025-06-06 12:35:34,429 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_reporting\report_formatter.py
2025-06-06 12:35:34,429 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_reporting\v3_allocation_report.py
2025-06-06 12:35:34,429 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_reporting\v3_performance_charts.py
2025-06-06 12:35:34,429 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_reporting\v3_performance_report.py
2025-06-06 12:35:34,430 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_reporting\v3_signal_history.py
2025-06-06 12:35:34,430 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_reporting\v3_trade_log.py
2025-06-06 12:35:34,430 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_reporting\v3_visualization.py
2025-06-06 12:35:34,430 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_reporting\visualization_parameters.py
2025-06-06 12:35:34,430 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_reporting\__init__.py
2025-06-06 12:35:34,430 - parameter_discovery - INFO - Scanning directory: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_reporting\__pycache__
2025-06-06 12:35:34,431 - parameter_discovery - INFO - Generating INI file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\CPS_v4\default_settings_CPS_v4.ini
2025-06-06 12:35:34,431 - parameter_discovery - INFO - INI file generated successfully: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\CPS_v4\default_settings_CPS_v4.ini
2025-06-06 12:35:34,431 - parameter_discovery - INFO - Generating parameter mapping document: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\CPS_v4\parameter_mapping_CPS_v4.md
2025-06-06 12:35:34,432 - parameter_discovery - INFO - Parameter mapping document generated successfully: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\CPS_v4\parameter_mapping_CPS_v4.md
2025-06-06 12:35:34,432 - parameter_discovery - INFO - Parameter discovery complete. Found 13 parameters.
2025-06-06 12:35:34,783 - verify_CPS_v4 - WARNING - Not using production data - this is not recommended for CPS v4 validation
2025-06-06 12:35:34,783 - verify_CPS_v4 - WARNING - Mock data testing is deprecated and will be removed
2025-06-06 12:35:34,784 - verify_CPS_v4 - WARNING - WARNING: Not using production data. Set USE_PRODUCTION_DATA=1 in environment.
2025-06-06 12:35:34,784 - verify_CPS_v4 - ERROR - ERROR: Production data path must be specified when not using environment variable.
[INFO] Activating virtual environment... 
[INFO] Setting PYTHONPATH... 
[INFO] Running parameter discovery... 
2025-06-06 12:48:45,305 - parameter_discovery - INFO - Starting parameter discovery
2025-06-06 12:48:45,305 - parameter_discovery - INFO - Scanning directory: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_engine
2025-06-06 12:48:45,305 - parameter_discovery - INFO - Scanning directory: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_engine\BU_Code
2025-06-06 12:48:45,305 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_engine\BU_Code\parameter_registry.py
2025-06-06 12:48:45,306 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_engine\BU_Code\performance_reporter_adapter - BU 0504.py
2025-06-06 12:48:45,306 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_engine\BU_Code\performance_reporter_adapter_buerror.py
2025-06-06 12:48:45,306 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_engine\BU_Code\performance_reporter_adapter_original.py
2025-06-06 12:48:45,307 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_engine\BU_Code\temp_v3_performance_reporter_adapter.py
2025-06-06 12:48:45,307 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_engine\config.py
2025-06-06 12:48:45,307 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_engine\config_adapter.py
2025-06-06 12:48:45,307 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_engine\config_integration.py
2025-06-06 12:48:45,307 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_engine\data_validator.py
2025-06-06 12:48:45,307 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_engine\debug_output.py
2025-06-06 12:48:45,307 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_engine\ema_v3_adapter.py
2025-06-06 12:48:45,308 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_engine\gui_parameter_manager.py
2025-06-06 12:48:45,308 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_engine\logging_config.py
2025-06-06 12:48:45,308 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_engine\parameters.py
2025-06-06 12:48:45,308 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_engine\parameter_gui_access.py
2025-06-06 12:48:45,308 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_engine\parameter_gui_controls.py
2025-06-06 12:48:45,308 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_engine\parameter_gui_sync.py
2025-06-06 12:48:45,308 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_engine\parameter_optimizer.py
2025-06-06 12:48:45,309 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_engine\parameter_registry.py
2025-06-06 12:48:45,309 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_engine\performance_reporter_adapter.py
2025-06-06 12:48:45,309 - parameter_discovery - INFO - Scanning directory: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_engine\reference
2025-06-06 12:48:45,309 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_engine\reference\strategy_optimize_example.py
2025-06-06 12:48:45,309 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_engine\reference\strategy_reporting_integration.py
2025-06-06 12:48:45,309 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_engine\reporting_parameters.py
2025-06-06 12:48:45,309 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_engine\signal_history_tracker.py
2025-06-06 12:48:45,310 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_engine\strategy_optimize_parameter.py
2025-06-06 12:48:45,310 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_engine\strategy_parameter_set.py
2025-06-06 12:48:45,310 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_engine\strategy_params.py
2025-06-06 12:48:45,310 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_engine\V3_perf_repadapt_core.py
2025-06-06 12:48:45,310 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_engine\V3_perf_repadapt_legacybridge.py
2025-06-06 12:48:45,310 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_engine\V3_perf_repadapt_metrics.py
2025-06-06 12:48:45,311 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_engine\V3_perf_repadapt_optimization.py
2025-06-06 12:48:45,311 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_engine\V3_perf_repadapt_paramconvert.py
2025-06-06 12:48:45,311 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_engine\V3_perf_repadapt_utils.py
2025-06-06 12:48:45,311 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_engine\__init__.py
2025-06-06 12:48:45,311 - parameter_discovery - INFO - Scanning directory: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_engine\__pycache__
2025-06-06 12:48:45,312 - parameter_discovery - INFO - Scanning directory: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_reporting
2025-06-06 12:48:45,312 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_reporting\parameter_registry_integration.py
2025-06-06 12:48:45,312 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_reporting\reporting_parameters.py
2025-06-06 12:48:45,312 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_reporting\report_formatter.py
2025-06-06 12:48:45,312 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_reporting\v3_allocation_report.py
2025-06-06 12:48:45,312 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_reporting\v3_performance_charts.py
2025-06-06 12:48:45,312 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_reporting\v3_performance_report.py
2025-06-06 12:48:45,312 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_reporting\v3_signal_history.py
2025-06-06 12:48:45,313 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_reporting\v3_trade_log.py
2025-06-06 12:48:45,313 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_reporting\v3_visualization.py
2025-06-06 12:48:45,313 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_reporting\visualization_parameters.py
2025-06-06 12:48:45,313 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_reporting\__init__.py
2025-06-06 12:48:45,313 - parameter_discovery - INFO - Scanning directory: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_reporting\__pycache__
2025-06-06 12:48:45,313 - parameter_discovery - INFO - Generating INI file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\CPS_v4\default_settings_CPS_v4.ini
2025-06-06 12:48:45,314 - parameter_discovery - INFO - INI file generated successfully: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\CPS_v4\default_settings_CPS_v4.ini
2025-06-06 12:48:45,314 - parameter_discovery - INFO - Generating parameter mapping document: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\CPS_v4\parameter_mapping_CPS_v4.md
2025-06-06 12:48:45,314 - parameter_discovery - INFO - Parameter mapping document generated successfully: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\CPS_v4\parameter_mapping_CPS_v4.md
2025-06-06 12:48:45,314 - parameter_discovery - INFO - Parameter discovery complete. Found 13 parameters.
[INFO] Parameter discovery complete. 
[INFO] Running production verification... 
2025-06-06 12:48:45,646 - verify_CPS_v4 - INFO - Running with PRODUCTION data validation
2025-06-06 12:48:45,646 - verify_CPS_v4 - INFO - Adding settings validation tests
Traceback (most recent call last):
  File "S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\CPS_v4\V4_transition\verify_CPS_v4.py", line 378, in <module>
    main()
    ~~~~^^
  File "S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\CPS_v4\V4_transition\verify_CPS_v4.py", line 363, in main
    test_suite.addTest(unittest.makeSuite(TestSettingsModule))
                       ^^^^^^^^^^^^^^^^^^
  File "C:\Python313\Lib\unittest\__init__.py", line 80, in __getattr__
    raise AttributeError(f"module {__name__!r} has no attribute {name!r}")
AttributeError: module 'unittest' has no attribute 'makeSuite'
[INFO] Production verification complete. 
[INFO] Activating virtual environment... 
[INFO] Setting PYTHONPATH... 
[INFO] Running parameter discovery... 
2025-06-06 14:59:31,063 - parameter_discovery - INFO - Starting parameter discovery
2025-06-06 14:59:31,063 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\config\config_v3.py
2025-06-06 14:59:31,064 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_engine\parameter_registry.py
2025-06-06 14:59:31,064 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_engine\parameters.py
2025-06-06 14:59:31,064 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_engine\strategy_parameter_set.py
2025-06-06 14:59:31,065 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_engine\parameter_optimizer.py
2025-06-06 14:59:31,065 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_engine\ema_v3_adapter.py
2025-06-06 14:59:31,065 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_strategies\ema_strategy.py
2025-06-06 14:59:31,065 - parameter_discovery - ERROR - Error scanning file S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_strategies\ema_strategy.py: [Errno 2] No such file or directory: 'S:\\Dropbox\\Scott Only Internal\\Quant_Python_24\\Backtest_FinAsset_Alloc_Template\\v3_strategies\\ema_strategy.py'
2025-06-06 14:59:31,065 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_strategies\strategy_base.py
2025-06-06 14:59:31,065 - parameter_discovery - ERROR - Error scanning file S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_strategies\strategy_base.py: [Errno 2] No such file or directory: 'S:\\Dropbox\\Scott Only Internal\\Quant_Python_24\\Backtest_FinAsset_Alloc_Template\\v3_strategies\\strategy_base.py'
2025-06-06 14:59:31,065 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_reporting\v3_performance_report.py
2025-06-06 14:59:31,065 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_reporting\v3_allocation_report.py
2025-06-06 14:59:31,065 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_reporting\reporting_parameters.py
2025-06-06 14:59:31,066 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_reporting\visualization_parameters.py
2025-06-06 14:59:31,066 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_reporting\parameter_registry_integration.py
2025-06-06 14:59:31,066 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\app\gui\v3_register_parameters.py
2025-06-06 14:59:31,066 - parameter_discovery - INFO - Generating INI file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\CPS_v4\default_settings_CPS_v4.ini
2025-06-06 14:59:31,067 - parameter_discovery - INFO - INI file generated successfully: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\CPS_v4\default_settings_CPS_v4.ini
2025-06-06 14:59:31,067 - parameter_discovery - INFO - Generating parameter mapping document: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\CPS_v4\parameter_mapping_CPS_v4.md
2025-06-06 14:59:31,068 - parameter_discovery - INFO - Parameter mapping document generated successfully: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\CPS_v4\parameter_mapping_CPS_v4.md
2025-06-06 14:59:31,068 - parameter_discovery - INFO - Parameter discovery complete. Found 10 parameters.
[INFO] Parameter discovery complete. 
[INFO] Running production verification... 
2025-06-06 14:59:33,293 - verify_CPS_v4 - INFO - Running with PRODUCTION data validation
2025-06-06 14:59:33,294 - verify_CPS_v4 - INFO - Adding settings validation tests
Traceback (most recent call last):
  File "S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\CPS_v4\V4_transition\verify_CPS_v4.py", line 378, in <module>
    main()
    ~~~~^^
  File "S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\CPS_v4\V4_transition\verify_CPS_v4.py", line 363, in main
    test_suite.addTest(unittest.makeSuite(TestSettingsModule))
                       ^^^^^^^^^^^^^^^^^^
  File "C:\Python313\Lib\unittest\__init__.py", line 80, in __getattr__
    raise AttributeError(f"module {__name__!r} has no attribute {name!r}")
AttributeError: module 'unittest' has no attribute 'makeSuite'
[INFO] Production verification complete. 
[INFO] Activating virtual environment... 
[INFO] Setting PYTHONPATH... 
[INFO] Running parameter discovery... 
2025-06-06 20:41:09,681 - parameter_discovery - INFO - Starting parameter discovery
2025-06-06 20:41:09,681 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\config\config_v3.py
2025-06-06 20:41:09,681 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_engine\parameter_registry.py
2025-06-06 20:41:09,682 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_engine\parameters.py
2025-06-06 20:41:09,682 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_engine\strategy_parameter_set.py
2025-06-06 20:41:09,682 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_engine\parameter_optimizer.py
2025-06-06 20:41:09,682 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_engine\ema_v3_adapter.py
2025-06-06 20:41:09,682 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_strategies\ema_strategy.py
2025-06-06 20:41:09,682 - parameter_discovery - ERROR - Error scanning file S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_strategies\ema_strategy.py: [Errno 2] No such file or directory: 'S:\\Dropbox\\Scott Only Internal\\Quant_Python_24\\Backtest_FinAsset_Alloc_Template\\v3_strategies\\ema_strategy.py'
2025-06-06 20:41:09,682 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_strategies\strategy_base.py
2025-06-06 20:41:09,682 - parameter_discovery - ERROR - Error scanning file S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_strategies\strategy_base.py: [Errno 2] No such file or directory: 'S:\\Dropbox\\Scott Only Internal\\Quant_Python_24\\Backtest_FinAsset_Alloc_Template\\v3_strategies\\strategy_base.py'
2025-06-06 20:41:09,682 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_reporting\v3_performance_report.py
2025-06-06 20:41:09,683 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_reporting\v3_allocation_report.py
2025-06-06 20:41:09,683 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_reporting\reporting_parameters.py
2025-06-06 20:41:09,683 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_reporting\visualization_parameters.py
2025-06-06 20:41:09,683 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_reporting\parameter_registry_integration.py
2025-06-06 20:41:09,683 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\app\gui\v3_register_parameters.py
2025-06-06 20:41:09,683 - parameter_discovery - INFO - Generating INI file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\CPS_v4\default_settings_CPS_v4.ini
2025-06-06 20:41:09,684 - parameter_discovery - INFO - INI file generated successfully: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\CPS_v4\default_settings_CPS_v4.ini
2025-06-06 20:41:09,684 - parameter_discovery - INFO - Generating parameter mapping document: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\CPS_v4\parameter_mapping_CPS_v4.md
2025-06-06 20:41:09,688 - parameter_discovery - INFO - Parameter mapping document generated successfully: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\CPS_v4\parameter_mapping_CPS_v4.md
2025-06-06 20:41:09,688 - parameter_discovery - INFO - Parameter discovery complete. Found 10 parameters.
[INFO] Parameter discovery complete. 
[INFO] Running production verification... 
2025-06-06 20:41:10,300 - verify_CPS_v4 - INFO - Running with PRODUCTION data validation
2025-06-06 20:41:10,301 - verify_CPS_v4 - INFO - Adding settings validation tests
2025-06-06 20:41:10,301 - verify_CPS_v4 - INFO - Adding report validation tests
2025-06-06 20:41:10,301 - verify_CPS_v4 - INFO - Running production validation tests
test_load_default_settings (__main__.TestSettingsModule.test_load_default_settings)
Test loading default settings. ... ok
test_settings_override (__main__.TestSettingsModule.test_settings_override)
Test settings override mechanism. ... ok
test_type_conversion (__main__.TestSettingsModule.test_type_conversion)
Test type conversion of settings values. ... ok
test_report_module_structure (__main__.TestReportGeneration.test_report_module_structure)
Test that report modules follow size constraints. ... ok

----------------------------------------------------------------------
Ran 4 tests in 0.013s

OK
[INFO] Production verification complete. 
[INFO] Activating virtual environment... 
[INFO] Setting PYTHONPATH... 
[INFO] Running parameter discovery... 
2025-06-06 20:46:57,435 - parameter_discovery - INFO - Starting parameter discovery
2025-06-06 20:46:57,435 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\config\config_v3.py
2025-06-06 20:46:57,436 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_engine\parameter_registry.py
2025-06-06 20:46:57,436 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_engine\parameters.py
2025-06-06 20:46:57,436 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_engine\strategy_parameter_set.py
2025-06-06 20:46:57,437 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_engine\parameter_optimizer.py
2025-06-06 20:46:57,437 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_engine\ema_v3_adapter.py
2025-06-06 20:46:57,437 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_strategies\ema_strategy.py
2025-06-06 20:46:57,437 - parameter_discovery - ERROR - Error scanning file S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_strategies\ema_strategy.py: [Errno 2] No such file or directory: 'S:\\Dropbox\\Scott Only Internal\\Quant_Python_24\\Backtest_FinAsset_Alloc_Template\\v3_strategies\\ema_strategy.py'
2025-06-06 20:46:57,437 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_strategies\strategy_base.py
2025-06-06 20:46:57,437 - parameter_discovery - ERROR - Error scanning file S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_strategies\strategy_base.py: [Errno 2] No such file or directory: 'S:\\Dropbox\\Scott Only Internal\\Quant_Python_24\\Backtest_FinAsset_Alloc_Template\\v3_strategies\\strategy_base.py'
2025-06-06 20:46:57,437 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_reporting\v3_performance_report.py
2025-06-06 20:46:57,437 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_reporting\v3_allocation_report.py
2025-06-06 20:46:57,437 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_reporting\reporting_parameters.py
2025-06-06 20:46:57,437 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_reporting\visualization_parameters.py
2025-06-06 20:46:57,437 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v3_reporting\parameter_registry_integration.py
2025-06-06 20:46:57,437 - parameter_discovery - INFO - Scanning file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\app\gui\v3_register_parameters.py
2025-06-06 20:46:57,442 - parameter_discovery - INFO - Generating INI file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\CPS_v4\default_settings_CPS_v4.ini
2025-06-06 20:46:57,442 - parameter_discovery - INFO - INI file generated successfully: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\CPS_v4\default_settings_CPS_v4.ini
2025-06-06 20:46:57,442 - parameter_discovery - INFO - Generating parameter mapping document: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\CPS_v4\parameter_mapping_CPS_v4.md
2025-06-06 20:46:57,442 - parameter_discovery - INFO - Parameter mapping document generated successfully: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\CPS_v4\parameter_mapping_CPS_v4.md
2025-06-06 20:46:57,442 - parameter_discovery - INFO - Parameter discovery complete. Found 10 parameters.
[INFO] Parameter discovery complete. 
[INFO] Running production verification... 
2025-06-06 20:46:57,763 - verify_CPS_v4 - INFO - Running with PRODUCTION data validation
2025-06-06 20:46:57,764 - verify_CPS_v4 - INFO - Adding settings validation tests
2025-06-06 20:46:57,764 - verify_CPS_v4 - INFO - Adding report validation tests
2025-06-06 20:46:57,764 - verify_CPS_v4 - INFO - Running production validation tests
test_load_default_settings (__main__.TestSettingsModule.test_load_default_settings)
Test loading default settings. ... ok
test_settings_override (__main__.TestSettingsModule.test_settings_override)
Test settings override mechanism. ... ok
test_type_conversion (__main__.TestSettingsModule.test_type_conversion)
Test type conversion of settings values. ... ok
test_report_module_structure (__main__.TestReportGeneration.test_report_module_structure)
Test that report modules follow size constraints. ... ok

----------------------------------------------------------------------
Ran 4 tests in 0.010s

OK
[INFO] Production verification complete. 
