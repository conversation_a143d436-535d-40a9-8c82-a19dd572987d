@echo off
REM run_allocation_debug.bat - Allocation debug test script
set VENV_PATH=F:\AI_Library\my_quant_env
call "%VENV_PATH%\Scripts\activate.bat"
if not exist "%~dp0output" mkdir "%~dp0output"

echo Pre-run batch working directory: %CD%
echo Batch file directory: %~dp0
echo Pre-run output directory contents:
dir /b "%~dp0output"








:: Run the script and capture output
python "%~dp0allocation_debug.py" > "%~dp0output\allocation_debug_output.txt" 2>&1

echo Post-run output directory contents:
dir /b "%~dp0output"


:: Check if the script ran successfully
if %ERRORLEVEL% EQU 0 (
    echo Debug completed successfully.
    echo Output saved to "%~dp0output\allocation_debug_output.txt"
    echo.
    echo Displaying output summary:
    echo ========================
    type "%~dp0output\allocation_debug_output.txt"
) else (
    echo Error running debug script. 
    echo Check output in "%~dp0output\allocation_debug_output.txt"
    echo.
    echo Displaying error output:
    echo =====================
    type "%~dp0output\allocation_debug_output.txt"
)

:: Pause to see output
REM pause removed for automated run
