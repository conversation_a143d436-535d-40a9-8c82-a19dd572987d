"""
v4/tests/test_backtest_v4.py
Test script for validating the refactored backtest engine.
Tests allocation history preservation and compares with previous version.
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
import matplotlib.pyplot as plt
from pathlib import Path
import csv

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Add parent directory to path to import modules
sys.path.append(str(Path(__file__).parent.parent.parent))

# Import backtest engine
from v4.engine.backtest_v4 import BacktestEngine
from v4.engine.signal_generator_v4 import generate_signals, create_signal_generator

def generate_test_data(start_date='2020-01-01', end_date='2022-12-31', symbols=None):
    """Generate synthetic price data for testing."""
    if symbols is None:
        symbols = ['AAPL', 'MSFT', 'AMZN', 'GOOGL']
    
    # Create date range
    dates = pd.date_range(start=start_date, end=end_date, freq='B')
    
    # Create price data with random walks
    np.random.seed(42)  # For reproducibility
    price_data = pd.DataFrame(index=dates)
    
    for symbol in symbols:
        # Start with 100
        prices = [100]
        # Generate random daily returns
        for _ in range(1, len(dates)):
            daily_return = np.random.normal(0.0005, 0.015)  # Mean and std dev
            prices.append(prices[-1] * (1 + daily_return))
        
        price_data[symbol] = prices
    
    return price_data

def run_backtest_test(price_data, strategy='equal_weight', rebalance_freq='M', execution_delay=0):
    """Run a backtest with the specified parameters."""
    logger.info(f"Running backtest with strategy: {strategy}, rebalance_freq: {rebalance_freq}, execution_delay: {execution_delay}")
    
    # Override settings for testing
    from v4.settings.settings_CPS_v4 import load_settings, save_settings
    settings = load_settings()
    
    # Update settings for test
    settings['backtest']['rebalance_freq'] = rebalance_freq
    settings['backtest']['execution_delay'] = execution_delay
    save_settings(settings)
    
    # Create backtest engine
    engine = BacktestEngine()
    
    # Run backtest
    results = engine.run_backtest(
        price_data=price_data,
        signal_generator=generate_signals,
        strategy=strategy
    )
    
    return results

def validate_weights_history(results):
    """Validate that weights history is preserved correctly."""
    weights_history = results['weights_history']
    signal_history = results['signal_history']
    
    logger.info(f"Weights history shape: {weights_history.shape}")
    logger.info(f"Signal history shape: {signal_history.shape}")
    
    # Check if weights_history and signal_history are different
    # (they should be if weights_history is preserved correctly)
    if weights_history.equals(signal_history):
        logger.warning("⚠️ Weights history equals signal history - this suggests allocation history is not preserved!")
        return False
    else:
        logger.info("✅ Weights history differs from signal history - allocation history is preserved!")
        return True

def compare_weights_vs_signals(results):
    """Compare weights history vs signal history."""
    weights_history = results['weights_history']
    signal_history = results['signal_history']
    
    # Get a sample of dates to compare
    sample_dates = weights_history.index[::20]  # Every 20th date
    
    for date in sample_dates:
        weights = weights_history.loc[date]
        signals = signal_history.loc[date] if date in signal_history.index else pd.Series()
        
        logger.info(f"\nDate: {date}")
        logger.info(f"Weights: {weights.to_dict()}")
        logger.info(f"Signals: {signals.to_dict() if not signals.empty else 'No signals'}")

def plot_performance(results, title="Backtest Performance"):
    """Plot cumulative returns of the strategy."""
    strategy_returns = results['strategy_returns']
    benchmark_returns = results['benchmark_returns']
    
    # Calculate cumulative returns
    strategy_cum_returns = (1 + strategy_returns).cumprod()
    benchmark_cum_returns = (1 + benchmark_returns).cumprod()
    
    # Plot
    plt.figure(figsize=(12, 6))
    plt.plot(strategy_cum_returns.index, strategy_cum_returns, label='Strategy')
    plt.plot(benchmark_cum_returns.index, benchmark_cum_returns, label='Benchmark')
    plt.title(title)
    plt.xlabel('Date')
    plt.ylabel('Cumulative Return')
    plt.legend()
    plt.grid(True)
    
    # Save plot
    output_dir = Path(__file__).parent / "output"
    output_dir.mkdir(exist_ok=True)
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    plt.savefig(output_dir / f"{title.replace(' ', '_').lower()}_{timestamp}.png")
    
    # Also save the returns data to CSV for further analysis
    strategy_cum_returns.to_csv(output_dir / f"{title.replace(' ', '_').lower()}_returns_{timestamp}.csv")
    
    logger.info(f"Performance plot and data saved to {output_dir}")

def export_results_to_csv(results_dict, output_dir):
    """Export test results to CSV file."""
    output_dir = Path(output_dir)
    output_dir.mkdir(exist_ok=True)
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # Export summary metrics
    with open(output_dir / f"backtest_results_summary_{timestamp}.csv", 'w', newline='') as f:
        writer = csv.writer(f)
        writer.writerow(['Strategy', 'CAGR', 'Volatility', 'Sharpe', 'MaxDrawdown', 'Turnover', 'Allocation_History_Preserved'])
        
        for strategy, data in results_dict.items():
            results = data['results']
            perf = results['performance']
            writer.writerow([
                strategy,
                f"{perf['cagr']:.4f}",
                f"{perf['volatility']:.4f}",
                f"{perf['sharpe']:.4f}",
                f"{perf['max_drawdown']:.4f}",
                f"{perf['turnover']:.4f}",
                'Yes' if data['allocation_preserved'] else 'No'
            ])
    
    logger.info(f"Results summary exported to {output_dir / f'backtest_results_summary_{timestamp}.csv'}")

def run_tests():
    """Run all tests."""
    print("=====================================================")
    print("BACKTEST ENGINE VALIDATION TEST - CPS V4 COMPLIANT")
    print(f"Run Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=====================================================\n")
    
    logger.info("Starting backtest engine validation tests")
    
    # Create output directory
    output_dir = Path(__file__).parent / "output"
    output_dir.mkdir(exist_ok=True)
    
    # Generate test data
    price_data = generate_test_data()
    logger.info(f"Generated test data with {len(price_data)} dates and {len(price_data.columns)} symbols")
    
    # Store results for comparison and export
    all_results = {}
    
    # Test 1: Equal weight strategy with monthly rebalancing
    print("\n=== Test 1: Equal Weight Strategy with Monthly Rebalancing ===")
    logger.info("\n=== Test 1: Equal Weight Strategy with Monthly Rebalancing ===")
    results_eq = run_backtest_test(price_data, strategy='equal_weight', rebalance_freq='M')
    valid_eq = validate_weights_history(results_eq)
    compare_weights_vs_signals(results_eq)
    plot_performance(results_eq, "Equal Weight Strategy")
    all_results['Equal Weight'] = {'results': results_eq, 'allocation_preserved': valid_eq}
    
    # Momentum strategy removed to keep the codebase simple
    
    # Test 3: EMA strategy with execution delay
    print("\n=== Test 3: EMA Strategy with Execution Delay ===")
    logger.info("\n=== Test 3: EMA Strategy with Execution Delay ===")
    results_ema = run_backtest_test(price_data, strategy='ema', rebalance_freq='M', execution_delay=1)
    valid_ema = validate_weights_history(results_ema)
    compare_weights_vs_signals(results_ema)
    plot_performance(results_ema, "EMA Strategy with Delay")
    all_results['EMA with Delay'] = {'results': results_ema, 'allocation_preserved': valid_ema}
    
    # Print performance metrics
    print("\n=====================================================\n")
    print("PERFORMANCE METRICS SUMMARY")
    print("=====================================================\n")
    
    for name, data in all_results.items():
        results = data['results']
        perf = results['performance']
        print(f"\n{name} Strategy:")
        print(f"CAGR: {perf['cagr']:.2%}")
        print(f"Volatility: {perf['volatility']:.2%}")
        print(f"Sharpe Ratio: {perf['sharpe']:.2f}")
        print(f"Max Drawdown: {perf['max_drawdown']:.2%}")
        print(f"Turnover: {perf['turnover']:.2f}")
        print(f"Allocation History Preserved: {'✅ Yes' if data['allocation_preserved'] else '❌ No'}")
    
    # Export results to CSV
    export_results_to_csv(all_results, output_dir)
    
    # Overall validation result
    print("\n=====================================================\n")
    print("OVERALL TEST RESULTS")
    print("=====================================================\n")
    
    if all([data['allocation_preserved'] for data in all_results.values()]):
        print("\n✅ ALL TESTS PASSED! Allocation history is preserved correctly.\n")
        logger.info("\n✅ All tests passed! Allocation history is preserved correctly.")
    else:
        print("\n❌ SOME TESTS FAILED! Check the logs for details.\n")
        logger.warning("\n⚠️ Some tests failed! Check the logs for details.")

if __name__ == "__main__":
    print("DIAGNOSTIC: test_backtest_v4.py script started")
    run_tests()
    print("DIAGNOSTIC: test_backtest_v4.py script completed")
