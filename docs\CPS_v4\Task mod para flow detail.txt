1 - take 1st module in production list
  - go through every item parameter @convert_param_to_v3.md 
 - find flag every parameter you find that needs conversion to new system v4
 convert it
 make a list of all NEWLY discovered parameters not listed in md files - we need to review for action on those
 - rename file
 log progress
  if it has v3 in name- it changes to v4, otherwise - add "_v4" to end of name
 No batch files
you do the work
make sense?
Questions?