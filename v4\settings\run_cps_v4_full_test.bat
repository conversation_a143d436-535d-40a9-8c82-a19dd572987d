@echo off
REM === CPS v4 Full Test Batch ===

REM Ensure Log directory exists
set LOGDIR=%~dp0Log
if not exist "%LOGDIR%" mkdir "%LOGDIR%"

REM Generate timestamp for log file
for /f "tokens=1-4 delims=/ " %%a in ("%date% %time%") do set TS=%%d%%b%%c_%%e%%f%%g
set LOGFILE=%LOGDIR%\cps_v4_full_test_%TS%.txt

REM --- CRITICAL: Set environment variables for production validation ---
set USE_PRODUCTION_DATA=1
set SKIP_MOCK_DATA=1

REM Activate environment
echo [INFO] Activating virtual environment... >> "%LOGFILE%" 2>&1
call F:\AI_Library\my_quant_env\Scripts\activate.bat

REM Set Python path
echo [INFO] Setting PYTHONPATH... >> "%LOGFILE%" 2>&1
set PYTHONPATH=S:\Dropbox\Scott Only Internal\Quant_Python_24\Custom Function Library

REM Run parameter discovery (capture all parameters)
echo [INFO] Running parameter discovery... >> "%LOGFILE%" 2>&1
python -u CPS_v4\V4_transition\parameter_discovery_CPS_v4.py --verbose >> "%LOGFILE%" 2>&1
echo [INFO] Parameter discovery complete. >> "%LOGFILE%" 2>&1

REM Run CPS v4 production verification (full test)
echo [INFO] Running production verification... >> "%LOGFILE%" 2>&1
python -u CPS_v4\V4_transition\verify_CPS_v4.py --validate-production --verbose >> "%LOGFILE%" 2>&1
echo [INFO] Production verification complete. >> "%LOGFILE%" 2>&1

echo Log written to: %LOGFILE%
REM pause
