# System Files + Flow Documentation (AI-Optimized)

<!-- AI: CREATE MEMORY FROM THIS FILE
Title: V3 System Architecture Reference
Tags: [system_architecture, module_flow, v3_system]
-->

> **Last Updated**: 2025-06-03
> **Purpose**: Central reference for system architecture, module relationships, and data flows
>
> **Related Files**:
>
> - [V3 Module Functions List](v3_module+functions_list_AI.md)
> - [Parameter System Reference](v3_parameter_system_reference.md)

## 🔍 Quick Reference

| Component Type | Key Files | Purpose |
|----------------|-----------|---------|
| **Parameter System** | `v3_engine/parameter_registry.py`, `v3_engine/parameters.py` | Type-safe parameter handling |
| **Backtest Engine** | `engine/backtest.py`, `engine/portfolio.py` | Core backtest logic |
| **Strategy** | `v3_engine/ema_v3_adapter.py` | Signal generation |
| **Reporting** | `v3_reporting/v3_performance_report.py` | Results output |

## 📊 System Flow Diagram

```mermaid
flowchart LR
    %% Data Flow
    DataLoader[data_loader.py] -->|OHLCV Data| Strategy[ema_v3_adapter.py]
    DataLoader -->|Benchmark Data| Backtest[backtest.py]

    %% Parameter Flow
    ParamRegistry[parameter_registry.py] -->|Parameters| Strategy
    ParamRegistry -->|Parameters| Backtest
    
    %% Execution Flow
    Strategy -->|Signals| Backtest
    Backtest -->|Results| Reports[v3_performance_report.py]
    
    %% Styling
    classDef data fill:#F5F5F5,stroke:#616161,color:#212121;
    classDef param fill:#C8E6C9,stroke:#388E3C,color:#1B5E20;
    classDef engine fill:#FFECB3,stroke:#F57F17,color:#E65100;
    classDef report fill:#F3E5F5,stroke:#8E24AA,color:#4A148C;
    
    class DataLoader data;
    class ParamRegistry param;
    class Strategy,Backtest engine;
    class Reports report;
```

## 🧩 Core Engine Components

| Module | Key Functions | Purpose | Status |
|--------|---------------|---------|--------|
| **`engine/backtest.py`** | `run_backtest()`, `calculate_metrics()` | Main backtest execution | ✅ |
| **`engine/portfolio.py`** | `update_portfolio()`, `calculate_value()` | Portfolio tracking | ✅ |
| **`engine/execution.py`** | `execute_trades()`, `apply_slippage()` | Trade execution | ✅ |
| **`v3_engine/parameter_registry.py`** | `register_parameter()`, `get_parameter()` | Parameter management | ✅ |

## 🔄 Key Data Flows

### 1. Parameter Flow

```text
GUI → Parameter Registry → Strategy → Backtest Engine → Reports
```

**Key Components**: See [Parameter Management](parameter_management_AI.md) for details

### 2. Data Flow

```text
Data Loader → Strategy → Backtest Engine → Portfolio → Reports
```

**Key Components**:

- `data/data_loader.py`: Loads price data
- `v3_engine/ema_v3_adapter.py`: Generates signals
- `engine/backtest.py`: Executes backtest
- `engine/portfolio.py`: Tracks portfolio state

### 3. Reporting Flow

```text
Backtest Engine → Performance Reporter → Excel/Charts
```

**Key Components**:
- `v3_reporting/v3_performance_report.py`: Generates performance reports
- `v3_reporting/v3_allocation_report.py`: Generates allocation reports
- `v3_engine/performance_reporter_adapter.py`: Adapts V3 parameters to reporting

## 📝 Parameter System

For detailed information about the parameter system, including registry API, parameter classes, and optimization, see the [Parameter Management](parameter_management_AI.md) document.

## 📈 V3 Reporting System

| Module | Purpose | Status |
|--------|---------|:------:|
| `v3_reporting/v3_performance_report.py` | Performance metrics | ✅ |
| `v3_reporting/v3_allocation_report.py` | Allocation history | ✅ |
| `v3_reporting/visualization_parameters.py` | Visualization parameter definitions | ✅ |
| `v3_reporting/reporting_parameters.py` | Reporting parameter definitions | ✅ |
| `v3_reporting/parameter_registry_integration.py` | Parameter registry integration | ✅ |
| `v3_engine/performance_reporter_adapter.py` | V2/V3 bridge | ✅ |
| `v3_engine/V3_perf_repadapt_legacybridge.py` | Legacy bridge functions | ✅ |
| `v3_engine/V3_perf_repadapt_paramconvert.py` | Parameter conversion utilities | ✅ |

## 🔄 V3 Process Flow

```mermaid
flowchart TD
    subgraph GUI[GUI Layer]
        A[v3_gui_core.py] -->|Initialize| B[v3_parameter_widgets.py]
        A -->|Actions| C[v3_gui_actions.py]
        B -->|Manage| D[gui_parameter_manager.py]
    end
    
    subgraph Engine[Engine Layer]
        E[parameter_registry.py] -->|Type Defs| F[parameters.py]
        E -->|Strategy Params| G[strategy_parameter_set.py]
        H[ema_v3_adapter.py] -->|Signals| I[backtest.py]
    end
    
    subgraph Reporting[Reporting Layer]
        J[v3_performance_report.py]
        K[v3_allocation_report.py]
        L[performance_reporter_adapter.py]
    end
    
    D -->|Sync| E
    G -->|Apply| H
    I -->|Results| J
    I -->|Allocations| K
    L -->|Bridge| J
    
    classDef gui fill:#BBDEFB,stroke:#1976D2,color:#0D47A1;
    classDef engine fill:#C8E6C9,stroke:#388E3C,color:#1B5E20;
    classDef report fill:#F3E5F5,stroke:#8E24AA,color:#4A148C;
    
    class A,B,C,D gui;
    class E,F,G,H,I engine;
    class J,K,L report;
```

## 📋 Verification System

| Component | Location | Purpose | Status |
|-----------|----------|---------|:------:|
| `tests/verify_v3_reporting.py` | Tests directory | Main verification script | ✅ |
| `run_v3_verification.bat` | Project root | Verification batch file | ✅ |
| `tests/test_v3_reporting.py` | Tests directory | End-to-end test script | ✅ |
| `verify_v3_reporting.py` (legacy) | Project root | Outdated verification script | ⚠️ |

### Verification Process Flow

```text
run_v3_verification.bat → tests/verify_v3_reporting.py → Verification Report
```

**Key Components**:

- `run_v3_verification.bat`: Sets environment variables and activates Python environment
- `tests/verify_v3_reporting.py`: Performs verification in isolated test environment
- Verification Report: Generated in the output directory with pass/fail status

## 🔍 Recent Changes (June 2025)

1. **Parameter System**: See [Parameter Management](parameter_management_AI.md) for recent changes

2. **Reporting**:
   - Refactored verification system into tests directory
   - Added parameter registry integration module
   - Split reporting and visualization parameters into separate modules
   - Enhanced adapter pattern with legacy bridge components

3. **GUI**:
   - Added parameter group visibility controls
   - Improved parameter widget layout
   - Fixed stalling issues in backtest execution

## 📋 Implementation Status

| Component | Status | Notes |
|-----------|:------:|-------|
| Parameter System | ✅ | See [Parameter Management](parameter_management_AI.md) |
| GUI Integration | ✅ | Fully functional |
| EMA Strategy Adapter | ✅ | Complete with parameter integration |
| Backtest Engine Integration | ✅ | Working with V3 parameters |
| Performance Reporting | ✅ | Complete with optimization support |
| Allocation Reporting | ✅ | Complete with improved formatting |
| Verification System | ✅ | Refactored into tests directory |
| Parameter Registry Integration | ✅ | Central integration point for reporting parameters |
| Data Validation | ⚠️ | Basic implementation, needs expansion |
| Error Handling | 🔄 | Planned for future implementation |
| Parameter Registry Size | ⚠️ | Exceeds 450-line limit, needs refactoring |

---

*This document is maintained as the central reference for V3 system architecture. Update as the system evolves.*
