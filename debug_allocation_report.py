"""
Debug script for allocation report generation.
This script isolates the allocation report generation to identify Excel formatting issues.
"""

import os
import sys
import logging
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta

# Set up logging
logging.basicConfig(level=logging.INFO, 
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Add project root to path
project_root = os.path.abspath(os.path.dirname(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# Import the allocation report module
from v3_reporting.v3_allocation_report import generate_v3_allocation_report

def create_test_data():
    """Create test data for allocation report."""
    # Create date range
    start_date = datetime(2024, 1, 1)
    end_date = datetime(2024, 4, 30)
    dates = pd.date_range(start=start_date, end=end_date, freq='D')
    
    # Create tickers
    tickers = ['SPY', 'TLT', 'EFA', 'SHV', 'PFF']
    
    # Create signal history
    signal_data = {}
    for ticker in tickers:
        # Create random signals that sum to 1 across tickers
        signal_data[ticker] = np.random.random(len(dates)) / len(tickers)
    
    # Add cash column
    signal_data['Cash'] = np.random.random(len(dates)) / len(tickers)
    
    # Create signal DataFrame
    signal_df = pd.DataFrame(signal_data, index=dates)
    
    # Normalize rows to sum to 1
    row_sums = signal_df.sum(axis=1)
    for col in signal_df.columns:
        signal_df[col] = signal_df[col] / row_sums
    
    return signal_df

def run_test():
    """Run test for allocation report generation."""
    logger.info("Starting allocation report test")
    
    # Create test data
    signal_df = create_test_data()
    logger.info(f"Created test data with {len(signal_df)} rows and {len(signal_df.columns)} columns")
    
    # Create output directory
    output_dir = os.path.join('output', 'debug_reports')
    os.makedirs(output_dir, exist_ok=True)
    
    # Generate allocation report
    try:
        logger.info("Generating allocation report")
        report_path = generate_v3_allocation_report(
            signal_df=signal_df,
            output_dir=output_dir,
            strategy_name='DEBUG',
            include_cash=True
        )
        
        logger.info(f"Generated allocation report: {report_path}")
        logger.info("Test completed successfully")
        return report_path
    except Exception as e:
        logger.error(f"Error generating allocation report: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return None

if __name__ == "__main__":
    run_test()
