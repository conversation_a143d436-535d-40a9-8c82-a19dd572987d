Parameter Flow Documentation
===========================

1. Parameter Source
   - Settings loaded from v4.settings.settings_CPS_v4.load_settings()

2. Report Parameters
   - create_excel: True
   - save_trade_log: True
   - include_summary: True
   - excel_format: {'default': "'xlsx'", 'picklist': ['Excel_XLSX', 'Excel_XLS', 'Excel_CSV']}
   - output_directory: output
   - risk_free_rate: 0.02

3. Parameter Flow Path
   - Settings loaded in v4_performance_report.py
   - Passed to _generate_excel_report function
   - Used to configure report generation
