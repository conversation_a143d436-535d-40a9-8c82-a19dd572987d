#!/usr/bin/env python3
"""
Memory System Command Line Interface

A simple CLI for interacting with the project's memory system.
"""

import argparse
import json
import logging
import sys
from pathlib import Path
from typing import List, Dict, Any, Optional

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Add project root to path
PROJECT_ROOT = Path(__file__).parent.absolute()
sys.path.insert(0, str(PROJECT_ROOT))

# Import knowledge manager
from docs.memory.scripts.knowledge_manager import KnowledgeManager

class MemoryCLI:
    """Command-line interface for the memory system."""
    
    def __init__(self):
        """Initialize the CLI with a knowledge manager."""
        self.km = KnowledgeManager()
        self.setup_parser()
    
    def setup_parser(self) -> None:
        """Set up the command-line argument parser."""
        self.parser = argparse.ArgumentParser(
            description='Project Memory System CLI',
            usage='''mem <command> [<args>]

Available commands:
  list        List all entities or relationships
  show        Show details of an entity
  search      Search the knowledge base
  graph       Generate a visualization of the knowledge graph
'''
        )
        self.parser.add_argument('command', help='Command to run')
        
        # Parse the command
        if len(sys.argv) < 2:
            self.parser.print_help()
            sys.exit(1)
        
    def run(self) -> None:
        """Run the CLI."""
        args = self.parser.parse_args(sys.argv[1:2])
        
        # Dispatch to appropriate method
        if not hasattr(self, args.command):
            print(f"Unknown command: {args.command}")
            self.parser.print_help()
            sys.exit(1)
            
        getattr(self, args.command)()
    
    def list(self) -> None:
        """List all entities or relationships."""
        parser = argparse.ArgumentParser(description='List entities or relationships')
        parser.add_argument('type', choices=['entities', 'relationships'], 
                          help='Type of items to list')
        args = parser.parse_args(sys.argv[2:])
        
        if args.type == 'entities':
            self._list_entities()
        else:
            self._list_relationships()
    
    def _list_entities(self) -> None:
        """List all entities in the knowledge base."""
        entities_dir = PROJECT_ROOT / 'docs' / 'memory' / 'entities'
        if not entities_dir.exists():
            print("No entities found. The entities directory does not exist.")
            return
            
        entities = list(entities_dir.glob('*.json'))
        if not entities:
            print("No entities found.")
            return
            
        print("\nEntities in knowledge base:")
        print("-" * 80)
        for i, entity_file in enumerate(entities, 1):
            try:
                with open(entity_file, 'r') as f:
                    data = json.load(f)
                    name = data.get('name', 'Unnamed')
                    desc = data.get('observations', ['No description'])[0][:60]
                    print(f"{i}. {name}")
                    print(f"   {desc}...")
                    print()
            except Exception as e:
                print(f"Error reading {entity_file}: {e}")
    
    def _list_relationships(self) -> None:
        """List all relationships in the knowledge base."""
        rels_dir = PROJECT_ROOT / 'docs' / 'memory' / 'relations'
        if not rels_dir.exists():
            print("No relationships found. The relations directory does not exist.")
            return
            
        rels = list(rels_dir.glob('*.json'))
        if not rels:
            print("No relationships found.")
            return
            
        print("\nRelationships in knowledge base:")
        print("-" * 80)
        for i, rel_file in enumerate(rels, 1):
            try:
                with open(rel_file, 'r') as f:
                    data = json.load(f)
                    print(f"{i}. {data.get('from')} --[{data.get('type')}]--> {data.get('to')}")
            except Exception as e:
                print(f"Error reading {rel_file}: {e}")
    
    def show(self) -> None:
        """Show details of an entity."""
        parser = argparse.ArgumentParser(description='Show entity details')
        parser.add_argument('entity', help='Name of the entity to show')
        args = parser.parse_args(sys.argv[2:])
        
        entity_file = PROJECT_ROOT / 'docs' / 'memory' / 'entities' / f"{args.entity}.json"
        if not entity_file.exists():
            print(f"Entity '{args.entity}' not found.")
            return
            
        try:
            with open(entity_file, 'r') as f:
                data = json.load(f)
                print(f"\nEntity: {data.get('name', 'Unnamed')}")
                print("=" * 80)
                
                # Display basic info
                if 'entityType' in data:
                    print(f"Type: {data['entityType']}")
                if 'created' in data:
                    print(f"Created: {data['created']}")
                if 'lastUpdated' in data:
                    print(f"Last Updated: {data['lastUpdated']}")
                    
                # Display observations
                if 'observations' in data and data['observations']:
                    print("\nObservations:")
                    for obs in data['observations']:
                        print(f"- {obs}")
                        
                # Display components
                if 'components' in data and data['components']:
                    print("\nComponents:")
                    for comp in data['components']:
                        print(f"- {comp}")
                        
                # Display files
                if 'files' in data and data['files']:
                    print("\nFiles:")
                    for f in data['files']:
                        print(f"- {f}")
                        
        except Exception as e:
            print(f"Error reading entity: {e}")
    
    def search(self) -> None:
        """Search the knowledge base."""
        parser = argparse.ArgumentParser(description='Search the knowledge base')
        parser.add_argument('query', help='Search query')
        args = parser.parse_args(sys.argv[2:])
        
        # Simple search implementation - could be enhanced with better search
        print(f"Searching for: {args.query}")
        print("-" * 80)
        
        # Search entities
        entities_dir = PROJECT_ROOT / 'docs' / 'memory' / 'entities'
        if entities_dir.exists():
            for entity_file in entities_dir.glob('*.json'):
                try:
                    with open(entity_file, 'r') as f:
                        data = json.load(f)
                        name = data.get('name', '').lower()
                        
                        # Check if query matches name or any observation
                        obs_matches = any(args.query.lower() in obs.lower() 
                                       for obs in data.get('observations', []))
                        
                        if args.query.lower() in name or obs_matches:
                            print(f"\nEntity: {data.get('name')}")
                            if 'observations' in data and data['observations']:
                                print(f"  {data['observations'][0]}")
                except Exception as e:
                    logger.error(f"Error processing {entity_file}: {e}")
    
    def graph(self) -> None:
        """Generate a visualization of the knowledge graph."""
        try:
            import graphviz
        except ImportError:
            print("Error: graphviz package is required for graph visualization.")
            print("Install it with: pip install graphviz")
            print("Also make sure Graphviz is installed on your system.")
            print("See: https://graphviz.org/download/")
            return
            
        print("Generating knowledge graph...")
        
        # Create a new directed graph
        dot = graphviz.Digraph(comment='Knowledge Graph')
        dot.attr(rankdir='LR')
        
        # Add entities as nodes
        entities_dir = PROJECT_ROOT / 'docs' / 'memory' / 'entities'
        if entities_dir.exists():
            for entity_file in entities_dir.glob('*.json'):
                with open(entity_file, 'r') as f:
                    data = json.load(f)
                    entity_name = data.get('name')
                    if entity_name:
                        dot.node(entity_name, shape='box')
        
        # Add relationships as edges
        rels_dir = PROJECT_ROOT / 'docs' / 'memory' / 'relations'
        if rels_dir.exists():
            for rel_file in rels_dir.glob('*.json'):
                with open(rel_file, 'r') as f:
                    data = json.load(f)
                    dot.edge(data.get('from'), data.get('to'), label=data.get('type', ''))
        
        # Render the graph
        output_path = PROJECT_ROOT / 'docs' / 'memory' / 'knowledge_graph'
        dot.render(output_path, format='png', cleanup=True)
        print(f"Knowledge graph saved to: {output_path}.png")


def main() -> None:
    """Run the memory system CLI."""
    try:
        cli = MemoryCLI()
        cli.run()
    except KeyboardInterrupt:
        print("\nOperation cancelled by user.")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Error: {e}", exc_info=True)
        sys.exit(1)


if __name__ == "__main__":
    main()
