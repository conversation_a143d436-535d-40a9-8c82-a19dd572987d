#!/usr/bin/env python3
"""
debug_config_test.py
Standalone test to diagnose config loading issues in PerformanceTableGenerator
"""

import configparser
import os
import sys

def test_config_loading():
    """Test config loading exactly like PerformanceTableGenerator does."""
    
    # Use the same config path as PerformanceTableGenerator
    config_path = "v4/settings/settings_parameters_v4.ini"
    
    print("=" * 60)
    print("CONFIG LOADING DIAGNOSTIC TEST")
    print("=" * 60)
    
    # Show current working directory
    print(f"Current working directory: {os.getcwd()}")
    
    # Show absolute path
    abs_config_path = os.path.abspath(config_path)
    print(f"Config path (relative): {config_path}")
    print(f"Config path (absolute): {abs_config_path}")
    print(f"Config file exists: {os.path.exists(abs_config_path)}")
    
    if not os.path.exists(abs_config_path):
        print("ERROR: Config file does not exist!")
        return
    
    # Load config exactly like PerformanceTableGenerator does
    print("\n" + "-" * 40)
    print("LOADING CONFIG")
    print("-" * 40)
    
    config = configparser.ConfigParser(interpolation=None)
    config.read(config_path)
    
    # Show all sections
    sections = config.sections()
    print(f"All sections found: {sections}")
    print(f"Total sections: {len(sections)}")
    
    # Check for System section specifically
    print("\n" + "-" * 40)
    print("SYSTEM SECTION ANALYSIS")
    print("-" * 40)
    
    if 'System' in sections:
        print("✓ System section FOUND")
        system_keys = list(config['System'].keys())
        print(f"System section keys: {system_keys}")
        
        # Show all System section values
        for key in config['System']:
            value = config['System'][key]
            print(f"  System.{key} = {value}")
            
        # Test the specific access that's failing
        print("\n" + "-" * 40)
        print("TESTING SPECIFIC ACCESS")
        print("-" * 40)
        
        try:
            debug_mode = config['System']['optimization_debug_mode']
            print(f"✓ Successfully accessed optimization_debug_mode: {debug_mode}")
        except KeyError as e:
            print(f"✗ KeyError accessing optimization_debug_mode: {e}")
            
    else:
        print("✗ System section NOT FOUND")
        print("This is the root cause of the KeyError!")
        
        # Show what sections DO exist
        print("\nSections that DO exist:")
        for section in sections:
            print(f"  - {section}")
    
    print("\n" + "=" * 60)
    print("TEST COMPLETE")
    print("=" * 60)

if __name__ == "__main__":
    test_config_loading()
