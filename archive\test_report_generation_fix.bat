@echo off
echo Testing Report Generation Fixes...
echo ===================================
echo.

REM Set up environment
call "F:\AI_Library\my_quant_env\Scripts\activate.bat"
cd /d "S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template"

echo Current directory: %cd%
echo.

REM Create output directory if it doesn't exist
if not exist "output" mkdir "output"

REM Test the PerformanceTableGenerator fixes
python test_report_fix.py > output\test_report_fix_output.txt 2>&1

echo.
echo Test results saved to: output\test_report_fix_output.txt
echo.
echo Displaying results...
type output\test_report_fix_output.txt

echo.
echo Test complete.
pause
