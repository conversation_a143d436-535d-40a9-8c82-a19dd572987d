"""
Performance reporting and visualization module.
Leverages the Custom Function Library's reporting and visualization capabilities.
"""

import os
import sys
import pandas as pd
import numpy as np
import logging
from pathlib import Path
import matplotlib.pyplot as plt
from datetime import date
from config.paths import *
from config.config import config
from utils.date_utils import (
    standardize_date,
    standardize_dataframe_index,
    display_date,
    format_dataframe_index_for_display
)

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Import visualization functions
from visualization.performance_charts import create_cumulative_returns_chart
from visualization.performance_charts import create_drawdown_chart
from visualization.performance_charts import create_return_distribution_chart
from visualization.performance_charts import create_monthly_returns_chart
from visualization.performance_charts import create_portfolio_weights_chart
from openpyxl import load_workbook

# Performance metric helper functions
def calculate_cagr(returns):
    """Calculate Compound Annual Growth Rate."""
    if returns is None or len(returns) == 0:
        return 0
    total_return = (1 + returns).prod() - 1
    years = len(returns) / 252  # Assuming 252 trading days per year
    if years > 0:
        return (1 + total_return) ** (1 / years) - 1
    return 0

def calculate_sharpe_ratio(returns, risk_free_rate=0.0):
    """Calculate Sharpe Ratio."""
    if returns is None or len(returns) == 0:
        return 0
    excess = returns - risk_free_rate
    std = excess.std()
    if std == 0:
        return 0
    return excess.mean() / std * np.sqrt(252)

def calculate_sortino_ratio(returns, risk_free_rate=0.0, target_return=0.0):
    """Calculate Sortino Ratio."""
    if returns is None or len(returns) == 0:
        return 0
    excess = returns - risk_free_rate
    downside = excess[excess < target_return]
    std_down = downside.std()
    if len(downside) == 0 or std_down == 0:
        return 0
    return excess.mean() / std_down * np.sqrt(252)

def calculate_max_drawdown(returns):
    """Calculate Maximum Drawdown."""
    if returns is None or len(returns) == 0:
        return 0
    cum = (1 + returns).cumprod()
    drawdown = cum / cum.cummax() - 1
    return drawdown.min()

def get_calendar_year_returns(returns, year):
    """Calculate returns for a specific calendar year."""
    if returns is None or len(returns) == 0:
        return None
    year_data = returns[returns.index.year == year]
    if year_data.empty:
        return None
    return (1 + year_data).prod() - 1

def get_ytd_returns(returns):
    """Calculate Year-to-Date returns."""
    if returns is None or len(returns) == 0:
        return None
    current_year = pd.Timestamp.now().year
    ytd = returns[returns.index.year == current_year]
    if ytd.empty:
        return None
    return (1 + ytd).prod() - 1

def calculate_performance_metrics(returns, risk_free_rate=None, benchmark_returns=None):
    """
    Calculate performance metrics for a strategy.
    
    Args:
        returns (Series): Series of strategy returns
        risk_free_rate (Series, optional): Series of risk-free rates
        benchmark_returns (Series, optional): Series of benchmark returns
        
    Returns:
        dict: Dictionary of performance metrics
    """
    # If no risk-free rate provided, use zeros
    if risk_free_rate is None or len(risk_free_rate) == 0:
        risk_free_rate = pd.Series(0.0, index=returns.index if returns is not None and not returns.empty else None)
    
    # Ensure risk_free_rate has the same index as returns
    if returns is not None and not returns.empty and not risk_free_rate.index.equals(returns.index):
        # Reindex risk_free_rate to match returns
        risk_free_rate = risk_free_rate.reindex(returns.index, method='ffill')
    
    # Calculate metrics using the existing functions
    metrics = {}
    
    if returns is not None and not returns.empty:
        metrics['total_return'] = (1 + returns).prod() - 1
        metrics['cagr'] = calculate_cagr(returns)
        metrics['volatility'] = returns.std() * np.sqrt(252)
        metrics['sharpe_ratio'] = calculate_sharpe_ratio(returns, risk_free_rate.mean())
        metrics['sortino_ratio'] = calculate_sortino_ratio(returns, risk_free_rate.mean())
        metrics['max_drawdown'] = calculate_max_drawdown(returns)
        
        # Calculate win rate
        metrics['win_rate'] = (returns > 0).mean()
    else:
        # Default values if returns are empty
        metrics['total_return'] = 0
        metrics['cagr'] = 0
        metrics['volatility'] = 0
        metrics['sharpe_ratio'] = 0
        metrics['sortino_ratio'] = 0
        metrics['max_drawdown'] = 0
        metrics['win_rate'] = 0
    
    # Calculate benchmark metrics if available
    if benchmark_returns is not None and not benchmark_returns.empty:
        metrics['benchmark_total_return'] = (1 + benchmark_returns).prod() - 1
        metrics['benchmark_cagr'] = calculate_cagr(benchmark_returns)
        metrics['benchmark_volatility'] = benchmark_returns.std() * np.sqrt(252)
        metrics['benchmark_sharpe_ratio'] = calculate_sharpe_ratio(benchmark_returns, risk_free_rate.mean())
        metrics['benchmark_max_drawdown'] = calculate_max_drawdown(benchmark_returns)
    else:
        # Default values if benchmark returns are empty
        metrics['benchmark_total_return'] = 0
        metrics['benchmark_cagr'] = 0
        metrics['benchmark_volatility'] = 0
        metrics['benchmark_sharpe_ratio'] = 0
        metrics['benchmark_max_drawdown'] = 0
    
    return metrics

def create_backtest_report(backtest_results, config, output_dir='output', prefix=''):
    """
    Generate a comprehensive performance report from backtest results.
    
    Args:
        backtest_results (dict): Results from backtest including:
            - returns: Series of portfolio returns
            - weights_history: DataFrame of portfolio weights over time
            - backtest_data: Dict with backtest metadata
        config (dict): Configuration dictionary
        output_dir (str): Directory to save report
        prefix (str): Prefix for filenames
        
    Returns:
        dict: Paths to generated files
    """
    try:
        # Generate output path
        if output_dir is None:
            output_dir = 'output'
        os.makedirs(output_dir, exist_ok=True)
        
        # Generate filename with standardized naming convention
        if prefix is None:
            prefix = 'EMA_V3_1'
        
        # Use provided timestamp if available, otherwise generate a new one
        timestamp = date.today().strftime('%Y%m%d_%H%M%S')
        filename = f"{prefix}_{timestamp}" if prefix else timestamp
        
        # Extract data from backtest results
        strategy_returns = backtest_results.get('returns')
        weights_history = backtest_results.get('weights_history')
        transaction_costs = backtest_results.get('transaction_costs', pd.Series(0, index=strategy_returns.index))
        benchmark_returns = backtest_results.get('benchmark_returns', None)
        
        # Create Excel path
        excel_path = os.path.join(output_dir, f"{filename}_performance.xlsx")
        
        # Create performance tables
        performance_data = pd.DataFrame({
            'Strategy': strategy_returns
        })
        if benchmark_returns is not None:
            performance_data['Benchmark'] = benchmark_returns
        
        # Import functions directly from the Custom Function Library
        # This avoids circular imports by only importing when needed
        # We need to add the custom library path to sys.path temporarily
        custom_lib_path = str(CUSTOM_LIB_PATH)
        if custom_lib_path not in sys.path:
            sys.path.insert(0, custom_lib_path)
            
        # Now import the functions from the Custom Function Library
        try:
            from reporting.performance_reporting import create_performance_tables
            from reporting.performance_reporting import format_excel_report
            
            # Use the create_performance_tables function from the Custom Function Library
            create_performance_tables(
                performance_data=performance_data,
                weights_history=weights_history,
                output_dir=output_dir,
                filename=f"{filename}_performance"
            )
            
            # Format the Excel report
            format_excel_report(
                excel_path=excel_path,
                percent_columns=['CAGR', 'Sharpe', 'Sortino', 'Max Drawdown'],
                highlight_best=True
            )
            
            logger.info(f"Performance tables saved to {excel_path}")
            
            # Remove extra Performance_Full sheet if present
            wb = load_workbook(excel_path)
            if 'Performance_Full' in wb.sheetnames:
                wb.remove(wb['Performance_Full'])
                wb.save(excel_path)
                logger.info(f"Removed extra sheet Performance_Full from {excel_path}")
        except ImportError as e:
            logger.error(f"Error importing from Custom Function Library: {e}")
            logger.warning("Creating basic performance report without Custom Function Library")
            
            # Create a basic Excel report without the Custom Function Library
            with pd.ExcelWriter(excel_path) as writer:
                performance_data.to_excel(writer, sheet_name='Performance')
                if weights_history is not None:
                    weights_history.to_excel(writer, sheet_name='Weights')
            
            logger.info(f"Basic performance tables saved to {excel_path}")
        
        # Generate and save visualizations
        viz_paths = generate_visualizations(
            backtest_results=backtest_results,
            output_dir=output_dir,
            filename=filename
        )
        
        # Return paths to all generated files
        return {
            'excel_report': excel_path,
            'visualizations': viz_paths
        }
        
    except Exception as e:
        logger.error(f"Error generating performance report: {e}")
        return {}

# ... (rest of the code remains the same)

def create_performance_table(returns, benchmark_returns=None, strategy_params=None, benchmark_only=False):
    """
    Create a performance table with strategy parameters and performance metrics.
    
    Layout:
    Left side: Strategy parameters (st_lookback, mt_lookback, lt_lookback, top_n positions, execution delay)
        - These column variables will change to new variables if/when different variables are tested.
    
    Right side:
    CAGR (%)
    Sharpe
    Sortino
    Max Drawdown (%)
    YTD '25 (%)
    2024 (%)
    2023 (%)
    2022 (%)
    2021 (%)
    2020 (%)
    
    Rows:
    Benchmark Results (for every column)
    Strategy Results (for every column)
    
    Notes:
    - When testing/optimizing ranges of values for key variables, there will be new sets of output rows
      for each parameter combination being tested.
    - Generally, only the strategy value metrics need to be shown for each parameter combination,
      as the benchmark results will not change unless the benchmark calculation itself is modified.
    - The benchmark row is included for reference and comparison purposes.
    
    Args:
        returns (Series): Strategy returns
        benchmark_returns (Series, optional): Benchmark returns
        strategy_params (dict, optional): Strategy parameters
        
    Returns:
        DataFrame: Performance table
    """
    # Create rows for strategy and benchmark
    rows = []
    
    # Define row types
    row_types = ['Benchmark', 'Strategy']
    
    # Get years for annual returns columns
    if returns is not None:
        start_year = returns.index[0].year
        end_year = returns.index[-1].year
    elif benchmark_returns is not None:
        start_year = benchmark_returns.index[0].year
        end_year = benchmark_returns.index[-1].year
    else:
        # If neither is available, use current year as fallback
        start_year = pd.Timestamp.now().year
        end_year = start_year
    
    years = list(range(start_year, end_year + 1))
    
    # Calculate current year for YTD
    current_year = pd.Timestamp.now().year
    
    # Define parameter columns (these will be on the left side of the table)
    if strategy_params:
        # Use all keys from strategy_params as parameter columns, but filter out any that aren't actual parameters
        param_cols = [k for k in strategy_params.keys() if not k.startswith('_') and k not in ['config', 'actual_execution_delay']]
        
        # Ensure critical parameters are always included and in the correct order
        standard_params = ['st_lookback', 'mt_lookback', 'lt_lookback', 'execution_delay', 'top_n', 'rebalance_frequency']
        for param in reversed(standard_params):  # Reverse to maintain order when inserting at position 0
            if param in param_cols:
                # Remove from current position and move to front
                param_cols.remove(param)
                param_cols.insert(0, param)
            elif param in strategy_params.get('strategy_params', {}):
                # If it's in a nested strategy_params dict, include it
                param_cols.insert(0, param)
    else:
        # Default parameters if none provided
        param_cols = ['st_lookback', 'mt_lookback', 'lt_lookback', 'execution_delay', 'top_n', 'rebalance_frequency']

    for row_type in row_types:
        if row_type == 'Strategy' and benchmark_only:
            continue
        
        # Use 'ema_strategy' as the name for strategy row, 'Benchmark Eq Wtg' for benchmark row
        row_data = {'Strategy': 'Benchmark Eq Wtg' if row_type == 'Benchmark' else 'ema_strategy'}
        # Add new Strategy Label column
        row_data['Strategy Label'] = '' if row_type == 'Benchmark' else 'ema'
        
        # Always add all parameter columns (dynamic for Strategy row)
        for param in param_cols:
            if strategy_params and row_type == 'Strategy':
                param_value = strategy_params.get(param, '')
                
                # Handle tuple parameter format (used for optimizable parameters)
                if isinstance(param_value, tuple) and len(param_value) == 5 and param_value[0] in ['Y', 'N']:
                    # For optimized parameters in performance table, we need the actual value used in this run,
                    # which should be in the params dictionary for this specific optimization run.
                    # The param value for optimized runs will be the actual value used, not the tuple
                    row_data[param] = param_value[1]  # Use default value as fallback
                    
                    # If parameter name is 'execution_delay' AND there are multiple parameter combinations 
                    # being shown, ensure we get the actual value used for this run
                    if param == 'execution_delay':
                        # Check if this is a param key whose value is not a tuple (actual value used in this run)
                        actual_param_val = strategy_params.get('actual_execution_delay') 
                        if actual_param_val is not None:
                            # Use the actual value that was used for this run
                            row_data[param] = actual_param_val
                else:
                    row_data[param] = param_value
            else:
                row_data[param] = ''

        
        # Get the appropriate returns series
        if row_type == 'Strategy':
            series = returns
        else:
            series = benchmark_returns if benchmark_returns is not None else pd.Series(0, index=returns.index if returns is not None else [])
            # DEBUG: Print/log benchmark_returns info
            print('DEBUG: benchmark_returns head:', series.head() if hasattr(series, 'head') else series)
            print('DEBUG: benchmark_returns describe:', series.describe() if hasattr(series, 'describe') else series)

        # DEBUG: Print/log calculated metrics for benchmark
        if row_type == 'Benchmark':
            print('DEBUG: Benchmark CAGR:', calculate_cagr(series))
            print('DEBUG: Benchmark Sharpe:', calculate_sharpe_ratio(series))
            print('DEBUG: Benchmark Sortino:', calculate_sortino_ratio(series))
            print('DEBUG: Benchmark Max Drawdown:', calculate_max_drawdown(series))
            print('DEBUG: Benchmark YTD:', get_ytd_returns(series))
            for year in reversed(years):
                print(f'DEBUG: Benchmark {year}:', get_calendar_year_returns(series, year))

        # Calculate performance metrics as raw numbers (no string formatting)
        row_data['CAGR'] = calculate_cagr(series)
        row_data['Sharpe'] = calculate_sharpe_ratio(series)
        row_data['Sortino'] = calculate_sortino_ratio(series)
        row_data['Max Drawdown'] = calculate_max_drawdown(series)
        
        # Calculate YTD return
        ytd_return = get_ytd_returns(series)
        row_data[f"YTD '{str(current_year)[2:]}"] = ytd_return if ytd_return is not None else 0
        
        # Calculate annual returns for each year
        for year in reversed(years):
            year_return = get_calendar_year_returns(series, year)
            row_data[str(year)] = year_return if year_return is not None else 0
        
        rows.append(row_data)
    
    # Create DataFrame
    performance_df = pd.DataFrame(rows)
    
    # Don't set any column as index to maintain the tabular structure
    # This ensures the Strategy column appears in the output
    
    # Reorder columns: Strategy first, then Strategy Label, then parameters, then metrics, then time periods
    # Always use the standard parameter list for ordering
    strategy_col = ['Strategy']
    additional_cols = ['Strategy Label']  # New column after Strategy
    
    # Get all parameter columns from the DataFrame (excluding metrics and year columns)
    all_param_cols = [col for col in performance_df.columns 
                     if col not in strategy_col + additional_cols + 
                        ['CAGR', 'Sharpe', 'Sortino', 'Max Drawdown'] + 
                        [str(y) for y in years] + 
                        [col for col in performance_df.columns if col.startswith('YTD')]]
    
    # Standard parameters to prioritize in order
    standard_params = ['st_lookback', 'mt_lookback', 'lt_lookback', 'execution_delay', 'top_n', 'rebalance_frequency']
    
    # Order parameter columns: first standard parameters in the specified order, then any other parameters
    param_cols = []
    for param in standard_params:
        if param in all_param_cols:
            param_cols.append(param)
            all_param_cols.remove(param)
    # Add any remaining parameter columns
    param_cols.extend(all_param_cols)
    
    # Metric columns should always be in a specific order
    metric_cols = ['CAGR', 'Sharpe', 'Sortino', 'Max Drawdown']
    metric_cols = [col for col in metric_cols if col in performance_df.columns]
    
    # Year and YTD columns
    ytd_col = [col for col in performance_df.columns if col.startswith('YTD')]
    year_cols = [str(year) for year in reversed(years) if str(year) in performance_df.columns]

    # --- CUSTOM LOGIC FOR CAGR COLUMN FILTERING (per user request) ---
    # Only show YTD for current year if not year-end; only show full-year if at year-end
    from datetime import date
    today = date.today()
    is_year_end = (today.month == 12 and today.day == 31)
    current_year_str = str(today.year)
    ytd_label = f"YTD {current_year_str}"
    ytd_col = [col for col in performance_df.columns if col.replace("'", "").replace(" ", "").startswith("YTD")]
    # Find the actual YTD column name (could be YTD '25 or YTD 2025, etc.)
    ytd_col_actual = [col for col in performance_df.columns if col.startswith("YTD")]
    # Remove current year from year_cols if not year-end; remove YTD if year-end
    if is_year_end:
        # Only show full-year column for current year, no YTD
        year_cols = [col for col in year_cols if col == current_year_str or col != current_year_str]
        ytd_col_actual = []
    else:
        # Only show YTD for current year, remove full-year column for current year
        year_cols = [col for col in year_cols if col != current_year_str]
    # If a YTD column exists, relabel it as 'YTD 2025' (or current year)
    if ytd_col_actual:
        performance_df = performance_df.rename(columns={ytd_col_actual[0]: ytd_label})
        ytd_col = [ytd_label]
    else:
        ytd_col = []
    # --- END CUSTOM LOGIC ---

    # Combine all columns in the correct order
    ordered_cols = strategy_col + additional_cols + param_cols + metric_cols + ytd_col + year_cols

    # Filter to only include columns that exist in the DataFrame
    ordered_cols = [col for col in ordered_cols if col in performance_df.columns]
    
    # Actually reorder the DataFrame columns
    performance_df = performance_df[ordered_cols]

    # Ensure column names are strings, not complex objects
    performance_df.columns = [str(col) for col in performance_df.columns]

    # DEBUG: Print DataFrame before returning
    print("DEBUG: performance_df before Excel output:\n", performance_df[ordered_cols])

    # Return the DataFrame with ordered columns (without reset_index)
    return performance_df[ordered_cols]

def generate_performance_report_local(data_dict, output_dir=None, filename_prefix=None):
    """
    Generate a performance report in Excel format.
    
    Args:
        data_dict (dict): Dictionary containing:
            - returns (Series): Strategy returns
            - weights_history (DataFrame): Asset weights over time
            - signal_history (DataFrame, optional): Signal history
            - benchmark_returns (Series, optional): Benchmark returns
            - strategy_params (dict, optional): Strategy parameters
            - old_report_path (str, optional): Path to old report to copy tabs from
        output_dir (str): Output directory
        filename_prefix (str): Prefix for the output filename
        
    Returns:
        str: Path to the generated report
    """
    if output_dir is None:
        output_dir = 'output'
    os.makedirs(output_dir, exist_ok=True)
    # Extract data
    returns = data_dict.get('returns')
    weights_history = data_dict.get('weights_history')
    signal_history = data_dict.get('signal_history')
    benchmark_returns = data_dict.get('benchmark_returns')
    portfolio_value = data_dict.get('portfolio_value')
    metrics = data_dict.get('metrics')
    if returns is None or returns.empty:
        logger.error("No returns data provided")
        return None
    # Build report filename with standardized naming convention
    # Use timestamp from data_dict if provided, otherwise generate new one
    timestamp = data_dict.get('timestamp', pd.Timestamp.now().strftime('%Y%m%d_%H%M%S'))
    report_file = f"{filename_prefix}_performance_tables_{timestamp}.xlsx" if filename_prefix else f"EMA_V3_1_performance_tables_{timestamp}.xlsx"
    output_path = os.path.join(output_dir, report_file)
    # Metrics
    strategy_metrics = calculate_performance_metrics(returns)
    benchmark_metrics = None
    if benchmark_returns is not None and not benchmark_returns.empty:
        benchmark_metrics = calculate_performance_metrics(benchmark_returns)
    # Performance table
    performance_data = create_performance_table(
        returns,
        benchmark_returns=benchmark_returns,
        strategy_params=data_dict.get('strategy_params', {})
    )
    # Write Performance Metrics sheet with formatting
    # Import the excel_write_with_formats function directly from the Custom Function Library
    import sys
    import importlib.util
    
    # Define the path to the excel_utils.py file in the Custom Function Library
    excel_utils_path = r"S:\Dropbox\Scott Only Internal\Quant_Python_24\Custom Function Library\data\excel_utils.py"
    
    # Import the module using importlib
    spec = importlib.util.spec_from_file_location("excel_utils", excel_utils_path)
    excel_utils = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(excel_utils)
    
    # Define formatting columns
    pct_cols = ['CAGR', 'Max Drawdown'] + [col for col in performance_data.columns if col.startswith('YTD')] + [col for col in performance_data.columns if col.isdigit()]
    dec_cols = ['Sharpe', 'Sortino']

    # Helper to convert column index to Excel letter (used by openpyxl for some operations if needed, though set_column uses index)
    def _col_idx_to_letter(idx):
        letters = ""
        idx_copy = idx # Work with a copy
        while idx_copy >= 0:
            letters = chr((idx_copy % 26) + 65) + letters
            idx_copy = idx_copy // 26 - 1
        return letters

    # Create/Open the Excel file with pd.ExcelWriter for all sheet operations
    with pd.ExcelWriter(output_path, engine='openpyxl', datetime_format='YYYY-MM-DD') as writer:
        # --- Write Performance Metrics sheet ---
        sheet_name_pm = 'Performance Metrics'
        params_written_to_a1_pm = False
        if 'strategy_params' in data_dict and data_dict['strategy_params']:
            params_str_list_pm = [
                f"{k}={v}" for k, v in data_dict['strategy_params'].items()
                if not isinstance(v, tuple) and k not in [
                    'config', 'actual_execution_delay', # Internal/runtime params
                    'tickers', 'start_date', 'end_date', 'price_field', # Data specification params
                    'risk_free_ticker', 'data_storage_mode', 'benchmark_ticker', # More data/config params
                    'strategy', 'ticker_group', # Often for naming or high-level grouping
                    'output_dir', 'strategy_name', 'filename_prefix', 'timestamp', # Reporting/output params
                    'old_report_path' # Util param
                ]
            ]
            if params_str_list_pm:
                params_str_pm = ", ".join(params_str_list_pm)
                # Create a single-cell DataFrame for the parameters string
                params_df_pm = pd.DataFrame([params_str_pm])
                # Write to A1 (row 0, col 0) without index or header for this temp DF
                params_df_pm.to_excel(writer, sheet_name=sheet_name_pm, startrow=0, startcol=0, index=False, header=False)
                params_written_to_a1_pm = True

        # Write the performance_data DataFrame, offsetting if parameters were written to A1
        performance_data_start_row = 1 if params_written_to_a1_pm else 0
        performance_data.to_excel(writer, sheet_name=sheet_name_pm, startrow=performance_data_start_row, index=False, header=True)

        # Apply formatting using openpyxl
        workbook_pm = writer.book
        worksheet_pm = writer.sheets[sheet_name_pm]

        # Format headers of performance_data (bold)
        # Headers are in `performance_data_start_row` (0-indexed for openpyxl from pandas `startrow`)
        header_format_pm = workbook_pm.add_format({'bold': True}) # In openpyxl, formats are added to workbook
        for col_num, value in enumerate(performance_data.columns.values):
            worksheet_pm.write(performance_data_start_row, col_num, value, header_format_pm)

        # Apply column widths and number formats for data rows of performance_data
        # Data rows start after the header row of performance_data
        data_body_start_row_excel = performance_data_start_row + 1 # Excel is 1-indexed for rows in cell operations

        for col_idx, col_name in enumerate(performance_data.columns):
            width = 20  # default width
            num_format_str = None # default number format

            if col_name in pct_cols:
                width = 15
                num_format_str = '0.00%'
            elif col_name in dec_cols:
                width = 12
                num_format_str = '0.0000'
            elif col_name == 'Strategy':
                width = 25
            elif col_name == 'Strategy Label':
                width = 15

            worksheet_pm.set_column(col_idx, col_idx, width) # set_column is 0-indexed for col_idx

            if num_format_str:
                cell_format = workbook_pm.add_format({'num_format': num_format_str})
                # Apply format to data cells in this column
                # worksheet.write(row, col, data, format)
                # Need to iterate through the rows of performance_data
                for i in range(len(performance_data)):
                    # actual_excel_row is 1-based, data_body_start_row_excel is 1-based start of data
                    actual_excel_row = data_body_start_row_excel + i
                    # col_idx is 0-based
                    # worksheet_pm.write() will overwrite existing value if any, but apply new format
                    # It's better to apply format to existing cells if possible, or write with format
                    # For simplicity, we assume pandas has written the value, and we are now styling
                    # However, openpyxl often requires writing the value again with the format,
                    # or getting the cell and setting its .number_format property.
                    # Let's try setting cell.number_format for existing cells.
                    cell = worksheet_pm.cell(row=actual_excel_row, column=col_idx + 1) # cell() is 1-indexed for row and col
                    cell.number_format = num_format_str
        
        logger.info(f"'Performance Metrics' sheet written to {output_path}")

        # --- Append other sheets using the same writer ---
        if portfolio_value is not None and not portfolio_value.empty:
            portfolio_value.to_excel(writer, sheet_name='Portfolio Value')
        if metrics is not None:
            if isinstance(metrics, dict) and metrics:
                # Convert dict to DataFrame for Excel output
                metrics_df = pd.DataFrame.from_dict(metrics, orient='index')
                metrics_df.columns = ['Value']
                metrics_df.to_excel(writer, sheet_name='Metrics')
            elif isinstance(metrics, pd.DataFrame) and not metrics.empty:
                metrics.to_excel(writer, sheet_name='Metrics')
        
        # Add Trade Log tab if trade_log data is available
        trade_log = data_dict.get('trade_log')
        if trade_log is not None and not (hasattr(trade_log, 'empty') and trade_log.empty):
            logger.info(f"Adding Trade Log tab with {len(trade_log)} trades")
            
            # Ensure we have all required columns
            required_columns = ['trade_num', 'symbol', 'quantity', 'execution_date', 'execution_price', 
                               'commission+slippage', 'amount', 'pnl']
            
            # Check if we need to rename or add columns
            trade_log_df = trade_log.copy()
            
            # Rename columns if needed
            column_mapping = {
                'trade_id': 'trade_num',
                'ticker': 'symbol',
                'shares': 'quantity',
                'date': 'execution_date',
                'price': 'execution_price',
                'commission': 'commission+slippage',
                'value': 'amount'
            }
            
            # Apply column renaming where needed
            for old_col, new_col in column_mapping.items():
                if old_col in trade_log_df.columns and new_col not in trade_log_df.columns:
                    trade_log_df.rename(columns={old_col: new_col}, inplace=True)
            
            # If commission and slippage are separate, combine them
            if 'commission' in trade_log_df.columns and 'slippage' in trade_log_df.columns and 'commission+slippage' not in trade_log_df.columns:
                trade_log_df['commission+slippage'] = trade_log_df['commission'] + trade_log_df['slippage']
            
            # Check for missing required columns
            missing_columns = [col for col in required_columns if col not in trade_log_df.columns]
            if missing_columns:
                logger.warning(f"Trade Log missing required columns: {missing_columns}")
                # Add missing columns with default values
                for col in missing_columns:
                    if col == 'trade_num':
                        trade_log_df['trade_num'] = range(1, len(trade_log_df) + 1)
                    else:
                        trade_log_df[col] = 0.0
            
            # Ensure proper column order
            trade_log_df = trade_log_df[required_columns + [col for col in trade_log_df.columns if col not in required_columns]]
            
            # Add parameter settings as header in cell A1 for 'Trade Log'
            params_written_to_a1_tl = False
            if 'strategy_params' in data_dict and data_dict['strategy_params']:
                params_str_list_tl = [
                    f"{k}={v}" for k, v in data_dict['strategy_params'].items()
                    if not isinstance(v, tuple) and k not in [
                        'config', 'actual_execution_delay', # Internal/runtime params
                        'tickers', 'start_date', 'end_date', 'price_field', # Data specification params
                        'risk_free_ticker', 'data_storage_mode', 'benchmark_ticker', # More data/config params
                        'strategy', 'ticker_group', # Often for naming or high-level grouping
                        'output_dir', 'strategy_name', 'filename_prefix', 'timestamp', # Reporting/output params
                        'old_report_path' # Util param
                    ]
                ]
                if params_str_list_tl:
                    params_str_tl = ", ".join(params_str_list_tl)
                    params_df_tl = pd.DataFrame([params_str_tl])
                    # Write to A1 (row 0, col 0) without index or header
                    params_df_tl.to_excel(writer, sheet_name='Trade Log', startrow=0, startcol=0, index=False, header=False)
                    params_written_to_a1_tl = True
            
            # Write the trade_log_df, offsetting if parameters were written
            trade_log_start_row = 1 if params_written_to_a1_tl else 0
            # trade_log_df already has its own headers, so they will go into `trade_log_start_row`
            trade_log_df.to_excel(writer, sheet_name='Trade Log', startrow=trade_log_start_row, index=False, header=True)

            # Apply formatting to the Trade Log sheet (headers bold, specific column formats)
            workbook_tl = writer.book # writer.book is the same as workbook_pm
            worksheet_tl = writer.sheets['Trade Log']

            # Format headers of trade_log_df (bold)
            header_format_tl = workbook_tl.add_format({'bold': True})
            for col_num, value in enumerate(trade_log_df.columns.values):
                worksheet_tl.write(trade_log_start_row, col_num, value, header_format_tl)

            # Format specific columns in Trade Log
            # Data rows start after the header row of trade_log_df
            trade_log_data_body_start_row_excel = trade_log_start_row + 1

            price_col_idx = trade_log_df.columns.get_loc('execution_price')
            money_col_indices = [trade_log_df.columns.get_loc(col) for col in ['commission+slippage', 'amount', 'pnl'] if col in trade_log_df.columns]

            price_format = workbook_tl.add_format({'num_format': '0.000'})
            money_format = workbook_tl.add_format({'num_format': '0.00'})

            worksheet_tl.set_column(price_col_idx, price_col_idx, 15) # Width for price
            for col_idx in money_col_indices:
                worksheet_tl.set_column(col_idx, col_idx, 15) # Width for money columns

            for i in range(len(trade_log_df)):
                actual_excel_row = trade_log_data_body_start_row_excel + i
                
                # Apply price format
                cell_price = worksheet_tl.cell(row=actual_excel_row, column=price_col_idx + 1)
                cell_price.number_format = '0.000'
                
                # Apply money format
                for mc_idx in money_col_indices:
                    cell_money = worksheet_tl.cell(row=actual_excel_row, column=mc_idx + 1)
                    cell_money.number_format = '0.00'
            
            # Apply formatting to the Trade Log sheet
            workbook = writer.book
            worksheet = writer.sheets['Trade Log']
            
            # Note: Original formatting for specific columns like execution_price, commission+slippage, amount, pnl
            # is now handled above by iterating rows and applying cell.number_format.
            # The set_column calls for width are also done above.
            
            logger.info("Trade Log tab added successfully")
        old = data_dict.get('old_report_path')
        if old and os.path.exists(old):
            try:
                old_xl = pd.ExcelFile(old)
                for sheet in old_xl.sheet_names:
                    df = pd.read_excel(old_xl, sheet_name=sheet)
                    df.to_excel(writer, sheet_name=sheet, index=False if not df.index.name else True)
                logger.info(f"Copied {len(old_xl.sheet_names)} tabs from old report")
            except Exception as e:
                logger.error(f"Error copying tabs from old report: {e}")
                
        # Add allocation history tab
        if weights_history is not None and not weights_history.empty and 'Allocation History' not in writer.sheets:
            # Create allocation history with proper date formatting
            allocation_history = weights_history.copy()
            # Reset index to get Date as a column
            allocation_history = allocation_history.reset_index().rename(columns={'index': 'Date'})
            # Write to Excel
            allocation_history.to_excel(writer, sheet_name='Allocation History', index=False)
            logger.info(f"Added Allocation History tab with {len(allocation_history)} rows")
        
        # Add signal history tab
        if signal_history is not None and not signal_history.empty and 'Signal History' not in writer.sheets:
            # Create signal history with proper date formatting
            signal_history_df = signal_history.copy()
            # Reset index to get Date as a column
            signal_history_df = signal_history_df.reset_index().rename(columns={'index': 'Date'})
            # Write to Excel
            signal_history_df.to_excel(writer, sheet_name='Signal History', index=False)
            logger.info(f"Added Signal History tab with {len(signal_history_df)} rows")
    logger.info(f"Performance report generated: {output_path}")
    return output_path
