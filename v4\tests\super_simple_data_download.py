#!/usr/bin/env python
# super_simple_data_download.py
"""
Super simple data downloader: loads CPS v4 settings, downloads price data,
prints shape and head, and saves a sample to CSV.
"""
import sys
from pathlib import Path

# 1) Add project root to sys.path
project_root = Path(__file__).resolve().parent.parent.parent
sys.path.insert(0, str(project_root))

# 2) Import settings and data fetch
from market_data import data_fetch_stock_data
from utils.date_utils import (
    standardize_date, standardize_date_range,
    standardize_dataframe_index, filter_dataframe_by_dates
)
# 2b) Remove import load_data_for_backtest

from v4.settings.settings_CPS_v4 import load_settings
import pandas as pd

# 3) Load settings and extract data params
settings = load_settings()
# Use core settings for data
data_core = settings.get('core', {})
tickers = data_core.get('tickers', {}).get('default', [])
start_date = data_core.get('start_date')
end_date = data_core.get('end_date')

# 4) Fetch data directly
print(f"Downloading data for: {tickers} from {start_date} to {end_date}")
# fetch data
data = {}
start_ts, end_ts = standardize_date_range(start_date, end_date)
for ticker in tickers:
    df = data_fetch_stock_data(ticker=ticker, period="max", interval="1d")
    if hasattr(df.index, 'tz') and df.index.tz is not None:
        df.index = df.index.tz_localize(None)
    df = standardize_dataframe_index(df)
    df = filter_dataframe_by_dates(df, start_ts, end_ts)
    # pick a price column
    if 'Adj Close' in df.columns:
        data[ticker] = df['Adj Close']
    elif 'Close' in df.columns:
        data[ticker] = df['Close']

import pandas as pd
price_data = pd.DataFrame(data)

# 4) Download the data
data_dict = load_data_for_backtest()
price_data = data_dict.get('price_data')

# 5) Print and verify
print(f"Data shape: {price_data.shape}")
print(price_data.head())

# 6) Save head to CSV
# Created by simplified fetch, saving head

out_csv = Path(__file__).resolve().parent / "price_data_head.csv"
price_data.head().to_csv(out_csv)
print(f"Wrote sample to {out_csv}")
