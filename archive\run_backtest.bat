@echo off
echo Running EMA Backtest with v2 Engine and Performance Metrics...
echo.

:: Check for debug flag
set DEBUG_FLAG=
if "%1"=="debug" (
    set DEBUG_FLAG=--debug
    echo Debug mode enabled. Full output will be logged.
) else (
    echo Normal mode. Only warnings and errors will be shown.
    echo To enable debug mode, run: %~nx0 debug
)

:: Activate the Python environment
call F:\AI_Library\my_quant_env\Scripts\activate.bat

:: Set Python path to include the Custom Function Library
set PYTHONPATH=%PYTHONPATH%;S:\Dropbox\Scott Only Internal\Quant_Python_24\Custom Function Library

:: Run the backtest script with performance metrics
python run_backtest_v2_with_metrics.py %DEBUG_FLAG%

:: Pause to see the output
pause
