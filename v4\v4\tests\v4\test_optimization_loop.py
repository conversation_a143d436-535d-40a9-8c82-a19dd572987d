#!/usr/bin/env python
# -*- coding: utf-8 -*-
# test_optimization_loop.py
"""
Test to isolate optimization loop parameter passing issues.
This test feeds real historic data through the calc functions with different parameter combinations.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

import pandas as pd
import numpy as np
from test_param_combinations import get_all_combinations, get_param_combination

def test_single_combination_with_real_data(price_data, combo):
    """
    Test a single parameter combination with real data.
    This isolates whether the issue is in parameter passing or calculation logic.
    """
    print(f"\n=== Testing combination: {combo['name']} ===")
    print(f"Parameters: {combo}")
    
    # Import the allocation model
    from v4.models.ema_allocation_model_v4 import ema_allocation_model_updated
    
    # Create parameters dict exactly as optimization loop would
    params = {
        'st_lookback': combo['st_lookback'],
        'mt_lookback': combo['mt_lookback'], 
        'lt_lookback': combo['lt_lookback'],
        'system_top_n': combo['system_top_n']
    }
    
    print(f"Input price_data shape: {price_data.shape}")
    print(f"Input price_data sample:\n{price_data.head(2)}")
    print(f"Input price_data has NaN: {price_data.isna().any().any()}")
    
    try:
        # Call the model exactly as optimization loop would
        result = ema_allocation_model_updated(
            price_data=price_data,
            trace_mode=True,
            **params
        )
        
        if isinstance(result, tuple):
            weights_dict, ratios, ranks, signals, short_ema, med_ema, long_ema, stmtemax, mtltemax, emaxavg = result
            
            print(f"SUCCESS: Got result tuple")
            print(f"Weights dict: {weights_dict}")
            print(f"Short EMA last value sample: {short_ema.iloc[-1].head(2)}")
            print(f"Med EMA last value sample: {med_ema.iloc[-1].head(2)}")  
            print(f"Long EMA last value sample: {long_ema.iloc[-1].head(2)}")
            print(f"EMAXAvg last value sample: {emaxavg.iloc[-1].head(2)}")
            
            # Check for all zeros (the main issue)
            if all(w == 0 for date_weights in weights_dict.values() for w in date_weights.values()):
                print("ERROR: All weights are zero!")
                return False
            else:
                print("SUCCESS: Non-zero weights found")
                return True
                
        else:
            print(f"Result type: {type(result)}")
            print(f"Result: {result}")
            return False
            
    except Exception as e:
        print(f"ERROR in combination {combo['name']}: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_test_price_data():
    """Create realistic test price data."""
    dates = pd.date_range('2023-01-01', periods=250, freq='D')  # 1 year of daily data
    symbols = ['SPY', 'QQQ', 'EFA', 'EEM', 'VTI']
    
    # Create realistic price movements
    np.random.seed(42)  # For reproducible results
    returns = np.random.normal(0.0008, 0.02, (len(dates), len(symbols)))  # Daily returns ~20% annual vol
    price_data = pd.DataFrame(
        100 * np.exp(returns.cumsum(axis=0)),  # Cumulative returns to prices
        index=dates,
        columns=symbols
    )
    
    return price_data

def run_optimization_loop_test():
    """Run the full optimization loop test."""
    print("=== OPTIMIZATION LOOP DIAGNOSTIC TEST ===")
    
    # Create test data
    price_data = create_test_price_data()
    print(f"Created test price data: {price_data.shape}")
    print(f"Date range: {price_data.index[0]} to {price_data.index[-1]}")
    print(f"Symbols: {list(price_data.columns)}")
    
    # Test each combination
    combinations = get_all_combinations()
    results = {}
    
    for i, combo in enumerate(combinations):
        success = test_single_combination_with_real_data(price_data, combo)
        results[combo['name']] = success
    
    # Summary
    print(f"\n=== TEST SUMMARY ===")
    print(f"Total combinations tested: {len(combinations)}")
    successful = sum(results.values())
    print(f"Successful: {successful}")
    print(f"Failed: {len(combinations) - successful}")
    
    for name, success in results.items():
        status = "PASS" if success else "FAIL"
        print(f"{name}: {status}")
    
    return results

if __name__ == '__main__':
    results = run_optimization_loop_test()
