"""
Grid search implementation for parameter optimization.
"""

import pandas as pd
import numpy as np
import itertools
import logging

logger = logging.getLogger(__name__)

def run_grid_search(objective_func, param_grid, evaluation_metric='sharpe_ratio', maximize=True):
    """
    Run grid search over parameter space.
    
    Args:
        objective_func (callable): Function to evaluate with parameters
        param_grid (dict): Dictionary of parameter names and values to search
        evaluation_metric (str): Metric to optimize
        maximize (bool): Whether to maximize or minimize the metric
        
    Returns:
        tuple: Best parameters and all results
    """
    logger.info(f"Running grid search with {len(param_grid)} parameters")
    
    # Generate all parameter combinations
    param_names = list(param_grid.keys())
    param_values = list(param_grid.values())
    param_combinations = list(itertools.product(*param_values))
    
    results = []
    best_score = -np.inf if maximize else np.inf
    best_params = None
    
    # Evaluate each parameter combination
    for i, values in enumerate(param_combinations):
        params = dict(zip(param_names, values))
        
        try:
            # Run objective function with these parameters
            result = objective_func(**params)
            
            # Extract score from result
            if isinstance(result, dict) and evaluation_metric in result:
                score = result[evaluation_metric]
            elif isinstance(result, (int, float)):
                score = result
            else:
                logger.warning(f"Could not extract {evaluation_metric} from result")
                continue
            
            # Store result
            params_result = {**params, evaluation_metric: score}
            results.append(params_result)
            
            # Update best parameters if better
            if (maximize and score > best_score) or (not maximize and score < best_score):
                best_score = score
                best_params = params
                
            logger.info(f"Evaluated {i+1}/{len(param_combinations)}: {params}, score: {score:.4f}")
            
        except Exception as e:
            logger.error(f"Error evaluating parameters {params}: {e}")
    
    # Convert results to DataFrame
    results_df = pd.DataFrame(results)
    
    # Sort by evaluation metric
    if not results_df.empty:
        results_df = results_df.sort_values(by=evaluation_metric, ascending=not maximize)
    
    return best_params, results_df
