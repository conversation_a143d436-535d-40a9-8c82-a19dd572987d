#!/usr/bin/env python
# verify_signal_history_reporting.py
"""
Verification Script for Signal History Tracking and Report Formatting

This script tests the enhanced signal history tracking and report formatting
functionality to ensure it meets the requirements specified in Phase 3 of the
refactoring plan.

It performs the following tests:
1. Signal history validation and recovery
2. Report formatting according to PRD standards
3. Configuration integration with signal history and reporting

FileName: verify_signal_history_reporting.py
"""

import os
import sys
import logging
import pandas as pd
import numpy as np
import datetime
import traceback
from pathlib import Path

# Add parent directory to path to import modules
sys.path.insert(0, str(Path(__file__).parent.parent))

# Import with error handling
try:
    from v3_engine.config import V3Config, StrategyConfig, SystemConfig, ReportConfig, VisualizationConfig
    from v3_engine.signal_history_tracker import validate_signal_history, ensure_signal_history, recover_signal_history
    from v3_reporting.report_formatter import format_excel_report, create_parameter_header
    from v3_engine.config_integration import (
        apply_config_to_backtest, 
        apply_config_to_reporting, 
        ensure_signal_history_from_config,
        format_reports_from_config
    )
    from tests.verification_helpers import generate_mock_backtest_results
    IMPORTS_SUCCESSFUL = True
except ImportError as e:
    print(f"Warning: Could not import all required modules: {e}")
    print("Some tests may be skipped.")
    traceback.print_exc()
    IMPORTS_SUCCESSFUL = False

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('verify_signal_history_reporting.log')
    ]
)
logger = logging.getLogger(__name__)


def test_signal_history_validation():
    """Test signal history validation functionality."""
    logger.info("Testing signal history validation...")
    
    # Create test output directory
    output_dir = Path("test_output")
    output_dir.mkdir(exist_ok=True)
    
    # Generate mock backtest results
    backtest_results = generate_mock_backtest_results(str(output_dir))
    
    # Get signal history
    signal_history = backtest_results.get('signal_history')
    
    # Test validation
    is_valid, issues = validate_signal_history(signal_history)
    logger.info(f"Signal history valid: {is_valid}")
    if not is_valid:
        logger.info(f"Validation issues: {issues}")
    
    # Test with invalid signal history
    logger.info("Testing with invalid signal history...")
    
    # Case 1: None signal history
    is_valid, issues = validate_signal_history(None)
    logger.info(f"None signal history valid: {is_valid}")
    logger.info(f"Validation issues: {issues}")
    
    # Case 2: Empty DataFrame
    is_valid, issues = validate_signal_history(pd.DataFrame())
    logger.info(f"Empty DataFrame valid: {is_valid}")
    logger.info(f"Validation issues: {issues}")
    
    # Case 3: Non-normalized rows
    modified_history = signal_history.copy() * 2  # Multiply by 2 to make rows sum to 2.0
    is_valid, issues = validate_signal_history(modified_history)
    logger.info(f"Non-normalized rows valid: {is_valid}")
    logger.info(f"Validation issues: {issues}")
    
    # Test ensure_signal_history
    logger.info("Testing ensure_signal_history...")
    
    # Case 1: Valid signal history
    updated_results = ensure_signal_history({'signal_history': signal_history})
    is_valid, issues = validate_signal_history(updated_results['signal_history'])
    logger.info(f"After ensure_signal_history with valid data: {is_valid}")
    
    # Case 2: Invalid signal history (non-normalized)
    updated_results = ensure_signal_history({'signal_history': modified_history})
    is_valid, issues = validate_signal_history(updated_results['signal_history'])
    logger.info(f"After ensure_signal_history with non-normalized data: {is_valid}")
    
    # Case 3: Missing signal history
    updated_results = ensure_signal_history({'allocation_history': signal_history})
    if 'signal_history' in updated_results:
        is_valid, issues = validate_signal_history(updated_results['signal_history'])
        logger.info(f"After ensure_signal_history with missing signal history: {is_valid}")
    else:
        logger.info("Failed to recover signal history")
    
    logger.info("Signal history validation tests completed")
    return True


def test_report_formatting():
    """Test report formatting functionality."""
    logger.info("Testing report formatting...")
    
    # Create test output directory
    output_dir = Path("test_output")
    output_dir.mkdir(exist_ok=True)
    
    # Generate mock backtest results
    backtest_results = generate_mock_backtest_results(str(output_dir))
    
    # Create test Excel file
    excel_path = output_dir / "test_report.xlsx"
    
    # Create a simple DataFrame for testing
    df = pd.DataFrame({
        'Date': pd.date_range(start='2023-01-01', periods=10),
        'Asset1': np.random.rand(10) * 0.5,
        'Asset2': np.random.rand(10) * 0.5
    })
    df.set_index('Date', inplace=True)
    
    # Normalize rows to sum to 1.0
    df = df.div(df.sum(axis=1), axis=0)
    
    # Create Excel file with multiple sheets
    with pd.ExcelWriter(excel_path) as writer:
        df.to_excel(writer, sheet_name='Signal History')
        df.to_excel(writer, sheet_name='Allocation History')
        
        # Create performance sheet
        perf_df = pd.DataFrame({
            'Metric': ['Total Return', 'Annualized Return', 'Volatility', 'Sharpe Ratio'],
            'Value': [0.15, 0.08, 0.12, 0.67]
        })
        perf_df.to_excel(writer, sheet_name='Performance', index=False)
    
    # Create config dictionary
    config = {
        'strategy': {
            'strategy_name': 'Test Strategy',
            'lookback_period': 20,
            'rebalance_freq': 'monthly'
        },
        'system': {
            'start_date': '2023-01-01',
            'end_date': '2023-12-31',
            'initial_capital': 100000
        }
    }
    
    # Test format_excel_report
    success = format_excel_report(str(excel_path), config)
    logger.info(f"Format Excel report success: {success}")
    
    # Test create_parameter_header
    header_df = create_parameter_header(config)
    logger.info(f"Parameter header created with shape: {header_df.shape}")
    
    logger.info("Report formatting tests completed")
    return True


def test_config_integration():
    """Test configuration integration with signal history and reporting."""
    logger.info("Testing configuration integration...")
    
    # Create test output directory
    output_dir = Path("test_output")
    output_dir.mkdir(exist_ok=True)
    
    # Create test configuration
    strategy_config = StrategyConfig(
        strategy_name="Test Strategy",
        lookback_period=20,
        rebalance_freq="monthly"
    )
    
    system_config = SystemConfig(
        start_date="2023-01-01",
        end_date="2023-12-31",
        initial_capital=100000
    )
    
    report_config = ReportConfig(
        output_dir=str(output_dir),
        include_trade_log=True,
        include_signal_history=True,
        signal_history_resample_freq="W-FRI"
    )
    
    viz_config = VisualizationConfig(
        chart_style="seaborn",
        show_drawdowns=True
    )
    
    config = V3Config(
        strategy=strategy_config,
        system=system_config,
        report=report_config,
        visualization=viz_config
    )
    
    # Generate mock backtest results
    backtest_results = generate_mock_backtest_results(str(output_dir))
    
    # Test apply_config_to_backtest
    backtest_params = {}
    updated_params = apply_config_to_backtest(config, backtest_params)
    logger.info(f"Backtest params after applying config: {updated_params}")
    
    # Test apply_config_to_reporting
    report_params = {}
    updated_params = apply_config_to_reporting(config, report_params)
    logger.info(f"Report params after applying config: {updated_params}")
    
    # Test ensure_signal_history_from_config
    updated_results = ensure_signal_history_from_config(backtest_results, config)
    if 'signal_history' in updated_results:
        is_valid, issues = validate_signal_history(updated_results['signal_history'])
        logger.info(f"Signal history valid after config integration: {is_valid}")
    
    # Test format_reports_from_config
    report_paths = {
        'allocation': str(output_dir / "allocation_report.xlsx"),
        'performance': str(output_dir / "performance_report.xlsx")
    }
    
    # Create test Excel files
    for report_type, path in report_paths.items():
        df = pd.DataFrame({
            'Date': pd.date_range(start='2023-01-01', periods=10),
            'Asset1': np.random.rand(10) * 0.5,
            'Asset2': np.random.rand(10) * 0.5
        })
        df.set_index('Date', inplace=True)
        df = df.div(df.sum(axis=1), axis=0)
        df.to_excel(path)
    
    success = format_reports_from_config(report_paths, config)
    logger.info(f"Format reports from config success: {success}")
    
    logger.info("Configuration integration tests completed")
    return True


def run_all_tests():
    """Run all verification tests."""
    if not IMPORTS_SUCCESSFUL:
        logger.error("Skipping tests due to import errors")
        return False
        
    logger.info("Starting verification tests...")
    
    tests = [
        test_signal_history_validation,
        test_report_formatting,
        test_config_integration
    ]
    
    results = []
    for test in tests:
        try:
            logger.info(f"\n{'='*50}")
            logger.info(f"Running test: {test.__name__}")
            logger.info(f"{'='*50}")
            success = test()
            results.append((test.__name__, success))
            logger.info(f"Test {test.__name__} completed: {'PASSED' if success else 'FAILED'}")
        except Exception as e:
            logger.error(f"Error in {test.__name__}: {e}")
            logger.error(traceback.format_exc())
            results.append((test.__name__, False))
    
    # Print summary
    logger.info("\n" + "="*50)
    logger.info("TEST RESULTS SUMMARY")
    logger.info("="*50)
    
    all_passed = True
    for test_name, success in results:
        status = "PASSED" if success else "FAILED"
        logger.info(f"{test_name}: {status}")
        if not success:
            all_passed = False
    
    if all_passed:
        logger.info("\nAll tests PASSED!")
    else:
        logger.info("\nSome tests FAILED!")
    
    return all_passed


if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
