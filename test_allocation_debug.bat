@echo off
REM test_allocation_debug.bat
REM Batch file to run allocation flow test with proper environment setup

echo === Allocation Flow Test ===
echo Testing the allocation data flow through the backtest system
echo Output will be saved to debug_allocations directory

REM Activate the virtual environment
call F:\AI_Library\my_quant_env\Scripts\activate.bat

REM Set environment variables
set EXPORT_ALLOCATIONS=true
set BACKTEST_LOG_LEVEL=INFO

REM Run the test script
python test_allocation_flow.py

REM Open the debug_allocations directory
explorer debug_allocations

echo === Test Complete ===
echo Check the debug_allocations directory for detailed output files
echo Press any key to exit...
pause > nul
