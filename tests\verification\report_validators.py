#!/usr/bin/env python
# report_validators.py
"""
Report Validation Utilities
This module contains validation functions for checking report compliance with PRD standards.
"""

import os
import logging
import pandas as pd
import numpy as np
from PIL import Image
from pathlib import Path

logger = logging.getLogger('report_validators')

# Report standards from PRD
REPORT_STANDARDS = {
    'performance': {
        'excel_tabs': ['Summary', 'Performance', 'Trades', 'Parameters'],
        'charts': ['equity_curve', 'drawdown', 'monthly_returns'],
        'required_headers': ['Strategy Name', 'Lookback Period', 'Execution Delay', 'Top N'],
        'chart_dpi': 600,
        'column_formats': {
            'Performance': {
                'percentage_cols': ['Return', 'Volatility', 'Max Drawdown', 'Win Rate'],
                'ratio_cols': ['Sharpe Ratio', 'Sortino Ratio', 'Calmar Ratio'],
                'currency_cols': ['Initial Capital', 'Final Capital', 'Profit/Loss']
            }
        }
    },
    'allocation': {
        'excel_tabs': ['Allocations', 'History'],
        'charts': ['allocation_pie', 'allocation_history'],
        'required_headers': ['Rebalance Date', 'Strategy Name'],
        'chart_dpi': 600,
        'column_formats': {
            'Allocations': {
                'percentage_cols': ['Weight'],
                'currency_cols': ['Amount']
            },
            'History': {
                'percentage_cols': ['all_numeric']
            }
        }
    },
    'signal_history': {
        'excel_tabs': ['Signal History', 'Signal Stats'],
        'charts': ['signal_history', 'signal_distribution'],
        'required_headers': ['Strategy Name', 'Signal Period'],
        'chart_dpi': 600,
        'column_formats': {
            'Signal History': {
                'percentage_cols': ['all_numeric']
            },
            'Signal Stats': {
                'percentage_cols': ['Mean', 'Std Dev', 'Min', 'Max']
            }
        }
    }
}

def validate_excel_file_exists(file_path):
    """Check if Excel file exists and has minimum size"""
    if not os.path.exists(file_path):
        logger.error(f"Excel file not found: {file_path}")
        return False
    
    file_size = os.path.getsize(file_path)
    if file_size < 10000:  # Minimum size check (10KB)
        logger.warning(f"Excel file may be incomplete: {file_path} ({file_size} bytes)")
        return False
    
    logger.info(f"Excel file exists and has adequate size: {file_path} ({file_size} bytes)")
    return True

def validate_excel_tabs(excel_file, report_type):
    """Validate that Excel file contains all required tabs"""
    required_tabs = REPORT_STANDARDS[report_type]['excel_tabs']
    
    try:
        excel = pd.ExcelFile(excel_file)
        sheet_names = excel.sheet_names
        
        missing_tabs = [tab for tab in required_tabs if tab not in sheet_names]
        if missing_tabs:
            logger.error(f"Missing required tabs: {missing_tabs}")
            return False
        
        logger.info(f"All required tabs present: {required_tabs}")
        return True
    except Exception as e:
        logger.error(f"Error validating Excel tabs: {e}")
        return False

def validate_excel_headers(excel_file, report_type):
    """Validate that Excel file contains required headers"""
    required_headers = REPORT_STANDARDS[report_type]['required_headers']
    
    try:
        # Check first tab for headers (usually in A1 or nearby cells)
        excel = pd.ExcelFile(excel_file)
        first_sheet = excel.sheet_names[0]
        df = pd.read_excel(excel, sheet_name=first_sheet, nrows=10)
        
        # Check if headers are in the first few rows
        header_found = False
        for header in required_headers:
            header_found = False
            for i in range(min(10, len(df))):
                for col in df.columns:
                    if isinstance(df.iloc[i][col], str) and header in df.iloc[i][col]:
                        header_found = True
                        break
                if header_found:
                    break
            
            if not header_found:
                logger.error(f"Required header not found: {header}")
                return False
        
        logger.info(f"All required headers present: {required_headers}")
        return True
    except Exception as e:
        logger.error(f"Error validating Excel headers: {e}")
        return False

def validate_excel_formatting(excel_file, report_type):
    """Validate Excel formatting according to standards"""
    try:
        excel = pd.ExcelFile(excel_file)
        column_formats = REPORT_STANDARDS[report_type]['column_formats']
        
        for sheet_name, format_specs in column_formats.items():
            if sheet_name not in excel.sheet_names:
                logger.warning(f"Sheet {sheet_name} not found, skipping format validation")
                continue
            
            df = pd.read_excel(excel, sheet_name=sheet_name)
            
            # Check percentage formatting
            if 'percentage_cols' in format_specs:
                if 'all_numeric' in format_specs['percentage_cols']:
                    # All numeric columns should be percentages
                    numeric_cols = df.select_dtypes(include=[np.number]).columns
                    for col in numeric_cols:
                        # Check if values are in percentage range (0-1 or 0-100)
                        if df[col].max() <= 1.0:
                            logger.info(f"Column {col} appears to use decimal percentages (0-1)")
                        elif df[col].max() <= 100.0:
                            logger.info(f"Column {col} appears to use percentage values (0-100)")
                        else:
                            logger.warning(f"Column {col} may not be formatted as percentage")
                else:
                    # Check specific percentage columns
                    for col in format_specs['percentage_cols']:
                        if col not in df.columns:
                            logger.warning(f"Percentage column {col} not found in {sheet_name}")
                            continue
                        
                        # Check if values are in percentage range
                        if df[col].max() <= 1.0:
                            logger.info(f"Column {col} appears to use decimal percentages (0-1)")
                        elif df[col].max() <= 100.0:
                            logger.info(f"Column {col} appears to use percentage values (0-100)")
                        else:
                            logger.warning(f"Column {col} may not be formatted as percentage")
        
        logger.info(f"Excel formatting validation completed for {report_type}")
        return True
    except Exception as e:
        logger.error(f"Error validating Excel formatting: {e}")
        return False

def validate_chart_exists(chart_path):
    """Check if chart file exists and has minimum size"""
    if not os.path.exists(chart_path):
        logger.error(f"Chart file not found: {chart_path}")
        return False
    
    file_size = os.path.getsize(chart_path)
    if file_size < 5000:  # Minimum size check (5KB)
        logger.warning(f"Chart file may be incomplete: {chart_path} ({file_size} bytes)")
        return False
    
    logger.info(f"Chart file exists and has adequate size: {chart_path} ({file_size} bytes)")
    return True

def validate_chart_resolution(chart_path, report_type):
    """Validate chart resolution meets DPI requirements"""
    required_dpi = REPORT_STANDARDS[report_type]['chart_dpi']
    
    try:
        with Image.open(chart_path) as img:
            width, height = img.size
            
            # Check minimum dimensions (rough estimate of DPI)
            if width < 800 or height < 600:
                logger.warning(f"Chart resolution may be too low: {width}x{height}")
                return False
            
            # For PNG files, we can check DPI directly
            if chart_path.lower().endswith('.png') and 'dpi' in img.info:
                dpi = img.info['dpi']
                if dpi[0] < required_dpi or dpi[1] < required_dpi:
                    logger.warning(f"Chart DPI below requirement: {dpi} (required: {required_dpi})")
                    return False
                logger.info(f"Chart meets DPI requirement: {dpi}")
            else:
                # Estimate based on dimensions
                logger.info(f"Chart dimensions: {width}x{height} (DPI information not available)")
            
            return True
    except Exception as e:
        logger.error(f"Error validating chart resolution: {e}")
        return False

def validate_signal_history_sums(excel_file):
    """Validate that Signal History rows sum to 100%"""
    try:
        excel = pd.ExcelFile(excel_file)
        if 'Signal History' not in excel.sheet_names:
            logger.warning("Signal History tab not found")
            return False
        
        df = pd.read_excel(excel, sheet_name='Signal History')
        
        # Find the first date column
        date_col = None
        for col in df.columns:
            if 'date' in str(col).lower():
                date_col = col
                break
        
        if date_col is None:
            logger.warning("Date column not found in Signal History")
            return False
        
        # Get numeric columns (excluding date)
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        
        # Check row sums (should be close to 1.0 or 100%)
        row_sums = df[numeric_cols].sum(axis=1)
        
        # Check if values are in 0-1 range or 0-100 range
        if row_sums.mean() > 10:  # Likely 0-100 range
            tolerance = 1.0  # 1% tolerance
            expected_sum = 100.0
        else:  # Likely 0-1 range
            tolerance = 0.01  # 1% tolerance
            expected_sum = 1.0
        
        # Check if all rows sum close to expected value
        valid_sums = ((row_sums >= expected_sum - tolerance) & 
                      (row_sums <= expected_sum + tolerance))
        
        if not valid_sums.all():
            invalid_rows = (~valid_sums).sum()
            logger.warning(f"{invalid_rows} rows in Signal History do not sum to {expected_sum}%")
            return False
        
        logger.info(f"All Signal History rows sum to {expected_sum}% (±{tolerance}%)")
        return True
    except Exception as e:
        logger.error(f"Error validating Signal History sums: {e}")
        return False

def validate_allocation_history_sums(excel_file):
    """Validate that Allocation History rows sum to 100%"""
    try:
        excel = pd.ExcelFile(excel_file)
        if 'History' not in excel.sheet_names and 'Allocation History' not in excel.sheet_names:
            logger.warning("Allocation History tab not found")
            return False
        
        # Find the correct sheet name
        sheet_name = 'Allocation History' if 'Allocation History' in excel.sheet_names else 'History'
        
        df = pd.read_excel(excel, sheet_name=sheet_name)
        
        # Find the first date column
        date_col = None
        for col in df.columns:
            if 'date' in str(col).lower():
                date_col = col
                break
        
        if date_col is None:
            logger.warning("Date column not found in Allocation History")
            return False
        
        # Get numeric columns (excluding date)
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        
        # Check row sums (should be close to 1.0 or 100%)
        row_sums = df[numeric_cols].sum(axis=1)
        
        # Check if values are in 0-1 range or 0-100 range
        if row_sums.mean() > 10:  # Likely 0-100 range
            tolerance = 1.0  # 1% tolerance
            expected_sum = 100.0
        else:  # Likely 0-1 range
            tolerance = 0.01  # 1% tolerance
            expected_sum = 1.0
        
        # Check if all rows sum close to expected value
        valid_sums = ((row_sums >= expected_sum - tolerance) & 
                      (row_sums <= expected_sum + tolerance))
        
        if not valid_sums.all():
            invalid_rows = (~valid_sums).sum()
            logger.warning(f"{invalid_rows} rows in Allocation History do not sum to {expected_sum}%")
            return False
        
        logger.info(f"All Allocation History rows sum to {expected_sum}% (±{tolerance}%)")
        return True
    except Exception as e:
        logger.error(f"Error validating Allocation History sums: {e}")
        return False

def validate_trade_log(excel_file):
    """Validate Trade Log structure and content"""
    try:
        excel = pd.ExcelFile(excel_file)
        if 'Trades' not in excel.sheet_names and 'Trade Log' not in excel.sheet_names:
            logger.warning("Trade Log tab not found")
            return False
        
        # Find the correct sheet name
        sheet_name = 'Trade Log' if 'Trade Log' in excel.sheet_names else 'Trades'
        
        df = pd.read_excel(excel, sheet_name=sheet_name)
        
        # Check required columns
        required_cols = ['trade_num', 'symbol', 'quantity', 'execution_date', 
                         'execution_price', 'amount']
        
        # Check for column presence (allowing for case differences and variations)
        for req_col in required_cols:
            found = False
            for col in df.columns:
                if req_col.lower() in str(col).lower():
                    found = True
                    break
            
            if not found:
                logger.warning(f"Required column not found in Trade Log: {req_col}")
                return False
        
        # Check for sorting by execution date
        date_col = None
        for col in df.columns:
            if 'date' in str(col).lower():
                date_col = col
                break
        
        if date_col is not None:
            # Convert to datetime if not already
            if not pd.api.types.is_datetime64_dtype(df[date_col]):
                try:
                    df[date_col] = pd.to_datetime(df[date_col])
                except:
                    logger.warning(f"Could not convert {date_col} to datetime")
            
            # Check if sorted
            is_sorted = df[date_col].is_monotonic_increasing
            if not is_sorted:
                logger.warning("Trade Log is not sorted by execution date")
                return False
        
        logger.info("Trade Log validation passed")
        return True
    except Exception as e:
        logger.error(f"Error validating Trade Log: {e}")
        return False

def validate_performance_metrics(excel_file):
    """Validate Performance metrics tab structure and content"""
    try:
        excel = pd.ExcelFile(excel_file)
        if 'Performance' not in excel.sheet_names:
            logger.warning("Performance tab not found")
            return False
        
        df = pd.read_excel(excel, sheet_name='Performance')
        
        # Check for key metrics
        key_metrics = ['Return', 'Volatility', 'Sharpe', 'Drawdown']
        
        # Check for metrics presence (allowing for variations)
        metrics_found = 0
        for metric in key_metrics:
            for col in df.columns:
                if metric.lower() in str(col).lower():
                    metrics_found += 1
                    break
        
        if metrics_found < len(key_metrics):
            logger.warning(f"Some key metrics missing in Performance tab. Found {metrics_found}/{len(key_metrics)}")
            return False
        
        logger.info("Performance metrics validation passed")
        return True
    except Exception as e:
        logger.error(f"Error validating Performance metrics: {e}")
        return False

# Comprehensive validation functions
def validate_performance_report(excel_path, chart_paths=None):
    """Comprehensive validation for performance report"""
    results = {
        'excel_exists': validate_excel_file_exists(excel_path),
        'tabs_present': validate_excel_tabs(excel_path, 'performance'),
        'headers_present': validate_excel_headers(excel_path, 'performance'),
        'formatting': validate_excel_formatting(excel_path, 'performance'),
        'trade_log': validate_trade_log(excel_path),
        'performance_metrics': validate_performance_metrics(excel_path)
    }
    
    # Validate charts if provided
    if chart_paths:
        for chart_type, path in chart_paths.items():
            results[f'chart_{chart_type}_exists'] = validate_chart_exists(path)
            results[f'chart_{chart_type}_resolution'] = validate_chart_resolution(path, 'performance')
    
    # Overall result
    overall = all(results.values())
    results['overall'] = overall
    
    return results

def validate_allocation_report(excel_path, chart_paths=None):
    """Comprehensive validation for allocation report"""
    results = {
        'excel_exists': validate_excel_file_exists(excel_path),
        'tabs_present': validate_excel_tabs(excel_path, 'allocation'),
        'headers_present': validate_excel_headers(excel_path, 'allocation'),
        'formatting': validate_excel_formatting(excel_path, 'allocation'),
        'allocation_sums': validate_allocation_history_sums(excel_path)
    }
    
    # Validate charts if provided
    if chart_paths:
        for chart_type, path in chart_paths.items():
            results[f'chart_{chart_type}_exists'] = validate_chart_exists(path)
            results[f'chart_{chart_type}_resolution'] = validate_chart_resolution(path, 'allocation')
    
    # Overall result
    overall = all(results.values())
    results['overall'] = overall
    
    return results

def validate_signal_history_report(excel_path, chart_paths=None):
    """Comprehensive validation for signal history report"""
    results = {
        'excel_exists': validate_excel_file_exists(excel_path),
        'tabs_present': validate_excel_tabs(excel_path, 'signal_history'),
        'headers_present': validate_excel_headers(excel_path, 'signal_history'),
        'formatting': validate_excel_formatting(excel_path, 'signal_history'),
        'signal_sums': validate_signal_history_sums(excel_path)
    }
    
    # Validate charts if provided
    if chart_paths:
        for chart_type, path in chart_paths.items():
            results[f'chart_{chart_type}_exists'] = validate_chart_exists(path)
            results[f'chart_{chart_type}_resolution'] = validate_chart_resolution(path, 'signal_history')
    
    # Overall result
    overall = all(results.values())
    results['overall'] = overall
    
    return results
