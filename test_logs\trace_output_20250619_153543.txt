2025-06-19 15:35:44,799 - INFO - Successfully loaded settings for ema_allocation_model_v4.
2025-06-19 15:35:44,815 - INFO - [TickerData] Mode=Read, file=S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\data\tickerdata_SPY_SHV_EFA_TLT_PFF_20200101_20250615.xlsx
2025-06-19 15:35:45,171 - INFO - Initialized backtest engine with $1,000,000.00 capital
2025-06-19 15:35:45,171 - INFO - Commission rate: 0.10%
2025-06-19 15:35:45,171 - INFO - Slippage rate: 0.05%
2025-06-19 15:35:45,178 - INFO - Starting backtest with monthly rebalancing
2025-06-19 15:35:45,178 - INFO - Execution delay: 0 days
2025-06-19 15:35:45,179 - INFO - Trace directory for this run: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4_trace_outputs\backtest_run_20250619_153545_179381
2025-06-19 15:35:45,188 - INFO - Saved Initial Price Data to S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4_trace_outputs\backtest_run_20250619_153545_179381\00_initial_price_data.csv
2025-06-19 15:35:45,194 - INFO - Initialized signal_history with shape (27, 5)
2025-06-19 15:35:45,194 - INFO - Initialized weights_history with shape (27, 5)
Python version: 3.13.1 (tags/v3.13.1:0671451, Dec  3 2024, 19:06:28) [MSC v.1942 64 bit (AMD64)]
Python paths:
  S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4
  S:\Dropbox\Scott Only Internal\Quant_Python_24\Custom Function Library
  S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template
  C:\Python313\python313.zip
  C:\Python313\DLLs
  C:\Python313\Lib
  C:\Python313
  F:\AI_Library\my_quant_env
  F:\AI_Library\my_quant_env\Lib\site-packages
  F:\GitHub_Clone\ai-sdlc
Loaded settings from: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\settings\settings_parameters_v4.ini
Found 12 parameter sections
Found 55 named lists for parameter references
Loaded settings from: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\settings\settings_parameters_v4.ini
Found 12 parameter sections
Found 55 named lists for parameter references
Loaded settings from: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\settings\settings_parameters_v4.ini
Found 12 parameter sections
Found 55 named lists for parameter references
Loaded settings from: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\settings\settings_parameters_v4.ini
Found 12 parameter sections
Found 55 named lists for parameter references
Loaded settings from: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\settings\settings_parameters_v4.ini
Found 12 parameter sections
Found 55 named lists for parameter references
Loaded settings from: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\settings\settings_parameters_v4.ini
Found 12 parameter sections
Found 55 named lists for parameter references
DEBUG: _initialize_module_parameter: param_name='st_lookback'
DEBUG: _initialize_module_parameter: raw_value='{'optimize': True, 'default_value': 15, 'min_value': 5, 'max_value': 30, 'increment': 1}', type='<class 'dict'>'
DEBUG: _initialize_module_parameter: actual_value='15', type='<class 'int'>'
DEBUG: _initialize_module_parameter: casted_value='15', type='<class 'int'>' for param_name='st_lookback'
DEBUG: _initialize_module_parameter: param_name='mt_lookback'
DEBUG: _initialize_module_parameter: raw_value='{'optimize': True, 'default_value': 70, 'min_value': 30, 'max_value': 100, 'increment': 5}', type='<class 'dict'>'
DEBUG: _initialize_module_parameter: actual_value='70', type='<class 'int'>'
DEBUG: _initialize_module_parameter: casted_value='70', type='<class 'int'>' for param_name='mt_lookback'
DEBUG: _initialize_module_parameter: param_name='lt_lookback'
DEBUG: _initialize_module_parameter: raw_value='{'optimize': True, 'default_value': 100, 'min_value': 50, 'max_value': 200, 'increment': 10}', type='<class 'dict'>'
DEBUG: _initialize_module_parameter: actual_value='100', type='<class 'int'>'
DEBUG: _initialize_module_parameter: casted_value='100', type='<class 'int'>' for param_name='lt_lookback'
DEBUG: _initialize_module_parameter: param_name='min_weight'
DEBUG: _initialize_module_parameter: raw_value='0.0', type='<class 'float'>'
DEBUG: _initialize_module_parameter: actual_value='0.0', type='<class 'float'>'
DEBUG: _initialize_module_parameter: casted_value='0.0', type='<class 'float'>' for param_name='min_weight'
DEBUG: _initialize_module_parameter: param_name='max_weight'
DEBUG: _initialize_module_parameter: raw_value='1.0', type='<class 'float'>'
DEBUG: _initialize_module_parameter: actual_value='1.0', type='<class 'float'>'
DEBUG: _initialize_module_parameter: casted_value='1.0', type='<class 'float'>' for param_name='max_weight'
Loaded settings from: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\settings\settings_parameters_v4.ini
Found 12 parameter sections
Found 55 named lists for parameter references
Successfully imported V4 modules.
--- Starting V4 Production Backtest Run ---

Step 1: Loading settings from settings_CPS_v4.ini...
Loaded settings from: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\settings\settings_parameters_v4.ini
Found 12 parameter sections
Found 55 named lists for parameter references
Settings loaded successfully.

Step 2: Loading market data...
Price data loaded for 5 assets.

Step 3: Running V4 backtest engine...
   - Using strategy: 'EMA_Crossover' with params: {'signal_algo': {'default': 'ema', 'picklist': ['Strategy_EMA', 'Strategy_SMA', 'Strategy_MACD', 'Strategy_RSI', 'Strategy_BB']}, 'min_weight': 0.0, 'max_weight': 1.0, 'strategy_name': 'EMA_Crossover', 'system_top_n': 3, 'execution_delay': 0, 'ema_short_period': {'optimize': True, 'default_value': 12, 'min_value': 5, 'max_value': 20, 'increment': 1}, 'ema_long_period': {'optimize': True, 'default_value': 26, 'min_value': 10, 'max_value': 100, 'increment': 1}, 'st_lookback': {'optimize': True, 'default_value': 15, 'min_value': 5, 'max_value': 30, 'increment': 1}, 'mt_lookback': {'optimize': True, 'default_value': 70, 'min_value': 30, 'max_value': 100, 'increment': 5}, 'lt_lookback': {'optimize': True, 'default_value': 100, 'min_value': 50, 'max_value': 200, 'increment': 10}}

!!! CASCADE DEBUG: ENTERING BacktestEngine.run_backtest - VERSION JUNE 20 10:00 AM !!!


===== BACKTEST ENGINE: run_backtest STARTED =====
Price data shape: (27, 5)
Price data head:
                   SPY       SHV        EFA         TLT        PFF
Date                                                              
2021-01-20  361.473053  97.16806  66.902039  133.785599  29.956203
2021-01-21  361.802612  97.16806  66.955254  132.846588  29.839361
2021-01-22  360.522034  97.16806  66.636024  133.276596  29.893879
2021-01-25  361.943848  97.16806  66.556213  134.847366  29.816002
2021-01-26  361.378937  97.16806  66.653755  134.592880  29.738111
Signal generator: generate_signals
Signal params: {'strategy': 'EMA_Crossover', 'signal_algo': {'default': 'ema', 'picklist': ['Strategy_EMA', 'Strategy_SMA', 'Strategy_MACD', 'Strategy_RSI', 'Strategy_BB']}, 'min_weight': 0.0, 'max_weight': 1.0, 'strategy_name': 'EMA_Crossover', 'system_top_n': 3, 'execution_delay': 0, 'ema_short_period': {'optimize': True, 'default_value': 12, 'min_value': 5, 'max_value': 20, 'increment': 1}, 'ema_long_period': {'optimize': True, 'default_value': 26, 'min_value': 10, 'max_value': 100, 'increment': 1}, 'st_lookback': {'optimize': True, 'default_value': 15, 'min_value': 5, 'max_value': 30, 'increment': 1}, 'mt_lookback': {'optimize': True, 'default_value': 70, 'min_value': 30, 'max_value': 100, 'increment': 5}, 'lt_lookback': {'optimize': True, 'default_value': 100, 'min_value': 50, 'max_value': 200, 'increment': 10}}
Rebalance frequency: {'default': "'monthly'", 'picklist': ['Rebalance_Daily', 'Rebalance_Weekly', 'Rebalance_Monthly', 'Rebalance_Quarterly', 'Rebalance_Yearly']}
Execution delay: 0
Stored price data in engine, shape: (27, 5)
INFO: Trace directory for this run: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4_trace_outputs\backtest_run_20250619_153545_179381
TRACE: Saved Initial Price Data to S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4_trace_outputs\backtest_run_20250619_153545_179381\00_initial_price_data.csv
Initialized signal_history with shape: (27, 5)

===== Starting date iteration =====

Processing date 1/27: 2021-01-20 00:00:00
  Current prices: {'SPY': 361.4730529785156, 'SHV': 97.16806030273438, 'EFA': 66.90203857421875, 'TLT': 133.7855987548828, 'PFF': 29.95620346069336}
  Portfolio value: $1,000,000.00
  Should rebalance: False

Processing date 2/27: 2021-01-21 00:00:00
  Current prices: {'SPY': 361.8026123046875, 'SHV': 97.16806030273438, 'EFA': 66.95525360107422, 'TLT': 132.8465881347656, 'PFF': 29.8393611907959}
  Portfolio value: $1,000,000.00
  Should rebalance: False

Processing date 3/27: 2021-01-22 00:00:00
  Current prices: {'SPY': 360.5220336914062, 'SHV': 97.16806030273438, 'EFA': 66.63602447509766, 'TLT': 133.2765960693359, 'PFF': 29.89387893676758}
  Portfolio value: $1,000,000.00
  Should rebalance: False

Processing date 4/27: 2021-01-25 00:00:00
  Current prices: {'SPY': 361.94384765625, 'SHV': 97.16806030273438, 'EFA': 66.55621337890625, 'TLT': 134.8473663330078, 'PFF': 29.81600189208984}
  Portfolio value: $1,000,000.00
  Should rebalance: False

Processing date 5/27: 2021-01-26 00:00:00
  Current prices: {'SPY': 361.3789367675781, 'SHV': 97.16806030273438, 'EFA': 66.65375518798828, 'TLT': 134.5928802490234, 'PFF': 29.73811149597168}
  Portfolio value: $1,000,000.00
  Should rebalance: False
  Generating signals for 2021-02-01 00:00:00 with TRACE MODE ENABLED

=== generate_signals wrapper ===
Strategy: EMA_Crossover
Price data shape: (9, 5)
Additional params: {'trace_mode': True, 'signal_algo': {'default': 'ema', 'picklist': ['Strategy_EMA', 'Strategy_SMA', 'Strategy_MACD', 'Strategy_RSI', 'Strategy_BB']}, 'min_weight': 0.0, 'max_weight': 1.0, 'strategy_name': 'EMA_Crossover', 'system_top_n': 3, 'execution_delay': 0, 'ema_short_period': {'optimize': True, 'default_value': 12, 'min_value': 5, 'max_value': 20, 'increment': 1}, 'ema_long_period': {'optimize': True, 'default_value': 26, 'min_value': 10, 'max_value': 100, 'increment': 1}, 'st_lookback': {'optimize': True, 'default_value': 15, 'min_value': 5, 'max_value': 30, 'increment': 1}, 'mt_lookback': {'optimize': True, 'default_value': 70, 'min_value': 30, 'max_value': 100, 'increment': 5}, 'lt_lookback': {'optimize': True, 'default_value': 100, 'min_value': 50, 'max_value': 200, 'increment': 10}}

=== create_signal_generator ===
Strategy: EMA_Crossover
Params: {}
Created signal generator: EMASignalGenerator
=== create_signal_generator completed ===


=== EMASignalGenerator.generate_signals ===
Price data shape: (9, 5)
Price data columns: ['SPY', 'SHV', 'EFA', 'TLT', 'PFF']
Price data head:
                   SPY       SHV        EFA         TLT        PFF
Date                                                              
2021-01-20  361.473053  97.16806  66.902039  133.785599  29.956203
2021-01-21  361.802612  97.16806  66.955254  132.846588  29.839361
2021-01-22  360.522034  97.16806  66.636024  133.276596  29.893879
2021-01-25  361.943848  97.16806  66.556213  134.847366  29.816002
2021-01-26  361.378937  97.16806  66.653755  134.592880  29.738111
EMA parameters - st_lookback: {'optimize': True, 'default_value': 15, 'min_value': 5, 'max_value': 30, 'increment': 1}, mt_lookback: {'optimize': True, 'default_value': 70, 'min_value': 30, 'max_value': 100, 'increment': 5}, lt_lookback: {'optimize': True, 'default_value': 100, 'min_value': 50, 'max_value': 200, 'increment': 10}
Additional params: {'trace_mode': True, 'signal_algo': {'default': 'ema', 'picklist': ['Strategy_EMA', 'Strategy_SMA', 'Strategy_MACD', 'Strategy_RSI', 'Strategy_BB']}, 'min_weight': 0.0, 'max_weight': 1.0, 'strategy_name': 'EMA_Crossover', 'system_top_n': 3, 'execution_delay': 0, 'ema_short_period': {'optimize': True, 'default_value': 12, 'min_value': 5, 'max_value': 20, 'increment': 1}, 'ema_long_period': {'optimize': True, 'default_value': 26, 'min_value': 10, 'max_value': 100, 'increment': 1}}
Calculating EMAs from price data...
FATAL: Backtest engine failed. Error: '<' not supported between instances of 'dict' and 'int'
