# Backtest Engine Handoff Document

## Objective
Provide a clear summary of the current custom backtest engine, highlight its objectives, identify potential issues, and document research on third-party alternatives—recommending a migration to Backtrader as the next focus.

---

## 1. Project Objective
- **Goal:**
  - To provide accurate, auditable, and flexible backtesting for multi-asset portfolio strategies.
  - Ensure all results (trades, portfolio value, performance metrics) are derived from a single, consistent simulation state (“single source of truth”).
  - Enable reproducible research, robust reporting, and easy future maintenance.

---

## 2. Potential Problems with Current Custom Engine
- **Lack of Single Source of Truth:**
  - Trades, portfolio value, and performance metrics are generated from separate calculations (weights, returns, inferred trades), which may lead to inconsistencies.
- **Auditability:**
  - Difficult to trace and reconcile all reported metrics back to the actual simulated trades.
- **Maintenance Burden:**
  - Custom logic requires ongoing maintenance and debugging.
- **Feature Gaps:**
  - Limited support for advanced features (slippage, commissions, multi-frequency, multi-asset, etc.) compared to mature libraries.
- **Scalability:**
  - Harder to extend for new asset classes, higher frequency, or more complex strategies.
- **Community & Documentation:**
  - Less support and fewer resources for troubleshooting or extending functionality.

---

## 3. Research on Third-Party Alternatives

### **Backtrader**
- **Strengths:**
  - Unified simulation: all trades, portfolio value, and metrics derive from the same cash/holdings simulation.
  - Supports stocks, futures, crypto, multi-asset, and multi-strategy.
  - Flexible: custom indicators, analyzers, commission schemes, slippage, etc.
  - Excellent logging, plotting, and trade inspection.
  - Active community and extensive documentation.
- **Limitations:**
  - API can be verbose for some tasks.
  - Project is mature but not under rapid development (still widely used).

### **Other Libraries Considered**
- **Zipline:** Good for equities, but dependency issues and less flexible for custom assets.
- **bt:** Great for allocation/weight-based strategies, but less granular for order-level simulation.
- **QuantConnect/Lean:** Institutional-grade, but heavier learning curve and cloud focus.
- **VectorBT:** Fast and research-friendly, but less suited for granular trade simulation.
- **PyAlgoTrade:** Clean API, but less active development and fewer features than Backtrader.

---

## 4. Recommendation
- **Focus on Backtrader for migration:**
  - It best meets the requirements for auditability, extensibility, and consistency.
  - Community support and documentation will speed up development and troubleshooting.
- **Next Steps:**
  - Prototype the core strategy in Backtrader using existing data.
  - Validate that all trades, portfolio value, and metrics are consistent and auditable.
  - Migrate reporting and analytics as needed.

---

*Prepared: 2025-04-14*
