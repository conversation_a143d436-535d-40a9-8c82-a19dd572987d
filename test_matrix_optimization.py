#!/usr/bin/env python
"""
Test Matrix Optimization Implementation
Quick test to verify our matrix-based optimization approach works.
"""

import sys
import os
sys.path.append('v4')

import pandas as pd
import numpy as np
from pathlib import Path
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_optimization_combinations():
    """Test optimization combinations generation."""
    try:
        from reporting.performance_table_generator import PerformanceTableGenerator
        
        # Create generator
        gen = PerformanceTableGenerator('v4/settings/settings_parameters_v4.ini')
        
        # Test if method exists
        if hasattr(gen, '_get_optimization_combinations'):
            logger.info("✅ _get_optimization_combinations method found")
            
            # Get combinations
            combinations = gen._get_optimization_combinations()
            logger.info(f"✅ Generated {len(combinations)} combinations:")
            
            for i, combo in enumerate(combinations):
                logger.info(f"  Combo {i}: {combo}")
                
            return combinations
        else:
            logger.error("❌ _get_optimization_combinations method NOT found")
            logger.info("Available methods:")
            methods = [method for method in dir(gen) if not method.startswith('__')]
            for method in sorted(methods):
                logger.info(f"  {method}")
            return None
            
    except Exception as e:
        logger.error(f"❌ Error testing optimization combinations: {e}")
        return None

def test_matrix_approach():
    """Test matrix-based optimization approach."""
    logger.info("Testing matrix optimization approach...")
    
    # Get combinations
    combinations = test_optimization_combinations()
    
    if combinations is None:
        logger.error("❌ Cannot test matrix approach - combinations failed")
        return False
        
    # Simulate matrix creation
    logger.info(f"✅ Testing matrix approach with {len(combinations)} combinations")
    
    # Create sample date range
    date_range = pd.date_range('2020-01-01', '2024-12-31', freq='D')
    logger.info(f"✅ Date range: {len(date_range)} days")
    
    # Pre-allocate matrix
    combo_names = [f"combo_{i}" for i in range(len(combinations))]
    equity_matrix = pd.DataFrame(index=date_range, columns=combo_names, dtype=float)
    
    logger.info(f"✅ Matrix allocated: {equity_matrix.shape}")
    logger.info(f"✅ Memory usage: ~{equity_matrix.memory_usage(deep=True).sum() / 1024 / 1024:.1f} MB")
    
    # Simulate filling matrix with different equity curves
    for i, combo in enumerate(combinations):
        # Create unique equity curve for each combination
        base_value = 1000000
        daily_returns = np.random.normal(0.0005, 0.01, len(date_range))  # Different seed per combo
        np.random.seed(i)  # Different seed for each combination
        equity_curve = base_value * (1 + daily_returns).cumprod()
        
        equity_matrix.iloc[:, i] = equity_curve
        logger.info(f"✅ Filled combo_{i}: Final value ${equity_curve[-1]:,.0f}")
    
    # Test saving matrix
    matrix_file = Path("test_optimization_matrix.csv")
    equity_matrix.to_csv(matrix_file)
    logger.info(f"✅ Matrix saved to {matrix_file}")
    
    # Test loading and extracting individual curves
    loaded_matrix = pd.read_csv(matrix_file, index_col=0, parse_dates=True)
    logger.info(f"✅ Matrix loaded: {loaded_matrix.shape}")
    
    # Extract individual equity curves
    for i in range(len(combinations)):
        combo_curve = loaded_matrix.iloc[:, i].dropna()
        final_value = combo_curve.iloc[-1]
        logger.info(f"✅ Extracted combo_{i}: Final value ${final_value:,.0f}")
    
    # Cleanup
    matrix_file.unlink()
    logger.info("✅ Test file cleaned up")
    
    return True

if __name__ == "__main__":
    logger.info("=" * 60)
    logger.info("MATRIX OPTIMIZATION TEST")
    logger.info("=" * 60)
    
    success = test_matrix_approach()
    
    if success:
        logger.info("=" * 60)
        logger.info("✅ MATRIX OPTIMIZATION TEST PASSED")
        logger.info("=" * 60)
    else:
        logger.info("=" * 60)
        logger.info("❌ MATRIX OPTIMIZATION TEST FAILED")
        logger.info("=" * 60)
