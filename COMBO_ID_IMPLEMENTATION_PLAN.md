# Combo ID System Implementation Plan

## Overview
Replace complex regex-based temporary settings file creation with a simple, readable combo ID system that provides universal identifiers for optimization combinations.

## Current Problem
- Complex regex manipulation of INI files
- Unreadable file names: `exec1_lt_lookback100_mt30_st5_system_lookback60_top2`
- No clear mapping between combo IDs and actual parameter values
- Hard to debug and maintain

## Solution: Universal Combo ID System
- Simple letter-number combinations: `S5_M30_L100_E1_T2`
- Lookup tables for human and AI reference
- No regex - direct INI file creation
- Consistent naming across all files and outputs

---

## 1. Combo ID Format Design

### Parameter Mapping
```
st_lookback    → S (Short-term)
mt_lookback    → M (Medium-term) 
lt_lookback    → L (Long-term)
execution_delay → E (Execution)
top_n          → T (Top N)
system_lookback → (included in lookup but not in ID for brevity)
```

### Example Combo IDs
```
S5_M30_L100_E1_T2   → st=5, mt=30, lt=100, exec=1, top=2
S15_M70_L100_E1_T2  → st=15, mt=70, lt=100, exec=1, top=2
S25_M90_L100_E1_T2  → st=25, mt=90, lt=100, exec=1, top=2
```

### Benefits
- **Readable**: Clear parameter identification
- **Compact**: Shorter than current approach
- **Sortable**: Natural alphabetical ordering
- **Universal**: Same ID used everywhere

---

## 2. Lookup Table System

### Three File Formats Generated

#### A. Human-Readable Text File
**File**: `combo_lookup_YYYYMMDD_HHMMSS.txt`
```
OPTIMIZATION COMBINATION LOOKUP TABLE
Generated: 2025-07-28 14:58:02
Total Combinations: 12
================================================================================

Combo ID: S5_M30_L100_E1_T2
  Description: Short=5, Medium=30, Long=100, Delay=1, TopN=2
  Parameters: st_lookback=5, mt_lookback=30, lt_lookback=100
  Settings: execution_delay=1, system_lookback=60, top_n=2
----------------------------------------
Combo ID: S5_M50_L100_E1_T2
  Description: Short=5, Medium=50, Long=100, Delay=1, TopN=2
  Parameters: st_lookback=5, mt_lookback=50, lt_lookback=100
  Settings: execution_delay=1, system_lookback=60, top_n=2
----------------------------------------
```

#### B. Excel/CSV File
**File**: `combo_lookup_YYYYMMDD_HHMMSS.csv`
```csv
combo_id,combination_number,st_lookback,mt_lookback,lt_lookback,execution_delay,system_lookback,top_n,description
S5_M30_L100_E1_T2,1,5,30,100,1,60,2,"Short=5, Medium=30, Long=100, Delay=1, TopN=2"
S5_M50_L100_E1_T2,2,5,50,100,1,60,2,"Short=5, Medium=50, Long=100, Delay=1, TopN=2"
S15_M30_L100_E1_T2,3,15,30,100,1,60,2,"Short=15, Medium=30, Long=100, Delay=1, TopN=2"
```

#### C. AI/JSON File
**File**: `combo_lookup_YYYYMMDD_HHMMSS.json`
```json
[
  {
    "combo_id": "S5_M30_L100_E1_T2",
    "combination_number": 1,
    "st_lookback": 5,
    "mt_lookback": 30,
    "lt_lookback": 100,
    "execution_delay": 1,
    "system_lookback": 60,
    "top_n": 2,
    "description": "Short=5, Medium=30, Long=100, Delay=1, TopN=2"
  }
]
```

---

## 3. Implementation Code Examples

### A. Combo ID Generation Function
```python
def generate_combo_id(combination):
    """Generate readable combo ID from parameter combination.
    
    Args:
        combination (dict): Parameter combination
        
    Returns:
        str: Combo ID like 'S5_M30_L100_E1_T2'
    """
    return f"S{combination['st_lookback']}_M{combination['mt_lookback']}_L{combination['lt_lookback']}_E{combination['execution_delay']}_T{combination['top_n']}"
```

### B. Lookup Table Creation Function
```python
def create_combo_lookup_table(combinations, output_dir):
    """Create lookup tables in TXT, CSV, and JSON formats.
    
    Args:
        combinations (list): List of parameter combinations
        output_dir (Path): Directory to save lookup files
        
    Returns:
        tuple: (json_file, csv_file, txt_file, lookup_data)
    """
    lookup_data = []
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    
    for i, combination in enumerate(combinations):
        combo_id = generate_combo_id(combination)
        
        lookup_entry = {
            "combo_id": combo_id,
            "combination_number": i + 1,
            "st_lookback": combination['st_lookback'],
            "mt_lookback": combination['mt_lookback'], 
            "lt_lookback": combination['lt_lookback'],
            "execution_delay": combination['execution_delay'],
            "system_lookback": combination['system_lookback'],
            "top_n": combination['top_n'],
            "description": f"Short={combination['st_lookback']}, Medium={combination['mt_lookback']}, Long={combination['lt_lookback']}, Delay={combination['execution_delay']}, TopN={combination['top_n']}"
        }
        lookup_data.append(lookup_entry)
    
    # Save as JSON (for AI/programmatic use)
    json_file = output_dir / f"combo_lookup_{timestamp}.json"
    with open(json_file, 'w') as f:
        json.dump(lookup_data, f, indent=2)
    
    # Save as CSV (for human/Excel use)
    csv_file = output_dir / f"combo_lookup_{timestamp}.csv"
    df = pd.DataFrame(lookup_data)
    df.to_csv(csv_file, index=False)
    
    # Save as human-readable TXT
    txt_file = output_dir / f"combo_lookup_{timestamp}.txt"
    with open(txt_file, 'w') as f:
        f.write(f"OPTIMIZATION COMBINATION LOOKUP TABLE\n")
        f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"Total Combinations: {len(combinations)}\n")
        f.write("=" * 80 + "\n\n")
        
        for entry in lookup_data:
            f.write(f"Combo ID: {entry['combo_id']}\n")
            f.write(f"  Description: {entry['description']}\n")
            f.write(f"  Parameters: st_lookback={entry['st_lookback']}, mt_lookback={entry['mt_lookback']}, lt_lookback={entry['lt_lookback']}\n")
            f.write(f"  Settings: execution_delay={entry['execution_delay']}, system_lookback={entry['system_lookback']}, top_n={entry['top_n']}\n")
            f.write("-" * 40 + "\n")
    
    return json_file, csv_file, txt_file, lookup_data
```

### C. Simple Settings File Creation (NO REGEX)
```python
def create_simple_settings_file(combination, combo_id):
    """Create simple INI file with combination parameters - NO REGEX.
    
    Args:
        combination (dict): Parameter combination
        combo_id (str): Combo ID for identification
        
    Returns:
        str: Path to temporary settings file
    """
    # Create temporary file
    temp_fd, temp_path = tempfile.mkstemp(suffix='.ini', prefix=f'temp_settings_{combo_id}_')
    os.close(temp_fd)
    
    # Simple INI content - NO REGEX MANIPULATION
    ini_content = f"""# Optimization Combination: {combo_id}
# Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
# Description: Short={combination['st_lookback']}, Medium={combination['mt_lookback']}, Long={combination['lt_lookback']}

[System]
csv_flag_use = False
optimization_active = False

[Strategy]
st_lookback = {combination['st_lookback']}
mt_lookback = {combination['mt_lookback']}
lt_lookback = {combination['lt_lookback']}
execution_delay = {combination['execution_delay']}
system_lookback = {combination['system_lookback']}
top_n = {combination['top_n']}
"""
    
    # Write to temp file
    with open(temp_path, 'w') as f:
        f.write(ini_content)
    
    logger.info(f"Created simple settings file for combo {combo_id}: {temp_path}")
    return temp_path
```

---

## 4. File Naming Impact Analysis

### Current File Names (Complex)
```
unified_portfolio_combo_exec1_lt_lookback100_mt30_st5_system_lookback60_top2_20250728_145802.csv
subprocess_exec1_lt_lookback100_mt30_st5_system_lookback60_top2_20250728_145802.log
temp_settings_exec1_lt_lookback100_mt30_st5_system_lookback60_top2.ini
```

### New File Names (Simple)
```
unified_portfolio_combo_S5_M30_L100_E1_T2_20250728_145802.csv
subprocess_S5_M30_L100_E1_T2_20250728_145802.log
temp_settings_S5_M30_L100_E1_T2.ini
```

### Benefits
- **50% shorter** file names
- **Human readable** at a glance
- **Consistent** across all file types
- **Sortable** naturally

---

## 5. Modules and Functions Affected

### Critical Analysis: Find All Impacted Code

#### A. Files That Generate Combo IDs
**Current Location**: `v4/py_reporting/report_modules/report_matrix_optimization.py`
- `generate_combo_id()` function ← **NEEDS REPLACEMENT**

#### B. Files That Create Settings Files
**Current Location**: `v4/py_reporting/report_modules/report_matrix_optimization.py`
- `_create_temp_settings_for_combination()` function ← **NEEDS COMPLETE REWRITE**

#### C. Files That Use Combo IDs in File Names
**Locations to Search**:
1. `v4/pipeline/modes.py` - Output file creation
2. `v4/py_reporting/report_modules/report_matrix_optimization.py` - File loading
3. Any logging functions that reference combo IDs

#### D. Files That Process Output Files
**Locations to Search**:
1. `v4/py_reporting/equity_curves_manager.py` - Matrix column naming
2. Excel generation modules - Column headers
3. Performance reporting modules - Strategy naming

#### E. Environment Variable Usage
**Locations to Search**:
1. `v4/pipeline/config.py` - `CPS_V4_COMBO_ID` usage
2. Any subprocess execution code

---

## 6. Implementation Steps

### Step 1: Code Impact Analysis
**Search for all references to combo IDs and file naming patterns**

```bash
# Search for current combo ID generation
grep -r "generate_combo_id" v4/
grep -r "combo_id" v4/
grep -r "exec.*_lt_lookback" v4/
grep -r "unified_portfolio_combo" v4/
grep -r "CPS_V4_COMBO_ID" v4/
```

### Step 2: Create New Functions
**File**: `v4/py_reporting/report_modules/report_matrix_optimization.py`

1. Replace `generate_combo_id()` function
2. Replace `_create_temp_settings_for_combination()` function  
3. Add `create_combo_lookup_table()` function

### Step 3: Update File Creation Logic
**File**: `v4/pipeline/modes.py`

Update output file naming to use new combo ID format:
```python
# OLD
unified_file = reporting_dir / f"unified_portfolio_combo_{old_combo_id}_{timestamp}.csv"

# NEW  
unified_file = reporting_dir / f"unified_portfolio_combo_{new_combo_id}_{timestamp}.csv"
```

### Step 4: Update File Loading Logic
**File**: `v4/py_reporting/report_modules/report_matrix_optimization.py`

Update `_load_unified_portfolio_for_combination()` to search for new file pattern:
```python
# OLD
expected_pattern = f"unified_portfolio_combo_{old_combo_id}_*.csv"

# NEW
expected_pattern = f"unified_portfolio_combo_{new_combo_id}_*.csv"
```

### Step 5: Update Excel/Matrix Generation
**Files to check**:
- `v4/py_reporting/equity_curves_manager.py`
- `v4/py_reporting/report_modules/report_excel.py`

Update column naming to use new combo IDs.

### Step 6: Update Logging
**All files that log combo information**

Replace old combo ID references with new format in log messages.

### Step 7: Integration Testing
1. Run optimization validation test
2. Verify lookup tables are created
3. Verify file naming is consistent
4. Verify Excel columns use new format

---

## 7. Testing Plan

### Test Cases
1. **Combo ID Generation**: Verify `S5_M30_L100_E1_T2` format
2. **Lookup Table Creation**: Verify TXT, CSV, JSON files created
3. **Settings File Creation**: Verify no regex, simple INI format
4. **File Naming**: Verify all files use consistent combo ID
5. **File Loading**: Verify files can be found with new naming
6. **Excel Integration**: Verify column headers use new format

### Validation Commands
```bash
# Test the new system
python test_optimization_validation.py

# Check lookup files created
ls optimization_validation/combo_lookup_*

# Check file naming consistency
ls reporting/unified_portfolio_combo_S*_M*_L*_E*_T*_*.csv
```

---

## 8. Rollback Plan

### If Issues Occur
1. **Keep old functions** as `_create_temp_settings_for_combination_OLD()`
2. **Feature flag** to switch between old/new systems
3. **Comparison testing** to verify identical results

### Rollback Code
```python
USE_NEW_COMBO_SYSTEM = True  # Feature flag

def _create_temp_settings_for_combination(combination, validation_mode=False, validation_dir=None):
    if USE_NEW_COMBO_SYSTEM:
        return _create_temp_settings_new(combination, validation_mode, validation_dir)
    else:
        return _create_temp_settings_OLD(combination, validation_mode, validation_dir)
```

---

## 9. Success Criteria

### Must Have
- ✅ No regex in settings file creation
- ✅ Readable combo IDs: `S5_M30_L100_E1_T2`
- ✅ Lookup tables created (TXT, CSV, JSON)
- ✅ Consistent file naming across all outputs
- ✅ Step 3 validation passes

### Nice to Have
- ✅ 50% shorter file names
- ✅ Natural alphabetical sorting
- ✅ Excel-friendly column headers
- ✅ AI-friendly JSON lookup

---

## 10. Documentation Updates

### Files to Update
1. **This document** - Complete implementation guide
2. **TESTING_SETUP.md** - Update with new file naming patterns
3. **AI_LOGGING_ENHANCEMENTS_SUMMARY.md** - Update with combo ID system
4. **README files** - Update any references to old combo ID format

### User Communication
- Explain new combo ID format
- Provide lookup table usage examples
- Show before/after file naming comparison

---

## Implementation Priority: HIGH

This change eliminates regex complexity, improves readability, and provides the universal identifier system needed for optimization tracking. The lookup table system ensures both human and AI can easily decode combo meanings.

**Next Step**: Begin Step 1 (Code Impact Analysis) to identify all affected modules and functions.