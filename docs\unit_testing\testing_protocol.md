# Testing Protocol
=============================================

## Core Testing Principles

### 1. Environment Requirements
- **CRITICAL RULE**: All Python code MUST run within the isolated virtual environment
- **NEVER** execute Python code directly outside the virtual environment
- **ALWAYS** use batch files for test execution to ensure proper environment setup

### 2. Test Organization

#### Test Types
1. **Unit Tests**: Test individual components in isolation
   - Located in `tests/` directory
   - Filename pattern: `test_[component]_[feature].py`
   - Each test should focus on a single component or feature

2. **Integration Tests**: Test interactions between components
   - Located in `tests/integration/` directory
   - Filename pattern: `test_integration_[workflow].py`
   - Focus on end-to-end workflows

3. **Verification Tests**: Validate specific requirements
   - Located in `tests/verification/` directory
   - Filename pattern: `test_verify_[requirement].py`
   - Used to confirm specific requirements are met

#### Batch File Organization
- **Unit Test Runners**: `run_test_[component]_[feature].bat`
- **Integration Test Runners**: `run_integration_test_[workflow].bat`
- **Verification Test Runners**: `run_verify_[requirement].bat`

### 3. Test Documentation

#### Required Documentation
- Each test must have corresponding documentation using the standard template
- Documentation should be stored in `docs/tests/[test_type]/[test_name].md`
- All test documentation must reference the appropriate batch file for execution

#### Documentation Updates
- Test documentation must be updated whenever test code changes
- Include a change history section in each test document
- Document both the test's purpose and implementation details

### 4. Test Implementation Standards

#### Code Organization
- Each test file should include a docstring explaining its purpose
- Group related test cases into test classes
- Use descriptive test method names that explain what is being tested
- Keep test files under 450 lines (follow module size limitation rule)

#### Test Data
- Store test data in `tests/data/` directory
- Use descriptive filenames for test data
- Document data dependencies in test documentation

#### Assertions
- Use specific assertion methods (e.g., `assertEqual`, `assertTrue`)
- Include descriptive failure messages
- Test both positive and negative cases

### 5. Test Execution Process

#### Pre-Execution Checklist
1. Verify the virtual environment is properly set up
2. Ensure all test dependencies are installed
3. Check that test data is available
4. Review test documentation for any special requirements

#### Execution Steps
1. Always run tests using the appropriate batch file
2. Verify that the batch file activates the correct virtual environment
3. Check test output for expected results
4. Document any failures or unexpected behavior

#### Post-Execution
1. Review test results thoroughly
2. Document any issues discovered
3. Update test documentation if needed
4. Never mark tests as "passed" until explicitly verified by the user

### 6. Test Maintenance

#### Regular Maintenance
- Review tests quarterly for relevance and accuracy
- Update tests when requirements change
- Remove obsolete tests
- Ensure all tests continue to run in the correct environment

#### Test Failure Response
1. Document the failure in detail
2. Identify whether the failure is in the test or the code being tested
3. Fix the issue at its source, not with workarounds
4. Add regression tests for any bugs discovered

## Implementation Examples

### Example: Running a Unit Test
```
# INCORRECT - Direct Python execution
python -m unittest tests/test_v3_reporting_parameters.py

# CORRECT - Using batch file
run_test_v3_reporting_parameters.bat
```

### Example: Test Documentation
See `docs/test_documentation_template.md` for the standard format.

### Example: Test Implementation
```python
# tests/test_example.py
"""
Tests for the example module.

This test suite verifies that the example module correctly handles input validation,
processing, and output formatting.
"""

import unittest
from example import process_data

class TestExampleProcessing(unittest.TestCase):
    """Test cases for the data processing functionality."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.test_data = [1, 2, 3, 4, 5]
        
    def test_valid_input_processing(self):
        """Test that valid input is processed correctly."""
        result = process_data(self.test_data)
        self.assertEqual(result, 15, "Sum of test data should be 15")
        
    def test_empty_input_handling(self):
        """Test that empty input is handled gracefully."""
        result = process_data([])
        self.assertEqual(result, 0, "Sum of empty list should be 0")
```
