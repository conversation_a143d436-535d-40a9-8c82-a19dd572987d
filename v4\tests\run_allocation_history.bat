@echo off
REM run_allocation_history.bat - Allocation history export script

set VENV_PATH=F:\AI_Library\my_quant_env
call "%VENV_PATH%\Scripts\activate.bat"

if not exist "%~dp0output" mkdir "%~dp0output"

echo Pre-run batch working directory: %CD%
echo Batch file directory: %~dp0
echo Pre-run output directory contents:
dir /b "%~dp0output"

echo Pre-run working directory contents:
dir /b

:: Run allocation debug script and capture output
python "%~dp0allocation_debug.py" > "%~dp0output\allocation_history_run_output.txt" 2>&1

echo Post-run output directory contents:
dir /b "%~dp0output"

echo Post-run working directory contents:
dir /b

:: Check exit code and display output
if %ERRORLEVEL% EQU 0 (
    echo Allocation history run completed successfully.
    echo Output saved to "%~dp0output\allocation_history_run_output.txt"
    echo.
    echo Displaying output summary:
    echo =============================
    type "%~dp0output\allocation_history_run_output.txt"
) else (
    echo Error running allocation history run.
    echo Check output in "%~dp0output\allocation_history_run_output.txt"
    echo.
    echo Displaying error output:
    echo =========================
    type "%~dp0output\allocation_history_run_output.txt"
)

:: Pause to view output
REM pause removed for automated run
