@echo on
REM super_simple_config.bat - Minimal test to import and print config settings

echo Activating virtual environment...
call "F:\AI_Library\my_quant_env\Scripts\activate.bat"
if %errorlevel% neq 0 (
    echo ERROR: Failed to activate virtual environment.
    pause
    goto :eof
)

echo.
echo Determining directories...
set "TESTS_DIR=%~dp0"
set "PROJECT_ROOT=%TESTS_DIR%..\.."
echo TESTS_DIR = %TESTS_DIR%
echo PROJECT_ROOT (should contain Backtest_FinAsset_Alloc_Template root) = %PROJECT_ROOT%

echo.
echo Importing settings via Python...
python -u -c "import sys; sys.path.insert(0, r'%PROJECT_ROOT%'); from v4.settings.settings_CPS_v4 import load_settings; settings = load_settings(); print(settings)"
echo Python exit code: %errorlevel%

echo.
echo Finished. Press any key to close.
pause
