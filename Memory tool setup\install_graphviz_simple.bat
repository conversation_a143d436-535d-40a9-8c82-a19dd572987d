@echo off
:: Check for admin rights
net session >nul 2>&1
if %errorLevel% == 0 (
    echo Running with admin privileges
) else (
    echo This script requires administrator privileges.
    echo Please right-click and select "Run as administrator"
    pause
    exit /b 1
)

echo Opening Graphviz download page...
start "" "https://graphviz.org/download/"

echo.
echo Please:
1. Download the "Stable Windows 10/11 installers" (64-bit)
2. Run the installer
3. Make sure to check "Add Graphviz to the system PATH for all users"
4. Complete the installation
5. Press any key to continue verification...
pause > nul

:: Verify installation
echo.
echo Verifying installation...
where dot 2>nul
if %ERRORLEVEL% EQU 0 (
    echo.
    dot -V
    echo.
    echo Graphviz is installed and in PATH!
) else (
    echo.
    echo Graphviz not found in PATH. Please make sure to:
    echo 1. Install Graphviz
    echo 2. Check "Add to PATH" during installation
    echo 3. Restart your terminal
)

pause
