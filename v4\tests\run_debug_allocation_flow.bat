@echo off
echo =====================================================
echo Allocation History Debug Trace - %date% %time%
echo =====================================================

REM Set up virtual environment path
set VENV_PATH=F:\AI_Library\my_quant_env

REM Activate virtual environment
echo Activating virtual environment at %VENV_PATH%...
call %VENV_PATH%\Scripts\activate.bat

REM Set output directory
set OUTPUT_DIR=s:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\tests\debug_output
echo Output directory path: %OUTPUT_DIR%
if not exist "%OUTPUT_DIR%" (
    echo Creating output directory...
    mkdir "%OUTPUT_DIR%"
    if exist "%OUTPUT_DIR%" (
        echo Output directory created successfully.
    ) else (
        echo ERROR: Failed to create output directory!
    )
) else (
    echo Output directory already exists.
)

REM Set log file
set LOG_FILE=%OUTPUT_DIR%\allocation_debug_%date:~-4,4%%date:~-7,2%%date:~-10,2%_%time:~0,2%%time:~3,2%%time:~6,2%.log
set LOG_FILE=%LOG_FILE: =0%

echo Starting allocation history debug trace...
echo Results will be saved to: %LOG_FILE%

REM Run the debug script with output to both console and log file
echo Running debug script...
echo Command: python s:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\tests\debug_allocation_flow.py
echo Output will be saved to: %LOG_FILE%

REM Check if Python is available
python --version
if %errorlevel% neq 0 (
    echo ERROR: Python not found or not in PATH!
    goto :error
)

REM Run the script with output to log file
python s:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\tests\debug_allocation_flow.py > "%LOG_FILE%" 2>&1
echo Python script completed with exit code: %errorlevel%

REM Display log file contents
echo.
echo Log file contents:
echo ==================
if exist "%LOG_FILE%" (
    type "%LOG_FILE%"
) else (
    echo ERROR: Log file not created!
)

REM Display output directory contents
echo.
echo Output directory contents:
dir "%OUTPUT_DIR%"

echo.

REM Deactivate virtual environment
echo Deactivating virtual environment...
call %VENV_PATH%\Scripts\deactivate.bat

goto :end

:error
echo Debug script execution failed!

:end

pause
