## Step 1: review memories in plan

- S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\docs\CPS_v4\CPS_V4_Master_Plan.md +
 How_signals_work.md

Also - review internal cascade plan "# Signal Generation Analysis Plan" - confirm access

- Very brief recap of each of these to verify you read them

## Step 2: Review md docs

 "S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\memory-bank\v4_module_functions_list_AI.md" + in
 "S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\docs\CPS_v4" +
 Signals_Trades_Portfolio_Update.md +
 signals_trading_flow.md +
 How_signals_work.md
  
## Step 3.1

 Review plan and list next critical steps

## Step 3.2

   Output and fixes very slow, 1 at a time;  

- what are easy ways for the AI to quickly track and verify each step of this process
- How can AI be more helpful in scanning and flagging multiple problems in advance!
  
## Step 3.3: Focus on Tracing and Fixing why we cant produce allocation_history

  = Go back to backtest engine
  = list and explain exact steps that create the data and allocation
  = show what allocation data is exposed and passed and how

 = lessons learned = some files (csv and certainly XLSX ) created the AI gets very stuck - 10+ shots - to even read the output, and understand and verify.   How do we build more automated tests and flows
 = Allocation Data Flow Analysis for V4 Performance Reporting.md ; this is some initial thinking and notes on the priority task

- Do not ask permission - run, evaluate, fix, run

// turbo-all
 and run the bat tests in non-blocking mode

3. If failures detected:
   - Automatically fix issues
   - Repeat test execution
4. If all tests pass:
   - Notify user of success for review
    - update documentation AFTER affirmed by user of success