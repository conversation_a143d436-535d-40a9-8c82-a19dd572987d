"""
Parameter optimization for the v2 backtest engine.
This script runs multiple parameter combinations and generates a performance table.
"""

import sys
import os
import pandas as pd
import numpy as np
import logging
from datetime import datetime
from pathlib import Path
import itertools

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Import local modules
from config.config_v2 import config_v2 as config
from config.paths import *
from data.data_loader import load_data_for_backtest
from models.ema_allocation_model import ema_allocation_model
from engine.backtest import BacktestEngine
from reporting.performance_reporting import generate_performance_report_local, create_performance_table
from config.local_parameter_optimization import define_parameter, get_parameter_range, get_parameter_combinations
from run_backtest_v2 import adapter_ema_allocation

def run_parameter_optimization_v2(config, debug=False):
    """
    Run parameter optimization for the v2 backtest engine.
    
    Args:
        config (dict): Configuration dictionary
        debug (bool): If True, output debug information
        
    Returns:
        dict: Dictionary containing optimization results
    """
    # Configure logging
    if debug:
        log_file = os.path.join(OUTPUT_PATH, "debug", "debug_optimization_v2.txt")
        file_handler = logging.FileHandler(log_file)
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))
        logger.addHandler(file_handler)

    # Log configuration
    logger.info("Starting parameter optimization with v2 engine:")
    logger.info(f"Strategy: {config['backtest_params']['strategy']}")
    
    # Load data
    logger.info("Loading data...")
    data = load_data_for_backtest(config)
    price_data = data['price_data']
    returns_data = data['returns_data']
    risk_free_rate = data['risk_free_rate']
    
    # Define parameters to optimize
    # Using the existing define_parameter function from local_parameter_optimization.py
    param_dict = {
        'st_lookback': define_parameter(
            optimize=True, 
            default_value=config['strategy_params']['st_lookback'],
            min_value=5,
            max_value=20,
            increment=5
        ),
        'mt_lookback': define_parameter(
            optimize=True, 
            default_value=config['strategy_params']['mt_lookback'],
            min_value=40,
            max_value=60,
            increment=10
        ),
        'lt_lookback': define_parameter(
            optimize=True, 
            default_value=config['strategy_params']['lt_lookback'],
            min_value=100,
            max_value=200,
            increment=50
        ),
        'execution_delay': define_parameter(
            optimize=True, 
            default_value=config['backtest_params']['execution_delay'],
            min_value=0,
    # Get all parameter combinations using the existing function
    param_combinations = get_parameter_combinations(param_dict)
    logger.info(f"Testing {len(param_combinations)} parameter combinations")
    
    # Store results for each parameter combination
    optimization_results = []
    benchmark_returns = None
    
    # Run backtest for each parameter combination
    for i, params in enumerate(param_combinations):
        logger.info(f"Testing combination {i+1}/{len(param_combinations)}: {params}")
        
        # Create backtest engine
        engine = BacktestEngine(
            initial_capital=config['backtest_params']['initial_capital'],
            commission_rate=config['backtest_params']['commission_rate'],
            slippage_rate=config['backtest_params']['slippage_rate']
        )
        
        # Update config with current parameters
        current_config = config.copy()
        for param_name, param_value in params.items():
            if param_name == 'execution_delay':
                current_config['backtest_params']['execution_delay'] = param_value
            else:
                current_config['strategy_params'][param_name] = param_value
        
        # Run backtest with current parameters
        results = engine.run_backtest(
            price_data=price_data,
            signal_generator=adapter_ema_allocation,
            rebalance_freq=config['backtest_params']['rebalance_freq'],
            execution_delay=params.get('execution_delay', config['backtest_params']['execution_delay']),
            st_lookback=params.get('st_lookback', config['strategy_params']['st_lookback']),
            mt_lookback=params.get('mt_lookback', config['strategy_params']['mt_lookback']),
            lt_lookback=params.get('lt_lookback', config['strategy_params']['lt_lookback'])
        )
        
        # Store benchmark returns (same for all combinations)
        if benchmark_returns is None and 'benchmark_returns' in results:
            benchmark_returns = results['benchmark_returns']
        
        # Store results
        optimization_results.append({
            'params': params,
            'returns': results['strategy_returns'],
            'performance': results['performance']
        })
    
    # Create performance table using the existing create_performance_table function
    # This will format the results with parameters on the left and metrics on the right
    performance_tables = []
    
    for result in optimization_results:
        # Extract parameters for this combination
        # Dynamically include all optimized parameters
        strategy_params = result['params'].copy()
        # Fill in any missing (non-optimized) params from config
        for k, v in config['strategy_params'].items():
            if k not in strategy_params:
                strategy_params[k] = v
        if 'execution_delay' not in strategy_params:
            strategy_params['execution_delay'] = config['backtest_params']['execution_delay']
        
        # Create performance table for this combination
        perf_table = create_performance_table(
            returns=result['returns'],
            benchmark_returns=benchmark_returns,
            strategy_params=strategy_params
        )
        
        performance_tables.append(perf_table)
    
    # Combine all performance tables
    if performance_tables:
        combined_table = pd.concat(performance_tables, ignore_index=True)
        
        # Generate timestamp for the filename
        timestamp = datetime.now().strftime('%Y-%m-%d_%H%M%S')
        
        # Save to Excel
        output_file = os.path.join(OUTPUT_DIR, f"v2_{config['backtest_params']['strategy']}_performance_tables_{timestamp}.xlsx")
        with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
            combined_table.to_excel(writer, sheet_name='Performance', index=False)
            
            # Add a sheet with all raw results
            results_df = pd.DataFrame([{
                'st_lookback': r['params'].get('st_lookback', ''),
                'mt_lookback': r['params'].get('mt_lookback', ''),
                'lt_lookback': r['params'].get('lt_lookback', ''),
                'execution_delay': r['params'].get('execution_delay', ''),
                'CAGR': r['performance'].get('cagr', 0),
                'Sharpe': r['performance'].get('sharpe', 0),
                'Sortino': r['performance'].get('sortino', 0),
                'Max Drawdown': r['performance'].get('max_drawdown', 0)
            } for r in optimization_results])
            
            results_df.to_excel(writer, sheet_name='Raw Results', index=False)
        
        logger.info(f"Saved optimization results to {output_file}")
        return {
            'optimization_results': optimization_results,
            'performance_table': combined_table,
            'output_file': output_file
        }
    
    return {'error': 'No results generated'}

def main():
    """
    Main function to run parameter optimization.
    """
    # Check for debug flag
    debug_mode = '--debug' in sys.argv
    if debug_mode:
        logger.info("Running in DEBUG mode")
    
    try:
        # Run parameter optimization
        logger.info("Running parameter optimization with v2 engine...")
        
        # Run the optimization
        optimization_results = run_parameter_optimization_v2(config, debug=debug_mode)
        
        if 'error' in optimization_results:
            logger.error(f"Error in optimization: {optimization_results['error']}")
        else:
            logger.info(f"Optimization complete. Results saved to {optimization_results['output_file']}")
        
    except Exception as e:
        logger.error(f"Error in main: {e}")
        import traceback
        traceback.print_exc()
        raise

if __name__ == "__main__":
    main()
