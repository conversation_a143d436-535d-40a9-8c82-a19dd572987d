"""
v4/models/ema_signal_bridge_optimization.py
Bridge module for OPTIMIZATION MODE - connects EMASignalGenerator with ema_allocation_model for tracing.

This is the optimization-specific version that accepts settings directly without environment variable dependencies.
For single mode, use ema_signal_bridge.py instead.
"""

import pandas as pd
import logging
from pathlib import Path
import os

from v4.models.ema_allocation_model_v4 import _extract_value_from_complexn_dict, _PARAM_NOT_FOUND, calculate_ema_metrics_with_params, calculate_ema_ratios, get_allocation_weights
from v4.utils.tracing_utils import save_df_to_trace_dir

logger = logging.getLogger(__name__)

def ema_allocation_model_optimization(price_data, returns_data=None, st_lookback=None, mt_lookback=None, lt_lookback=None, min_weight=None, max_weight=None, system_top_n=None, trace_mode=False, settings=None, **params):
    """
    OPTIMIZATION VERSION: EMA-based allocation model that accepts settings directly.
    Uses Short, Medium, and Long-term EMAs to determine asset allocation.

    Ranking Rule:
    - Rank all assets by EMAXAvg (Rank 1 to X, where X is # of assets)

    Signal Allocation Rule:
    - Own top Y assets out of X assets (rank 1 or 2), and 0 allocation to all others
    - Rank 1 = 60% allocation
    - Rank 2 = 40% allocation

    Args:
        price_data (DataFrame): Historical price data
        returns_data (DataFrame, optional): Historical returns data
        st_lookback (int): Short-term lookback period override
        mt_lookback (int): Medium-term lookback period override
        lt_lookback (int): Long-term lookback period override
        min_weight (float): Minimum weight per asset
        max_weight (float): Maximum weight per asset
        system_top_n (int): Number of top assets to allocate
        trace_mode (bool): Whether to return trace data
        settings (dict): Settings dictionary to use instead of loading from file
        **params: Additional parameters

    Returns:
        dict: Asset weights dictionary (or tuple if trace_mode=True)
    """
    if settings is None:
        raise ValueError("Settings must be provided for optimization version")

    # Extract parameters from provided settings
    ema_config = settings.get('ema_model', {})
    strategy_config = settings.get('strategy', {})
    system_config = settings.get('system', {})

    def _get_param_from_settings(key, default=None):
        """Fetch parameter from settings sections."""
        value = ema_config.get(key, _PARAM_NOT_FOUND)
        if value is _PARAM_NOT_FOUND:
            value = strategy_config.get(key, _PARAM_NOT_FOUND)
        if value is _PARAM_NOT_FOUND:
            value = system_config.get(key, _PARAM_NOT_FOUND)

        if value is _PARAM_NOT_FOUND:
            if default is not None:
                return default
            error_msg = f"Parameter '{key}' not found in '[ema_model]', '[strategy]', or '[system]' sections of provided settings."
            logger.critical(error_msg)
            raise KeyError(error_msg)

        # Handle ComplexN parameters
        if isinstance(value, dict) and 'default_value' in value:
            return value['default_value']
        return _extract_value_from_complexn_dict(value)

    # Use provided parameters or extract from settings
    effective_st_lookback = st_lookback if st_lookback is not None else int(_get_param_from_settings('st_lookback'))
    effective_mt_lookback = mt_lookback if mt_lookback is not None else int(_get_param_from_settings('mt_lookback'))
    effective_lt_lookback = lt_lookback if lt_lookback is not None else int(_get_param_from_settings('lt_lookback'))

    # For weight constraints, use passed values or defaults
    effective_min_weight = min_weight if min_weight is not None else float(_get_param_from_settings('min_weight', 0.0))
    effective_max_weight = max_weight if max_weight is not None else float(_get_param_from_settings('max_weight', 1.0))

    logger.debug(f"EMA Model Inputs (OPTIMIZATION, Date: {price_data.index[-1]}) -- ST: {effective_st_lookback}, MT: {effective_mt_lookback}, LT: {effective_lt_lookback}, min_w: {effective_min_weight}, max_w: {effective_max_weight}, system_top_n_arg: {system_top_n}")
    logger.debug(f"Price data shape: {price_data.shape}, last date: {price_data.index[-1]}, first 3 columns: {list(price_data.columns[:3])}")

    # Use precomputed EMAs if available, else compute with effective parameters
    if 'precomputed_emas' in params:
        short_ema, med_ema, long_ema = params.pop('precomputed_emas')
    else:
        short_ema, med_ema, long_ema = calculate_ema_metrics_with_params(price_data, effective_st_lookback, effective_mt_lookback, effective_lt_lookback)
    logger.debug(f"Short EMA shape: {short_ema.shape}, last date: {short_ema.index[-1]}, first 3 columns: {list(short_ema.columns[:3])}")
    logger.debug(f"Medium EMA shape: {med_ema.shape}, last date: {med_ema.index[-1]}, first 3 columns: {list(med_ema.columns[:3])}")
    logger.debug(f"Long EMA shape: {long_ema.shape}, last date: {long_ema.index[-1]}, first 3 columns: {list(long_ema.columns[:3])}")

    # Calculate EMA ratios - now returns both Series and DataFrames
    stmtemax, mtltemax, emaxavg, stmtemax_df, mtltemax_df, emaxavg_df = calculate_ema_ratios(short_ema, med_ema, long_ema)
    logger.debug(f"""Calculated EMAXAVG (Series for current date {price_data.index[-1]}):
{emaxavg}""")

    # Store the full history in params for debugging/analysis if needed
    params['ema_history'] = {
        'short_ema': short_ema,
        'med_ema': med_ema,
        'long_ema': long_ema,
        'stmtemax_df': stmtemax_df,
        'mtltemax_df': mtltemax_df,
        'emaxavg_df': emaxavg_df
    }

    # Rank assets by EMAXAvg (descending order)
    ranked_assets = emaxavg.sort_values(ascending=False)
    logger.debug(f"""Ranked assets by EMAXAVG (for current date {price_data.index[-1]}):
{ranked_assets}""")

    # Initialize weights dictionary
    weights = {asset: 0.0 for asset in price_data.columns}

    # Determine number of top assets to allocate (hard fail on missing setting)
    if system_top_n is None:
        raise ValueError("Required parameter 'system_top_n' was not provided or is None")
    top_n = int(system_top_n)

    # Get algorithm name from settings, extracting the default value from the dict
    signal_algo_setting = strategy_config.get('signal_algo', {})
    if isinstance(signal_algo_setting, dict):
        algo_name = signal_algo_setting.get('default', 'ema')
    else:
        # Fallback for old string-based setting format
        algo_name = signal_algo_setting or 'ema'
    logger.debug(f"Resolved algo_name: {algo_name}, Effective top_n: {top_n} (Date: {price_data.index[-1]})")

    # Get allocation rules for the specified algorithm
    rule_weights = get_allocation_weights(top_n, algorithm=algo_name)
    selected_assets = ranked_assets.index[:top_n]
    logger.debug(f"Selected top {top_n} assets (Date: {price_data.index[-1]}): {list(selected_assets)}")
    logger.debug(f"Allocation rule weights for {algo_name} (top_n={top_n}, Date: {price_data.index[-1]}): {rule_weights}")

    # Allocate weights only if rules match the number of selected assets
    if rule_weights and len(rule_weights) == len(selected_assets):
        for asset, w in zip(selected_assets, rule_weights):
            weights[asset] = w
        logger.debug(f"Weights assigned (before normalization, Date: {price_data.index[-1]}): {weights}")
    else:
        # Hard fail if rules don't match selected assets
        raise ValueError(
            f"Allocation failed: Rule mismatch for algorithm '{algo_name}'. "
            f"Retrieved {len(rule_weights)} weights for top_n={top_n}, but {len(selected_assets)} assets were selected. "
            f"Check allocation rules and asset data availability."
        )

    # Normalize weights and ensure allocation was successful
    total_weight = sum(weights.values())

    if total_weight <= 0:
        # This should not be reached if the logic above is correct, but serves as a final guard
        raise ValueError(
            "Allocation failed: Resulting weights are zero or negative. "
            f"The allocation rules for '{algo_name}' were not applied correctly."
        )

    # Normalize the valid weights to sum to 1.0
    for asset in weights:
        weights[asset] /= total_weight
    logger.debug(f"Final normalized weights (OPTIMIZATION, Date: {price_data.index[-1]}): {weights}")

    logger.debug("Applied ema allocation weights (OPTIMIZATION VERSION)")

    if trace_mode:
        # 1. Ratios (EMAXAvg for the current date)
        # emaxavg is a Series: index=asset, values=EMAXAvg for the current date
        ratios_output = emaxavg.copy()
        ratios_output.name = "EMAXAvg_Ratio"

        # 2. Ranks (Ranked assets with EMAXAvg values and ordinal rank for the current date)
        # ranked_assets is a Series: index=Asset, values=EMAXAvg_Value, sorted descending
        ranks_df = ranked_assets.reset_index()
        ranks_df.columns = ['Asset', 'EMAXAvg_Value']
        ranks_df['Rank_Ordinal'] = range(1, len(ranks_df) + 1)
        # ranks_df = ranks_df.set_index('Asset') # Keep as default index for simpler CSV write if needed by caller

        # 3. Signal (Final allocation weights as a Series for the current date)
        signal_output = pd.Series(weights)
        signal_output.name = "AllocationSignal"

        logger.debug(f"Trace mode enabled (OPTIMIZATION). Returning weights, current ratios/ranks/signals, and full EMA/ratio history DataFrames.")
        # short_ema, med_ema, long_ema are full DataFrames from calculate_ema_metrics or params
        # stmtemax_df, mtltemax_df, emaxavg_df are full DataFrames from calculate_ema_ratios
        return (weights, ratios_output, ranks_df, signal_output,
                short_ema, med_ema, long_ema,
                stmtemax_df, mtltemax_df, emaxavg_df)
    else:
        return weights

def ema_allocation_model_updated_optimization(price_data, returns_data=None, trace_mode=False, settings=None, **params):
    """
    OPTIMIZATION VERSION: EMA-based allocation model that accepts settings directly.
    This ensures compatibility with the signal_history format expected by the test framework.

    Args:
        price_data (DataFrame): Historical price data
        returns_data (DataFrame, optional): Historical returns data (not used in this model)
        settings (dict): Settings dictionary to use instead of loading from file
        **params: Additional parameters

    Uses provided settings instead of loading from CPS_v4 settings files.

    Returns:
        dict: Asset allocations with timestamp keys {pd.Timestamp: {symbol: weight}}
    """
    if settings is None:
        raise ValueError("Settings must be provided for optimization version")

    # Extract parameters from provided settings
    ema_config = settings.get('ema_model', {})
    strategy_config = settings.get('strategy', {})
    system_config = settings.get('system', {})

    def _get_param_from_settings(key):
        """Fetch parameter from settings sections."""
        value = ema_config.get(key, _PARAM_NOT_FOUND)
        if value is _PARAM_NOT_FOUND:
            value = strategy_config.get(key, _PARAM_NOT_FOUND)
        if value is _PARAM_NOT_FOUND:
            value = system_config.get(key, _PARAM_NOT_FOUND)

        if value is _PARAM_NOT_FOUND:
            error_msg = f"Parameter '{key}' not found in '[ema_model]', '[strategy]', or '[system]' sections of provided settings."
            logger.critical(error_msg)
            raise KeyError(error_msg)

        # Handle ComplexN parameters
        if isinstance(value, dict) and 'default_value' in value:
            return value['default_value']
        return _extract_value_from_complexn_dict(value)

    # Extract lookback parameters
    st_lookback = int(_get_param_from_settings('st_lookback'))
    mt_lookback = int(_get_param_from_settings('mt_lookback'))
    lt_lookback = int(_get_param_from_settings('lt_lookback'))

    logger.debug(f"Running optimization EMA allocation model with lookbacks: {st_lookback}, {mt_lookback}, {lt_lookback}")

    # Get the current date from price_data
    if isinstance(price_data.index, pd.DatetimeIndex):
        current_date = price_data.index[-1]
    else:
        current_date = pd.Timestamp.now().normalize()

    # Get system_top_n from settings
    raw_system_top_n = system_config.get('system_top_n', _PARAM_NOT_FOUND)
    if raw_system_top_n is _PARAM_NOT_FOUND:
        error_msg = "Parameter 'system_top_n' not found in '[system]' section."
        logger.critical(error_msg)
        raise KeyError(error_msg)

    system_top_n_val = _extract_value_from_complexn_dict(raw_system_top_n)

    if system_top_n_val is None:
        error_msg = "Parameter 'system_top_n' resolved to None from settings. This is not allowed."
        logger.critical(error_msg)
        raise ValueError(error_msg)
    try:
        current_system_top_n = int(system_top_n_val)
    except (ValueError, TypeError) as e:
        error_msg = f"Parameter 'system_top_n' with value '{system_top_n_val}' could not be converted to int. Error: {e}"
        logger.critical(error_msg)
        raise type(e)(error_msg) from e

    logger.debug(f"Passing system_top_n={current_system_top_n} to ema_allocation_model from optimization version.")

    # Call optimization version of ema_allocation_model with trace_mode
    params_for_model = params.copy()
    params_for_model.pop('system_top_n', None)
    model_output = ema_allocation_model_optimization(
        price_data=price_data,
        returns_data=returns_data,
        system_top_n=current_system_top_n,
        trace_mode=trace_mode,  # Pass trace_mode
        settings=settings,  # Pass settings directly
        **params_for_model
    )

    if trace_mode:
        (weights, ratios_output, ranks_df, signal_output,
         short_ema_df, med_ema_df, long_ema_df,
         stmtemax_hist_df, mtltemax_hist_df, emaxavg_hist_df) = model_output

        # Create historical ranks DataFrame
        historical_ranks_list = []

        # For each date in the historical EMAXAvg data
        for date in emaxavg_hist_df.index:
            # Get EMAXAvg values for this date
            date_emax = emaxavg_hist_df.loc[date]

            # Create a DataFrame with assets and their EMAXAvg values
            date_assets = pd.DataFrame({
                'Asset': date_emax.index,
                'EMAXAvg_Value': date_emax.values
            })

            # Sort by EMAXAvg in descending order and add rank
            date_assets = date_assets.sort_values('EMAXAvg_Value', ascending=False)
            date_assets['Rank_Ordinal'] = range(1, len(date_assets) + 1)
            date_assets['Date'] = date

            # Add to our list
            historical_ranks_list.append(date_assets)

        # Combine all dates into a single DataFrame
        if historical_ranks_list:
            historical_ranks_df = pd.concat(historical_ranks_list, ignore_index=True)
            # Reorder columns for consistency
            historical_ranks_df = historical_ranks_df[['Date', 'Asset', 'EMAXAvg_Value', 'Rank_Ordinal']]

            # Update the ranks_df to include all historical ranks
            ranks_df = historical_ranks_df

            logger.debug(f"Generated historical ranks for {len(historical_ranks_df['Date'].unique())} dates")
        else:
            logger.warning("No historical ranks were generated")

        logger.debug(f"Trace mode enabled in optimization model. Returning dict_weights, current ratios/ranks/signals, and historical EMA/ratio DataFrames.")
        return ({current_date: weights}, ratios_output, ranks_df, signal_output,
                short_ema_df, med_ema_df, long_ema_df,
                stmtemax_hist_df, mtltemax_hist_df, emaxavg_hist_df)
    else:
        weights = model_output # Original behavior, model_output is just weights
        # Return a dictionary with the current date as a Timestamp key
        return {current_date: weights}

def run_ema_model_with_tracing_optimization(price_data, settings, **params):
    """
    OPTIMIZATION VERSION: Run the EMA allocation model in trace mode with settings passed directly.
    
    Args:
        price_data (DataFrame): Historical price data
        settings (dict): Settings dictionary to use instead of loading from file
        **params: Additional parameters for the EMA model
        
    Returns:
        dict: Asset allocations with timestamp keys {pd.Timestamp: {symbol: weight}}
    """
    logger.info("Running EMA model with tracing enabled (OPTIMIZATION VERSION)")
    
    # Run the EMA model in trace mode with settings passed directly
    trace_results = ema_allocation_model_updated_optimization(
        price_data=price_data,
        trace_mode=True,
        settings=settings,  # Pass settings directly
        **params
    )
    
    # Unpack the results
    (dict_weights, ratios_output, ranks_df, signal_output, 
     short_ema_df, med_ema_df, long_ema_df,
     stmtemax_hist_df, mtltemax_hist_df, emaxavg_hist_df) = trace_results
    
    # Save the detailed signal history data to CSV files
    try:
        # Save EMA averages with current timestamp
        current_timestamp = pd.Timestamp.now().strftime("%Y%m%d_%H%M%S")
        
        # Save individual EMA dataframes to match expected filenames
        # Save short EMA
        save_df_to_trace_dir(short_ema_df, f"ema_short_{current_timestamp}.csv",
                            step_description="Short-term EMA History")
        
        # Save medium EMA
        save_df_to_trace_dir(med_ema_df, f"ema_medium_{current_timestamp}.csv",
                            step_description="Medium-term EMA History")
        
        # Save long EMA
        save_df_to_trace_dir(long_ema_df, f"ema_long_{current_timestamp}.csv",
                            step_description="Long-term EMA History")
        
        # WORKING - ONLY CHANGE WITH PERMISSION
        # Format ranking data as a matrix with multi-level columns
        # Convert ranks_df to a pivoted format with Date as index and multi-level columns
        if 'Date' in ranks_df.columns and 'Asset' in ranks_df.columns:
            # Create a copy to avoid modifying the original
            ranks_pivot_df = ranks_df.copy()
            
            # Ensure Date is datetime
            ranks_pivot_df['Date'] = pd.to_datetime(ranks_pivot_df['Date'])
            
            # Create separate DataFrames for EMAXAvg_Value and Rank_Ordinal
            emaxavg_df = ranks_pivot_df.pivot(index='Date', columns='Asset', values='EMAXAvg_Value')
            rank_df = ranks_pivot_df.pivot(index='Date', columns='Asset', values='Rank_Ordinal')
            
            # Create multi-level columns
            emaxavg_df.columns = pd.MultiIndex.from_product([['EMAXAvg_Value'], emaxavg_df.columns])
            rank_df.columns = pd.MultiIndex.from_product([['Rank_Ordinal'], rank_df.columns])
            
            # Combine into a single DataFrame with multi-level columns
            matrix_ranks_df = pd.concat([emaxavg_df, rank_df], axis=1)
            
            # Save the matrix-formatted ranking data
            save_df_to_trace_dir(matrix_ranks_df, f"ranking_{current_timestamp}.csv",
                                step_description="Asset Ranking History (Matrix Format)")
        else:
            # Fallback to original format if expected columns are missing
            logger.warning("Cannot create matrix format for ranking data - missing required columns")
            save_df_to_trace_dir(ranks_df, f"ranking_{current_timestamp}.csv",
                                step_description="Asset Ranking History")
        
        # Signal history full CSV generation removed as requested
        
        # Also keep the original combined files for backward compatibility
        # Save combined EMA averages
        ema_averages = pd.concat([short_ema_df, med_ema_df, long_ema_df], axis=1)
        ema_averages.columns = [f"{col}_ST" for col in short_ema_df.columns] + \
                              [f"{col}_MT" for col in med_ema_df.columns] + \
                              [f"{col}_LT" for col in long_ema_df.columns]
        save_df_to_trace_dir(ema_averages, f"02_ema_average_history_{current_timestamp}.csv", 
                            step_description="Combined EMA Averages History")
        
        # Save raw signal history (EMA ratios)
        raw_signals = pd.concat([stmtemax_hist_df, mtltemax_hist_df, emaxavg_hist_df], axis=1)
        raw_signals.columns = [f"{col}_STMTEMAX" for col in stmtemax_hist_df.columns] + \
                              [f"{col}_MTLTEMAX" for col in mtltemax_hist_df.columns] + \
                              [f"{col}_EMAXAvg" for col in emaxavg_hist_df.columns]
        save_df_to_trace_dir(raw_signals, f"04_raw_algocalc_history_{current_timestamp}.csv", 
                            step_description="Raw Algorithm Calculation History")
        
        logger.info("Successfully saved detailed signal history data to CSV files including individual EMA files")
    except Exception as e:
        logger.error(f"Failed to save detailed signal history data: {e}")
    
    # Create a proper date matrix DataFrame for the full signal history
    # Initialize with zeros, using price_data's dates and tickers
    signal_history = pd.DataFrame(0.0, index=price_data.index, columns=price_data.columns)
    signal_history.index.name = 'Date'
    
    logger.info(f"Initialized signal_history DataFrame with shape {signal_history.shape}")
    logger.info(f"Signal history date range: {signal_history.index[0]} to {signal_history.index[-1]}")
    logger.info(f"Ranks DataFrame shape: {ranks_df.shape}")
    logger.info(f"Ranks date range: {ranks_df.index[0]} to {ranks_df.index[-1]}")
    
    # Log initial ranks_df info
    logger.info(f"Initial ranks_df info:")
    logger.info(f"Index type: {type(ranks_df.index)}")
    logger.info(f"Columns: {ranks_df.columns.tolist()}")
    logger.info(f"Sample data:\n{ranks_df.head(2)}")
    
    # The ranks_df has columns: ['Asset', 'EMAXAvg_Value', 'Rank_Ordinal']
    # We need to ensure we have a proper date index from price_data
    allocation_count = 0
    signal_history = pd.DataFrame(0.0, index=price_data.index, columns=price_data.columns)
    signal_history.index.name = 'Date'
    
    try:
        # First, ensure we have a date column in ranks_df
        if 'Date' not in ranks_df.columns:
            # If not, we'll assume ranks_df is for the last date in price_data
            last_date = price_data.index[-1]
            ranks_df['Date'] = last_date
        
        # Convert Date to datetime if it's not already
        ranks_df['Date'] = pd.to_datetime(ranks_df['Date'])
        
        # Pivot the ranks DataFrame to get assets as columns
        ranks_pivot = ranks_df.pivot(index='Date', columns='Asset', values='Rank_Ordinal')
        logger.info(f"Pivoted ranks DataFrame shape: {ranks_pivot.shape}")
        logger.info(f"Pivoted columns: {ranks_pivot.columns.tolist()}")
        
        # For each date in price data, get the most recent ranks as of that date
        for i, date in enumerate(price_data.index):
            try:
                # Get all ranks up to and including the current date
                current_ranks = ranks_pivot[ranks_pivot.index <= date]
                
                if len(current_ranks) == 0:
                    logger.warning(f"No ranks available for {date}")
                    continue
                    
                # Get the most recent ranks
                latest_ranks = current_ranks.iloc[-1].dropna()
                
                if len(latest_ranks) >= 2:
                    # Sort by rank (ascending) and get top 2 assets
                    top_assets = latest_ranks.sort_values().head(2).index.tolist()
                    
                    if len(top_assets) >= 2:
                        # Allocate 60% to top asset, 40% to second
                        signal_history.loc[date, top_assets[0]] = 0.6
                        signal_history.loc[date, top_assets[1]] = 0.4
                        allocation_count += 1
                        
                        # Log only occasionally to avoid flooding the logs
                        if i % 50 == 0 or i == len(price_data) - 1:
                            logger.info(f"{date}: Allocated 60% to {top_assets[0]}, 40% to {top_assets[1]}")
                
            except Exception as e:
                logger.error(f"Error processing date {date}: {e}", exc_info=True)
                continue
                
    except Exception as e:
        logger.error(f"Error in signal generation: {e}", exc_info=True)
        # Fall back to empty signal history
        signal_history = pd.DataFrame(0.0, index=price_data.index, columns=price_data.columns)
    
    logger.info(f"Signal generation complete. Made {allocation_count} allocations.")
    logger.info(f"Final signal_history shape: {signal_history.shape}")
    logger.info(f"Non-zero allocations: {(signal_history != 0).sum().sum()}")
    
    return signal_history
            
# ---------------------------------------------------------------------------
# Attach class-like interface so backtest_v4.py can treat this function as an
# object with generate_signals/validate_signals methods (backward compatibility)
# ---------------------------------------------------------------------------

def _bridge_generate_signals_optimization(price_data, settings, **params):
    """Alias for run_ema_model_with_tracing_optimization so backtest expects .generate_signals."""
    return run_ema_model_with_tracing_optimization(price_data, settings, **params)

def _bridge_validate_signals_optimization(self, signals):
    """Placeholder validator – passthrough for now."""
    return signals

# Monkey-patch the function to have expected attributes
run_ema_model_with_tracing_optimization.generate_signals = _bridge_generate_signals_optimization  # type: ignore
run_ema_model_with_tracing_optimization.validate_signals = _bridge_validate_signals_optimization  # type: ignore
