"""
utils/allocation_debug.py
Utility module for debugging allocation data in the backtest system.
Provides functions to export and analyze allocation data at different stages.
"""

import pandas as pd
import numpy as np
from datetime import datetime
import os
from pathlib import Path
import logging

# Configure logging
logger = logging.getLogger(__name__)

def export_allocation_data(data_dict, stage_name, output_dir="debug_allocations"):
    """Export allocation data for debugging.
    
    Args:
        data_dict (dict): Dictionary of DataFrames to export
        stage_name (str): Name of the allocation stage (e.g., 'signals', 'weights')
        output_dir (str): Directory to save outputs
    """
    # Skip if debug mode not enabled
    if os.getenv("EXPORT_ALLOCATIONS", "").lower() != "true":
        return
        
    # Create output directory if it doesn't exist
    Path(output_dir).mkdir(exist_ok=True)
    
    # Process and export each DataFrame
    for name, data in data_dict.items():
        if data is None:
            logger.warning(f"Cannot export {name} at stage {stage_name}: Data is None")
            continue
            
        if not hasattr(data, 'to_csv'):
            logger.warning(f"Cannot export {name} at stage {stage_name}: Not a DataFrame")
            continue
        
        # Get timestamps
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Create filenames
        csv_file = Path(output_dir) / f"{stage_name}_{name}_{timestamp}.csv"
        txt_file = Path(output_dir) / f"{stage_name}_{name}_{timestamp}.txt"
        
        # Export as CSV for machine processing
        data.to_csv(csv_file)
        
        # Export as readable text for human/AI review
        with open(txt_file, "w") as f:
            f.write(f"=== {stage_name}: {name} ===\n\n")
            f.write(f"Shape: {data.shape}\n")
            f.write(f"Columns: {list(data.columns)}\n")
            f.write(f"Index: {list(data.index)[:5]}... (truncated)\n\n")
            f.write("First 10 rows:\n")
            f.write(str(data.head(10)))
            f.write("\n\nLast 10 rows:\n")
            f.write(str(data.tail(10)))
            
        logger.info(f"Exported {name} at stage {stage_name} to {csv_file} and {txt_file}")

def compare_allocations(signal_history, weights_history, output_dir="debug_allocations"):
    """Compare signal history (targets) with weights history (actuals).
    
    Args:
        signal_history (DataFrame): Target allocations
        weights_history (DataFrame): Actual allocations
        output_dir (str): Directory to save outputs
    """
    # Skip if debug mode not enabled
    if os.getenv("EXPORT_ALLOCATIONS", "").lower() != "true":
        return
        
    # Create output directory if it doesn't exist
    Path(output_dir).mkdir(exist_ok=True)
    
    # Get timestamps
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # Create filename
    txt_file = Path(output_dir) / f"allocation_comparison_{timestamp}.txt"
    
    # Calculate differences
    if signal_history is not None and weights_history is not None:
        try:
            # Align indices
            common_idx = signal_history.index.intersection(weights_history.index)
            if len(common_idx) > 0:
                s_aligned = signal_history.loc[common_idx]
                w_aligned = weights_history.loc[common_idx]
                
                # Calculate absolute differences
                diff = (w_aligned - s_aligned).abs()
                
                # Export comparison
                with open(txt_file, "w") as f:
                    f.write("=== ALLOCATION COMPARISON ===\n\n")
                    f.write(f"Signal History Shape: {signal_history.shape}\n")
                    f.write(f"Weights History Shape: {weights_history.shape}\n")
                    f.write(f"Common Dates: {len(common_idx)}\n\n")
                    
                    f.write("Average Absolute Difference by Asset:\n")
                    f.write(str(diff.mean()) + "\n\n")
                    
                    f.write("Maximum Absolute Difference by Asset:\n")
                    f.write(str(diff.max()) + "\n\n")
                    
                    f.write("Sample Differences (First 5 dates):\n")
                    for date in common_idx[:5]:
                        f.write(f"\nDate: {date}\n")
                        f.write("Signal (Target):\n")
                        f.write(str(s_aligned.loc[date]) + "\n")
                        f.write("Weight (Actual):\n")
                        f.write(str(w_aligned.loc[date]) + "\n")
                        f.write("Difference:\n")
                        f.write(str(diff.loc[date]) + "\n")
                        
                logger.info(f"Allocation comparison saved to {txt_file}")
            else:
                logger.warning("No common dates found between signal and weights history")
        except Exception as e:
            logger.error(f"Error comparing allocations: {e}")
    else:
        logger.warning("Cannot compare allocations: One or both histories are None")
