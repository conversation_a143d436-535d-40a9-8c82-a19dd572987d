#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
v4/run_unified_pipeline.py

Unified Pipeline Shell for CPS v4 Backtest System (REFACTORED)
Main entry point that orchestrates the complete signal generation and trading workflow.

This module serves as the main shell file after refactoring the original 713-line monolithic file.
The core functionality has been broken into smaller modules in v4/pipeline/:
- config.py: Configuration, logging setup, and CLI argument parsing
- modes.py: Pipeline execution modes (optimization and single run)
- trading.py: Trading operations and signal handling

This shell file now contains only:
- Main router function (run_unified_pipeline)
- Main entry point (main)
- Import coordination from feeder modules

ALL EXISTING FUNCTIONALITY IS PRESERVED EXACTLY AS IT WAS.

Author: AI Assistant
Date: 2025-07-26 (Refactored from original 713-line file)
"""

import sys
from typing import Optional, Dict, Any

# Import all pipeline functions from feeder modules
from v4.pipeline.config import setup_logger, parse_cli_arguments, determine_pipeline_mode
from v4.pipeline.modes import run_optimization_pipeline, run_single_pipeline, run_baseline_optimization_iteration
from v4.pipeline.trading import modify_run_trading_to_accept_dataframe


def run_unified_pipeline(custom_settings_file: Optional[str] = None,
                        signals_file: Optional[str] = None,
                        skip_signal_generation: bool = False) -> Dict[str, Any]:
    """Main router function that determines pipeline mode and executes appropriately.
    
    This is the MAIN ENTRY POINT that replaces the old monolithic function.
    It routes to either optimization or single-run pipeline based on settings.
    
    Args:
        custom_settings_file: Optional path to custom settings file
        signals_file: Optional path to pre-computed signals file (skips signal generation)
        skip_signal_generation: If True, skip signal generation and use existing signals
        
    Returns:
        Dictionary containing results from the appropriate pipeline
    """
    # Step 1: Determine pipeline mode FIRST (before any signal/trading logic)
    mode, settings, num_combinations = determine_pipeline_mode(custom_settings_file)
    
    print(f"\n[PIPELINE MODE] {mode.upper()}")
    if mode == 'optimization':
        print(f"   Will test {num_combinations} parameter combinations")
    else:
        print(f"   Single run with default parameters")
    print()
    
    # Step 2: Route to appropriate pipeline
    if mode == 'optimization':
        return run_optimization_pipeline(settings, num_combinations)
    else:
        return run_single_pipeline(settings, signals_file, skip_signal_generation, custom_settings_file)


def main() -> None:
    """Main entry point for the unified pipeline."""
    try:
        # Parse CLI arguments
        args = parse_cli_arguments()
        
        # Run the unified pipeline
        results = run_unified_pipeline(
            custom_settings_file=args.settings,
            signals_file=args.signals,
            skip_signal_generation=args.skip_signals
        )
        
        # Exit successfully
        sys.exit(0)
        
    except KeyboardInterrupt:
        print("\nPipeline interrupted by user")
        sys.exit(1)
        
    except Exception as e:
        print(f"\nPipeline failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
