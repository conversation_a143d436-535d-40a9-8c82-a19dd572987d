"""
Simple verification test for execution delay parameter fix.
"""
import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

# Import necessary modules
from engine.backtest import BacktestEngine

# Create test data
import pandas as pd
import numpy as np
from datetime import datetime

# Create minimal price data
dates = pd.date_range(start='2025-01-01', periods=5)
price_data = pd.DataFrame({
    'SPY': [100, 101, 102, 103, 104],
    'TLT': [90, 91, 92, 93, 94]
}, index=dates)

# Define a simple signal generator
def dummy_signal(price_data, **params):
    return {'SPY': 0.6, 'TLT': 0.4}

# Test with direct value
print("Test 1: Using direct value (execution_delay=1)")
try:
    engine1 = BacktestEngine()
    result1 = engine1.run_backtest(
        price_data=price_data,
        signal_generator=dummy_signal,
        execution_delay=1
    )
    print("✓ Success: Backtest ran with direct value")
except Exception as e:
    print(f"✗ Error: {e}")

# Test with parameter tuple
print("\nTest 2: Using parameter tuple (execution_delay=('Y', 1, 0, 3, 1))")
try:
    engine2 = BacktestEngine()
    result2 = engine2.run_backtest(
        price_data=price_data,
        signal_generator=dummy_signal,
        execution_delay=('Y', 1, 0, 3, 1)
    )
    print("✓ Success: Backtest ran with parameter tuple")
except Exception as e:
    print(f"✗ Error: {e}")

print("\nVerification complete.")
