#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Test script for optimization validation with unified portfolio file solution.

This script tests the complete optimization validation flow:
1. Enable CSV generation for testing
2. Run a small optimization validation
3. Verify file collision prevention
4. Check that unique files are created per combination

Author: AI Assistant
Date: 2025-07-27
"""

import sys
import os
from pathlib import Path
import configparser
import shutil
from datetime import datetime

# Add project root to path
_script_path = Path(__file__).resolve()
_project_root = _script_path.parent
if str(_project_root) not in sys.path:
    sys.path.insert(0, str(_project_root))

# Import required modules
from v4.py_reporting.performance_table_generator import PerformanceTableGenerator
from v4.config.paths_v4 import V4_SETTINGS_FILE, OUTPUT_DIR, OPTIMIZATION_VALIDATION_DIR

def backup_settings():
    """Create backup of current settings."""
    backup_path = V4_SETTINGS_FILE.with_suffix('.ini.backup')
    shutil.copy2(V4_SETTINGS_FILE, backup_path)
    print(f"✅ Settings backed up to: {backup_path}")
    return backup_path

def restore_settings(backup_path):
    """Restore settings from backup."""
    if backup_path.exists():
        shutil.copy2(backup_path, V4_SETTINGS_FILE)
        backup_path.unlink()
        print(f"✅ Settings restored from backup")

def enable_csv_for_testing():
    """Temporarily enable CSV generation for testing."""
    config = configparser.ConfigParser()
    config.read(V4_SETTINGS_FILE)
    
    # Enable CSV generation
    config.set('System', 'csv_flag_use', 'True')
    config.set('System', 'csv_valid_det', 'False')  # Skip extra files during optimization
    config.set('System', 'optimization_active', 'True')
    
    # Reduce optimization scope for testing
    config.set('Strategy', 'st_lookback', '(optimize=True, default_value=15, min_value=10, max_value=20, increment=5)')
    config.set('Strategy', 'mt_lookback', '(optimize=False, default_value=70, min_value=50, max_value=100, increment=10)')
    config.set('Core', 'top_n', '(optimize=True, default_value=2, min_value=1, max_value=3, increment=1)')
    
    with open(V4_SETTINGS_FILE, 'w') as f:
        config.write(f)
    
    print("✅ CSV generation enabled for testing")
    print("✅ Optimization scope reduced for testing")

def test_validation_setup():
    """Test the validation setup and directory creation."""
    print("="*60)
    print("TEST 1: Validation Setup")
    print("="*60)
    
    generator = PerformanceTableGenerator()
    generator.validation_mode = True
    generator._setup_validation_directories()
    
    # Check if validation directory was created
    if generator.validation_dir.exists():
        print(f"✅ Validation directory created: {generator.validation_dir}")
        
        # Check for flat structure (no subfolders)
        subdirs = [d for d in generator.validation_dir.iterdir() if d.is_dir()]
        if len(subdirs) <= 1:  # Only logs directory allowed
            print("✅ Flat directory structure confirmed")
        else:
            print(f"❌ Unexpected subdirectories found: {subdirs}")
            return False
            
        return True
    else:
        print("❌ Validation directory not created")
        return False

def test_combination_generation():
    """Test optimization combination generation."""
    print("="*60)
    print("TEST 2: Combination Generation")
    print("="*60)
    
    generator = PerformanceTableGenerator()
    
    # Get optimization combinations
    combinations = generator._get_optimization_combinations()
    
    print(f"Generated {len(combinations)} optimization combinations:")
    for i, combo in enumerate(combinations[:5]):  # Show first 5
        combo_id = generator.generate_combo_id(combo)
        print(f"  {i+1}: {combo} → {combo_id}")
    
    if len(combinations) > 5:
        print(f"  ... and {len(combinations) - 5} more combinations")
    
    # Verify combinations are unique
    combo_ids = [generator.generate_combo_id(combo) for combo in combinations]
    unique_ids = set(combo_ids)
    
    if len(combo_ids) == len(unique_ids):
        print(f"✅ All {len(combinations)} combination IDs are unique")
        return True
    else:
        duplicates = len(combo_ids) - len(unique_ids)
        print(f"❌ Found {duplicates} duplicate combination IDs")
        return False

def test_file_naming_logic():
    """Test the file naming logic for different modes."""
    print("="*60)
    print("TEST 3: File Naming Logic")
    print("="*60)
    
    generator = PerformanceTableGenerator()
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # Test combination
    test_combo = {'st_lookback': 15, 'mt_lookback': 70, 'top_n': 2, 'execution_delay': 1}
    combo_id = generator.generate_combo_id(test_combo)
    
    # Test file naming patterns
    test_cases = [
        ("Single mode", None, False, f"unified_portfolio_{timestamp}.csv"),
        ("Optimization mode", combo_id, True, f"unified_portfolio_combo_{combo_id}_{timestamp}.csv"),
    ]
    
    for description, combo_id_param, opt_active, expected_pattern in test_cases:
        print(f"{description}:")
        print(f"  combo_id: {combo_id_param}")
        print(f"  optimization_active: {opt_active}")
        print(f"  Expected pattern: {expected_pattern}")
        
        # Verify pattern contains combo_id when expected
        if opt_active and combo_id_param:
            if combo_id_param in expected_pattern:
                print("  ✅ Combination ID included in filename")
            else:
                print("  ❌ Combination ID missing from filename")
                return False
        else:
            if "combo_" not in expected_pattern:
                print("  ✅ Standard filename for single mode")
            else:
                print("  ❌ Unexpected combo pattern in single mode")
                return False
    
    print("✅ File naming logic test completed")
    return True

def test_csv_flag_behavior():
    """Test CSV flag behavior in different scenarios."""
    print("="*60)
    print("TEST 4: CSV Flag Behavior")
    print("="*60)
    
    generator = PerformanceTableGenerator()
    
    print(f"Current settings:")
    print(f"  csv_flag_use: {generator.csv_flag_use}")
    print(f"  csv_valid_det: {generator.csv_valid_det}")
    print(f"  optimization_active: {generator.optimization_active}")
    
    # Test scenarios
    scenarios = [
        ("Optimization with csv_valid_det=False", True, True, False),
        ("Optimization with csv_valid_det=True", True, True, True),
        ("Single mode with csv_flag_use=True", True, False, False),
    ]
    
    for description, csv_flag, opt_active, csv_valid in scenarios:
        should_generate_extra = csv_flag and (not opt_active or csv_valid)
        should_generate_unified = csv_flag
        
        print(f"\n{description}:")
        print(f"  Should generate unified portfolio: {should_generate_unified}")
        print(f"  Should generate extra files: {should_generate_extra}")
        
        if opt_active and not csv_valid:
            print("  ✅ Extra files skipped during optimization (prevents collisions)")
        elif not opt_active:
            print("  ✅ All files generated in single mode")
        else:
            print("  ✅ All files generated in optimization debug mode")
    
    print("\n✅ CSV flag behavior test completed")
    return True

def cleanup_test_files():
    """Clean up any test files created."""
    print("="*60)
    print("CLEANUP")
    print("="*60)
    
    # Clean up validation directories
    if OPTIMIZATION_VALIDATION_DIR.exists():
        for item in OPTIMIZATION_VALIDATION_DIR.iterdir():
            if item.is_dir() and item.name.startswith('2025'):
                try:
                    shutil.rmtree(item)
                    print(f"✅ Cleaned up validation directory: {item}")
                except Exception as e:
                    print(f"⚠️  Could not clean up {item}: {e}")
    
    # Clean up any test unified portfolio files
    if OUTPUT_DIR.exists():
        for item in OUTPUT_DIR.glob("unified_portfolio_combo_*_test_*.csv"):
            try:
                item.unlink()
                print(f"✅ Cleaned up test file: {item}")
            except Exception as e:
                print(f"⚠️  Could not clean up {item}: {e}")

def main():
    """Run all validation tests."""
    print("OPTIMIZATION VALIDATION FIX - COMPREHENSIVE TESTS")
    print("="*60)
    
    # Backup current settings
    backup_path = backup_settings()
    
    try:
        # Enable CSV for testing
        enable_csv_for_testing()
        
        # Run tests
        tests = [
            test_validation_setup,
            test_combination_generation,
            test_file_naming_logic,
            test_csv_flag_behavior,
        ]
        
        passed = 0
        failed = 0
        
        for test_func in tests:
            try:
                if test_func():
                    passed += 1
                else:
                    failed += 1
            except Exception as e:
                print(f"❌ Test {test_func.__name__} failed with exception: {e}")
                import traceback
                traceback.print_exc()
                failed += 1
            print()
        
        print("="*60)
        print("TEST SUMMARY")
        print("="*60)
        print(f"Tests passed: {passed}")
        print(f"Tests failed: {failed}")
        print(f"Total tests: {passed + failed}")
        
        if failed == 0:
            print("🎉 ALL VALIDATION TESTS PASSED!")
            print("\n✅ The unified portfolio file solution is ready for deployment!")
            success = True
        else:
            print("❌ Some validation tests failed")
            success = False
            
    finally:
        # Always restore settings and cleanup
        restore_settings(backup_path)
        cleanup_test_files()
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
