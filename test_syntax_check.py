#!/usr/bin/env python
"""
Test syntax of performance_table_generator.py
"""

import sys
import ast

def check_syntax(file_path):
    """Check Python syntax of a file."""
    try:
        with open(file_path, 'r') as f:
            source = f.read()
        
        # Try to parse the AST
        ast.parse(source)
        print(f"✅ Syntax check PASSED for {file_path}")
        return True
        
    except SyntaxError as e:
        print(f"❌ Syntax error in {file_path}:")
        print(f"  Line {e.lineno}: {e.text}")
        print(f"  Error: {e.msg}")
        return False
    except Exception as e:
        print(f"❌ Error checking {file_path}: {e}")
        return False

if __name__ == "__main__":
    file_path = "v4/reporting/performance_table_generator.py"
    success = check_syntax(file_path)
    
    if success:
        print("\n✅ File syntax is valid")
        
        # Try to import the class
        try:
            sys.path.append('v4')
            from reporting.performance_table_generator import PerformanceTableGenerator
            print("✅ Class import successful")
            
            # Check if method exists
            if hasattr(PerformanceTableGenerator, '_get_optimization_combinations'):
                print("✅ _get_optimization_combinations method found")
            else:
                print("❌ _get_optimization_combinations method NOT found")
                
        except Exception as e:
            print(f"❌ Import error: {e}")
    else:
        print("\n❌ File has syntax errors")
