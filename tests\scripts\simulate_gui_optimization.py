"""
Simulate GUI optimization of execution_delay parameter.
This script will reproduce the exact scenario that causes the error in the GUI.
"""

import sys
import os
from pathlib import Path
import logging
import traceback

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

from config.paths import OUTPUT_DIR

# Define output file
output_file = OUTPUT_DIR / "debug" / "gui_optimization_simulation.txt"
os.makedirs(output_file.parent, exist_ok=True)

with open(output_file, 'w') as f:
    f.write("GUI OPTIMIZATION SIMULATION\n")
    f.write("==========================\n\n")
    
    try:
        f.write("Step 1: Importing modules\n")
        from config.config_v2 import config_v2
        from config.local_parameter_optimization import define_parameter, get_parameter_combinations
        from engine.backtest import BacktestEngine
        from run_backtest_v2_with_metrics import run_backtest_with_metrics
        f.write("SUCCESS: All modules imported\n\n")
    except Exception as e:
        f.write(f"ERROR: Failed to import modules: {e}\n")
        f.write(traceback.format_exc())
        f.write("\n")
        sys.exit(1)
    
    # Simulate what the GUI does when optimizing execution_delay
    try:
        f.write("Step 2: Simulating GUI optimization setup\n")
        
        # Create a modified config with execution_delay set to be optimized
        modified_config = config_v2.copy()
        
        # This is what happens when the user checks the "Optimize" checkbox for execution_delay
        modified_config['backtest_params'] = config_v2['backtest_params'].copy()
        modified_config['backtest_params']['execution_delay'] = ('Y', 1, 0, 3, 1)  # optimize=True, default=1, min=0, max=3, step=1
        
        f.write(f"Modified config execution_delay: {modified_config['backtest_params']['execution_delay']}\n")
        f.write("SUCCESS: Created modified config with optimizable execution_delay\n\n")
    except Exception as e:
        f.write(f"ERROR: Failed to create modified config: {e}\n")
        f.write(traceback.format_exc())
        f.write("\n")
    
    # Create parameter combinations
    try:
        f.write("Step 3: Creating parameter combinations\n")
        
        # Extract execution_delay parameter
        exec_delay_param = modified_config['backtest_params']['execution_delay']
        
        # Create parameter dictionary (similar to what happens in run_backtest_with_metrics)
        param_dict = {'execution_delay': exec_delay_param}
        
        # Get parameter combinations
        param_combinations = get_parameter_combinations(param_dict)
        
        f.write(f"Parameter combinations: {param_combinations}\n")
        f.write(f"Number of combinations: {len(param_combinations)}\n")
        f.write("SUCCESS: Created parameter combinations\n\n")
    except Exception as e:
        f.write(f"ERROR: Failed to create parameter combinations: {e}\n")
        f.write(traceback.format_exc())
        f.write("\n")
    
    # Create test data
    try:
        f.write("Step 4: Creating minimal test data\n")
        import pandas as pd
        import numpy as np
        
        # Create minimal price data
        dates = pd.date_range(start='2025-01-01', periods=10)
        price_data = pd.DataFrame({
            'SPY': np.linspace(100, 110, 10),
            'TLT': np.linspace(90, 100, 10)
        }, index=dates)
        
        f.write("SUCCESS: Created test data\n\n")
    except Exception as e:
        f.write(f"ERROR: Failed to create test data: {e}\n")
        f.write(traceback.format_exc())
        f.write("\n")
    
    # Simulate the optimization loop
    try:
        f.write("Step 5: Simulating optimization loop\n")
        f.write("This simulates what happens when the GUI runs multiple backtests with different execution_delay values\n\n")
        
        for i, params in enumerate(param_combinations):
            f.write(f"Running combination {i+1}/{len(param_combinations)}: {params}\n")
            
            try:
                # Create engine with the current parameters
                engine = BacktestEngine()
                
                # Define a simple signal generator
                def dummy_signal(price_data, **kwargs):
                    return {'SPY': 0.6, 'TLT': 0.4}
                
                # Run backtest with the current execution_delay value
                result = engine.run_backtest(
                    price_data=price_data,
                    signal_generator=dummy_signal,
                    execution_delay=params['execution_delay']
                )
                
                f.write(f"  SUCCESS: Ran backtest with execution_delay={params['execution_delay']}\n")
            except Exception as e:
                f.write(f"  ERROR: Failed to run backtest with execution_delay={params['execution_delay']}: {e}\n")
                f.write("  " + traceback.format_exc().replace("\n", "\n  "))
                f.write("\n")
        
        f.write("\nSimulation of optimization loop complete\n\n")
    except Exception as e:
        f.write(f"ERROR: Failed during optimization simulation: {e}\n")
        f.write(traceback.format_exc())
        f.write("\n")
    
    # Try to run the actual run_backtest_with_metrics function
    try:
        f.write("Step 6: Testing run_backtest_with_metrics function\n")
        f.write("This is what actually runs when the GUI calls the backtest function\n\n")
        
        # Modify config to use our test data
        test_config = modified_config.copy()
        
        # Attempt to run the function
        try:
            results = run_backtest_with_metrics(
                config=test_config,
                debug=True,
                debug_paramflow=True
            )
            f.write("SUCCESS: run_backtest_with_metrics completed without errors\n")
        except Exception as e:
            f.write(f"ERROR: run_backtest_with_metrics failed: {e}\n")
            f.write(traceback.format_exc())
            f.write("\n")
            
            # Analyze the error
            error_str = str(e)
            if "isinstance()" in error_str and "arg 2" in error_str:
                f.write("\nANALYSIS: This is the 'isinstance() arg 2' error you're seeing in the GUI.\n")
                
                # Look for the problematic code in the BacktestEngine class
                import inspect
                
                # Check the BacktestEngine.run_backtest method
                run_backtest_source = inspect.getsource(BacktestEngine.run_backtest)
                
                # Look for get_param_value calls before docstring
                docstring_pos = run_backtest_source.find('"""')
                pre_docstring = run_backtest_source[:docstring_pos]
                
                if "get_param_value" in pre_docstring:
                    f.write("\nROOT CAUSE: get_param_value is called BEFORE the docstring in BacktestEngine.run_backtest.\n")
                    f.write("This causes Python to interpret the code incorrectly.\n")
                    f.write("The fix is to move these calls AFTER the docstring.\n\n")
                    
                    f.write("Problematic code:\n")
                    f.write(pre_docstring)
                    f.write("\n\n")
                
                # Look for isinstance calls with date
                f.write("Looking for isinstance calls with date...\n")
                from datetime import date
                
                # Find all lines with isinstance and date
                lines = run_backtest_source.split("\n")
                for i, line in enumerate(lines):
                    if "isinstance" in line and "date" in line:
                        f.write(f"Line {i+1}: {line.strip()}\n")
                
                f.write("\nSOLUTION: Move the get_param_value calls AFTER the docstring in BacktestEngine.run_backtest.\n")
    
    f.write("\nSimulation complete. Check the file for detailed results.\n")

print(f"GUI optimization simulation results saved to: {output_file}")
