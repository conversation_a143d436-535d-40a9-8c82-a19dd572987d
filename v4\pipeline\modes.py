#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
v4/pipeline/modes.py

Pipeline Execution Modes for CPS v4 Pipeline

This module contains pipeline mode execution functions extracted from run_unified_pipeline.py:
- run_optimization_pipeline(): Run the complete optimization pipeline
- run_single_pipeline(): Run the complete single pipeline (signal generation + trading)
- run_baseline_optimization_iteration(): Run baseline iteration for optimization

These functions handle the core pipeline execution logic for both optimization and single-run modes.
All functions preserve their original functionality and imports exactly as they were.

Author: AI Assistant
Date: 2025-07-26
"""

import os
from datetime import datetime
from pathlib import Path
from typing import Optional, Dict, Any
import pandas as pd

# Import centralized path configuration
from v4.config.paths_v4 import (
    V4_SETTINGS_FILE, V4_TRACE_OUTPUTS_DIR, OUTPUT_DIR, 
    get_reporting_file_path, EQUITY_CURVE_STRATEGY_LATEST
)

# Import pipeline components
from .config import setup_logger
from .trading import modify_run_trading_to_accept_dataframe

# Import core V4 components
from v4.Algo_signal_phase import run_signal_phase
from v4.utils.log_milestone import log_milestone, log_phase_start, log_phase_complete, log_error_milestone
from v4.utils.tracing_utils import save_df_to_trace_dir


def _create_unified_portfolio_data(results, timestamp):
    """Create unified portfolio DataFrame with dollar positions and portfolio values.

    Args:
        results: Pipeline results dictionary containing portfolio_values, allocation_history, etc.
        timestamp: Timestamp for the data

    Returns:
        pd.DataFrame: Unified portfolio data with columns:
            Date, Cash_USD, [Ticker]_USD, Total_USD, Portfolio_Value, Daily_Return, Cumulative_Return
    """
    import pandas as pd
    import numpy as np

    # Get portfolio values (equity curve)
    portfolio_values = results.get('portfolio_values', None)
    if portfolio_values is None:
        raise ValueError("No portfolio_values found in results")

    # Get allocation history if available
    allocation_history = results.get('allocation_history', None)

    # Create base DataFrame with dates from portfolio values
    if hasattr(portfolio_values, 'index'):
        dates = portfolio_values.index
        portfolio_vals = portfolio_values.values
    else:
        # If portfolio_values is a simple list/array, create date index
        dates = pd.date_range(start='2020-01-01', periods=len(portfolio_values), freq='D')
        portfolio_vals = portfolio_values

    unified_data = []

    for i, date in enumerate(dates):
        row_data = {'Date': date}

        # Get portfolio value for this date
        portfolio_value = portfolio_vals[i]

        # If allocation history is available, calculate dollar positions
        if allocation_history is not None and date in allocation_history.index:
            allocation_row = allocation_history.loc[date]

            # Calculate dollar positions for each asset
            cash_pct = allocation_row.get('Cash', 0.0)
            cash_usd = cash_pct * portfolio_value
            row_data['Cash_USD'] = cash_usd

            total_allocated = cash_usd

            # Process each ticker
            for col in allocation_row.index:
                if col != 'Cash' and not col.endswith('_shares'):
                    # This is an allocation percentage
                    allocation_pct = allocation_row[col]
                    dollar_position = allocation_pct * portfolio_value
                    row_data[f'{col}_USD'] = dollar_position
                    total_allocated += dollar_position

            row_data['Total_USD'] = total_allocated
        else:
            # No allocation history - assume all in portfolio value
            row_data['Cash_USD'] = 0.0
            row_data['Total_USD'] = portfolio_value

        # Add portfolio metrics
        row_data['Portfolio_Value'] = portfolio_value

        # Calculate returns
        if i == 0:
            row_data['Daily_Return'] = None
            row_data['Cumulative_Return'] = 0.0
        else:
            prev_value = portfolio_vals[i-1]
            daily_return = (portfolio_value / prev_value) - 1 if prev_value > 0 else 0.0
            cumulative_return = (portfolio_value / portfolio_vals[0]) - 1 if portfolio_vals[0] > 0 else 0.0

            row_data['Daily_Return'] = daily_return
            row_data['Cumulative_Return'] = cumulative_return

        unified_data.append(row_data)

    # Convert to DataFrame
    df = pd.DataFrame(unified_data)

    return df


def run_optimization_pipeline(settings: Dict[str, Any], num_combinations: int) -> Dict[str, Any]:
    """Run the complete optimization pipeline.
    
    Args:
        settings: Configuration settings
        num_combinations: Number of parameter combinations to test
        
    Returns:
        Dictionary containing optimization results
    """
    pipeline_start_time = datetime.now()
    
    print("="*60)
    print(f"OPTIMIZATION PIPELINE v4 - STARTING ({num_combinations} combinations)")
    print("="*60)
    
    # Initialize logger
    logger = setup_logger(settings)
    log_milestone(logger, f"Optimization Pipeline Starting - {num_combinations} combinations", "START")
    
    try:
        from v4.optimization_detector import get_optimization_combinations
        config_path = str(V4_SETTINGS_FILE)
        combinations = get_optimization_combinations(config_path)
        
        logger.info(f"OPTIMIZATION MODE: Testing {len(combinations)} parameter combinations")
        for i, combo in enumerate(combinations[:3]):
            logger.info(f"  Sample combo {i}: {combo}")
        
        # Load base data for optimization framework
        from v4.engine.data_loader_v4 import load_data_for_backtest
        data_result = load_data_for_backtest(settings)
        price_data = data_result['price_data']
        
        # RUN ACTUAL OPTIMIZATION LOOP - NOT BASELINE ITERATION
        logger.info("Running ACTUAL optimization loop for all parameter combinations")

        try:
            from v4.py_reporting.v4_performance_report import PerformanceTableGenerator

            # Create PerformanceTableGenerator with proper CSV flags
            ptg = PerformanceTableGenerator(csv_flag_use=True)

            # Run the REAL matrix optimization that processes all combinations
            logger.info(f"Starting matrix optimization for {len(combinations)} combinations...")
            equity_matrix, combination_metadata = ptg._run_matrix_optimization(combinations)

            # Generate the final XLSX report with real optimization results
            report_path = ptg.generate_performance_table(output_dir=str(OUTPUT_DIR))
            
            logger.info(f"Optimization performance report XLSX generated: {report_path}")

            # Store results
            results = {
                'optimization_mode': True,
                'report_path': str(report_path),
                'equity_matrix': equity_matrix,
                'combination_metadata': combination_metadata,
                'optimization_combinations': combinations,
                'pipeline_start_time': pipeline_start_time,
                'pipeline_end_time': datetime.now(),
                'pipeline_duration': datetime.now() - pipeline_start_time
            }
            
        except Exception as e:
            logger.error(f"Failed to generate optimization report: {e}")
            logger.error("Full traceback:", exc_info=True)
            raise RuntimeError(f"Optimization report generation failed: {e}") from e
        
        log_milestone(logger, f"OPTIMIZATION PIPELINE COMPLETE - Duration: {results['pipeline_duration']}", "COMPLETE")
        
        print("="*60)
        print("OPTIMIZATION PIPELINE v4 - COMPLETED SUCCESSFULLY")
        print(f"Total Duration: {results['pipeline_duration']}")
        print(f"Tested {len(combinations)} parameter combinations")
        print("="*60)
        
        return results
        
    except Exception as e:
        logger.error(f"Optimization pipeline failed: {e}")
        logger.error("Full traceback:", exc_info=True)
        raise


def run_single_pipeline(settings: Dict[str, Any],
                       signals_file: Optional[str] = None,
                       skip_signal_generation: bool = False,
                       custom_settings_file: Optional[str] = None) -> Dict[str, Any]:
    """Run the single-run pipeline with signal generation and trading phases.

    Args:
        settings: Configuration settings
        signals_file: Optional path to pre-computed signals file (skips signal generation)
        skip_signal_generation: If True, skip signal generation and use existing signals
        custom_settings_file: Optional path to custom settings file

    Returns:
        Dictionary containing results from both phases
    """
    pipeline_start_time = datetime.now()

    print("="*60)
    print("SINGLE RUN PIPELINE v4 - STARTING")
    print("="*60)

    # Initialize logger
    logger = setup_logger(settings)
    log_milestone(logger, "Single Run Pipeline Starting", "START")

    results = {}
    signals_df = None

    try:
        # 3. Signal Generation Phase
        if not skip_signal_generation and signals_file is None:
            log_milestone(logger, "Beginning Signal Generation Phase", "SIGNAL_START")

            try:
                # Run signal generation and capture the output
                signals_output_path = run_signal_phase(custom_settings_file=custom_settings_file)

                if signals_output_path and Path(signals_output_path).exists():
                    # Normal mode: Load the generated signals DataFrame (CSV only)
                    # Note: Uses CSV format only (Parquet eliminated for performance)
                    signals_df = pd.read_csv(signals_output_path, index_col='Date', parse_dates=True)

                    logger.info(f"Successfully loaded signals DataFrame with shape: {signals_df.shape}")
                    results['signals_file_path'] = signals_output_path
                    results['signals_dataframe'] = signals_df

                    log_milestone(logger, "Signal Generation Complete", "POST_SIGNAL")
                else:
                    raise RuntimeError("Signal generation did not produce expected output file")

            except Exception as e:
                logger.error(f"Signal generation phase failed: {e}")
                raise
        else:
            if signals_file:
                logger.info(f"Skipping signal generation, using provided signals file: {signals_file}")
            else:
                logger.info("Skipping signal generation as requested")

        # 4. Trading Phase - Feed signals directly
        log_milestone(logger, "Beginning Trading Phase", "TRADING_START")

        try:
            # Direct execution without retry
            log_milestone(logger, "Executing trading phase directly")
            trading_results = modify_run_trading_to_accept_dataframe(
                signals_df=signals_df,
                signals_file=signals_file,
                custom_settings_file=custom_settings_file
            )

            results.update(trading_results)
            log_milestone(logger, "Trading Phase Complete", "POST_TRADING")

        except Exception as e:
            log_error_milestone(logger, f"Trading phase failed: {e}")
            raise

        # 5. Save consolidated output files to v4_trace_outputs/
        log_milestone(logger, "Saving Consolidated Output Files", "SAVING_OUTPUTS")

        # Get CSV flags - csv_flag_use is HARD STOP, csv_valid_det controls extra files
        csv_flag_use = settings.get('csv_flag_use', False)
        if isinstance(csv_flag_use, str):
            csv_flag_use = csv_flag_use.lower() == 'true'

        csv_valid_det = settings.get('csv_valid_det', False)
        if isinstance(csv_valid_det, str):
            csv_valid_det = csv_valid_det.lower() == 'true'

        # Check optimization_active from both settings and environment variables
        # SINGLE MODE: Always generate full CSV output
        retain_csv_signal_output = settings.get('retain_csv_signal_output', False)
        if isinstance(retain_csv_signal_output, str):
            retain_csv_signal_output = retain_csv_signal_output.lower() == 'true'

        from v4.utils.tracing_utils import setup_trace_directory
        trace_dir = setup_trace_directory()
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # Save signals if available - controlled by retain_csv_signal_output flag
        if signals_df is not None:
            if csv_flag_use and retain_csv_signal_output:
                save_df_to_trace_dir(
                    signals_df,
                    f"unified_signals_{timestamp}.csv",
                    step_description="Unified Pipeline Signals"
                )
            else:
                skip_reason = "csv_flag_use = False" if not csv_flag_use else "retain_csv_signal_output = False"
                logger.info(f"Skipped signals CSV save ({skip_reason})")

        # Save unified allocation history - SINGLE MODE: Always create standard file
        if 'allocation_history' in results and results['allocation_history'] is not None:
            if csv_flag_use:
                filename = f"unified_allocation_history_{timestamp}.csv"
                save_df_to_trace_dir(
                    results['allocation_history'],
                    filename,
                    step_description="Unified Pipeline Allocation History"
                )
            else:
                logger.info(f"Skipped allocation history CSV save (csv_flag_use = False)")

        # Save trade log if available - SINGLE MODE: Always create when csv_flag_use is True
        if 'trade_log' in results and results['trade_log'] is not None:
            if csv_flag_use:
                filename = f"unified_trade_log_{timestamp}.csv"
                save_df_to_trace_dir(
                    results['trade_log'],
                    filename,
                    step_description="Unified Pipeline Trade Log"
                )
            else:
                logger.info(f"Skipped trade log CSV save (csv_flag_use = False)")

        # Create unified portfolio file - SINGLE MODE: Standard naming + legacy equity curve file
        if 'portfolio_values' in results and results['portfolio_values'] is not None:
            if csv_flag_use:
                # Create the reporting directory if it doesn't exist
                reporting_dir = OUTPUT_DIR
                reporting_dir.mkdir(exist_ok=True)

                # SINGLE MODE: Standard naming + legacy equity curve file
                unified_file = reporting_dir / f"unified_portfolio_{timestamp}.csv"

                # Create legacy equity curve file for single mode
                equity_df = pd.DataFrame({'Portfolio_Value': results['portfolio_values']})
                equity_file = EQUITY_CURVE_STRATEGY_LATEST
                equity_df.to_csv(equity_file)
                logger.info(f"Saved legacy equity curve: {equity_file}")

                # Create unified portfolio file with comprehensive data
                unified_data = _create_unified_portfolio_data(results, timestamp)
                unified_data.to_csv(unified_file, index=False)
                logger.info(f"[UNIFIED] Created unified portfolio file: {unified_file}")

            else:
                logger.info("Skipped unified portfolio file creation (csv_flag_use = False)")

        # 6. Generate Performance Table XLSX Report
        log_milestone(logger, "Generating Performance Table XLSX Report", "XLSX_REPORT")

        try:
            # Import the performance table generator from v4/py_reporting
            from v4.py_reporting.report_modules.report_pipeline_excel import generate_performance_table_from_pipeline_results

            # Generate XLSX report using pipeline results
            xlsx_filepath = generate_performance_table_from_pipeline_results(
                results=results,
                signals_df=signals_df,
                output_dir=str(OUTPUT_DIR)
            )

            results['xlsx_report_path'] = str(xlsx_filepath)
            log_milestone(logger, f"Performance Table XLSX generated: {xlsx_filepath}", "XLSX_COMPLETE")

        except Exception as e:
            logger.warning(f"Failed to generate Performance Table XLSX: {e}")
            # Don't fail the entire pipeline for reporting issues
            logger.warning("Continuing pipeline execution despite XLSX generation failure")

        # 7. Pipeline Complete
        pipeline_end_time = datetime.now()
        pipeline_duration = pipeline_end_time - pipeline_start_time

        log_milestone(logger, f"SINGLE RUN PIPELINE COMPLETE - Duration: {pipeline_duration}", "COMPLETE")

        results['pipeline_start_time'] = pipeline_start_time
        results['pipeline_end_time'] = pipeline_end_time
        results['pipeline_duration'] = pipeline_duration

        print("="*60)
        print("SINGLE RUN PIPELINE v4 - COMPLETED SUCCESSFULLY")
        print(f"Total Duration: {pipeline_duration}")
        print("="*60)

        return results

    except Exception as e:
        # Route exceptions to logger then re-raise
        if 'logger' in locals():
            logger.error(f"Single run pipeline failed with error: {e}")
            logger.error("Full traceback:", exc_info=True)
        else:
            print(f"Pipeline failed before logger initialization: {e}")

        print("="*60)
        print("SINGLE RUN PIPELINE v4 - FAILED")
        print("="*60)

        # Re-raise the exception
        raise


def run_optimization_combination(settings: Dict[str, Any],
                               combination: Dict[str, Any],
                               combo_id: str) -> Dict[str, Any]:
    """Run pure optimization combination - NO single-mode logic.

    This function implements the separate function architecture to eliminate
    the architectural violation where optimization flow calls single-mode functions.

    Args:
        settings: Base configuration settings
        combination: Parameter combination to test
        combo_id: Unique identifier for this combination

    Returns:
        Dictionary with equity curve and minimal results

    Note:
        - No CSV file generation
        - No environment variable checking
        - Minimal logging
        - Pure optimization logic only
        - No single-mode logic whatsoever
    """
    from v4.settings.settings_CPS_v4 import load_settings
    from v4.engine.data_loader_v4 import load_data_for_backtest
    from v4.models.ema_signal_bridge import run_ema_model_with_tracing_single
    from v4.models.ema_signal_bridge_optimization import run_ema_model_with_tracing_optimization

    # Apply combination parameters to settings
    optimization_settings = settings.copy()

    # Update strategy parameters with combination values
    if 'strategy' not in optimization_settings:
        optimization_settings['strategy'] = {}

    # Map combination parameters to strategy settings
    param_mapping = {
        'st_lookback': 'st_lookback',
        'mt_lookback': 'mt_lookback',
        'lt_lookback': 'lt_lookback',
        'execution_delay': 'execution_delay',
        'top_n': 'top_n'
    }

    for combo_key, value in combination.items():
        if combo_key in param_mapping:
            strategy_key = param_mapping[combo_key]
            optimization_settings['strategy'][strategy_key] = value

    # Load data for backtest
    data_result = load_data_for_backtest(optimization_settings)
    price_data = data_result['price_data']

    # Generate signals using optimization parameters
    strategy_params = optimization_settings.get('strategy', {}).copy()
    strategy_to_run = strategy_params.pop('strategy_name', 'ema')

    # Use optimization-specific EMA function that accepts settings directly
    signals_df = run_ema_model_with_tracing_optimization(
        price_data=price_data,
        settings=optimization_settings,  # Pass settings directly
        **strategy_params
    )

    # Run trading phase with signals
    trading_results = modify_run_trading_to_accept_dataframe(
        signals_df=signals_df,
        signals_file=None,
        custom_settings_file=None
    )

    # Extract equity curve from results
    equity_curve = None
    if 'portfolio_values' in trading_results and trading_results['portfolio_values'] is not None:
        # Use portfolio_values directly - this is the equity curve
        equity_curve = trading_results['portfolio_values'].copy()
    elif 'allocation_history' in trading_results and trading_results['allocation_history'] is not None:
        # Fallback: try to find Portfolio_Value column in allocation_history
        allocation_df = trading_results['allocation_history']
        if 'Portfolio_Value' in allocation_df.columns:
            equity_curve = allocation_df['Portfolio_Value'].copy()

    # Return minimal results for optimization
    return {
        'equity_curve': equity_curve,
        'combo_id': combo_id,
        'combination': combination,
        'success': equity_curve is not None
    }


def run_baseline_optimization_iteration(settings: Dict[str, Any],
                                      signals_file: Optional[str] = None,
                                      skip_signal_generation: bool = False,
                                      custom_settings_file: Optional[str] = None) -> Dict[str, Any]:
    """Run a baseline iteration for optimization pipeline with signal generation and trading phases.

    This function is specifically designed for optimization mode baseline runs. It follows
    the same structure as run_single_pipeline but with clearer naming to distinguish
    optimization baseline iterations from regular single runs.

    Args:
        settings: Configuration settings
        signals_file: Optional path to pre-computed signals file (skips signal generation)
        skip_signal_generation: If True, skip signal generation and use existing signals
        custom_settings_file: Optional path to custom settings file

    Returns:
        Dictionary containing results from both phases
    """
    # Use the same implementation as run_single_pipeline since the core logic is identical
    return run_single_pipeline(settings, signals_file, skip_signal_generation, custom_settings_file)
