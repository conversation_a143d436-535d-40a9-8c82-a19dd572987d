@echo on
REM ==============================================================================
REM simple_console_test.bat - Run the console test for V4 backtest engine
REM ==============================================================================

echo Activating virtual environment...
call "F:\AI_Library\my_quant_env\Scripts\activate.bat"
if %errorlevel% neq 0 (
    echo ERROR: Failed to activate virtual environment
    pause
    goto :eof
)

echo.
echo Setting PYTHONPATH to project root...
set "PYTHONPATH=%~dp0..\..\"
echo PYTHONPATH is: %PYTHONPATH%

echo Running console test for V4 backtest engine...
python "%~dp0simple_console_test.py"
echo Exit code: %errorlevel%

echo.
echo Test complete. Press any key to exit.
