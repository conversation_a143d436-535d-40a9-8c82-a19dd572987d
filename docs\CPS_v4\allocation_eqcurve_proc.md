# Optimization Mode: Allocation to Equity Curve Processing

## Critical Problem Identified: File Collision During Optimization

**Root Cause**: Multiple parameter combinations overwrite each other's files during optimization runs, causing validation failures and data corruption.

## Overview
This document focuses specifically on **optimization_active=True** state and how to fix the file collision problem that causes Step 3 validation failures.

## Current Problem: File Overwriting During Optimization

### The Issue
When optimization runs multiple parameter combinations:
1. **Combination A** writes to `equity_curve_strategy_latest.csv`
2. **Combination B** overwrites the same file
3. **Combination A validation** tries to read the file but gets Combination B's data
4. **Result**: "Portfolio_Value column not found" errors and validation failures

### File Collision Points
```
# These files get overwritten by each combination:
reporting/equity_curve_strategy_latest.csv          ← COLLISION
v4_trace_outputs/allocation_history_20250726.csv   ← COLLISION
v4_trace_outputs/trade_log_20250726.csv            ← COLLISION
v4_trace_outputs/signal_history_20250726.csv       ← COLLISION

## **SOLUTION: New CSV Control Flag + Unified Portfolio File**

### **1. Settings Flag: `csv_valid_det`**

**Added to**: `v4/settings/settings_parameters_v4.ini`
```ini
; CSV Validation Detail Control (controls extra validation/debugging CSV files)
; csv_valid_det: False = skip allocation_history, trade_log, signal_history CSVs during optimization, True = generate all
csv_valid_det = False
```

**Purpose**: When `csv_valid_det = False`, skip the 3 extra collision-prone files:
- ❌ `allocation_history_*.csv` (SKIPPED)
- ❌ `trade_log_*.csv` (SKIPPED)
- ❌ `signal_history_*.csv` (SKIPPED)
- ✅ `unified_portfolio_combo_*.csv` (ONLY ESSENTIAL FILE)

### **2. New Unified Portfolio File Format**

**Filename Pattern**: `unified_portfolio_combo_{combo_id}_{timestamp}.csv`

**Example**: `unified_portfolio_combo_st15_mt70_top2_exec1_20250726_210021.csv`

**File Content**:
```csv
Date,Cash_USD,SPY_USD,SHV_USD,TLT_USD,PFF_USD,EFA_USD,Total_USD,Portfolio_Value,Daily_Return,Cumulative_Return
2020-01-02,0.0,500000.0,0.0,300000.0,200000.0,0.0,1000000.0,1000000.0,,0.0
2020-01-03,50000.0,450000.0,0.0,300000.0,200000.0,0.0,1000000.0,1000079.37,7.937e-05,7.937e-05
```

**Key Features**:
- **Dollar positions** (not percentages) for each asset
- **Portfolio_Value** column for validation compatibility
- **Unique combo_id** prevents file collisions
- **Single file** replaces 4 separate collision-prone files

### **3. Implementation Details**

#### **A. File Creation Control**

**Module**: `v4/pipeline/unified_pipeline.py`
**Function**: `run_pipeline()` (enhanced)

```python
def run_pipeline(settings_file, combo_id=None, optimization_active=False):
    """Enhanced pipeline with unified file output and collision prevention."""

    # Load csv_valid_det flag from settings
    config = configparser.ConfigParser()
    config.read(settings_file)
    csv_valid_det = config.getboolean('System', 'csv_valid_det', fallback=False)

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

    if optimization_active and combo_id:
        # OPTIMIZATION MODE: Single unified file per combination
        unified_file = f"reporting/unified_portfolio_combo_{combo_id}_{timestamp}.csv"

        # Skip extra files when csv_valid_det = False
        if csv_valid_det:
            allocation_file = f"v4_trace_outputs/allocation_history_combo_{combo_id}_{timestamp}.csv"
            trade_file = f"v4_trace_outputs/trade_log_combo_{combo_id}_{timestamp}.csv"
            signal_file = f"v4_trace_outputs/signal_history_combo_{combo_id}_{timestamp}.csv"
        else:
            allocation_file = None  # SKIP
            trade_file = None       # SKIP
            signal_file = None      # SKIP
    else:
        # SINGLE MODE: Standard file naming
        unified_file = f"reporting/unified_portfolio_{timestamp}.csv"
        allocation_file = f"v4_trace_outputs/allocation_history_{timestamp}.csv"
        trade_file = f"v4_trace_outputs/trade_log_{timestamp}.csv"
        signal_file = f"v4_trace_outputs/signal_history_{timestamp}.csv"

    return run_with_unified_output(unified_file, allocation_file, trade_file, signal_file)
```

#### **B. File Creation Loop**

**Module**: `v4/engine/backtest_engine.py`
**Function**: `_write_unified_portfolio_file()` (NEW)

```python
def _write_unified_portfolio_file(self, output_path, combo_id=None):
    """Write unified portfolio file with dollar positions and portfolio values."""

    unified_data = []

    # MAIN LOOP: Iterate through each trading day
    for date in self.portfolio_history.index:
        row_data = {'Date': date}

        # Get portfolio state for this date
        portfolio_state = self.portfolio_history.loc[date]
        current_prices = self.price_data.loc[date]

        # Calculate dollar positions for each asset
        cash_usd = portfolio_state.get('Cash', 0.0)
        total_portfolio_value = cash_usd

        row_data['Cash_USD'] = cash_usd

        # Loop through each ticker to get dollar positions
        for ticker in self.tickers:
            if ticker != 'Cash':
                shares = portfolio_state.get(f'{ticker}_shares', 0.0)
                price = current_prices[ticker]
                dollar_position = shares * price

                row_data[f'{ticker}_USD'] = dollar_position
                total_portfolio_value += dollar_position

        # Add portfolio metrics
        row_data['Total_USD'] = total_portfolio_value
        row_data['Portfolio_Value'] = total_portfolio_value  # For validation compatibility

        # Calculate returns
        if len(unified_data) == 0:
            row_data['Daily_Return'] = None
            row_data['Cumulative_Return'] = 0.0
        else:
            prev_value = unified_data[-1]['Portfolio_Value']
            daily_return = (total_portfolio_value / prev_value) - 1 if prev_value > 0 else 0.0
            cumulative_return = (total_portfolio_value / unified_data[0]['Portfolio_Value']) - 1

            row_data['Daily_Return'] = daily_return
            row_data['Cumulative_Return'] = cumulative_return

        unified_data.append(row_data)

    # Convert to DataFrame and save
    df = pd.DataFrame(unified_data)
    df.to_csv(output_path, index=False)

    logger.info(f"[UNIFIED] Created unified portfolio file: {output_path}")
    if combo_id:
        logger.info(f"[UNIFIED] File tagged for combination: {combo_id}")

    return output_path
```

#### **C. Combination ID Generation**

```python
def generate_combo_id(combination_params):
    """Generate unique, readable combination identifier."""
    # Sort parameters for consistent naming
    sorted_params = sorted(combination_params.items())

    # Create readable ID: st15_mt70_top2_exec1
    param_parts = []
    for key, value in sorted_params:
        if key == 'st_lookback':
            param_parts.append(f"st{value}")
        elif key == 'mt_lookback':
            param_parts.append(f"mt{value}")
        elif key == 'top_n':
            param_parts.append(f"top{value}")
        elif key == 'execution_delay':
            param_parts.append(f"exec{value}")
        else:
            param_parts.append(f"{key}{value}")

    combo_id = "_".join(param_parts)
    return combo_id

# Example output: "st15_mt70_top2_exec1"
```

#### **D. File Reading and Mapping**

**Module**: `v4/py_reporting/performance_table_generator.py`
**Function**: `_load_unified_portfolio_for_combination()` (NEW)

```python
def _load_unified_portfolio_for_combination(self, combo_id, timestamp):
    """Load unified portfolio file for specific optimization combination."""

    # Construct expected filename
    expected_file = get_reporting_file_path(f"unified_portfolio_combo_{combo_id}_{timestamp}.csv")

    if not expected_file.exists():
        # Fallback: search for any file with this combo_id
        pattern = f"unified_portfolio_combo_{combo_id}_*.csv"
        matching_files = list(get_reporting_dir().glob(pattern))

        if not matching_files:
            raise FileNotFoundError(f"No unified portfolio file found for combination {combo_id}")

        # Use most recent file if multiple found
        expected_file = max(matching_files, key=lambda f: f.stat().st_mtime)
        logger.warning(f"[MAPPING] Using fallback file: {expected_file}")

    # Load and validate file
    df = pd.read_csv(expected_file, index_col='Date', parse_dates=True)

    # Validate required columns
    required_cols = ['Portfolio_Value', 'Total_USD']
    missing_cols = [col for col in required_cols if col not in df.columns]
    if missing_cols:
        raise ValueError(f"Missing required columns in {expected_file}: {missing_cols}")

    # Extract equity curve for validation
    equity_curve = df['Portfolio_Value'].copy()

    logger.info(f"[MAPPING] Loaded unified portfolio for combo {combo_id}")
    logger.info(f"[MAPPING] File: {expected_file}")
    logger.info(f"[MAPPING] Data points: {len(equity_curve)}")
    logger.info(f"[MAPPING] Final portfolio value: ${equity_curve.iloc[-1]:,.2f}")

    return equity_curve, df
```

### **4. Complete Flow Summary**

1. **Settings Control**: `csv_valid_det = False` → Skip 3 extra CSV files during optimization
2. **File Creation**: `_write_unified_portfolio_file()` → Single file per combination with unique naming
3. **Unique Naming**: `unified_portfolio_combo_st15_mt70_top2_exec1_20250726_210021.csv`
4. **File Reading**: `_load_unified_portfolio_for_combination()` → Maps combo_id to specific file
5. **Validation**: Uses `Portfolio_Value` column from unified file

**Result**:
- ✅ File collisions eliminated (4 files → 1 file)
- ✅ Optimization validation works with Portfolio_Value column
- ✅ Clear traceability from creation to consumption via combo_id
- ✅ Reduced I/O overhead during optimization runs
```

## Solution: Unique File Naming Per Optimization Combination

### 1. Combination Identifier System

Each parameter combination needs a unique identifier:
```python
# Generate unique combo ID from parameters
def generate_combo_id(parameters):
    """Create unique identifier for parameter combination."""
    param_str = "_".join([f"{k}{v}" for k, v in sorted(parameters.items())])
    return f"combo_{hash(param_str) % 10000:04d}"

# Example:
# {'st_lookback': 15, 'mt_lookback': 70, 'lt_lookback': 100, 'top_n': 2}
# → "combo_1234"
```

### 2. Optimization File Naming Convention

**Pattern**: `{base_name}_combo_{combo_id}_{timestamp}.{ext}`

```python
# Current (BROKEN - causes overwrites):
equity_curve_strategy_latest.csv
allocation_history_20250726_210021.csv

# Fixed (UNIQUE per combination):
equity_curve_strategy_combo_1234_20250726_210021.csv
allocation_history_combo_1234_20250726_210021.csv
trade_log_combo_1234_20250726_210021.csv
signal_history_combo_1234_20250726_210021.csv
```

## Step-by-Step Optimization Process Flow

### Step 1: Parameter Combination Setup
```python
# Each combination gets unique settings file
combination = {'st_lookback': 15, 'mt_lookback': 70, 'lt_lookback': 100, 'top_n': 2}
combo_id = generate_combo_id(combination)  # → "combo_1234"
timestamp = "20250726_210021"

# Create unique settings file for this combination
settings_file = f"settings_combo_{combo_id}_{timestamp}.ini"
```

### Step 2: Pipeline Execution with Unique Files
```python
# Pipeline runs with combination-specific file paths
def run_pipeline_for_combination(combination, combo_id, timestamp):
    # Set unique output paths for this combination
    equity_file = f"reporting/equity_curve_combo_{combo_id}_{timestamp}.csv"
    allocation_file = f"v4_trace_outputs/allocation_history_combo_{combo_id}_{timestamp}.csv"
    trade_file = f"v4_trace_outputs/trade_log_combo_{combo_id}_{timestamp}.csv"

    # Run pipeline with these specific paths
    result = subprocess.run([
        "python", "v4/pipeline/unified_pipeline.py",
        "--settings", settings_file,
        "--equity-output", equity_file,
        "--allocation-output", allocation_file,
        "--trade-output", trade_file
    ])

    return equity_file  # Return path for validation
```

### Step 3: Validation with Correct File
```python
def validate_combination_results(combo_id, timestamp):
    # Read the specific file for THIS combination
    equity_file = f"reporting/equity_curve_combo_{combo_id}_{timestamp}.csv"

    if not os.path.exists(equity_file):
        raise FileNotFoundError(f"Equity file not found: {equity_file}")

    df = pd.read_csv(equity_file)

    # Handle both column naming conventions
    if 'Portfolio_Value' in df.columns:
        return df['Portfolio_Value']
    elif 'Total' in df.columns:
        return df['Total']
    else:
        raise ValueError(f"Neither Portfolio_Value nor Total found in {equity_file}")
```

## Required Code Changes to Fix File Collisions

### 1. Update Performance Table Generator

**File**: `v4/py_reporting/performance_table_generator.py`

**Current Problem Code** (Line ~1247):
```python
# BROKEN: All combinations use same file
equity_file = EQUITY_CURVE_STRATEGY_LATEST  # ← COLLISION POINT
```

**Fixed Code**:
```python
# FIXED: Each combination uses unique file
combo_id = self.generate_combo_id(combination)
equity_file = get_reporting_file_path(f"equity_curve_combo_{combo_id}_{self.timestamp}.csv")
```

### 2. Update Pipeline Output Paths

**File**: `v4/pipeline/unified_pipeline.py`

**Current Problem**:
```python
# BROKEN: Same output files for all combinations
equity_output = "reporting/equity_curve_strategy_latest.csv"
allocation_output = f"v4_trace_outputs/allocation_history_{timestamp}.csv"
```

**Fixed Code**:
```python
# FIXED: Unique files per combination
if optimization_active and combo_id:
    equity_output = f"reporting/equity_curve_combo_{combo_id}_{timestamp}.csv"
    allocation_output = f"v4_trace_outputs/allocation_history_combo_{combo_id}_{timestamp}.csv"
    trade_output = f"v4_trace_outputs/trade_log_combo_{combo_id}_{timestamp}.csv"
else:
    # Single mode uses standard names
    equity_output = "reporting/equity_curve_strategy_latest.csv"
    allocation_output = f"v4_trace_outputs/allocation_history_{timestamp}.csv"
```

### 3. Update File Reading Logic

**Current Problem** (Line ~1369):
```python
# BROKEN: Looks for wrong file during optimization
latest_file = max(equity_files, key=os.path.getmtime)  # ← Gets random file
```

**Fixed Code**:
```python
# FIXED: Reads specific combination file
if optimization_active:
    target_file = f"equity_curve_combo_{combo_id}_{timestamp}.csv"
    equity_file = os.path.join(reporting_dir, target_file)
else:
    # Single mode uses latest file
    latest_file = max(equity_files, key=os.path.getmtime)
    equity_file = latest_file
```

## File Format Requirements for Optimization

### 1. Equity Curve File Format (CRITICAL)

**Required Columns** (must include BOTH for compatibility):
```csv
Date,Portfolio_Value,Total,Daily_Return,Cumulative_Return
2020-01-02,1000000.0,1000000.0,,0.0
2020-01-03,1000079.37,1000079.37,7.937e-05,7.937e-05
```

**Why Both Columns**:
- `Portfolio_Value`: Expected by optimization validation
- `Total`: Used by allocation history files
- Having both prevents column name errors

### 2. Allocation History File Format

**Required Columns**:
```csv
Date,Cash,SPY,SHV,TLT,PFF,EFA,Total,Portfolio_Value
2020-01-02,0.0,0.5,0.0,0.3,0.2,0.0,1.0,1000000.0
2020-01-03,0.05,0.45,0.0,0.3,0.2,0.0,1.0,1000079.37
```

**Column Specifications**:
- `Total`: Sum of allocation percentages (validation = 1.0)
- `Portfolio_Value`: Absolute dollar value (for equity curve compatibility)

### 3. Settings File Isolation

**Current Problem**: All combinations overwrite same settings file
```ini
# BROKEN: settings.ini gets overwritten by each combination
```

**Fixed Approach**: Unique settings per combination
```ini
# FIXED: settings_combo_1234_20250726_210021.ini
# Each combination gets its own settings file
```

### 4. Optimization Loop Control Structure

```python
def run_optimization_validation(combinations):
    results = {}

    for i, combination in enumerate(combinations):
        # Generate unique identifiers
        combo_id = generate_combo_id(combination)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # Create isolated file paths
        settings_file = f"settings_combo_{combo_id}_{timestamp}.ini"
        equity_file = f"reporting/equity_curve_combo_{combo_id}_{timestamp}.csv"

        try:
            # Step 1: Create unique settings file
            create_settings_file(combination, settings_file)

            # Step 2: Run pipeline with unique outputs
            run_pipeline_with_unique_files(settings_file, equity_file, combo_id)

            # Step 3: Validate using the specific file for THIS combination
            equity_curve = validate_combination_results(combo_id, timestamp)

            # Step 4: Store results with combination ID
            results[combo_id] = {
                'combination': combination,
                'equity_curve': equity_curve,
                'files': {
                    'settings': settings_file,
                    'equity': equity_file
                }
            }

        except Exception as e:
            logger.error(f"Combination {combo_id} failed: {e}")
            results[combo_id] = {'error': str(e)}

        finally:
            # Clean up temporary files if needed
            cleanup_temp_files(settings_file)

    return results
```

## Immediate Implementation Steps

### Step 1: Fix Column Name Handling (URGENT)

**File**: `v4/py_reporting/performance_table_generator.py`
**Line**: ~1251 in `_run_pipeline_for_combination()`

**Current Code**:
```python
if 'Portfolio_Value' in equity_df.columns:
    equity_curve = equity_df['Portfolio_Value'].copy()
    return equity_curve
else:
    # BROKEN: No fallback to 'Total' column
    error_msg = f"Portfolio_Value column not found..."
    raise ValueError(error_msg)
```

**Fixed Code**:
```python
if 'Portfolio_Value' in equity_df.columns:
    equity_curve = equity_df['Portfolio_Value'].copy()
    return equity_curve
elif 'Total' in equity_df.columns:
    # FIXED: Fallback to 'Total' column
    equity_curve = equity_df['Total'].copy()
    logger.info(f"[TRACE] Using 'Total' column as Portfolio_Value for combo {combo_id}")
    return equity_curve
else:
    error_msg = f"Neither Portfolio_Value nor Total column found in {equity_file}"
    raise ValueError(error_msg)
```

### Step 2: Implement Unique File Naming

**Add to Performance Table Generator**:
```python
def generate_combo_id(self, combination):
    """Generate unique ID for parameter combination."""
    param_items = sorted(combination.items())
    param_str = "_".join([f"{k}{v}" for k, v in param_items])
    return f"combo_{hash(param_str) % 10000:04d}"

def get_unique_file_path(self, base_name, combo_id, extension="csv"):
    """Get unique file path for optimization combination."""
    if self.optimization_active:
        filename = f"{base_name}_combo_{combo_id}_{self.timestamp}.{extension}"
    else:
        filename = f"{base_name}_{self.timestamp}.{extension}"
    return get_reporting_file_path(filename)
```

### Step 3: Update Pipeline File Outputs

**File**: `v4/pipeline/unified_pipeline.py`

**Add Combination ID Parameter**:
```python
def run_pipeline(settings_file, combo_id=None, optimization_active=False):
    """Run pipeline with optional combination-specific file naming."""

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

    if optimization_active and combo_id:
        # Unique files for optimization
        equity_output = f"reporting/equity_curve_combo_{combo_id}_{timestamp}.csv"
        allocation_output = f"v4_trace_outputs/allocation_history_combo_{combo_id}_{timestamp}.csv"
    else:
        # Standard files for single mode
        equity_output = "reporting/equity_curve_strategy_latest.csv"
        allocation_output = f"v4_trace_outputs/allocation_history_{timestamp}.csv"

    # Pass file paths to pipeline components
    return run_with_file_paths(equity_output, allocation_output)
```

### Step 4: Validation Testing

**Test Script**:
```python
def test_optimization_file_isolation():
    """Test that optimization combinations don't overwrite each other."""

    combinations = [
        {'st_lookback': 15, 'mt_lookback': 70, 'top_n': 2},
        {'st_lookback': 20, 'mt_lookback': 80, 'top_n': 3}
    ]

    files_created = []

    for combo in combinations:
        combo_id = generate_combo_id(combo)

        # Run pipeline for this combination
        equity_file = run_pipeline_for_combination(combo, combo_id)
        files_created.append(equity_file)

        # Verify file exists and has correct data
        assert os.path.exists(equity_file), f"File not created: {equity_file}"

        df = pd.read_csv(equity_file)
        assert 'Portfolio_Value' in df.columns or 'Total' in df.columns

    # Verify all files still exist (no overwrites)
    for file_path in files_created:
        assert os.path.exists(file_path), f"File was overwritten: {file_path}"

    print("✅ File isolation test passed!")
```

## Priority Action Plan

### IMMEDIATE (Fix Step 3 Validation Failure)

1. **Column Name Fallback** - Add `Total` column support to `_run_pipeline_for_combination()`
2. **Unique File Naming** - Implement combination-specific file paths
3. **Settings File Isolation** - Prevent settings file overwrites

### SHORT-TERM (Prevent File Collisions)

1. **Pipeline File Outputs** - Update unified_pipeline.py with combo_id parameter
2. **File Reading Logic** - Read specific combination files, not latest files
3. **Validation Testing** - Test file isolation works correctly

### LONG-TERM (Architecture Improvements)

1. **Memory-First Processing** - Reduce file I/O for performance
2. **Matrix-Based Optimization** - Handle multiple combinations efficiently
3. **Comprehensive Error Handling** - Better debugging and validation

---

## Root Cause Summary

**The Step 3 validation failures are caused by**:

1. **File Overwrites**: Multiple optimization combinations overwrite each other's output files
2. **Column Name Mismatch**: System expects `Portfolio_Value` but gets `Total` column
3. **No File Isolation**: All combinations use same file paths during optimization

**The fix requires**:

1. **Unique file naming** per optimization combination
2. **Column name fallback** logic (Portfolio_Value OR Total)
3. **Proper file isolation** during optimization runs

This documentation provides the complete technical foundation to fix the optimization validation failures by addressing the file collision problem at its source.
