"""
Debug script that writes execution delay parameter diagnostics to a CSV file.
"""

import sys
import os
import csv
from pathlib import Path
from datetime import datetime
import logging

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

from config.paths import OUTPUT_DIR

# Create output directory
output_dir = OUTPUT_DIR / "debug"
output_dir.mkdir(parents=True, exist_ok=True)

# Create CSV file for logging
csv_path = output_dir / f"execution_delay_debug_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"

with open(csv_path, 'w', newline='') as csvfile:
    writer = csv.writer(csvfile)
    writer.writerow(['Test', 'Description', 'Result', 'Error'])
    
    # Test 1: Import modules
    try:
        from config.config_v2 import config_v2, _get_param_value
        from config.local_parameter_optimization import define_parameter, validate_parameter
        from engine.backtest import BacktestEngine
        writer.writerow(['Import', 'Import required modules', 'Success', ''])
    except Exception as e:
        writer.writerow(['Import', 'Import required modules', 'Failure', str(e)])
        print(f"Error importing modules: {e}")
        sys.exit(1)
    
    # Test 2: Check execution_delay parameter in config
    try:
        raw_exec_delay = config_v2['backtest_params'].get('execution_delay')
        writer.writerow(['Config', f'Raw execution_delay: {raw_exec_delay}, Type: {type(raw_exec_delay)}', 'Success', ''])
    except Exception as e:
        writer.writerow(['Config', 'Get execution_delay from config', 'Failure', str(e)])
    
    # Test 3: Extract value with _get_param_value
    try:
        value = _get_param_value(raw_exec_delay)
        writer.writerow(['Extract', f'Extracted value: {value}, Type: {type(value)}', 'Success', ''])
    except Exception as e:
        writer.writerow(['Extract', 'Extract value with _get_param_value', 'Failure', str(e)])
    
    # Test 4: Create optimizable parameter
    try:
        opt_param = define_parameter(True, 1, 0, 5, 1)
        writer.writerow(['Define', f'Optimizable parameter: {opt_param}, Type: {type(opt_param)}', 'Success', ''])
    except Exception as e:
        writer.writerow(['Define', 'Create optimizable parameter', 'Failure', str(e)])
    
    # Test 5: Check BacktestEngine initialization with direct value
    try:
        engine1 = BacktestEngine(execution_delay=1)
        writer.writerow(['Engine1', 'Initialize BacktestEngine with direct value', 'Success', ''])
    except Exception as e:
        writer.writerow(['Engine1', 'Initialize BacktestEngine with direct value', 'Failure', str(e)])
    
    # Test 6: Check BacktestEngine initialization with tuple
    try:
        engine2 = BacktestEngine(execution_delay=opt_param)
        writer.writerow(['Engine2', 'Initialize BacktestEngine with parameter tuple', 'Success', ''])
    except Exception as e:
        writer.writerow(['Engine2', 'Initialize BacktestEngine with parameter tuple', 'Failure', str(e)])
    
    # Test 7: Create minimal test data
    try:
        import pandas as pd
        import numpy as np
        dates = pd.date_range(start='2025-01-01', periods=10)
        price_data = pd.DataFrame({
            'SPY': np.linspace(100, 110, 10),
            'TLT': np.linspace(90, 100, 10)
        }, index=dates)
        
        def dummy_signal(price_data, **params):
            return {'SPY': 0.6, 'TLT': 0.4}
        
        writer.writerow(['Data', 'Create test data and signal function', 'Success', ''])
    except Exception as e:
        writer.writerow(['Data', 'Create test data and signal function', 'Failure', str(e)])
    
    # Test 8: Run backtest with direct value
    try:
        engine3 = BacktestEngine()
        result1 = engine3.run_backtest(
            price_data=price_data,
            signal_generator=dummy_signal,
            execution_delay=1
        )
        writer.writerow(['Run1', 'Run backtest with direct value', 'Success', ''])
    except Exception as e:
        writer.writerow(['Run1', 'Run backtest with direct value', 'Failure', str(e)])
        # Get detailed traceback
        import traceback
        tb = traceback.format_exc()
        writer.writerow(['Run1-Trace', tb[:1000], '', ''])
    
    # Test 9: Run backtest with parameter tuple
    try:
        engine4 = BacktestEngine()
        result2 = engine4.run_backtest(
            price_data=price_data,
            signal_generator=dummy_signal,
            execution_delay=opt_param
        )
        writer.writerow(['Run2', 'Run backtest with parameter tuple', 'Success', ''])
    except Exception as e:
        writer.writerow(['Run2', 'Run backtest with parameter tuple', 'Failure', str(e)])
        # Get detailed traceback
        import traceback
        tb = traceback.format_exc()
        writer.writerow(['Run2-Trace', tb[:1000], '', ''])
    
    # Test 10: Check specific issue with isinstance
    try:
        from datetime import date
        # This is the line that's causing the error
        if isinstance(opt_param, (int, float)):
            writer.writerow(['isinstance1', 'Check if parameter is int/float', 'True', ''])
        else:
            writer.writerow(['isinstance1', 'Check if parameter is int/float', 'False', ''])
        
        # Try with date
        if isinstance(opt_param, date):
            writer.writerow(['isinstance2', 'Check if parameter is date', 'True', ''])
        else:
            writer.writerow(['isinstance2', 'Check if parameter is date', 'False', ''])
        
        # Try with tuple of types
        if isinstance(opt_param, (int, float, date)):
            writer.writerow(['isinstance3', 'Check if parameter is int/float/date', 'True', ''])
        else:
            writer.writerow(['isinstance3', 'Check if parameter is int/float/date', 'False', ''])
    except Exception as e:
        writer.writerow(['isinstance', 'Check isinstance behavior', 'Failure', str(e)])
        # Get detailed traceback
        import traceback
        tb = traceback.format_exc()
        writer.writerow(['isinstance-Trace', tb[:1000], '', ''])

print(f"Debug information saved to: {csv_path}")
