#!/usr/bin/env python
"""
Test Script for Enhanced Optimization Workflow

This script demonstrates the new EquityCurvesManager approach for handling
parameter optimization results. It shows how to:

1. Generate parameter combinations
2. Use EquityCurvesManager to consolidate results
3. Create performance summaries
4. Integrate with existing performance table generator

Author: AI Assistant
Date: 2025-01-24
"""

import sys
from pathlib import Path
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging

# Add project paths
sys.path.insert(0, str(Path.cwd()))
sys.path.insert(0, str(Path.cwd() / "v4"))

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_mock_equity_curve(base_value=1000000, days=500, volatility=0.015, trend=0.0003):
    """Create a realistic mock equity curve for testing."""
    # Create date range
    start_date = datetime.now() - timedelta(days=days)
    dates = pd.date_range(start=start_date, periods=days, freq='D')
    
    # Generate random returns with trend
    np.random.seed(42)  # For reproducible results
    daily_returns = np.random.normal(trend, volatility, days)
    
    # Calculate equity curve
    equity_values = [base_value]
    for ret in daily_returns[1:]:
        equity_values.append(equity_values[-1] * (1 + ret))
    
    return pd.Series(equity_values, index=dates, name='Portfolio_Value')

def mock_optimization_runner(combination):
    """Mock optimization function that simulates running backtest for a combination."""
    logger.info(f"  Running optimization for: {combination}")
    
    # Simulate processing time (remove in real implementation)
    import time
    time.sleep(0.1)
    
    # Create parameter-dependent variation in results
    base_value = 1000000
    
    # Simulate parameter effects on performance
    st_effect = (combination.get('st_lookback', 15) - 15) * 0.001  # +/- 0.1% per unit
    mt_effect = (combination.get('mt_lookback', 70) - 70) * 0.0005  # +/- 0.05% per unit
    top_n_effect = (combination.get('top_n', 2) - 2) * 0.01  # +/- 1% per unit
    
    # Total effect on performance
    performance_factor = 1.0 + st_effect + mt_effect + top_n_effect
    
    # Create slightly different equity curve based on parameters
    base_curve = create_mock_equity_curve(base_value=base_value)
    
    # Apply parameter-based scaling
    modified_curve = base_curve * performance_factor
    
    # Add some parameter-specific noise for realism
    param_seed = abs(hash(str(combination))) % 1000
    np.random.seed(param_seed)
    noise = np.random.normal(0, 0.002, len(modified_curve))
    
    for i in range(1, len(modified_curve)):
        modified_curve.iloc[i] *= (1 + noise[i])
    
    return modified_curve

def test_equity_curves_manager():
    """Test the EquityCurvesManager functionality."""
    from v4.reporting.equity_curves_manager import EquityCurvesManager
    
    logger.info("=" * 60)
    logger.info("TESTING EQUITY CURVES MANAGER")
    logger.info("=" * 60)
    
    # Create test parameter combinations
    combinations = [
        {"st_lookback": 10, "mt_lookback": 60, "lt_lookback": 100, "top_n": 2, "execution_delay": 1},
        {"st_lookback": 15, "mt_lookback": 70, "lt_lookback": 100, "top_n": 2, "execution_delay": 1},
        {"st_lookback": 20, "mt_lookback": 80, "lt_lookback": 100, "top_n": 2, "execution_delay": 1},
        {"st_lookback": 10, "mt_lookback": 60, "lt_lookback": 100, "top_n": 3, "execution_delay": 1},
        {"st_lookback": 15, "mt_lookback": 70, "lt_lookback": 100, "top_n": 3, "execution_delay": 1},
    ]
    
    logger.info(f"Testing with {len(combinations)} parameter combinations")
    
    # Initialize manager
    manager = EquityCurvesManager(output_dir="reporting")
    
    # Add combinations one by one
    for i, combination in enumerate(combinations):
        logger.info(f"Processing combination {i+1}/{len(combinations)}")
        
        # Run mock optimization
        equity_curve = mock_optimization_runner(combination)
        
        # Add to manager
        column_name = manager.add_combination_result(combination, equity_curve)
        logger.info(f"  Added as column: {column_name}")
    
    # Add benchmark
    benchmark_curve = create_mock_equity_curve(base_value=1000000, trend=0.0002)
    manager.add_benchmark_curve(benchmark_curve)
    
    # Save results
    equity_file, metadata_file = manager.save_to_disk()
    
    # Create performance summary
    summary_df = manager.create_performance_summary()
    
    logger.info("\nPERFORMANCE SUMMARY:")
    logger.info("=" * 60)
    
    # Display top combinations
    if not summary_df.empty:
        display_cols = ['Column_Name', 'st_lookback', 'mt_lookback', 'top_n', 'CAGR', 'Sharpe_Ratio', 'Max_Drawdown']
        display_df = summary_df[display_cols].head(10)
        
        for _, row in display_df.iterrows():
            logger.info(f"{row['Column_Name']:15} | ST:{row['st_lookback']:2} MT:{row['mt_lookback']:2} TOP:{row['top_n']} | "
                       f"CAGR:{row['CAGR']:7.2%} Sharpe:{row['Sharpe_Ratio']:5.2f} DD:{row['Max_Drawdown']:7.2%}")
    
    return manager, equity_file, metadata_file

def test_performance_table_integration():
    """Test integration with PerformanceTableGenerator."""
    from v4.reporting.performance_table_generator import PerformanceTableGenerator
    
    logger.info("\n" + "=" * 60)
    logger.info("TESTING PERFORMANCE TABLE INTEGRATION")
    logger.info("=" * 60)
    
    try:
        # Initialize generator
        generator = PerformanceTableGenerator(csv_flag_use=True)
        
        # Get optimization combinations from config
        combinations = generator.get_optimization_combinations()
        logger.info(f"Found {len(combinations)} combinations from config")
        
        if len(combinations) > 0:
            # Show sample combinations
            for i, combo in enumerate(combinations[:3]):
                logger.info(f"  Sample {i+1}: {combo}")
            
            logger.info("Integration test successful - generator can read optimization parameters")
            return True
        else:
            logger.warning("No optimization combinations found in config")
            return False
            
    except Exception as e:
        logger.error(f"Integration test failed: {e}")
        return False

def test_workflow_function():
    """Test the complete workflow function."""
    from v4.reporting.equity_curves_manager import create_optimization_workflow
    
    logger.info("\n" + "=" * 60)
    logger.info("TESTING COMPLETE WORKFLOW FUNCTION")
    logger.info("=" * 60)
    
    # Create test combinations
    combinations = [
        {"st_lookback": 12, "mt_lookback": 65, "lt_lookback": 100, "top_n": 2, "execution_delay": 1},
        {"st_lookback": 18, "mt_lookback": 75, "lt_lookback": 100, "top_n": 2, "execution_delay": 1},
        {"st_lookback": 15, "mt_lookback": 70, "lt_lookback": 100, "top_n": 3, "execution_delay": 1},
    ]
    
    # Run complete workflow
    manager = create_optimization_workflow(
        combinations=combinations,
        optimization_runner_func=mock_optimization_runner,
        output_dir="reporting"
    )
    
    logger.info("Complete workflow test successful!")
    return manager

def demo_file_analysis():
    """Demonstrate analysis of saved equity curves file."""
    logger.info("\n" + "=" * 60)
    logger.info("DEMONSTRATING FILE ANALYSIS")
    logger.info("=" * 60)
    
    # Look for recent equity curves files
    reporting_dir = Path("reporting")
    if not reporting_dir.exists():
        logger.warning("No reporting directory found")
        return
    
    equity_files = list(reporting_dir.glob("optimization_equity_curves_*.csv"))
    if not equity_files:
        logger.warning("No equity curves files found")
        return
    
    # Load most recent file
    latest_file = max(equity_files, key=lambda f: f.stat().st_mtime)
    logger.info(f"Loading equity curves from: {latest_file}")
    
    # Load and analyze
    equity_df = pd.read_csv(latest_file, index_col=0, parse_dates=True)
    
    logger.info(f"Loaded equity curves: {equity_df.shape}")
    logger.info(f"Date range: {equity_df.index[0]} to {equity_df.index[-1]}")
    logger.info(f"Combinations: {len(equity_df.columns)}")
    logger.info(f"Column names: {list(equity_df.columns)}")
    
    # Show final values
    final_values = equity_df.iloc[-1].sort_values(ascending=False)
    logger.info("\nFinal Portfolio Values (sorted):")
    for col, value in final_values.items():
        logger.info(f"  {col:20}: ${value:,.0f}")
    
    # Show correlations
    correlations = equity_df.corr()
    logger.info(f"\nAverage correlation between strategies: {correlations.mean().mean():.3f}")

if __name__ == "__main__":
    logger.info("Starting Enhanced Optimization Workflow Tests")
    logger.info("=" * 80)
    
    try:
        # Test 1: Basic EquityCurvesManager functionality
        manager, equity_file, metadata_file = test_equity_curves_manager()
        
        # Test 2: Integration with PerformanceTableGenerator
        integration_success = test_performance_table_integration()
        
        # Test 3: Complete workflow function
        workflow_manager = test_workflow_function()
        
        # Test 4: File analysis demonstration
        demo_file_analysis()
        
        logger.info("\n" + "=" * 80)
        logger.info("ALL TESTS COMPLETED SUCCESSFULLY!")
        logger.info("=" * 80)
        
        logger.info("\nKey Benefits of New Approach:")
        logger.info("✓ No more file overwriting - each combination gets unique storage")
        logger.info("✓ Consolidated CSV with Date + multiple combination columns")
        logger.info("✓ Automatic performance metrics calculation")
        logger.info("✓ Progress saving during optimization")
        logger.info("✓ Scalable to 5000+ combinations")
        logger.info("✓ Easy integration with existing performance table generator")
        logger.info("✓ Comprehensive metadata tracking")
        
        if equity_file:
            logger.info(f"\nGenerated files:")
            logger.info(f"  Equity curves: {equity_file}")
            logger.info(f"  Metadata: {metadata_file}")
        
    except Exception as e:
        logger.error(f"Test failed with error: {e}")
        import traceback
        traceback.print_exc()
