# V3 Transition: From `strategy` to `signal_algo`

## Overview

This document explains the transition from the legacy `strategy` parameter (in `backtest_params`) to the V3 standard `signal_algo` parameter (in `strategy_params`). This change aligns with the V3 parameter system design requirements and improves consistency in the codebase.

## Why This Change?

1. **Improved Parameter Organization**:
   - The `signal_algo` parameter is properly defined as a `CategoricalParam` in the V3 parameter system
   - It's grouped with other strategy-specific parameters in `strategy_params`
   - This allows for better organization and clearer separation of concerns

2. **Enhanced GUI Integration**:
   - `signal_algo` is properly exposed in the GUI with the V3 parameter system
   - It supports proper type handling and validation

3. **Optimization Support**:
   - `signal_algo` can be included in optimization loops as a categorical parameter
   - This enables testing different strategy algorithms in a single optimization run

4. **Future Extensibility**:
   - As noted in the memory bank, there are plans to "treat signal algorithms as categorical list groups for optimization in future runs"
   - The `signal_algo` parameter is better suited for this purpose than the legacy `strategy` parameter

## Implementation Details

### Changes Made

1. **Configuration Files**:
   - Removed `strategy` from `backtest_params` in `config_v3.py`
   - Enhanced `signal_algo` definition in `strategy_params` with appropriate options

2. **Parameter Registry**:
   - Added `get_signal_algo()` method to `ParameterRegistry` class to retrieve the signal algorithm

3. **Parameter Conversion**:
   - Updated `V3_perf_repadapt_paramconvert.py` to use `signal_algo` directly
   - Removed all references to the legacy `strategy` parameter

4. **Documentation**:
   - Updated `v3_config_parameter_mapping.md` to mark `strategy` as deprecated
   - Enhanced `signal_algo` description to indicate it replaces `strategy`

## How to Use

### In V3 Engine Code

Use the `signal_algo` parameter from the registry:

```python
from v3_engine.parameter_registry import get_registry

# Get directly from the registry
registry = get_registry()
signal_algo = registry.get_signal_algo()

# Use the signal_algo value to determine the strategy
if signal_algo == 'ema':
    # Use EMA strategy
elif signal_algo == 'momentum':
    # Use momentum strategy
else:
    # Use default or equal_weight strategy
```

### In Configuration Files

Define `signal_algo` in `strategy_params`:

```python
strategy_params = {
    # Other parameters...
    'signal_algo': CategoricalParam(
        id='signal_algo',
        default='ema',
        choices=['ema', 'momentum', 'equal_weight'],
        meta={
            'description': 'Algorithm for signal generation (replaces strategy parameter)',
            'v3_standard': True
        }
    )
}
```

## Future Considerations

1. **Categorical List Groups**:
   - Future development will treat signal algorithms as categorical list groups for optimization
   - This will allow for more flexible strategy selection and optimization

2. **Strategy Parameter Sets**:
   - Each strategy will define its own set of parameters
   - These will be automatically included in the GUI when the strategy is selected

3. **Parameter Flow**:
   - Parameters will flow consistently from definition → GUI → engine → reports
   - This will ensure a seamless user experience and consistent behavior

## Related Documentation

- [v3_config_parameter_mapping.md](v3_config_parameter_mapping.md): Updated parameter mapping matrix
- [v3_parameter_classification.md](v3_parameter_classification.md): Parameter classification documentation
- [v3_parameter_flow.md](v3_parameter_flow.md): Parameter flow documentation
