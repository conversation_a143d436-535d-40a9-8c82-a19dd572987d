@echo off
echo Testing CPS V4 Optimization Fix...
echo.

echo Step 1: Check optimization parameters and set mode FIRST
cd /d "S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template"
echo Checking for optimization parameters in settings...
python -c "from v4.optimization_detector import has_any_optimization_parameters; import os; has_opt = has_any_optimization_parameters(); print(f'Optimization parameters found: {has_opt}'); os.environ['CPS_V4_OPTIMIZATION_ACTIVE'] = 'true' if has_opt else 'false'; print(f'Set CPS_V4_OPTIMIZATION_ACTIVE={os.environ.get(\"CPS_V4_OPTIMIZATION_ACTIVE\")}')"
if %errorlevel% neq 0 (
    echo ERROR: Optimization parameter check failed!
    pause
    exit /b 1
)

echo.
echo Step 2: Check optimization combinations count
python -c "from v4.py_reporting.v4_performance_report import PerformanceTableGenerator; ptg = PerformanceTableGenerator(); combos = ptg.get_optimization_combinations(); print(f'Found {len(combos)} optimization combinations:'); [print(f'  {i}: {combo}') for i, combo in enumerate(combos[:5])]"
if %errorlevel% neq 0 (
    echo ERROR: Optimization combinations check failed!
    pause
    exit /b 1
)

echo.
echo Step 3: Set optimization environment and run optimization
set CPS_V4_OPTIMIZATION_ACTIVE=true
echo Set CPS_V4_OPTIMIZATION_ACTIVE=%CPS_V4_OPTIMIZATION_ACTIVE%
python -c "from v4.py_reporting.v4_performance_report import PerformanceTableGenerator; ptg = PerformanceTableGenerator(csv_flag_use=True); ptg.generate_performance_table()"
if %errorlevel% neq 0 (
    echo ERROR: Optimization run failed!
    pause
    exit /b 1
)

echo.
echo SUCCESS: Optimization test completed!
echo Check the reporting folder for XLSX output and optimization_equity_curves CSV
pause
