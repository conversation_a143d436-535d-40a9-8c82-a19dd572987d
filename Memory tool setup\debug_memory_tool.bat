@echo off
echo Debug started: %date% %time% > debug_simple.log
echo Step 1: Checking Python >> debug_simple.log
python --version >> debug_simple.log 2>&1
if errorlevel 1 (
    echo Python check FAILED >> debug_simple.log
    goto end
)

echo Step 2: Checking memory_tool >> debug_simple.log
python -c "print('Trying to import memory_tool...'); import memory_tool; print('Success:', memory_tool.__file__)" >> debug_simple.log 2>&1
if errorlevel 1 (
    echo memory_tool import FAILED >> debug_simple.log
    goto end
)

:end
echo Debug completed: %date% %time% >> debug_simple.log
type debug_simple.log
pause
