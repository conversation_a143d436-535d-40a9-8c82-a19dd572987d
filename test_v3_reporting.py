"""
V3 Reporting Test Script

Tests the V3 reporting system with both simple and complex parameter sets.
"""

import os
import sys
import logging
from datetime import datetime
import pandas as pd
import numpy as np

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Add project root to path if needed
project_root = os.path.abspath(os.path.dirname(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# Import modules
from v3_engine.parameter_registry import get_registry
from v3_engine.parameters import NumericParameter, CategoricalParameter, ConfigParameter
import v3_engine.reporting_parameters  # Ensure reporting parameters are registered
from v3_reporting.v3_performance_report import generate_v3_performance_report
from v3_reporting.v3_allocation_report import generate_v3_allocation_report
from v3_reporting.v3_visualization import generate_v3_performance_charts

def create_test_results():
    """Create dummy backtest results for testing."""
    # Import config_v2 to use the exact parameters
    from config.config_v2 import config_v2, _get_param_value
    
    # Get the correct tickers from config
    tickers = config_v2['data_params']['tickers']  # ['SPY', 'SHV', 'EFA', 'TLT', 'PFF']
    
    # Create date range matching config
    start_date = config_v2['data_params']['start_date']
    end_date = config_v2['data_params']['end_date']
    dates = pd.date_range(start=start_date, end=end_date, freq='B').normalize()
    dates_str = [d.strftime('%Y-%m-%d') for d in dates]
    
    # Create portfolio values with some randomness
    np.random.seed(42)  # For reproducibility
    returns = np.random.normal(0.001, 0.01, len(dates))
    initial_capital = config_v2['backtest_params']['initial_capital']
    portfolio_values = [initial_capital]
    for r in returns:
        portfolio_values.append(portfolio_values[-1] * (1 + r))
    portfolio_values = portfolio_values[1:]  # Remove initial value
    
    # Get rebalance frequency from config
    rebalance_freq = config_v2['backtest_params']['rebalance_freq']
    if rebalance_freq == 'daily':
        freq = 'B'
    elif rebalance_freq == 'weekly':
        freq = 'W-FRI'
    elif rebalance_freq == 'monthly':
        freq = 'MS'
    elif rebalance_freq == 'quarterly':
        freq = 'QS'
    elif rebalance_freq == 'yearly':
        freq = 'AS'
    else:
        freq = 'W-FRI'  # Default to weekly
    
    # Create signal history as DataFrame with DatetimeIndex based on rebalance frequency
    signal_dates = pd.date_range(start=start_date, end=end_date, freq=freq).normalize()
    
    # Create signal data for all tickers
    signal_data = {}
    for i, ticker in enumerate(tickers):
        # Create different allocation patterns for each ticker
        # This is just for test data - in real backtest this would be based on strategy
        base_value = 1.0 / len(tickers)  # Equal weight base
        variation = np.sin(np.linspace(0, 4*np.pi, len(signal_dates))) * 0.2  # Add some variation
        signal_data[ticker] = [max(0, base_value + variation[j] * (i+1)/len(tickers)) for j in range(len(signal_dates))]
    
    # Normalize signal data to sum to 1.0 for each date
    signal_df = pd.DataFrame(signal_data, index=signal_dates)
    for date in signal_dates:
        row_sum = signal_df.loc[date].sum()
        if row_sum > 0:
            signal_df.loc[date] = signal_df.loc[date] / row_sum
    
    signal_history = signal_df.copy()
    
    # Create weights history by forward-filling signal history for all business days
    # This simulates what happens in a real backtest where weights are applied and held
    all_dates = pd.date_range(start=start_date, end=end_date, freq='B').normalize()
    weights_history = pd.DataFrame(index=all_dates, columns=tickers)
    
    # Initialize with zeros
    weights_history.fillna(0, inplace=True)
    
    # Apply signals on signal dates with execution delay
    execution_delay = _get_param_value(config_v2['backtest_params']['execution_delay'])
    
    # Apply signals with execution delay
    for i, date in enumerate(signal_dates):
        # Find the execution date (signal date + execution_delay business days)
        if execution_delay > 0:
            execution_idx = all_dates.get_indexer([date])[0] + execution_delay
            if execution_idx < len(all_dates):
                execution_date = all_dates[execution_idx]
            else:
                continue  # Skip if execution date is beyond the backtest period
        else:
            execution_date = date
        
        # Apply the signal to weights on execution date
        for ticker in tickers:
            if ticker in signal_history.columns:
                weights_history.loc[execution_date, ticker] = signal_history.loc[date, ticker]
    
    # Forward fill to ensure continuous weights
    weights_history = weights_history.ffill()
    
    # Create strategy and ticker returns (required by the adapter)
    strategy_returns = pd.Series(returns, index=dates)
    
    # Create ticker returns for all tickers in config
    ticker_returns_data = {}
    for ticker in tickers:
        # Create slightly different return patterns for each ticker
        ticker_returns_data[ticker] = np.random.normal(
            0.001 * (1 + tickers.index(ticker) * 0.2),  # Different mean for each ticker
            0.01 * (1 + tickers.index(ticker) * 0.1),   # Different volatility for each ticker
            len(dates)
        )
    ticker_returns = pd.DataFrame(ticker_returns_data, index=dates)
    
    # Create benchmark returns - use SPY as default benchmark since not specified in config_v2
    benchmark_ticker = 'SPY'  # Default benchmark ticker
    benchmark_returns = pd.Series(
        np.random.normal(0.0008, 0.008, len(dates)),  # Slightly different pattern
        index=dates,
        name=benchmark_ticker
    )
    
    # Create performance metrics
    performance = {
        'cagr': 0.15,
        'sharpe': 1.2,
        'sortino': 1.5,
        'max_drawdown': 0.02,
        'volatility': 0.10,
        'total_return': 0.15,
        'annualized_return': 0.15,
        'win_rate': 0.6,
        'turnover': 0.35,  # Add turnover metric
    }
    
    # Create trade log as DataFrame with required columns per standards doc
    # Required columns: trade_num, symbol, quantity, execution_date, execution_price, commission+slippage, amount, pnl
    num_trades = 20  # More realistic number of trades
    trade_dates = []
    trade_nums = []
    symbols = []
    quantities = []
    execution_prices = []
    commission_slippages = []
    amounts = []
    pnls = []
    
    # Commission and slippage rates from config
    commission_rate = config_v2['backtest_params']['commission_rate']
    slippage_rate = config_v2['backtest_params']['slippage_rate']
    
    # Generate trades for each rebalance date
    for i, date in enumerate(signal_dates[:10]):  # Use first 10 signal dates
        # Find execution date based on delay
        if execution_delay > 0:
            execution_idx = all_dates.get_indexer([date])[0] + execution_delay
            if execution_idx < len(all_dates):
                execution_date = all_dates[execution_idx]
            else:
                continue
        else:
            execution_date = date
        
        # For each ticker, generate a trade
        for j, ticker in enumerate(tickers):
            # Only generate trades for some tickers to be realistic
            if np.random.random() > 0.3:  # 70% chance of trading this ticker
                trade_dates.append(execution_date)
                trade_nums.append(len(trade_nums) + 1)
                symbols.append(ticker)
                
                # Quantity - positive for buy, negative for sell
                quantity = int(np.random.normal(0, 100))
                quantities.append(quantity)
                
                # Price - use a realistic price for each ticker
                base_prices = {'SPY': 450.0, 'SHV': 110.0, 'EFA': 75.0, 'TLT': 95.0, 'PFF': 35.0}
                price = base_prices.get(ticker, 100.0) * (1 + np.random.normal(0, 0.02))
                execution_prices.append(round(price, 3))
                
                # Commission + slippage
                commission_slippage = abs(quantity * price * (commission_rate + slippage_rate))
                commission_slippages.append(round(commission_slippage, 2))
                
                # Amount (total trade value including commission/slippage)
                amount = quantity * price
                amounts.append(round(amount, 2))
                
                # PnL (0 for new positions, calculated for exits)
                pnl = 0.0 if quantity > 0 else round(np.random.normal(0, 50), 2)
                pnls.append(pnl)
    
    # Create trade log DataFrame
    trade_log = pd.DataFrame({
        'trade_num': trade_nums,
        'symbol': symbols,
        'quantity': quantities,
        'execution_date': trade_dates,
        'execution_price': execution_prices,
        'commission+slippage': commission_slippages,
        'amount': amounts,
        'pnl': pnls
    })
    trade_log.set_index('execution_date', inplace=True)
    
    # Create monthly returns for heatmap
    monthly_returns = strategy_returns.resample('ME').apply(lambda x: (1 + x).prod() - 1)
    annual_returns = strategy_returns.resample('YE').apply(lambda x: (1 + x).prod() - 1)
    
    # Calculate drawdowns for the drawdown chart
    cum_returns = (1 + strategy_returns).cumprod()
    running_max = cum_returns.cummax()
    drawdowns = (cum_returns / running_max - 1) * 100  # Convert to percentage
    
    return {
        'portfolio_value': portfolio_values,
        'dates': dates_str,
        'performance': performance,
        'signal_history': signal_history,
        'strategy_returns': strategy_returns,
        'ticker_returns': ticker_returns,
        'benchmark_returns': benchmark_returns,
        'trade_log': trade_log,
        'weights_history': weights_history,
        'monthly_returns': monthly_returns,
        'annual_returns': annual_returns,
        'drawdowns': drawdowns
    }

def register_test_parameters(registry):
    """Register test parameters with the registry to avoid 'not found in any group' warnings."""
    from v3_engine.parameters import NumericParameter, ConfigParameter, CategoricalParameter
    
    # Check if parameter groups already exist
    existing_groups = registry.get_groups()
    
    # Helper function to safely register parameters
    def safe_register(group, param_name, param_obj):
        if group not in existing_groups or param_name not in registry.get_parameters(group):
            registry.register_parameter(group, param_obj)
            return True
        else:
            logger.info(f"Parameter '{param_name}' already exists in group '{group}', skipping registration")
            return False
    
    # Register strategy parameters
    safe_register('strategy_ema', 'st_lookback', NumericParameter(
        name='st_lookback',
        default=15,
        min_value=10,
        max_value=100,
        step=5
    ))
    
    safe_register('strategy_ema', 'mt_lookback', NumericParameter(
        name='mt_lookback',
        default=70,
        min_value=20,
        max_value=200,
        step=10
    ))
    
    safe_register('strategy_ema', 'lt_lookback', NumericParameter(
        name='lt_lookback',
        default=100,
        min_value=50,
        max_value=300,
        step=10
    ))
    
    safe_register('strategy_ema', 'top_n', NumericParameter(
        name='top_n',
        default=2,
        min_value=1,
        max_value=5,
        step=1
    ))
    
    # Register core parameters
    safe_register('core', 'strategy', ConfigParameter(
        name='strategy',
        default='ema'
    ))
    
    safe_register('core', 'execution_delay', NumericParameter(
        name='execution_delay',
        default=1,
        min_value=0,
        max_value=5,
        step=1
    ))
    
    safe_register('core', 'rebalance_frequency', CategoricalParameter(
        name='rebalance_frequency',
        default='weekly',
        choices=['daily', 'weekly', 'monthly', 'quarterly', 'yearly']
    ))
    
    safe_register('core', 'initial_capital', ConfigParameter(
        name='initial_capital',
        default=1000000
    ))
    
    safe_register('core', 'commission_rate', ConfigParameter(
        name='commission_rate',
        default=0.001
    ))
    
    safe_register('core', 'slippage_rate', ConfigParameter(
        name='slippage_rate',
        default=0.001
    ))

def run_test():
    """Run tests for the V3 reporting system."""
    logger.info("Starting V3 reporting test")
    
    # Import config_v2 to use the exact parameters
    from config.config_v2 import config_v2, _get_param_value
    
    # Create output directory structure per standards
    output_dir = os.path.join('output', 'v3_test_reports')
    os.makedirs(output_dir, exist_ok=True)
    
    # Create allocation reports directory
    allocation_dir = os.path.join(output_dir, 'allocation_reports')
    os.makedirs(allocation_dir, exist_ok=True)
    
    # Create test parameters
    registry = get_registry()
    
    # Ensure all reporting parameters are registered
    v3_engine.reporting_parameters.register_reporting_parameters()
    
    # Register test parameters with the registry
    register_test_parameters(registry)
    
    # Create test parameters from config_v2
    parameters = {
        'strategy': config_v2['backtest_params']['strategy'],
        'st_lookback': _get_param_value(config_v2['strategy_params']['st_lookback']),
        'mt_lookback': _get_param_value(config_v2['strategy_params']['mt_lookback']),
        'lt_lookback': _get_param_value(config_v2['strategy_params']['lt_lookback']),
        'execution_delay': _get_param_value(config_v2['backtest_params']['execution_delay']),
        'rebalance_frequency': config_v2['backtest_params']['rebalance_freq'],
        'top_n': _get_param_value(config_v2['strategy_params']['top_n']),
        'initial_capital': config_v2['backtest_params']['initial_capital'],
        'commission_rate': config_v2['backtest_params']['commission_rate'],
        'slippage_rate': config_v2['backtest_params']['slippage_rate']
    }
    
    try:
        # Create test results
        results = create_test_results()
        
        # Get timestamp for consistent file naming
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # ===== TIER 1: ALLOCATION WEIGHTS GRAPHIC (PNG) =====
        logger.info("Generating Allocation Weights Graphic (PNG)")
        
        # Generate allocation report - do this first as it's the most reliable
        try:
            allocation_report_path = generate_v3_allocation_report(
                signal_df=results['signal_history'],
                weights_df=results['weights_history'],
                output_dir=allocation_dir,
                strategy_name='EMA_V3_1',
                timestamp=timestamp
            )
            
            logger.info(f"Generated Allocation Report: {allocation_report_path}")
        except Exception as e:
            logger.error(f"Error generating allocation report: {e}")
            import traceback
            logger.error(traceback.format_exc())
            allocation_report_path = {}
        
        # ===== TIER 1: MONTHLY & ANNUAL RETURNS GRAPHIC (PNG) =====
        logger.info("Generating Monthly & Annual Returns Graphic (PNG)")
        
        # Filename format per standards: EMA_V3_1_monthly_returns_YYYYMMDD_HHMMSS.png
        monthly_returns_path = os.path.join(output_dir, f"EMA_V3_1_monthly_returns_{timestamp}.png")
        
        # Use the visualization module to create the monthly returns chart
        try:
            from visualization.performance_charts import create_monthly_returns_chart
            create_monthly_returns_chart(
                returns=results['strategy_returns'],
                save_path=monthly_returns_path
            )
            
            logger.info(f"Generated Monthly & Annual Returns Graphic: {monthly_returns_path}")
        except Exception as e:
            logger.error(f"Error generating monthly returns chart: {e}")
            import traceback
            logger.error(traceback.format_exc())
            monthly_returns_path = ""
        
        # ===== TIER 1: COMBINED CUMULATIVE RETURNS & DRAWDOWN GRAPHIC (PNG) =====
        logger.info("Generating Combined Cumulative Returns & Drawdown Graphic (PNG)")
        
        # Filename format per standards: EMA_V3_1_cumulative_returns_drawdown_YYYYMMDD_HHMMSS.png
        combined_path = os.path.join(output_dir, f"EMA_V3_1_cumulative_returns_drawdown_{timestamp}.png")
        
        try:
            # Use the visualization module to create the combined chart
            from visualization.performance_charts import create_combined_returns_drawdown_chart
            
            # Extract backtest period
            start_date = results['strategy_returns'].index[0].strftime('%Y-%m-%d')
            end_date = results['strategy_returns'].index[-1].strftime('%Y-%m-%d')
            backtest_period = (start_date, end_date)
            
            # Create the combined chart
            create_combined_returns_drawdown_chart(
                returns=results['strategy_returns'],
                benchmark_returns=results['benchmark_returns'],
                parameters=parameters,
                backtest_period=backtest_period,
                save_path=combined_path
            )
            
            logger.info(f"Generated Combined Cumulative Returns & Drawdown Graphic: {combined_path}")
        except Exception as e:
            logger.error(f"Error generating combined returns and drawdown chart: {e}")
            import traceback
            logger.error(traceback.format_exc())
            combined_path = ""
        
        # ===== TIER 1: PERFORMANCE TABLE REPORT (XLSX) =====
        # Do this last as it's the most complex and error-prone
        logger.info("Generating Performance Table Report (XLSX)")
        
        # Filename format per standards: EMA_V3_1_performance_tables_YYYYMMDD_HHMMSS.xlsx
        output_path = os.path.join(output_dir, f"EMA_V3_1_performance_tables_{timestamp}.xlsx")
        
        try:
            # Generate performance report
            report_path = generate_v3_performance_report(
                backtest_results=results,
                output_path=output_path,
                strategy_name='ema',
                parameters=parameters
            )
            
            logger.info(f"Generated Performance Table Report: {report_path}")
        except Exception as e:
            logger.error(f"Error generating performance report: {e}")
            import traceback
            logger.error(traceback.format_exc())
            report_path = ""
        
        # Print summary of generated reports
        logger.info("\nV3 reporting test completed successfully")
        logger.info("Generated reports:")
        if report_path:
            logger.info(f"1. Performance Table Report: {os.path.basename(report_path)}")
        if allocation_report_path:
            logger.info(f"2. Allocation Weights Graphic: {os.path.basename(allocation_report_path.get('chart', ''))}")
        if monthly_returns_path:
            logger.info(f"3. Monthly & Annual Returns Graphic: {os.path.basename(monthly_returns_path)}")
        if combined_path:
            logger.info(f"4. Combined Cumulative Returns & Drawdown Graphic: {os.path.basename(combined_path)}")
        
        return {
            'performance_report': report_path,
            'allocation_report': allocation_report_path,
            'monthly_returns': monthly_returns_path,
            'combined_chart': combined_path
        }
    except Exception as e:
        logger.error(f"Error in run_test: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return {}

if __name__ == "__main__":
    run_test()
