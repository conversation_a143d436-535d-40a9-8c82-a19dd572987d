"""
Equity Curve Diagnostic Tool
Shows exactly what's being calculated for strategy vs benchmark
"""

import pandas as pd
import numpy as np
from pathlib import Path
import configparser
import logging

logger = logging.getLogger(__name__)

def diagnose_equity_curves(allocation_df, price_df, config):
    """
    Diagnostic function to show strategy vs benchmark calculations
    """
    print("=" * 80)
    print("EQUITY CURVE DIAGNOSTIC ANALYSIS")
    print("=" * 80)
    
    # Get initial capital
    initial_capital = float(config.get('Backtest', 'initial_capital', fallback=1000000))
    print(f"Initial Capital: ${initial_capital:,.2f}")
    
    # Align dates
    common_dates = allocation_df.index.intersection(price_df.index)
    allocation_aligned = allocation_df.loc[common_dates]
    price_aligned = price_df.loc[common_dates]
    
    print(f"Date Range: {common_dates[0]} to {common_dates[-1]}")
    print(f"Total Days: {len(common_dates)}")
    
    # Get ticker columns
    ticker_cols = [col for col in allocation_aligned.columns
                  if col in price_aligned.columns and col not in ['Cash', 'Total']]
    print(f"Tickers: {ticker_cols}")
    
    print("\n" + "=" * 40)
    print("STRATEGY EQUITY CURVE CALCULATION")
    print("=" * 40)
    
    # Strategy equity curve
    strategy_curve = pd.Series(index=common_dates, dtype=float)
    strategy_curve.iloc[0] = initial_capital
    
    print(f"Sample Strategy Allocations (first 5 days):")
    for i in range(min(5, len(allocation_aligned))):
        date = common_dates[i]
        allocations = allocation_aligned.loc[date]
        print(f"  {date.strftime('%Y-%m-%d')}: {dict(allocations)}")
    
    # Calculate strategy performance for first few days
    for i in range(1, min(6, len(strategy_curve))):
        prev_date = common_dates[i-1]
        curr_date = common_dates[i]
        
        prev_allocations = allocation_aligned.loc[prev_date]
        prev_prices = price_aligned.loc[prev_date]
        curr_prices = price_aligned.loc[curr_date]
        
        portfolio_value = 0.0
        
        # Cash component
        cash_allocation = prev_allocations.get('Cash', 0.0)
        cash_value = strategy_curve.iloc[i-1] * cash_allocation
        portfolio_value += cash_value
        
        print(f"\n  Day {i} ({curr_date.strftime('%Y-%m-%d')}):")
        print(f"    Previous Portfolio Value: ${strategy_curve.iloc[i-1]:,.2f}")
        print(f"    Cash Allocation: {cash_allocation:.1%} = ${cash_value:,.2f}")
        
        # Ticker allocations
        for ticker in ticker_cols:
            if ticker in prev_allocations and ticker in prev_prices and ticker in curr_prices:
                allocation = prev_allocations[ticker]
                if allocation > 0 and prev_prices[ticker] > 0:
                    ticker_return = (curr_prices[ticker] - prev_prices[ticker]) / prev_prices[ticker]
                    ticker_value = strategy_curve.iloc[i-1] * allocation * (1 + ticker_return)
                    portfolio_value += ticker_value
                    
                    print(f"    {ticker}: {allocation:.1%} allocation, {ticker_return:.2%} return = ${ticker_value:,.2f}")
        
        strategy_curve.iloc[i] = portfolio_value
        print(f"    New Portfolio Value: ${portfolio_value:,.2f}")
    
    print("\n" + "=" * 40)
    print("BENCHMARK EQUITY CURVE CALCULATION")
    print("=" * 40)
    
    # Benchmark equity curve (equal weight)
    equal_weight = 1.0 / len(ticker_cols)
    print(f"Equal Weight per Ticker: {equal_weight:.1%}")
    
    benchmark_curve = pd.Series(index=common_dates, dtype=float)
    benchmark_curve.iloc[0] = initial_capital
    
    # Calculate benchmark performance for first few days
    for i in range(1, min(6, len(benchmark_curve))):
        prev_date = common_dates[i-1]
        curr_date = common_dates[i]
        
        prev_prices = price_aligned.loc[prev_date]
        curr_prices = price_aligned.loc[curr_date]
        
        portfolio_value = 0.0
        
        print(f"\n  Day {i} ({curr_date.strftime('%Y-%m-%d')}):")
        print(f"    Previous Portfolio Value: ${benchmark_curve.iloc[i-1]:,.2f}")
        
        for ticker in ticker_cols:
            if ticker in prev_prices and ticker in curr_prices and prev_prices[ticker] > 0:
                ticker_return = (curr_prices[ticker] - prev_prices[ticker]) / prev_prices[ticker]
                ticker_value = benchmark_curve.iloc[i-1] * equal_weight * (1 + ticker_return)
                portfolio_value += ticker_value
                
                print(f"    {ticker}: {equal_weight:.1%} allocation, {ticker_return:.2%} return = ${ticker_value:,.2f}")
        
        benchmark_curve.iloc[i] = portfolio_value
        print(f"    New Portfolio Value: ${portfolio_value:,.2f}")
    
    print("\n" + "=" * 40)
    print("FINAL COMPARISON")
    print("=" * 40)
    
    strategy_final = strategy_curve.iloc[-1]
    benchmark_final = benchmark_curve.iloc[-1]
    
    strategy_return = (strategy_final / initial_capital) - 1
    benchmark_return = (benchmark_final / initial_capital) - 1
    
    print(f"Strategy Final Value: ${strategy_final:,.2f} ({strategy_return:.2%} total return)")
    print(f"Benchmark Final Value: ${benchmark_final:,.2f} ({benchmark_return:.2%} total return)")
    print(f"Difference: ${strategy_final - benchmark_final:,.2f} ({strategy_return - benchmark_return:.2%})")
    
    # Save curves for inspection
    curves_df = pd.DataFrame({
        'Strategy': strategy_curve,
        'Benchmark': benchmark_curve
    })
    
    output_path = Path("v4_trace_outputs") / "equity_curves_diagnostic.csv"
    curves_df.to_csv(output_path)
    print(f"\nEquity curves saved to: {output_path}")
    
    return strategy_curve, benchmark_curve

if __name__ == "__main__":
    # Load data for diagnostic
    from v4.py_reporting.performance_table_generator import PerformanceTableGenerator
    
    generator = PerformanceTableGenerator()
    
    # Load most recent data
    signals_df, allocation_df, trade_df = generator._load_data_files()
    price_df = generator._load_price_data()
    
    # Run diagnostic
    strategy_curve, benchmark_curve = diagnose_equity_curves(allocation_df, price_df, generator.config)
