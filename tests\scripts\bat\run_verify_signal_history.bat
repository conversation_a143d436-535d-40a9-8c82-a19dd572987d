@echo off
REM Run verification script for signal history tracking and report formatting
REM This batch file activates the virtual environment and runs the verification script

echo Running Signal History and Report Format Verification...

REM Activate virtual environment
call F:\AI_Library\my_quant_env\Scripts\activate.bat

REM Set logging level
set BACKTEST_LOG_LEVEL=INFO

REM Create test output directory if it doesn't exist
if not exist "test_output" mkdir "test_output"

REM Run verification script
python tests\verify_signal_history_reporting.py

REM Check exit code
if %ERRORLEVEL% EQU 0 (
    echo Verification completed successfully!
) else (
    echo Verification failed with errors. Check the log file for details.
)

REM Pause to see results
pause
