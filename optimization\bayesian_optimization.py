"""
Bayesian optimization implementation for parameter optimization.
"""

import pandas as pd
import numpy as np
import logging
import warnings

# Try to import scikit-optimize, but provide graceful fallback if not available
try:
    from skopt import gp_minimize
    from skopt.space import Real, Integer
    from skopt.utils import use_named_args
    SKOPT_AVAILABLE = True
except ImportError:
    SKOPT_AVAILABLE = False
    warnings.warn("scikit-optimize not available. Bayesian optimization will use fallback method.")

logger = logging.getLogger(__name__)

def run_bayesian_optimization(objective_func, param_bounds, n_iter=10, evaluation_metric='sharpe_ratio', maximize=True):
    """
    Run Bayesian optimization over parameter space.
    
    Args:
        objective_func (callable): Function to evaluate with parameters
        param_bounds (dict): Dictionary of parameter names and bounds (min, max) to search
        n_iter (int): Number of iterations
        evaluation_metric (str): Metric to optimize
        maximize (bool): Whether to maximize or minimize the metric
        
    Returns:
        tuple: Best parameters and all results
    """
    logger.info(f"Running Bayesian optimization with {len(param_bounds)} parameters for {n_iter} iterations")
    
    # Check if scikit-optimize is available
    if not SKOPT_AVAILABLE:
        logger.warning("scikit-optimize not available. Using random search as fallback.")
        # Import random search as fallback
        from optimization.random_search import run_random_search
        return run_random_search(objective_func, param_bounds, n_iter, evaluation_metric, maximize)
    
    # Convert param_bounds to skopt space
    dimensions = []
    param_names = []
    
    for param_name, bounds in param_bounds.items():
        min_val, max_val = bounds
        param_names.append(param_name)
        
        if isinstance(min_val, int) and isinstance(max_val, int):
            dimensions.append(Integer(min_val, max_val, name=param_name))
        else:
            dimensions.append(Real(min_val, max_val, name=param_name))
    
    # Define the objective function for skopt
    @use_named_args(dimensions)
    def skopt_objective(**params):
        try:
            # Run objective function with these parameters
            result = objective_func(**params)
            
            # Extract score from result
            if isinstance(result, dict) and evaluation_metric in result:
                score = result[evaluation_metric]
            elif isinstance(result, (int, float)):
                score = result
            else:
                logger.warning(f"Could not extract {evaluation_metric} from result")
                return float('inf') if maximize else float('-inf')
            
            # Negate score if maximizing (skopt minimizes by default)
            return -score if maximize else score
            
        except Exception as e:
            logger.error(f"Error evaluating parameters {params}: {e}")
            return float('inf') if maximize else float('-inf')
    
    # Run Bayesian optimization
    try:
        result = gp_minimize(
            skopt_objective,
            dimensions,
            n_calls=n_iter,
            random_state=42,
            verbose=True
        )
        
        # Extract best parameters
        best_params = {param_names[i]: result.x[i] for i in range(len(param_names))}
        
        # Create results dataframe
        results = []
        for i, (x, y) in enumerate(zip(result.x_iters, result.func_vals)):
            params = {param_names[j]: x[j] for j in range(len(param_names))}
            score = -y if maximize else y  # Convert back to original score
            params_result = {**params, evaluation_metric: score}
            results.append(params_result)
        
        results_df = pd.DataFrame(results)
        
        # Sort by evaluation metric
        if not results_df.empty:
            results_df = results_df.sort_values(by=evaluation_metric, ascending=not maximize)
        
        return best_params, results_df
        
    except Exception as e:
        logger.error(f"Error in Bayesian optimization: {e}")
        logger.warning("Falling back to random search")
        # Import random search as fallback
        from optimization.random_search import run_random_search
        return run_random_search(objective_func, param_bounds, n_iter, evaluation_metric, maximize)
