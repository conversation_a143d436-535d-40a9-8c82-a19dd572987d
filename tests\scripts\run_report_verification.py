#!/usr/bin/env python
# run_report_verification.py
"""
Report Verification Runner
This script runs the report verification tests and logs the results.
"""

import os
import sys
import logging
import datetime
import subprocess
import argparse
from pathlib import Path

# Add project root to path
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.insert(0, project_root)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(os.path.join(project_root, 'tests', 'report_verification_runner.log')),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('report_verification_runner')

# Constants
CHANGE_LOG_PATH = os.path.join(project_root, 'docs', 'para_RF', 'Problem_Changes_Fixes_C_Log.md')

def run_verification_tests(report_type='all'):
    """Run verification tests for specified report type"""
    logger.info(f"Running verification tests for: {report_type}")
    
    cmd = [sys.executable, os.path.join(project_root, 'tests', 'verify_report_output.py')]
    if report_type != 'all':
        cmd.extend(['--report', report_type])
    
    try:
        logger.info(f"Executing command: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            logger.info("Verification tests passed")
            return True, result.stdout
        else:
            logger.error(f"Verification tests failed with exit code {result.returncode}")
            logger.error(f"Error output: {result.stderr}")
            return False, result.stderr
    except Exception as e:
        logger.error(f"Error running verification tests: {e}")
        return False, str(e)

def log_verification_results(success, output, report_type='all'):
    """Log verification results to the change log file"""
    timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    # Create log entry
    log_entry = f"""
## Report Verification Results - {timestamp}

### Report Type: {report_type}
**Status**: {'PASSED' if success else 'FAILED'}

#### Details:
```
{output[:500]}  # Truncated for brevity
```

#### Files Affected:
- tests/verify_report_output.py
- tests/report_validators.py

#### Next Steps:
{'All reports meet PRD standards.' if success else 'Address issues identified in the verification report.'}

---
"""
    
    # Append to change log
    try:
        with open(CHANGE_LOG_PATH, 'a') as f:
            f.write(log_entry)
        logger.info(f"Verification results logged to {CHANGE_LOG_PATH}")
        return True
    except Exception as e:
        logger.error(f"Error logging verification results: {e}")
        return False

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='Run report verification tests and log results')
    parser.add_argument('--report', choices=['performance', 'allocation', 'signal', 'all'], 
                        default='all', help='Report type to verify')
    args = parser.parse_args()
    
    # Run verification tests
    success, output = run_verification_tests(args.report)
    
    # Log results
    log_verification_results(success, output, args.report)
    
    # Return exit code
    return 0 if success else 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
