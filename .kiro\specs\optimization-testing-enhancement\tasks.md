# Implementation Plan

- [x] 1. Create new matrix optimization module structure

  - Create `v4/py_reporting/report_modules/report_matrix_optimization.py` with proper imports
  - Set up module structure with EquityCurvesManager import and logging configuration
  - _Requirements: 1.1_

- [x] 2. Migrate core optimization functions from performance_table_generator.py

  - [x] 2.1 Extract and migrate `get_optimization_combinations()` function

    - Copy function with identical signature and behavior from performance_table_generator.py
    - Ensure proper parameter parsing for ComplexN and SimpleN formats
    - _Requirements: 1.1, 1.2_

  - [x] 2.2 Extract and migrate `_run_matrix_optimization()` function

    - Copy function with EquityCurvesManager integration intact
    - Maintain matrix-based equity curve processing logic
    - _Requirements: 1.1, 1.2, 5.3_

  - [x] 2.3 Extract and migrate `_validate_single_combination()` function

    - Copy validation logic with 10-step framework support
    - Maintain validation directory and logging functionality
    - _Requirements: 1.1, 1.2, 5.1_

- [ ] 3. Migrate subprocess management functions
  - [x] 3.1 Extract and migrate `_run_pipeline_for_combination()` function

    - Copy subprocess execution logic with environment variable handling
    - Maintain timeout and error handling mechanisms
    - _Requirements: 1.1, 4.3, 4.4_

  - [x] 3.2 Extract and migrate `_create_temp_settings_for_combination()` function

    - Copy temporary settings file creation logic
    - Ensure proper INI format generation for parameter combinations
    - _Requirements: 4.1, 4.2_

  - [x] 3.3 Extract and migrate `_load_unified_portfolio_for_combination()` function

    - Copy file loading logic with combination-specific naming patterns
    - Maintain Portfolio_Value column extraction functionality
    - _Requirements: 3.1, 3.2, 3.3_

- [ ] 4. Migrate supporting utility functions
  - [x] 4.1 Extract and migrate validation framework functions

    - Copy `_setup_validation_directories()` and `_log_validation_step()` functions
    - Maintain flat-file validation artifact generation
    - _Requirements: 1.1, 5.4_

  - [x] 4.2 Extract and migrate helper functions

    - Copy `generate_combo_id()`, `_cleanup_temp_settings()`, and other utility functions
    - Ensure proper file handling and cleanup mechanisms

    - _Requirements: 1.1, 4.4_

- [ ] 5. Enhance v4_performance_report.py to expose optimization functions
  - [x] 5.1 Add imports for new matrix optimization module

    - Import all optimization functions from report_matrix_optimization module
    - Set up proper module initialization and configuration
    - _Requirements: 1.4, 5.4_

  - [x] 5.2 Create class methods that delegate to optimization module

    - Add `get_optimization_combinations()` method that calls module function
    - Add `_validate_single_combination()` method with proper parameter passing
    - Add `_run_matrix_optimization()` method with context preservation
    - _Requirements: 1.2, 1.4_

  - [x] 5.3 Ensure backward compatibility with existing interfaces

    - Verify that `test_optimization_validation.py` can call through refactored interface

    - Maintain identical function signatures and return types
    - _Requirements: 1.2, 5.4_

- [ ] 6. Fix pipeline mode detection in v4/pipeline/config.py
  - [x] 6.1 Update `determine_pipeline_mode()` to check environment variables first

    - Add check for `CPS_V4_COMBO_ID` environment variable before INI file settings
    - Force single mode when combo_id is present regardless of global optimization flag
    - _Requirements: 2.1, 2.4_

  - [x] 6.2 Add logging for pipeline mode detection debugging

    - Log environment variable values and mode selection decisions

    - Add diagnostic information for troubleshooting mode routing issues
    - _Requirements: 2.2, 4.3_

- [ ] 7. Fix file output handling in v4/pipeline/modes.py
  - [x] 7.1 Update `_create_unified_portfolio_data()` to handle combination-specific naming

    - Check for `CPS_V4_COMBO_ID` environment variable in file creation logic
    - Generate `unified_portfolio_combo_{combo_id}_{timestamp}.csv` when combo_id is present
    - _Requirements: 3.1, 3.4_

  - [x] 7.2 Ensure Portfolio_Value column is properly included in output files

    - Verify that unified portfolio data contains required Portfolio_Value column
    - Add validation to ensure numerical data integrity in equity curves
    - _Requirements: 3.3_

- [ ] 8. Add comprehensive error handling and logging
  - [x] 8.1 Enhance subprocess error reporting in optimization module

    - Capture and log both stdout and stderr from subprocess execution
    - Report return codes and command details for failed subprocess calls
    - _Requirements: 4.3, 4.4_

  - [x] 8.2 Add timeout handling for subprocess execution

    - Implement graceful timeout termination with diagnostic information
    - Log timeout events with command and environment context
    - _Requirements: 4.4_

- [ ] 9. Test integration with existing validation framework
  - [x] 9.1 Run `test_optimization_validation.py` through refactored system

    - Execute validation script using v4_performance_report.py instead of performance_table_generator.py
    - Verify that Step 3 (Single Combination Test) completes successfully
    - _Requirements: 5.1, 5.4_

  - [x] 9.2 Verify matrix optimization processes all parameter combinations

    - Confirm that all 12 parameter combinations are processed without errors
    - Validate that unique equity curves are generated for each combination
    - _Requirements: 5.3_

- [x] 10. Complete testing and validation



  - [x] 10.1 Compare outputs between original and refactored systems

    - Run identical parameter combinations through both systems
    - Verify that equity curves and performance metrics match exactly
    - _Requirements: 5.4_

  - [x] 10.2 Perform end-to-end optimization validation testing

    - Execute complete 10-step validation framework through refactored system
    - Confirm that all validation steps complete successfully
    - Generate final XLSX performance reports with real optimization results
    - _Requirements: 5.1, 5.2, 5.3, 5.4_
- [ ] 11. Enhance logging and tracing for AI automation

  - [ ] 11.1 Implement enhanced subprocess execution logging

    - Add comprehensive subprocess stdout/stderr capture to `_run_pipeline_for_combination()`
    - Log command, environment variables, working directory, and return codes
    - Create step-specific subprocess log files with format `step03__subprocess_{combo_id}.log`
    - Include timing information and resource usage metrics
    - _Requirements: 4.3, 4.4_

  - [ ] 11.2 Add pipeline mode detection debugging

    - Enhance `determine_pipeline_mode()` with detailed environment variable logging
    - Create centralized pipeline mode debug log at `optimization_validation/pipeline_mode_debug.log`
    - Log all environment variables, INI file settings, and mode selection decisions
    - Add timestamps and process IDs for multi-process debugging
    - _Requirements: 2.1, 2.2, 2.4_

  - [ ] 11.3 Implement automated error pattern analysis

    - Create `analyze_step3_failure()` function for intelligent error categorization
    - Implement pattern recognition for common failure types (timeout, file missing, permission errors)
    - Generate structured error analysis with failure type, likely cause, and suggested fixes
    - Add evidence collection from log files and system state
    - _Requirements: 4.3, 4.4, 5.1_

  - [ ] 11.4 Add performance metrics and resource tracking

    - Implement step-level timing and memory usage tracking
    - Add subprocess count monitoring and file creation tracking
    - Create performance metrics JSON files for each validation step
    - Include system resource utilization and bottleneck identification
    - _Requirements: 4.4, 5.4_

  - [ ] 11.5 Create AI-friendly error reporting interface

    - Design structured error reporting format for automated analysis
    - Implement machine-readable error codes and categorization
    - Add automated retry logic with exponential backoff for transient failures
    - Create error trend analysis and regression detection capabilities
    - _Requirements: 4.3, 4.4, 5.1, 5.4_

  - [ ] 11.6 Enhance batch file logging with structured output

    - Improve `test_optimization_fix_simple.bat` with detailed progress reporting
    - Add structured error detection and categorization at batch level
    - Implement automated log analysis and summary generation
    - Create integration with Python-level error analysis for unified reporting
    - _Requirements: 4.3, 4.4_