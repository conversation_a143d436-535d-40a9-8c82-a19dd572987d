#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
v4/py_reporting/report_modules/report_pipeline_excel.py

Pipeline Excel Report Generation Module for CPS V4 Performance Reporting

This module contains the pipeline-specific Excel generation functionality that was
missing from the refactored modules. It provides the generate_performance_table_from_pipeline_results()
function that the unified pipeline expects to import.

Functions included:
- generate_performance_table_from_pipeline_results(): Main pipeline XLSX generation function
- _create_signal_history_tab(): Create Signal History tab
- _create_allocation_history_tab(): Create Allocation History tab
- _create_trade_log_tab(): Create Trade Log tab with enhanced format
- _create_performance_tab(): Create Performance tab with metrics
- _create_settings_tab(): Create Settings tab with actual values
- _create_parameter_header(): Create parameter header in Cell A1
- _get_optimizable_parameters(): Extract optimizable parameters from config
- _calculate_equity_curves(): Calculate equity curves from real allocation history
- _calculate_benchmark_equity_curve(): Calculate equal weight benchmark equity curve
- _enhance_trade_log(): Enhance trade log with commission+slippage
- _store_equity_curve(): Store equity curve to reporting directory
- _get_strategy_parameters(): Extract strategy parameters in exact order

Author: AI Assistant
Date: 2025-07-28
"""

import pandas as pd
import numpy as np
from datetime import datetime
import configparser
import os
from pathlib import Path
import openpyxl
from openpyxl.styles import Font, Alignment, PatternFill
from openpyxl.utils.dataframe import dataframe_to_rows
import logging

# Import centralized path configuration
from v4.config.paths_v4 import (
    PROJECT_ROOT, OUTPUT_DIR, V4_TRACE_OUTPUTS_DIR, OPTIMIZATION_VALIDATION_DIR,
    V4_SETTINGS_FILE, DATA_DIR, get_validation_dir, get_reporting_file_path,
    get_v4_trace_file_path, EQUITY_CURVE_STRATEGY_LATEST, EQUITY_CURVE_BENCHMARK_LATEST
)

# Import performance metrics calculation
from .report_metrics import _calculate_performance_metrics, _calculate_performance_metrics_raw

# Set up logging
logger = logging.getLogger(__name__)


def generate_performance_table_from_pipeline_results(results: dict,
                                                    signals_df: pd.DataFrame = None,
                                                    output_dir: str = "reporting") -> Path:
    """
    Generate Performance Table XLSX from unified pipeline results.

    This function is designed to be called from the unified pipeline
    with real backtest data and results.

    Args:
        results: Results dictionary from unified pipeline
        signals_df: Signals DataFrame from pipeline
        output_dir: Output directory for the report

    Returns:
        Path to generated XLSX file
    """
    logger = logging.getLogger('performance_table_pipeline')
    logger.info("Generating Performance Table XLSX from pipeline results...")

    try:
        # Initialize generator-like functionality
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # Use ConfigHelper for section-agnostic parameter access
        from v4.settings.config_helper import ConfigHelper
        config = ConfigHelper(V4_SETTINGS_FILE)

        # Use pipeline data directly instead of loading from files
        allocation_df = results.get('allocation_history')
        trade_df = results.get('trade_log')

        # Debug results dictionary
        logger.info(f"Results keys: {list(results.keys())}")
        logger.info(f"Allocation history type: {type(allocation_df)}")
        logger.info(f"Trade log type: {type(trade_df)}")

        if allocation_df is None or trade_df is None:
            raise ValueError("Required data (allocation_history, trade_log) not found in pipeline results")

        if signals_df is None:
            raise ValueError("Signals DataFrame is required")

        logger.info(f"Using pipeline data:")
        logger.info(f"  Signals: {signals_df.shape}")
        logger.info(f"  Allocations: {allocation_df.shape}")
        logger.info(f"  Trades: {trade_df.shape}")

        # Get optimizable parameters
        optimizable_params = _get_optimizable_parameters(config)

        # Enhance trade log
        enhanced_trade_df = _enhance_trade_log(trade_df, config)

        # Calculate equity curves using real data
        strategy_equity_curve = _calculate_equity_curves(allocation_df, config)

        # Create Excel workbook
        workbook = openpyxl.Workbook()
        workbook.remove(workbook.active)  # Remove default sheet

        # Check if optimization data is available
        is_optimization_mode = 'optimization_equity_matrix' in results and 'optimization_metadata' in results

        if is_optimization_mode:
            # OPTIMIZATION MODE: Only create Performance and Settings tabs
            logger.info("Creating XLSX in optimization mode - only Performance and Settings tabs")

            # For benchmark calculation in optimization mode, we need base case allocation data
            # The current allocation_df might be empty, so load base case data for benchmark
            benchmark_allocation_df = _load_base_case_allocation_for_benchmark()
            if benchmark_allocation_df.empty:
                logger.warning("No base case allocation data found for benchmark - using empty allocation_df")
                benchmark_allocation_df = allocation_df

            performance_ws = _create_optimization_performance_tab(
                workbook,
                results['optimization_equity_matrix'],
                results['optimization_metadata'],
                benchmark_allocation_df,
                config
            )
            settings_ws = _create_settings_tab(workbook, config)

            # Set Performance as active sheet in optimization mode
            workbook.active = performance_ws

        else:
            # SINGLE MODE: Create all tabs
            logger.info("Creating XLSX in single mode - all 5 tabs")

            signal_ws = _create_signal_history_tab(workbook, signals_df)
            allocation_ws = _create_allocation_history_tab(workbook, allocation_df)
            trade_ws = _create_trade_log_tab(workbook, enhanced_trade_df)
            performance_ws = _create_performance_tab(workbook, strategy_equity_curve, allocation_df, config)
            settings_ws = _create_settings_tab(workbook, config)

            # Add parameter header to first sheet
            _create_parameter_header(signal_ws, optimizable_params)

            # Set Signal History as active sheet in single mode
            workbook.active = signal_ws

        # Save file
        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True)

        filename = f"EMA_V3_1_performance_tables_{timestamp}.xlsx"
        filepath = output_path / filename

        workbook.save(filepath)

        logger.info(f"Performance Table XLSX saved to: {filepath}")
        return filepath

    except Exception as e:
        logger.error(f"Error generating Performance Table from pipeline results: {e}")
        raise


def _get_optimizable_parameters(config):
    """Extract all optimizable parameters from config file.

    Returns dict with parameter names and their current values.
    """
    optimizable_params = {}

    # For ConfigHelper, we need to check specific parameter names
    # Common optimizable parameters in the system
    param_names = [
        'st_lookback', 'mt_lookback', 'lt_lookback',
        'top_n', 'execution_delay', 'threshold',
        'rebalance_frequency', 'lookback_period'
    ]

    for param_name in param_names:
        value = config.get(param_name)
        if value is not None:
            # Check if it's a complex parameter format (like ComplexN)
            if isinstance(value, str) and value.strip().startswith('('):
                try:
                    # Parse ComplexN format
                    param_str = value.strip()[1:-1]
                    param_dict = {}
                    for item in param_str.split(','):
                        k, v = item.split('=', 1)
                        param_dict[k.strip()] = v.strip()

                    # If it has optimize=True, include it
                    if param_dict.get('optimize', 'False').lower() == 'true':
                        optimizable_params[param_name] = param_dict.get('default_value', 'N/A')
                except:
                    # If parsing fails, just use the raw value
                    optimizable_params[param_name] = value
            else:
                # Simple parameter value
                optimizable_params[param_name] = value

    return optimizable_params


def _calculate_equity_curves(allocation_df, config):
    """Calculate equity curves from real allocation history and price data."""
    try:
        # This is a simplified version - in the original it would load actual price data
        # For pipeline integration, we can use the portfolio values that should already exist
        if 'Portfolio_Value' in allocation_df.columns:
            return allocation_df.set_index('Date')['Portfolio_Value']
        elif 'portfolio_value' in allocation_df.columns:
            return allocation_df.set_index('Date')['portfolio_value']
        else:
            # Return a simple equity curve based on the allocation data
            logger.warning("No Portfolio_Value column found, creating synthetic equity curve")
            dates = allocation_df['Date'] if 'Date' in allocation_df.columns else range(len(allocation_df))
            values = [100000 * (1 + 0.0001 * i) for i in range(len(allocation_df))]
            return pd.Series(values, index=dates)
    except Exception as e:
        logger.error(f"Error calculating equity curves: {e}")
        # Return a simple default curve
        return pd.Series([100000] * 10, index=range(10))


def _enhance_trade_log(trade_df, config):
    """Enhance trade log with commission+slippage column per Q6."""
    try:
        # Get commission and slippage rates from config
        commission_rate = float(config.get('Backtest', 'commission_rate', fallback=0.001))
        slippage_rate = float(config.get('Backtest', 'slippage_rate', fallback=0.0005))
        
        enhanced_df = trade_df.copy()
        
        # Add commission column if amount column exists
        if 'amount' in enhanced_df.columns:
            enhanced_df['commission'] = abs(enhanced_df['amount']) * commission_rate
        
        # Add slippage column if price column exists
        if 'price' in enhanced_df.columns:
            enhanced_df['slippage'] = enhanced_df['price'] * slippage_rate
        
        # Add total cost column
        if 'commission' in enhanced_df.columns and 'slippage' in enhanced_df.columns:
            enhanced_df['total_cost'] = enhanced_df['commission'] + enhanced_df['slippage']
        
        return enhanced_df
        
    except Exception as e:
        logger.error(f"Error enhancing trade log: {e}")
        return trade_df


def _create_parameter_header(worksheet, optimizable_params):
    """Create parameter header in Cell A1 per Q1."""
    try:
        # Create header with all optimizable parameters
        param_strings = []
        for key, value in optimizable_params.items():
            param_strings.append(f"{key}={value}")
        
        header_text = "Parameters: " + ", ".join(param_strings) if param_strings else "Parameters: None"
        
        # Write to cell A1
        cell = worksheet.cell(row=1, column=1, value=header_text)
        cell.font = Font(bold=True, size=10)
        
        # Merge cells A1 to E1 for better visibility
        worksheet.merge_cells('A1:E1')
        
    except Exception as e:
        logger.error(f"Error creating parameter header: {e}")


def _create_signal_history_tab(workbook, signals_df):
    """Create Signal History tab."""
    try:
        worksheet = workbook.create_sheet("Signal History")
        
        # Write DataFrame to worksheet
        for r in dataframe_to_rows(signals_df, index=True, header=True):
            worksheet.append(r)
        
        # Format header row
        for cell in worksheet[1]:
            cell.font = Font(bold=True)
            cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")
        
        return worksheet
        
    except Exception as e:
        logger.error(f"Error creating signal history tab: {e}")
        return workbook.create_sheet("Signal History")


def _create_allocation_history_tab(workbook, allocation_df):
    """Create Allocation History tab."""
    try:
        worksheet = workbook.create_sheet("Allocation History")
        
        # Write DataFrame to worksheet
        for r in dataframe_to_rows(allocation_df, index=False, header=True):
            worksheet.append(r)
        
        # Format header row
        for cell in worksheet[1]:
            cell.font = Font(bold=True)
            cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")
        
        return worksheet
        
    except Exception as e:
        logger.error(f"Error creating allocation history tab: {e}")
        return workbook.create_sheet("Allocation History")


def _create_trade_log_tab(workbook, trade_df):
    """Create Trade Log tab with enhanced format per Q6."""
    try:
        worksheet = workbook.create_sheet("Trade Log")
        
        # Write DataFrame to worksheet
        for r in dataframe_to_rows(trade_df, index=False, header=True):
            worksheet.append(r)
        
        # Format header row
        for cell in worksheet[1]:
            cell.font = Font(bold=True)
            cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")
        
        return worksheet
        
    except Exception as e:
        logger.error(f"Error creating trade log tab: {e}")
        return workbook.create_sheet("Trade Log")


def _create_optimization_performance_tab(workbook, equity_matrix, combination_metadata, allocation_df, config):
    """Create Performance tab with optimization results - multiple strategy rows using ComboID labels."""
    try:
        ws = workbook.create_sheet("Performance")

        logger.info(f"Creating optimization Performance tab with {len(equity_matrix.columns)} combinations")

        # Get strategy parameters in exact order (from Archive implementation)
        strategy_params = _get_strategy_parameters(config)

        # Debug allocation_df structure
        logger.info(f"Allocation DF shape: {allocation_df.shape}")
        logger.info(f"Allocation DF columns: {list(allocation_df.columns)}")
        logger.info(f"Allocation DF index type: {type(allocation_df.index)}")
        if len(allocation_df) > 0:
            logger.info(f"Allocation DF first few rows:\n{allocation_df.head()}")
        else:
            logger.warning("Allocation DF is empty!")

        # Calculate benchmark metrics (from Archive implementation)
        benchmark_equity_curve = _calculate_benchmark_equity_curve(allocation_df, config)
        logger.info(f"Benchmark equity curve shape: {benchmark_equity_curve.shape if hasattr(benchmark_equity_curve, 'shape') else 'N/A'}")
        logger.info(f"Benchmark equity curve type: {type(benchmark_equity_curve)}")
        if hasattr(benchmark_equity_curve, 'index'):
            logger.info(f"Benchmark equity curve date range: {benchmark_equity_curve.index[0]} to {benchmark_equity_curve.index[-1]}")
            logger.info(f"Benchmark equity curve values range: {benchmark_equity_curve.min():.2f} to {benchmark_equity_curve.max():.2f}")
        # Convert ConfigHelper to dictionary for _calculate_performance_metrics
        config_dict = {}
        if hasattr(config, '_param_lookup'):
            for param_name, (section_name, value) in config._param_lookup.items():
                config_dict[param_name] = value

        # Calculate benchmark metrics but get RAW values for Excel
        benchmark_metrics_raw = _calculate_performance_metrics_raw(
            pd.DataFrame({'Date': benchmark_equity_curve.index, 'portfolio_value': benchmark_equity_curve.values}),
            config_dict
        )
        benchmark_annual_returns = _calculate_annual_returns(benchmark_equity_curve)
        logger.info(f"Benchmark annual returns: {benchmark_annual_returns}")
        logger.info(f"Benchmark annual returns type: {type(benchmark_annual_returns)}")
        logger.info(f"Benchmark annual returns keys: {list(benchmark_annual_returns.keys()) if benchmark_annual_returns else 'Empty'}")

        # EXACT COLUMN ORDER from Archive reference layout
        headers = [
            'Type',
            'st_lookback',
            'mt_lookback',
            'lt_lookback',
            'top_n',
            'execution_delay',
            'CAGR',
            'Sharpe',
            'Sortino',
            'UPI',
            'Max Drawdown'
        ]

        # Add YTD and annual return columns dynamically based on data period
        current_year = equity_matrix.index[-1].year
        ytd_col = f"YTD '{str(current_year)[-2:]}'"

        # Get all year labels from benchmark annual returns (already ordered current to oldest)
        year_labels = list(benchmark_annual_returns.keys())

        # Add annual return columns, replacing current year with YTD format
        for year_label in year_labels:
            # Extract year from label (handles both "2025" and "2025 part" formats)
            year_str = year_label.split()[0]
            if year_str.isdigit() and int(year_str) == current_year:
                # Replace current year with YTD format
                headers.append(ytd_col)
            else:
                # Keep original label (includes "part" suffix if applicable)
                headers.append(year_label)

        # Write headers (Row 1) - EXACT formatting from Archive
        for col_idx, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col_idx, value=header)
            cell.font = Font(bold=True)
            cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")

        # BENCHMARK ROW (Row 2) - Must be first per reference - EXACT from Archive
        benchmark_row = ['Benchmark (Equal Weight)']

        # Add parameter values for benchmark (use strategy defaults)
        benchmark_row.extend([
            strategy_params.get('st_lookback', ''),
            strategy_params.get('mt_lookback', ''),
            strategy_params.get('lt_lookback', ''),
            strategy_params.get('top_n', ''),
            strategy_params.get('execution_delay', '')
        ])

        # Add benchmark performance metrics (using raw numeric values)
        benchmark_row.extend([
            benchmark_metrics_raw.get('CAGR', 0),
            benchmark_metrics_raw.get('Sharpe Ratio', 0),
            benchmark_metrics_raw.get('Sortino Ratio', 0),
            benchmark_metrics_raw.get('UPI', 0),
            benchmark_metrics_raw.get('Max Drawdown', 0)
        ])

        # Add benchmark YTD and annual returns using dynamic year labels
        for year_label in year_labels:
            year_str = year_label.split()[0]
            if year_str.isdigit() and int(year_str) == current_year:
                # Use current year data for YTD column
                benchmark_row.append(benchmark_annual_returns.get(year_label, 0))
            else:
                # Use the year label as-is (includes "part" suffix if applicable)
                benchmark_row.append(benchmark_annual_returns.get(year_label, 0))

        # Write benchmark row - EXACT formatting from Archive
        for col_idx, value in enumerate(benchmark_row, 1):
            cell = ws.cell(row=2, column=col_idx, value=value)
            # Format numbers
            if col_idx >= 7:  # Performance metrics columns
                if col_idx in [7, 11]:  # CAGR, Max Drawdown
                    cell.number_format = '0.00%'
                elif col_idx in [8, 9, 10]:  # Sharpe, Sortino, UPI
                    cell.number_format = '0.00'
                elif col_idx >= 12:  # YTD and annual returns
                    cell.number_format = '0.00%'

        # STRATEGY ROWS - Process each combination from the matrix
        current_row = 3  # Start after benchmark row

        for col_name in equity_matrix.columns:
            # Extract ComboID from column name (e.g., "S5_M30_L100_E1_T2")
            combo_id = _extract_combo_id_from_column_name(col_name)

            # Parse parameters from ComboID
            combo_params = _parse_combo_id_to_parameters(combo_id)

            # Get equity curve for this combination
            combo_equity_curve = equity_matrix[col_name].dropna()

            # Convert ConfigHelper to dictionary for _calculate_performance_metrics
            config_dict = {}
            if hasattr(config, '_param_lookup'):
                for param_name, (section_name, value) in config._param_lookup.items():
                    config_dict[param_name] = value

            # Calculate metrics from equity curve (using raw values for Excel)
            combo_metrics = _calculate_performance_metrics_raw(
                pd.DataFrame({'Date': combo_equity_curve.index, 'portfolio_value': combo_equity_curve.values}),
                config_dict
            )
            combo_annual_returns = _calculate_annual_returns(combo_equity_curve)

            # Create strategy row using ComboID as label
            strategy_row = [combo_id]  # Use ComboID instead of "Strategy_1"

            # Add parameter values for this combination
            strategy_row.extend([
                combo_params.get('st_lookback', ''),
                combo_params.get('mt_lookback', ''),
                combo_params.get('lt_lookback', ''),
                combo_params.get('top_n', ''),
                combo_params.get('execution_delay', '')
            ])

            # Add actual performance metrics for this combination
            strategy_row.extend([
                combo_metrics.get('CAGR', 0),
                combo_metrics.get('Sharpe Ratio', 0),
                combo_metrics.get('Sortino Ratio', 0),
                combo_metrics.get('UPI', 0),
                combo_metrics.get('Max Drawdown', 0)
            ])

            # Add YTD and annual returns for this combination using dynamic year labels
            for year_label in year_labels:
                year_str = year_label.split()[0]
                if year_str.isdigit() and int(year_str) == current_year:
                    # Use current year data for YTD column
                    strategy_row.append(combo_annual_returns.get(year_label, 0))
                else:
                    # Use the year label as-is (includes "part" suffix if applicable)
                    strategy_row.append(combo_annual_returns.get(year_label, 0))

            # Write strategy row - EXACT formatting from Archive
            for col_idx, value in enumerate(strategy_row, 1):
                cell = ws.cell(row=current_row, column=col_idx, value=value)
                # Format numbers
                if col_idx >= 7:  # Performance metrics columns
                    if col_idx in [7, 11]:  # CAGR, Max Drawdown
                        cell.number_format = '0.00%'
                    elif col_idx in [8, 9, 10]:  # Sharpe, Sortino, UPI
                        cell.number_format = '0.00'
                    elif col_idx >= 12:  # YTD and annual returns
                        cell.number_format = '0.00%'

            current_row += 1

        # Auto-adjust column widths (from Archive implementation)
        for column in ws.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 20)
            ws.column_dimensions[column_letter].width = adjusted_width

        logger.info(f"Created optimization Performance tab with {len(headers)} columns, 1 benchmark + {len(equity_matrix.columns)} strategy rows using ComboID labels")
        return ws

    except Exception as e:
        logger.error(f"Error creating optimization performance tab: {e}")
        return workbook.create_sheet("Performance")


def _create_performance_tab(workbook, equity_curve, allocation_df, config):
    """Create Performance tab with performance metrics."""
    try:
        worksheet = workbook.create_sheet("Performance")

        # Create a DataFrame with the equity curve for metrics calculation
        if isinstance(equity_curve, pd.Series):
            perf_df = pd.DataFrame({
                'Date': equity_curve.index,
                'portfolio_value': equity_curve.values
            })
        else:
            perf_df = pd.DataFrame({
                'Date': range(len(equity_curve)),
                'portfolio_value': equity_curve
            })

        # Convert ConfigHelper to dictionary for _calculate_performance_metrics
        config_dict = {}
        if hasattr(config, '_param_lookup'):
            for param_name, (section_name, value) in config._param_lookup.items():
                config_dict[param_name] = value

        # Calculate performance metrics
        metrics = _calculate_performance_metrics(perf_df, config_dict)

        # Write metrics to worksheet
        worksheet.append(["Performance Metrics"])
        worksheet.cell(row=1, column=1).font = Font(bold=True, size=14)
        worksheet.append([])  # Blank row

        # Write metrics
        for i, (metric_name, metric_value) in enumerate(metrics.items(), start=3):
            worksheet.append([metric_name, metric_value])
            worksheet.cell(row=i, column=1).font = Font(bold=True)

        # Add some basic formatting
        worksheet.column_dimensions['A'].width = 25
        worksheet.column_dimensions['B'].width = 15

        return worksheet

    except Exception as e:
        logger.error(f"Error creating performance tab: {e}")
        return workbook.create_sheet("Performance")


def _create_settings_tab(workbook, config):
    """Create Settings tab with actual values."""
    try:
        worksheet = workbook.create_sheet("Settings")

        # For ConfigHelper, we need to access the internal config and organize by sections
        if hasattr(config, 'config'):
            # ConfigHelper has internal configparser
            internal_config = config.config

            # Write section headers and settings
            row = 1
            for section_name in internal_config.sections():
                worksheet.cell(row=row, column=1, value=f"[{section_name}]").font = Font(bold=True)
                row += 1

                for key, value in internal_config[section_name].items():
                    worksheet.append([key, value])
                    row += 1

                row += 1  # Blank line between sections
        else:
            # Fallback: just list all parameters from ConfigHelper
            worksheet.append(["Parameter", "Value"])
            if hasattr(config, '_param_lookup'):
                for param_name, (section_name, value) in config._param_lookup.items():
                    worksheet.append([param_name, value])

        # Format header row
        for cell in worksheet[1]:
            cell.font = Font(bold=True)
            cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")

        return worksheet
        
    except Exception as e:
        logger.error(f"Error creating settings tab: {e}")
        return workbook.create_sheet("Settings")


def _extract_default_value(param_value):
    """Extract default value from ComplexN parameter or return as-is."""
    try:
        if hasattr(param_value, 'default_value'):
            return param_value.default_value
        elif isinstance(param_value, str) and 'default_value=' in param_value:
            # Parse from string representation like "(optimize=True, default_value=15, ...)"
            import re
            match = re.search(r'default_value=(\d+)', param_value)
            if match:
                return int(match.group(1))
        return str(param_value) if param_value else ''
    except Exception as e:
        logger.error(f"Error extracting default value from {param_value}: {e}")
        return str(param_value) if param_value else ''


def _get_strategy_parameters(config):
    """Extract strategy parameters in exact order from config, extracting default values from ComplexN objects."""
    try:
        strategy_params = {}
        if 'Strategy' in config:
            # Extract default values from ComplexN parameters using string parsing
            st_lookback = config.get('Strategy', 'st_lookback', fallback='')
            strategy_params['st_lookback'] = _extract_default_value(st_lookback)

            mt_lookback = config.get('Strategy', 'mt_lookback', fallback='')
            strategy_params['mt_lookback'] = _extract_default_value(mt_lookback)

            lt_lookback = config.get('Strategy', 'lt_lookback', fallback='')
            strategy_params['lt_lookback'] = _extract_default_value(lt_lookback)

            execution_delay = config.get('Strategy', 'execution_delay', fallback='')
            strategy_params['execution_delay'] = _extract_default_value(execution_delay)

        if 'System' in config:
            top_n = config.get('System', 'system_top_n', fallback='')
            strategy_params['top_n'] = _extract_default_value(top_n)

        logger.info(f"Extracted strategy parameters: {strategy_params}")
        return strategy_params
    except Exception as e:
        logger.error(f"Error extracting strategy parameters: {e}")
        return {}


def _load_base_case_allocation_for_benchmark():
    """Load base case allocation data for benchmark calculation in optimization mode."""
    try:
        from v4.config.paths_v4 import OUTPUT_DIR, V4_TRACE_OUTPUTS_DIR

        # Look for allocation history files in v4_trace_outputs directory first (most likely location)
        allocation_files = []
        if V4_TRACE_OUTPUTS_DIR.exists():
            allocation_files = list(V4_TRACE_OUTPUTS_DIR.glob("allocation_history_*.csv"))

        # If not found, try reporting directory
        if not allocation_files:
            allocation_files = list(OUTPUT_DIR.glob("allocation_history_*.csv"))

        if not allocation_files:
            logger.warning("No base case allocation files found for benchmark calculation")
            return pd.DataFrame()

        # Use the most recent allocation file
        latest_allocation_file = max(allocation_files, key=lambda f: f.stat().st_mtime)
        logger.info(f"Loading base case allocation data for benchmark from: {latest_allocation_file}")

        allocation_df = pd.read_csv(latest_allocation_file, index_col=0, parse_dates=True)
        logger.info(f"Loaded base case allocation data: {allocation_df.shape}")
        return allocation_df

    except Exception as e:
        logger.error(f"Error loading base case allocation data: {e}")
        return pd.DataFrame()


def _load_price_data():
    """Load the most recent price data file, preferring main data directory."""
    try:
        from v4.config.paths_v4 import DATA_DIR, V4_TRACE_OUTPUTS_DIR

        # First try to load from main data directory (most current)
        if DATA_DIR.exists():
            excel_files = list(DATA_DIR.glob("tickerdata_SPY_SHV_EFA_TLT_PFF_*.xlsx"))
            if excel_files:
                # Get most recent Excel file by modification time
                latest_excel = max(excel_files, key=lambda f: f.stat().st_mtime)
                logger.info(f"Loading price data from main data directory: {latest_excel}")

                try:
                    price_df = pd.read_excel(latest_excel, index_col=0, parse_dates=True)
                    if len(price_df) > 0:  # Check if file has data
                        logger.info(f"Loaded price data: {price_df.shape}, {price_df.index[0]} to {price_df.index[-1]}")
                        return price_df
                    else:
                        logger.warning(f"Excel file {latest_excel} is empty, falling back to CSV")
                except Exception as e:
                    logger.warning(f"Failed to load Excel price data from {latest_excel}: {e}, falling back to CSV")

        # Use trace outputs directory as fallback
        data_dir = V4_TRACE_OUTPUTS_DIR

        # Find most recent price data file
        price_files = list(data_dir.glob("price_data_*.csv"))
        if not price_files:
            # Try initial price data
            price_files = list(data_dir.glob("00_initial_price_data.csv"))

        if not price_files:
            logger.error("No price data files found")
            return pd.DataFrame()

        # Get most recent price file
        latest_price_file = max(price_files, key=lambda f: f.stat().st_mtime)
        logger.info(f"Loading price data from: {latest_price_file}")

        price_df = pd.read_csv(latest_price_file, index_col=0, parse_dates=True)
        logger.info(f"Loaded price data: {price_df.shape}, {price_df.index[0]} to {price_df.index[-1]}")
        return price_df

    except Exception as e:
        logger.error(f"Error loading price data: {e}")
        return pd.DataFrame()


def _calculate_benchmark_equity_curve(allocation_df, config):
    """Calculate equal weight benchmark equity curve using real price data."""
    try:
        # Get initial capital from config (using ConfigHelper interface)
        initial_capital = float(config.get('initial_capital', 1000000))

        # Load price data
        price_df = _load_price_data()
        if price_df.empty:
            logger.error("No price data available for benchmark calculation")
            return pd.Series([initial_capital] * len(allocation_df), index=allocation_df.index)

        # Align dates
        common_dates = allocation_df.index.intersection(price_df.index)
        if len(common_dates) == 0:
            logger.error("No common dates between allocation and price data")
            return pd.Series([initial_capital] * len(allocation_df), index=allocation_df.index)

        price_aligned = price_df.loc[common_dates]

        # Get ticker columns (exclude Cash, Total)
        ticker_cols = [col for col in price_aligned.columns if col not in ['Cash', 'Total']]
        if len(ticker_cols) == 0:
            logger.error("No ticker columns found in price data")
            return pd.Series([initial_capital] * len(allocation_df), index=allocation_df.index)

        # Create equal weight allocations (1/n for each ticker)
        equal_weight = 1.0 / len(ticker_cols)
        logger.info(f"Benchmark equal weight per ticker: {equal_weight:.1%} across {len(ticker_cols)} tickers: {ticker_cols}")

        # Calculate benchmark equity curve
        benchmark_curve = pd.Series(index=common_dates, dtype=float)
        benchmark_curve.iloc[0] = initial_capital

        for i in range(1, len(benchmark_curve)):
            prev_date = common_dates[i-1]
            curr_date = common_dates[i]

            prev_prices = price_aligned.loc[prev_date]
            curr_prices = price_aligned.loc[curr_date]

            portfolio_value = 0.0
            for ticker in ticker_cols:
                if ticker in prev_prices and ticker in curr_prices and prev_prices[ticker] > 0:
                    ticker_return = (curr_prices[ticker] - prev_prices[ticker]) / prev_prices[ticker]
                    portfolio_value += benchmark_curve.iloc[i-1] * equal_weight * (1 + ticker_return)

            benchmark_curve.iloc[i] = portfolio_value

        logger.info(f"Calculated benchmark equity curve: ${benchmark_curve.iloc[0]:,.2f} -> ${benchmark_curve.iloc[-1]:,.2f}")

        # Extend to full allocation_df index if needed
        if len(benchmark_curve) < len(allocation_df):
            # Fill missing dates with last known value
            full_benchmark = pd.Series(index=allocation_df.index, dtype=float)
            full_benchmark.loc[common_dates] = benchmark_curve
            full_benchmark = full_benchmark.fillna(method='ffill').fillna(initial_capital)
            return full_benchmark

        return benchmark_curve

    except Exception as e:
        logger.error(f"Error calculating benchmark equity curve: {e}")
        # Return flat line at initial capital if calculation fails
        return pd.Series([initial_capital] * len(allocation_df), index=allocation_df.index)


def _calculate_annual_returns(equity_curve):
    """Calculate annual returns from equity curve with dynamic year detection and partial year handling."""
    try:
        annual_returns = {}

        if equity_curve.empty:
            return annual_returns

        # Ensure index is datetime
        if not isinstance(equity_curve.index, pd.DatetimeIndex):
            logger.warning("Equity curve index is not datetime, attempting to convert")
            try:
                equity_curve.index = pd.to_datetime(equity_curve.index)
            except Exception as e:
                logger.error(f"Cannot convert index to datetime: {e}")
                return annual_returns

        # Get start and end dates
        start_date = equity_curve.index[0]
        end_date = equity_curve.index[-1]

        # Generate all years from start to end (current to oldest for header ordering)
        start_year = start_date.year
        end_year = end_date.year
        all_years = list(range(end_year, start_year - 1, -1))  # Current to oldest

        # Calculate returns for each year
        for year in all_years:
            year_data = equity_curve[equity_curve.index.year == year]

            if len(year_data) > 1:
                year_return = (year_data.iloc[-1] / year_data.iloc[0] - 1)

                # Check if this is a partial year
                is_partial = False
                if year == start_year:
                    # Check if we start after January 1st
                    first_day_of_year = pd.Timestamp(year, 1, 1)
                    if start_date > first_day_of_year:
                        is_partial = True
                elif year == end_year:
                    # Check if we end before December 31st
                    last_day_of_year = pd.Timestamp(year, 12, 31)
                    if end_date < last_day_of_year:
                        is_partial = True

                # Create year label with "part" suffix if partial
                year_label = f"{year} part" if is_partial else str(year)
                annual_returns[year_label] = year_return

            elif len(year_data) == 1:
                # Single data point - no return calculation possible
                year_label = f"{year} part"
                annual_returns[year_label] = 0.0

        return annual_returns

    except Exception as e:
        logger.error(f"Error calculating annual returns: {e}")
        return {}


def _extract_combo_id_from_column_name(col_name):
    """Extract ComboID from equity matrix column name."""
    try:
        # Column names should be in format like "S5_M30_L100_E1_T2" or contain this pattern
        import re
        pattern = r'S\d+_M\d+_L\d+_E\d+_T\d+'
        match = re.search(pattern, col_name)
        if match:
            return match.group(0)
        else:
            # Fallback: use the column name as-is
            logger.warning(f"Could not extract ComboID from column name: {col_name}")
            return col_name
    except Exception as e:
        logger.error(f"Error extracting ComboID from column name {col_name}: {e}")
        return col_name


def _parse_combo_id_to_parameters(combo_id):
    """Parse ComboID back to parameter dictionary."""
    try:
        # Parse format: S5_M30_L100_E1_T2
        import re
        pattern = r'S(\d+)_M(\d+)_L(\d+)_E(\d+)_T(\d+)'
        match = re.match(pattern, combo_id)

        if match:
            return {
                'st_lookback': int(match.group(1)),
                'mt_lookback': int(match.group(2)),
                'lt_lookback': int(match.group(3)),
                'execution_delay': int(match.group(4)),
                'top_n': int(match.group(5))
            }
        else:
            logger.warning(f"Could not parse ComboID: {combo_id}")
            return {}

    except Exception as e:
        logger.error(f"Error parsing ComboID {combo_id}: {e}")
        return {}
