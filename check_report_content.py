"""
Simple script to check the content of the generated reports.
"""
import os
import pandas as pd
import glob
from PIL import Image

def check_excel_file(filepath):
    """Check the content of an Excel file."""
    print(f"\n=== Checking Excel file: {os.path.basename(filepath)} ===")
    try:
        # Get all sheet names
        xl = pd.ExcelFile(filepath)
        sheets = xl.sheet_names
        print(f"Sheets in the file: {sheets}")
        
        # Check each sheet
        for sheet in sheets:
            df = pd.read_excel(filepath, sheet_name=sheet)
            print(f"\nSheet: {sheet}")
            print(f"Shape: {df.shape} (rows x columns)")
            print(f"Columns: {list(df.columns)}")
            
            # Print first few rows if not empty
            if not df.empty:
                print("\nFirst 5 rows:")
                print(df.head().to_string())
            else:
                print("\nSheet is empty!")
    except Exception as e:
        print(f"Error checking Excel file: {e}")

def check_image_file(filepath):
    """Check the content of an image file."""
    print(f"\n=== Checking image file: {os.path.basename(filepath)} ===")
    try:
        with Image.open(filepath) as img:
            width, height = img.size
            print(f"Image dimensions: {width}x{height}")
            
            # Check if image has DPI information
            if 'dpi' in img.info:
                dpi_x, dpi_y = img.info['dpi']
                print(f"DPI: {dpi_x}x{dpi_y}")
            else:
                print("No DPI information available")
            
            print(f"Format: {img.format}")
            print(f"Mode: {img.mode}")
            print(f"File size: {os.path.getsize(filepath) / 1024:.2f} KB")
    except Exception as e:
        print(f"Error checking image file: {e}")

def main():
    """Main function to check report content."""
    reports_dir = os.path.join('output', 'v3_test_reports')
    
    # Check Excel files
    excel_files = glob.glob(os.path.join(reports_dir, "*.xlsx"))
    for file in excel_files:
        check_excel_file(file)
    
    # Check PNG files
    png_files = glob.glob(os.path.join(reports_dir, "*.png"))
    for file in png_files:
        check_image_file(file)
    
    # Check allocation report PNGs
    allocation_dir = os.path.join(reports_dir, 'allocation_reports')
    if os.path.isdir(allocation_dir):
        for root, dirs, files in os.walk(allocation_dir):
            for file in files:
                if file.endswith('.png'):
                    check_image_file(os.path.join(root, file))

if __name__ == "__main__":
    main()
