2025-06-19 15:59:59,028 - INFO - Successfully loaded settings for ema_allocation_model_v4.
2025-06-19 15:59:59,046 - INFO - [TickerData] Mode=Read, file=S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\data\tickerdata_SPY_SHV_EFA_TLT_PFF_20200101_20250615.xlsx
2025-06-19 15:59:59,405 - INFO - Initialized backtest engine with $1,000,000.00 capital
2025-06-19 15:59:59,405 - INFO - Commission rate: 0.10%
2025-06-19 15:59:59,405 - INFO - Slippage rate: 0.05%
2025-06-19 15:59:59,412 - INFO - Starting backtest with monthly rebalancing
2025-06-19 15:59:59,413 - INFO - Execution delay: 0 days
2025-06-19 15:59:59,414 - INFO - Trace directory for this run: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4_trace_outputs\backtest_run_20250619_155959_413711
2025-06-19 15:59:59,422 - INFO - Saved Initial Price Data to S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4_trace_outputs\backtest_run_20250619_155959_413711\00_initial_price_data.csv
2025-06-19 15:59:59,426 - INFO - Initialized signal_history with shape (27, 5)
2025-06-19 15:59:59,427 - INFO - Initialized weights_history with shape (27, 5)
Python version: 3.13.1 (tags/v3.13.1:0671451, Dec  3 2024, 19:06:28) [MSC v.1942 64 bit (AMD64)]
Python paths:
  S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4
  S:\Dropbox\Scott Only Internal\Quant_Python_24\Custom Function Library
  S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template
  C:\Python313\python313.zip
  C:\Python313\DLLs
  C:\Python313\Lib
  C:\Python313
  F:\AI_Library\my_quant_env
  F:\AI_Library\my_quant_env\Lib\site-packages
  F:\GitHub_Clone\ai-sdlc
Loaded settings from: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\settings\settings_parameters_v4.ini
Found 12 parameter sections
Found 55 named lists for parameter references
Loaded settings from: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\settings\settings_parameters_v4.ini
Found 12 parameter sections
Found 55 named lists for parameter references
Loaded settings from: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\settings\settings_parameters_v4.ini
Found 12 parameter sections
Found 55 named lists for parameter references
Loaded settings from: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\settings\settings_parameters_v4.ini
Found 12 parameter sections
Found 55 named lists for parameter references
Loaded settings from: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\settings\settings_parameters_v4.ini
Found 12 parameter sections
Found 55 named lists for parameter references
Loaded settings from: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\settings\settings_parameters_v4.ini
Found 12 parameter sections
Found 55 named lists for parameter references
Loaded settings from: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\settings\settings_parameters_v4.ini
Found 12 parameter sections
Found 55 named lists for parameter references
Successfully imported V4 modules.
--- Starting V4 Production Backtest Run ---

Step 1: Loading settings from settings_CPS_v4.ini...
Loaded settings from: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\settings\settings_parameters_v4.ini
Found 12 parameter sections
Found 55 named lists for parameter references
Settings loaded successfully.

Step 2: Loading market data...
Price data loaded for 5 assets.

Step 3: Running V4 backtest engine...
   - Using strategy: 'EMA_Crossover' with params: {'signal_algo': {'default': 'ema', 'picklist': ['Strategy_EMA', 'Strategy_SMA', 'Strategy_MACD', 'Strategy_RSI', 'Strategy_BB']}, 'min_weight': 0.0, 'max_weight': 1.0, 'strategy_name': 'EMA_Crossover', 'system_top_n': 3, 'execution_delay': 0, 'ema_short_period': {'optimize': True, 'default_value': 12, 'min_value': 5, 'max_value': 20, 'increment': 1}, 'ema_long_period': {'optimize': True, 'default_value': 26, 'min_value': 10, 'max_value': 100, 'increment': 1}, 'st_lookback': {'optimize': True, 'default_value': 15, 'min_value': 5, 'max_value': 30, 'increment': 1}, 'mt_lookback': {'optimize': True, 'default_value': 70, 'min_value': 30, 'max_value': 100, 'increment': 5}, 'lt_lookback': {'optimize': True, 'default_value': 100, 'min_value': 50, 'max_value': 200, 'increment': 10}}

!!! CASCADE DEBUG: ENTERING BacktestEngine.run_backtest - VERSION JUNE 20 10:00 AM !!!


===== BACKTEST ENGINE: run_backtest STARTED =====
Price data shape: (27, 5)
Price data head:
                   SPY       SHV        EFA         TLT        PFF
Date                                                              
2021-01-20  361.473053  97.16806  66.902039  133.785599  29.956203
2021-01-21  361.802612  97.16806  66.955254  132.846588  29.839361
2021-01-22  360.522034  97.16806  66.636024  133.276596  29.893879
2021-01-25  361.943848  97.16806  66.556213  134.847366  29.816002
2021-01-26  361.378937  97.16806  66.653755  134.592880  29.738111
Signal generator: generate_signals
Signal params: {'strategy': 'EMA_Crossover', 'signal_algo': {'default': 'ema', 'picklist': ['Strategy_EMA', 'Strategy_SMA', 'Strategy_MACD', 'Strategy_RSI', 'Strategy_BB']}, 'min_weight': 0.0, 'max_weight': 1.0, 'strategy_name': 'EMA_Crossover', 'system_top_n': 3, 'execution_delay': 0, 'ema_short_period': {'optimize': True, 'default_value': 12, 'min_value': 5, 'max_value': 20, 'increment': 1}, 'ema_long_period': {'optimize': True, 'default_value': 26, 'min_value': 10, 'max_value': 100, 'increment': 1}, 'st_lookback': {'optimize': True, 'default_value': 15, 'min_value': 5, 'max_value': 30, 'increment': 1}, 'mt_lookback': {'optimize': True, 'default_value': 70, 'min_value': 30, 'max_value': 100, 'increment': 5}, 'lt_lookback': {'optimize': True, 'default_value': 100, 'min_value': 50, 'max_value': 200, 'increment': 10}}
Rebalance frequency: {'default': "'monthly'", 'picklist': ['Rebalance_Daily', 'Rebalance_Weekly', 'Rebalance_Monthly', 'Rebalance_Quarterly', 'Rebalance_Yearly']}
Execution delay: 0
Stored price data in engine, shape: (27, 5)
INFO: Trace directory for this run: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4_trace_outputs\backtest_run_20250619_155959_413711
TRACE: Saved Initial Price Data to S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4_trace_outputs\backtest_run_20250619_155959_413711\00_initial_price_data.csv
Initialized signal_history with shape: (27, 5)

===== Starting date iteration =====

Processing date 1/27: 2021-01-20 00:00:00
  Current prices: {'SPY': 361.4730529785156, 'SHV': 97.16806030273438, 'EFA': 66.90203857421875, 'TLT': 133.7855987548828, 'PFF': 29.95620346069336}
  Portfolio value: $1,000,000.00
  Should rebalance: False

Processing date 2/27: 2021-01-21 00:00:00
  Current prices: {'SPY': 361.8026123046875, 'SHV': 97.16806030273438, 'EFA': 66.95525360107422, 'TLT': 132.8465881347656, 'PFF': 29.8393611907959}
  Portfolio value: $1,000,000.00
  Should rebalance: False

Processing date 3/27: 2021-01-22 00:00:00
  Current prices: {'SPY': 360.5220336914062, 'SHV': 97.16806030273438, 'EFA': 66.63602447509766, 'TLT': 133.2765960693359, 'PFF': 29.89387893676758}
  Portfolio value: $1,000,000.00
  Should rebalance: False

Processing date 4/27: 2021-01-25 00:00:00
  Current prices: {'SPY': 361.94384765625, 'SHV': 97.16806030273438, 'EFA': 66.55621337890625, 'TLT': 134.8473663330078, 'PFF': 29.81600189208984}
  Portfolio value: $1,000,000.00
  Should rebalance: False

Processing date 5/27: 2021-01-26 00:00:00
  Current prices: {'SPY': 361.3789367675781, 'SHV': 97.16806030273438, 'EFA': 66.65375518798828, 'TLT': 134.5928802490234, 'PFF': 29.73811149597168}
  Portfolio value: $1,000,000.00
  Should rebalance: False
  Generating signals for 2021-02-01 00:00:00 with TRACE MODE ENABLED

=== generate_signals wrapper ===
Strategy: EMA_Crossover
Price data shape: (9, 5)
Additional params: {'trace_mode': True, 'signal_algo': {'default': 'ema', 'picklist': ['Strategy_EMA', 'Strategy_SMA', 'Strategy_MACD', 'Strategy_RSI', 'Strategy_BB']}, 'min_weight': 0.0, 'max_weight': 1.0, 'strategy_name': 'EMA_Crossover', 'system_top_n': 3, 'execution_delay': 0, 'ema_short_period': {'optimize': True, 'default_value': 12, 'min_value': 5, 'max_value': 20, 'increment': 1}, 'ema_long_period': {'optimize': True, 'default_value': 26, 'min_value': 10, 'max_value': 100, 'increment': 1}, 'st_lookback': {'optimize': True, 'default_value': 15, 'min_value': 5, 'max_value': 30, 'increment': 1}, 'mt_lookback': {'optimize': True, 'default_value': 70, 'min_value': 30, 'max_value': 100, 'increment': 5}, 'lt_lookback': {'optimize': True, 'default_value': 100, 'min_value': 50, 'max_value': 200, 'increment': 10}}

=== create_signal_generator ===
Strategy: EMA_Crossover
Params: {}
Created signal generator: EMASignalGenerator
=== create_signal_generator completed ===


=== EMASignalGenerator.generate_signals ===
Price data shape: (9, 5)
Price data columns: ['SPY', 'SHV', 'EFA', 'TLT', 'PFF']
Price data head:
                   SPY       SHV        EFA         TLT        PFF
Date                                                              
2021-01-20  361.473053  97.16806  66.902039  133.785599  29.956203
2021-01-21  361.802612  97.16806  66.955254  132.846588  29.839361
2021-01-22  360.522034  97.16806  66.636024  133.276596  29.893879
2021-01-25  361.943848  97.16806  66.556213  134.847366  29.816002
2021-01-26  361.378937  97.16806  66.653755  134.592880  29.7381112025-06-19 15:59:59,439 - INFO -   Generated signals_dict (simple): {Timestamp('2021-02-01 00:00:00'): {'SHV': np.float64(0.06050243379113796), 'TLT': np.float64(0.939497566208862)}}
2025-06-19 15:59:59,440 - INFO -   Stored signals for 2021-02-01 in signal_history
2025-06-19 15:59:59,440 - INFO -   Target allocation: {'SHV': 0.06050243379113796, 'TLT': 0.939497566208862}
2025-06-19 15:59:59,440 - INFO -   Generated 2 orders
2025-06-19 15:59:59,440 - INFO -   Execution delay 0, executing orders immediately
2025-06-19 15:59:59,447 - INFO - Final signal_history shape: (27, 5)
2025-06-19 15:59:59,447 - INFO - Final weights_history shape: (27, 5)
2025-06-19 15:59:59,452 - INFO - Signal history sample (first 5 rows):
           SPY SHV EFA TLT PFF
Date                          
2021-01-20   0   0   0   0   0
2021-01-21   0   0   0   0   0
2021-01-22   0   0   0   0   0
2021-01-25   0   0   0   0   0
2021-01-26   0   0   0   0   0
2025-06-19 15:59:59,456 - INFO - Weights history sample (first 5 rows):
            SPY  SHV  EFA  TLT  PFF
Date                               
2021-01-20  0.0  0.0  0.0  0.0  0.0
2021-01-21  0.0  0.0  0.0  0.0  0.0
2021-01-22  0.0  0.0  0.0  0.0  0.0
2021-01-25  0.0  0.0  0.0  0.0  0.0
2021-01-26  0.0  0.0  0.0  0.0  0.0
2025-06-19 15:59:59,458 - INFO - Saved Full Signal History (Target Allocations) to S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4_trace_outputs\backtest_run_20250619_155959_413711\06_signal_history_target_allocations.csv
2025-06-19 15:59:59,460 - INFO - Saved Full Weights History (Actual Allocations) to S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4_trace_outputs\backtest_run_20250619_155959_413711\07_weights_history_actual_allocations.csv
2025-06-19 15:59:59,462 - INFO - Saved EMA Average History to S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4_trace_outputs\backtest_run_20250619_155959_413711\02_ema_average_history.csv
2025-06-19 15:59:59,464 - INFO - Saved Ticker Ranks History to S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4_trace_outputs\backtest_run_20250619_155959_413711\03_ranks_history.csv
2025-06-19 15:59:59,466 - INFO - Saved Raw Signal History to S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4_trace_outputs\backtest_run_20250619_155959_413711\04_raw_signal_history.csv
2025-06-19 15:59:59,469 - INFO - SUCCESS: weights_history appears valid.

Data sample:
            SPY  SHV  EFA  TLT  PFF
Date                               
2021-01-20  0.0  0.0  0.0  0.0  0.0
2021-01-21  0.0  0.0  0.0  0.0  0.0
2021-01-22  0.0  0.0  0.0  0.0  0.0
2021-01-25  0.0  0.0  0.0  0.0  0.0
2021-01-26  0.0  0.0  0.0  0.0  0.0
S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\engine\backtest_v4.py:496: FutureWarning: 'M' is deprecated and will be removed in a future version, please use 'ME' instead.
  monthly_returns = strategy_returns.resample('M').apply(lambda x: (1 + x).prod() - 1)
S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\engine\backtest_v4.py:502: FutureWarning: 'Y' is deprecated and will be removed in a future version, please use 'YE' instead.
  yearly_returns = strategy_returns.resample('Y').apply(lambda x: (1 + x).prod() - 1)
2025-06-19 15:59:59,483 - INFO - Using provided weights_history for results

EMA parameters - st_lookback: 15, mt_lookback: 70, lt_lookback: 100
Additional params: {'trace_mode': True, 'signal_algo': {'default': 'ema', 'picklist': ['Strategy_EMA', 'Strategy_SMA', 'Strategy_MACD', 'Strategy_RSI', 'Strategy_BB']}, 'min_weight': 0.0, 'max_weight': 1.0, 'strategy_name': 'EMA_Crossover', 'system_top_n': 3, 'execution_delay': 0, 'ema_short_period': {'optimize': True, 'default_value': 12, 'min_value': 5, 'max_value': 20, 'increment': 1}, 'ema_long_period': {'optimize': True, 'default_value': 26, 'min_value': 10, 'max_value': 100, 'increment': 1}}
Calculating EMAs from price data...
Calculated EMAs - shapes: st_ema: (9, 5), mt_ema: (9, 5), lt_ema: (9, 5)
Most recent EMA values (sample):
st_ema: {'SPY': 356.2532977919558, 'SHV': 97.17048940714159, 'EFA': 65.65568083131508}...
mt_ema: {'SPY': 357.27090621049086, 'SHV': 97.17012530907952, 'EFA': 65.88357558605301}...
lt_ema: {'SPY': 357.3553248140613, 'SHV': 97.17009259958803, 'EFA': 65.90244706643088}...
Calculating trend strength for each symbol...
  SPY: st_mt_ratio=-0.0028, mt_lt_ratio=-0.0002, strength=-0.0031, final=0.0000
  SHV: st_mt_ratio=0.0000, mt_lt_ratio=0.0000, strength=0.0000, final=0.0000
  EFA: st_mt_ratio=-0.0035, mt_lt_ratio=-0.0003, strength=-0.0037, final=0.0000
Positive trends: 2/5 assets
Total trend strength: 0.0001
Allocated weights proportionally to trend strength
Sample allocations (first 3): {'SHV': np.float64(0.06050243379113796), 'TLT': np.float64(0.939497566208862)}...
Validated signals sample (first 3): {'SHV': np.float64(0.06050243379113796), 'TLT': np.float64(0.939497566208862)}...
=== EMASignalGenerator.generate_signals completed ===

Signals returned from generator: <class 'dict'>
Number of symbols with allocations: 2
Sample allocations (first 3): {'SHV': np.float64(0.06050243379113796), 'TLT': np.float64(0.939497566208862)}...
=== generate_signals wrapper completed ===


!!! CASCADE DEBUG: About to call calculate_rebalance_orders. Portfolio type: <class 'v4.engine.portfolio_v4.Portfolio'>, Signals type: <class 'pandas.core.series.Series'> !!!


Processing date 23/27: 2021-02-22 00:00:00
  Current prices: {'SPY': 364.4296875, 'SHV': 97.1768569946289, 'EFA': 67.44292449951172, 'TLT': 124.9094009399414, 'PFF': 29.34813690185547}
  Portfolio value: $937,963.63
  Should rebalance: False

Processing date 24/27: 2021-02-23 00:00:00
  Current prices: {'SPY': 364.8722229003906, 'SHV': 97.1768569946289, 'EFA': 67.5050048828125, 'TLT': 124.5491714477539, 'PFF': 29.32467842102051}
  Portfolio value: $935,435.54
  Should rebalance: False

Processing date 25/27: 2021-02-24 00:00:00
  Current prices: {'SPY': 368.8928833007812, 'SHV': 97.1768569946289, 'EFA': 67.71781921386719, 'TLT': 123.7321548461914, 'PFF': 29.28558349609375}
  Portfolio value: $929,701.72
  Should rebalance: False

Processing date 26/27: 2021-02-25 00:00:00
  Current prices: {'SPY': 360.004150390625, 'SHV': 97.16806030273438, 'EFA': 66.56509399414062, 'TLT': 121.7114944458008, 'PFF': 29.05097961425781}
  Portfolio value: $915,515.26
  Should rebalance: False

Processing date 27/27: 2021-02-26 00:00:00
  Current prices: {'SPY': 358.1492004394531, 'SHV': 97.1768569946289, 'EFA': 65.62519073486328, 'TLT': 125.7351837158203, 'PFF': 29.22301292419434}
  Portfolio value: $943,758.98
  Should rebalance: False
TRACE: Saved Full Signal History (Target Allocations) to S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4_trace_outputs\backtest_run_20250619_155959_413711\06_signal_history_target_allocations.csv
TRACE: Saved Full Weights History (Actual Allocations) to S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4_trace_outputs\backtest_run_20250619_155959_413711\07_weights_history_actual_allocations.csv
TRACE: Saved EMA Average History to S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4_trace_outputs\backtest_run_20250619_155959_413711\02_ema_average_history.csv
TRACE: Saved Ticker Ranks History to S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4_trace_outputs\backtest_run_20250619_155959_413711\03_ranks_history.csv
TRACE: Saved Raw Signal History to S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4_trace_outputs\backtest_run_20250619_155959_413711\04_raw_signal_history.csv

===== _calculate_results STARTED =====
Using provided weights_history for results
Weights history shape: (27, 5)
Weights history sample:
            SPY  SHV  EFA  TLT  PFF
Date                               
2021-01-20  0.0  0.0  0.0  0.0  0.0
2021-01-21  0.0  0.0  0.0  0.0  0.0
2021-01-22  0.0  0.0  0.0  0.0  0.0
2021-01-25  0.0  0.0  0.0  0.0  0.0
2021-01-26  0.0  0.0  0.0  0.0  0.0
===== _calculate_results COMPLETED =====


===== Results summary =====
Initial capital: $1,000,000.00
Final value: $943,758.98
Total return: -5.62%
CAGR: -43.53%
Sharpe: -3.14
Max drawdown: -8.45%
Weights history shape: (27, 5)
Signal history shape: (27, 5)
===== BACKTEST ENGINE: run_backtest COMPLETED =====

Backtest engine finished.

Step 4: Verifying backtest results...
'weights_history' found with shape: (27, 5)
Basic 'weights_history' validation passed.
Successfully saved weights history to S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\output\trace_csv\weights_history_output_20250619_155959.csv

--- V4 Production Backtest Run Finished ---
