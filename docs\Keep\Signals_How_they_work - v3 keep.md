# Signals: How They Work in the V3 System

## Overview
Signals in the V3 backtesting and allocation system represent the model’s instructions for portfolio allocation at each rebalance point. They are the foundation for building signal history, which is critical for reporting, analysis, and validation.

---

## 1. Where Are Signals Created?
- **Signals are generated inside allocation model functions** (e.g., `ema_allocation_model`) in modules like `models/ema_allocation_model.py`.
- Each model computes, for a given rebalance date, the target allocation **weights** for each ticker.
- The typical output for a single date is a dictionary: `{ticker: weight, ...}`.

---

## 2. How Is Signal History Built?
- **Signal history** is constructed by collecting these allocation signals for every rebalance date (usually daily or at the model’s rebalance frequency).
- **Structure:**
  - As a DataFrame: `index = pd.DatetimeIndex([dates...]), columns = [tickers...], values = weights`
  - Or as a dict: `{pd.Timestamp: {ticker: weight, ...}, ...}`
- This structure allows for easy validation, reporting, and downstream processing.

---

## 3. What Does a "Signal" Mean?
- In most models, the signal is the **target portfolio weight** for each ticker (not a raw buy/sell or number of shares).
- If the model produces buy/sell or share quantities, the output would be structured as `{ticker: {'action': 'buy', 'shares': N}}` or similar.
- **In current production, the signal is almost always the allocation weight.**

---

## 4. Where Should Signal History Be Built?
- **At the point where allocation signals are generated for each rebalance date** (inside the main loop of the backtest engine, e.g., `BacktestEngine.run_backtest`).
- **Capture the output of the model function** for each rebalance date and store it in a DataFrame or dict as the signal history.

---

## 5. Structure and Semantics
- **Frequency:** Daily or at the model’s rebalance frequency.
- **By Ticker:** Each date/ticker pair has an entry.
- **Form:** Each entry is the target allocation weight for that ticker on that date.
- **Buy/Sell/Share Signals:** If required, must be explicitly output by the model/engine (not standard).

---

## 6. Validation and Normalization
- After building the signal history, use functions from `v3_engine/signal_history_tracker.py`:
  - `validate_signal_history()`: Ensures structure and values are correct.
  - `normalize_signal_history()`: Adjusts for consistency if needed.

---

## 7. Code Evidence and Best Practices
| Location of Signal Creation      | Structure Captured                | Signal Meaning         | How to Build History                |
|----------------------------------|-----------------------------------|-----------------------|-------------------------------------|
| Allocation model (`models/`)     | `{ticker: weight, ...}`           | Target allocation     | Collect model output per date       |
| Backtest engine (`engine/`)      | DataFrame or dict `{date: ...}`   | Per-date allocations  | Append to DataFrame/dict per day    |
| Signal history tracker (`v3_engine/`) | DataFrame (validation)         | Validated allocations | Use after building history          |

---

## 8. Recommendations
- **Capture signals at the source:** In the main backtest loop, after calling the allocation model, append the output to your signal history structure.
- **Use a DataFrame for efficiency and compatibility.**
- **Validate and normalize** after construction, especially before reporting or further analysis.
- **If you want to track buy/sell or shares,** extend the model or engine logic to output that information.

---

## Summary
- Signals are target allocation weights per ticker, per date.
- Signal history is a DataFrame or dict mapping dates to per-ticker allocations.
- Validation and normalization are handled by the signal history tracker module.
- For buy/sell/share-level signals, explicit logic must be added to models/engine.
