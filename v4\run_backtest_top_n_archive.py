"""
run_backtest_top_n.py
Main script to run backtests using the Top-N signal generator.
CPS v4 compliance verified: 2025-06-21
"""

import pandas as pd
import numpy as np
from pathlib import Path
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Import local modules
from v4.engine.backtest_v4 import BacktestEngine
from v4.engine.signals.top_n_signal_generator import TopNSignalGenerator
from v4.settings.settings_CPS_v4 import load_settings

def main():
    """Run the backtest using Top-N signal generator."""
    logger.info("Starting Top-N backtest")
    
    # Load settings
    settings = load_settings()
    signal_params = settings['signal_generation']
    
    # Initialize backtest engine
    engine = BacktestEngine()
    
    # Load price data
    data_path = Path("v4/data/tickerdata_SPY_SHV_EFA_TLT_PFF_20200101_20250615.xlsx")
    price_data = pd.read_excel(data_path, index_col=0, parse_dates=True)
    
    # Create signal generator
    signal_generator = TopNSignalGenerator(signal_params)
    
    # Run backtest
    results = engine.run_backtest(
        price_data=price_data,
        signal_generator=signal_generator,
        **signal_params
    )
    
    # Save results
    output_dir = Path("v4_trace_outputs")
    output_dir.mkdir(exist_ok=True)
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results['portfolio_values'].to_csv(output_dir / f"portfolio_values_{timestamp}.csv")
    results['strategy_returns'].to_csv(output_dir / f"strategy_returns_{timestamp}.csv")
    results['allocation_history'].to_csv(output_dir / f"allocation_history_{timestamp}.csv")
    
    # Print performance metrics
    perf = results['performance']
    print("\nPerformance Summary:")
    print(f"CAGR: {perf['cagr']:.2%}")
    print(f"Volatility: {perf['volatility']:.2%}")
    print(f"Sharpe Ratio: {perf['sharpe']:.2f}")
    print(f"Max Drawdown: {perf['max_drawdown']:.2%}")
    print(f"Turnover: {perf['turnover']:.2f}")
    print(f"Win Rate: {perf['win_rate']:.2%}")
    
    logger.info("Backtest completed successfully")

if __name__ == "__main__":
    main()
