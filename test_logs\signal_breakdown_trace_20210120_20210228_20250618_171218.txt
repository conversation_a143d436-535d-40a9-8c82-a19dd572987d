2025-06-18 17:12:18,689 - INFO - Script setup complete. Settings loaded and loggers configured.
2025-06-18 17:12:18,690 - INFO - EMA Signal Breakdown Trace Log - 20250618_171218
2025-06-18 17:12:18,690 - INFO - Period: 2021-01-20 to 2021-02-28
2025-06-18 17:12:20,230 - INFO - Price data loaded. Shape: (1370, 5)
2025-06-18 17:12:20,234 - INFO - --- Effective Settings for Trace ---
2025-06-18 17:12:20,235 - INFO - EMA Lookbacks: ST=15, MT=70, LT=100
2025-06-18 17:12:20,235 - INFO - EMA Weights: Min=0.0, Max=1.0
2025-06-18 17:12:20,235 - INFO - EMA Strategy Top N: 3
2025-06-18 17:12:20,255 - INFO - System Top N (for allocation rules): 2
2025-06-18 17:12:20,256 - INFO - Signal Algorithm for Rules: {'default': 'ema', 'picklist': ['Strategy_EMA', 'Strategy_SMA', 'Strategy_MACD', 'Strategy_RSI', 'Strategy_BB']}
2025-06-18 17:12:20,256 - INFO - -----------------------------------

2025-06-18 17:12:20,261 - INFO - 
========== Tracing for Date: 2021-01-20 ==========
2025-06-18 17:12:20,261 - INFO - --- Calling Production Model with trace_mode=True ---
2025-06-18 17:12:20,274 - ERROR - An unexpected error occurred: unhashable type: 'dict'
Traceback (most recent call last):
  File "S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\trace_ema_signal_breakdown.py", line 184, in main
    model_output = ema_model_module.ema_allocation_model_updated(
        price_data=current_price_slice,
        trace_mode=True
    )
  File "S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\models\ema_allocation_model_v4.py", line 309, in ema_allocation_model_updated
    model_output = ema_allocation_model(
        price_data=price_data,
    ...<3 lines>...
        **params
    )
  File "S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\models\ema_allocation_model_v4.py", line 219, in ema_allocation_model
    rule_weights = get_allocation_weights(top_n, algorithm=algo_name)
  File "S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\config\allocation_rules_v4.py", line 60, in get_allocation_weights
    if algorithm not in ALGO_ALLOCATION_RULES:
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: unhashable type: 'dict'
2025-06-18 17:12:20,277 - INFO - EMA signal breakdown trace finished.
