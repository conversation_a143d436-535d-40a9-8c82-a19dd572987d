"""
Test script for Performance Table XLSX Generator
Tests the complete generation process and validates output.
"""

import sys
import os
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from reporting.performance_table_generator import PerformanceTableGenerator
import logging

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_performance_table_generation():
    """Test the complete Performance Table generation process."""
    try:
        logger.info("=" * 60)
        logger.info("TESTING PERFORMANCE TABLE XLSX GENERATOR")
        logger.info("=" * 60)
        
        # Initialize generator
        generator = PerformanceTableGenerator()
        
        # Test parameter extraction
        logger.info("\n1. Testing parameter extraction...")
        params = generator._get_optimizable_parameters()
        logger.info(f"Found optimizable parameters: {list(params.keys())}")
        for param, value in params.items():
            logger.info(f"  {param}: {value}")
        
        # Test data loading
        logger.info("\n2. Testing data loading...")
        signals_df, allocation_df, trade_df = generator._load_data_files()
        logger.info(f"Signals shape: {signals_df.shape}")
        logger.info(f"Allocation shape: {allocation_df.shape}")
        logger.info(f"Trade log shape: {trade_df.shape}")
        
        # Test trade log enhancement
        logger.info("\n3. Testing trade log enhancement...")
        enhanced_trade = generator._enhance_trade_log(trade_df)
        logger.info(f"Enhanced trade log columns: {list(enhanced_trade.columns)}")
        
        # Test equity curve calculation
        logger.info("\n4. Testing equity curve calculation...")
        equity_curve = generator._calculate_equity_curves(allocation_df)
        logger.info(f"Equity curve length: {len(equity_curve)}")
        logger.info(f"Start value: {equity_curve.iloc[0]:,.2f}")
        logger.info(f"End value: {equity_curve.iloc[-1]:,.2f}")
        
        # Test performance metrics
        logger.info("\n5. Testing performance metrics...")
        metrics = generator._calculate_performance_metrics(equity_curve)
        for metric, value in metrics.items():
            if isinstance(value, (int, float)):
                logger.info(f"  {metric}: {value:.4f}")
            else:
                logger.info(f"  {metric}: {value}")
        
        # Generate complete report
        logger.info("\n6. Generating complete Performance Table...")
        output_file = generator.generate_performance_table()
        
        logger.info(f"\n✅ SUCCESS: Performance Table generated at {output_file}")
        
        # Validate file exists and has content
        if output_file.exists():
            file_size = output_file.stat().st_size
            logger.info(f"File size: {file_size:,} bytes")
            
            if file_size > 1000:  # Should be at least 1KB
                logger.info("✅ File size validation passed")
            else:
                logger.warning("⚠️ File size seems small, may be incomplete")
        else:
            logger.error("❌ Output file does not exist")
            return False
        
        logger.info("\n" + "=" * 60)
        logger.info("PERFORMANCE TABLE TEST COMPLETED SUCCESSFULLY")
        logger.info("=" * 60)
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

def validate_excel_structure(filepath):
    """Validate the structure of the generated Excel file."""
    try:
        import openpyxl
        
        logger.info(f"\n7. Validating Excel structure: {filepath}")
        
        workbook = openpyxl.load_workbook(filepath)
        
        # Check required sheets
        required_sheets = ["Signal History", "Allocation History", "Trade Log", "Performance"]
        existing_sheets = workbook.sheetnames
        
        logger.info(f"Found sheets: {existing_sheets}")
        
        for sheet in required_sheets:
            if sheet in existing_sheets:
                logger.info(f"✅ {sheet} sheet found")
                
                # Check sheet has data
                ws = workbook[sheet]
                if ws.max_row > 1:  # More than just header
                    logger.info(f"  - Has {ws.max_row - 1} data rows")
                else:
                    logger.warning(f"  - ⚠️ Sheet appears empty")
            else:
                logger.error(f"❌ {sheet} sheet missing")
                return False
        
        # Check parameter header in Signal History
        signal_ws = workbook["Signal History"]
        header_cell = signal_ws['A1'].value
        if header_cell and 'Parameters:' in str(header_cell):
            logger.info("✅ Parameter header found in A1")
        else:
            logger.warning("⚠️ Parameter header not found in A1")
        
        workbook.close()
        logger.info("✅ Excel structure validation passed")
        return True
        
    except Exception as e:
        logger.error(f"❌ Excel validation failed: {e}")
        return False

if __name__ == "__main__":
    # Run the test
    success = test_performance_table_generation()
    
    if success:
        # Find the generated file for validation
        reporting_dir = Path("reporting")
        excel_files = list(reporting_dir.glob("EMA_V3_1_performance_tables_*.xlsx"))
        
        if excel_files:
            latest_file = max(excel_files, key=os.path.getctime)
            validate_excel_structure(latest_file)
        
        print("\n🎉 All tests completed successfully!")
        sys.exit(0)
    else:
        print("\n💥 Tests failed!")
        sys.exit(1)
