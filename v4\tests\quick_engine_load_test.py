# quick_engine_load_test.py
# WORKING - ONLY CHANGE WITH PERMISSION

"""
quick_engine_load_test.py
Test loading settings and data into backtest engine.
"""

import sys
from pathlib import Path

# Add project root to sys.path
project_root = Path(__file__).parent.parent.parent
if str(project_root) not in sys.path:
    sys.path.append(str(project_root))

from v4.settings.settings_CPS_v4 import load_settings
from v4.engine.data_loader_v4 import load_data_for_backtest
from v4.engine.backtest_v4 import BacktestEngine
from v4.engine.signal_generator_v4 import generate_signals

def main():
    settings = load_settings()
    print("Loaded settings:", settings.get('data_params', {}))
    # Assertion to validate settings loading
    assert 'backtest' in settings, "'backtest' section missing in settings"
    data = load_data_for_backtest()
    print("Loaded data keys:", list(data.keys()))
    print(f"price_data: {data['price_data'].shape}")
    print(f"returns_data: {data['returns_data'].shape}")
    print(f"risk_free_rate length: {len(data['risk_free_rate'])}")
    # Assertions to validate data loading
    assert data.get('price_data') is not None and not data['price_data'].empty, "price_data not loaded or empty"
    assert data.get('returns_data') is not None and not data['returns_data'].empty, "returns_data not loaded or empty"
    assert data.get('risk_free_rate') is not None and len(data['risk_free_rate']) > 0, "risk_free_rate not loaded or empty"
    # Step 3: Run backtest with default settings
    engine = BacktestEngine()
    results = engine.run_backtest(
        price_data=data['price_data'],
        signal_generator=generate_signals
    )
    # Assertions to validate backtest results
    assert isinstance(results, dict), "run_backtest did not return a dict"
    required_keys = {'initial_capital','final_value','total_return','strategy_returns','benchmark_returns'}
    missing = required_keys - set(results.keys())
    assert not missing, f"Missing result keys: {missing}"
    # Consolidated assertions block for backtest results
    assert results.get('final_value', 0) > 0, "final_value should be positive"
    assert results.get('total_return') is not None, "total_return missing or None"
    assert results['strategy_returns'].shape[0] > 0, "strategy_returns is empty"
    assert results['benchmark_returns'].shape[0] > 0, "benchmark_returns is empty"


if __name__ == "__main__":
    main()
