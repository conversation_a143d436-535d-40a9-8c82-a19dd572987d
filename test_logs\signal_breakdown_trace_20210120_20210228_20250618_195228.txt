2025-06-18 19:52:28,205 - INFO - Script setup complete. Settings loaded and loggers configured.
2025-06-18 19:52:28,206 - INFO - EMA Signal Breakdown Trace Log - 20250618_195228
2025-06-18 19:52:28,206 - INFO - Period: 2021-01-20 to 2021-02-28
2025-06-18 19:52:28,206 - INFO - Initializing Backtest Engine components...
2025-06-18 19:52:28,206 - INFO - Backtest Engine initialized with initial capital: $1,000,000.00 and execution delay: 0 days.
2025-06-18 19:52:28,559 - INFO - Price data loaded. Shape: (1370, 5)
2025-06-18 19:52:28,562 - INFO - --- Effective Settings for Trace ---
2025-06-18 19:52:28,563 - INFO - EMA Lookbacks: ST=15, MT=70, LT=100
2025-06-18 19:52:28,584 - INFO - EMA Weights: Min=0.0, Max=1.0
2025-06-18 19:52:28,584 - INFO - EMA Strategy Top N: 3
2025-06-18 19:52:28,584 - INFO - System Top N (for allocation rules): 2
2025-06-18 19:52:28,584 - INFO - Signal Algorithm for Rules: ema
2025-06-18 19:52:28,584 - INFO - -----------------------------------

2025-06-18 19:52:28,588 - INFO - 
========== Tracing for Date: 2021-01-20 ==========
2025-06-18 19:52:28,588 - INFO - --- Calling Production Model with trace_mode=True ---
2025-06-18 19:52:28,603 - INFO - --- Step 1: Ratios (EMAXAvg) ---
2025-06-18 19:52:28,604 - INFO - Ratios received:
SPY    1.031513
SHV    1.000027
EFA    1.039898
TLT    0.985320
PFF    1.014675
2025-06-18 19:52:28,604 - INFO - --- Step 2: Ranks ---
2025-06-18 19:52:28,607 - INFO - Ranks received:
  Asset  EMAXAvg_Value  Rank_Ordinal
0   EFA       1.039898             1
1   SPY       1.031513             2
2   PFF       1.014675             3
3   SHV       1.000027             4
4   TLT       0.985320             5
2025-06-18 19:52:28,608 - INFO - --- Step 3: Signal (Allocation) ---
2025-06-18 19:52:28,609 - INFO - Signal received:
SPY    0.4
SHV    0.0
EFA    0.6
TLT    0.0
PFF    0.0
2025-06-18 19:52:28,609 - INFO - --- Processing Backtest Engine for date: 2021-01-20 ---
2025-06-18 19:52:28,609 - INFO - No orders from previous signals to execute for 2021-01-20
2025-06-18 19:52:28,609 - INFO - Generated 2 new orders based on today's signal for 2021-01-20
2025-06-18 19:52:28,610 - INFO - T+0 Execution: Processing 2 orders generated from today's signal.
2025-06-18 19:52:28,610 - INFO - Executed 2 T+0 trades from today's signal for 2021-01-20
2025-06-18 19:52:28,614 - ERROR - An unexpected error occurred: 'Trade' object has no attribute 'order'
Traceback (most recent call last):
  File "S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\trace_ema_signal_breakdown.py", line 314, in main
    trace_file_logger.debug(f"  Executed (T+0) Trade: {trade.symbol} {trade.quantity} @ {trade.execution_price:.2f} ({trade.order.order_type})")
                                                                                                                      ^^^^^^^^^^^
AttributeError: 'Trade' object has no attribute 'order'
2025-06-18 19:52:28,617 - INFO - EMA signal breakdown trace finished.
