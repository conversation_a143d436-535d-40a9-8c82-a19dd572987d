#!/usr/bin/env python
"""
Debug script to test optimization parameter detection
"""

from v4.optimization_detector import get_optimization_combinations
from v4.config.paths_v4 import V4_SETTINGS_FILE

def debug_optimization_detector():
    print("=" * 60)
    print("DEBUGGING OPTIMIZATION PARAMETER DETECTION")
    print("=" * 60)
    
    print(f"Settings file: {V4_SETTINGS_FILE}")
    
    try:
        combinations = get_optimization_combinations()
        print(f"\nGenerated {len(combinations)} combinations:")
        
        for i, combo in enumerate(combinations):
            print(f"  Combination {i}: {combo}")
            
        # Expected combinations based on settings:
        # st_lookback: 5, 15, 25 (min=5, max=30, increment=10)
        # mt_lookback: 30, 50, 70, 90 (min=30, max=100, increment=20)
        # Should be 3 × 4 = 12 combinations

        print(f"\nExpected: 12 combinations (3 st_lookback × 4 mt_lookback)")
        print(f"Actual: {len(combinations)} combinations")

        if len(combinations) == 12:
            print("✅ SUCCESS: Correct number of combinations generated")
        else:
            print("❌ ERROR: Wrong number of combinations generated")

        # Check parameter ranges
        st_values = set()
        mt_values = set()

        for combo in combinations:
            if 'st_lookback' in combo:
                st_values.add(combo['st_lookback'])
            if 'mt_lookback' in combo:
                mt_values.add(combo['mt_lookback'])

        print(f"\nst_lookback values found: {sorted(st_values)}")
        print(f"Expected st_lookback: [5, 15, 25]")

        print(f"mt_lookback values found: {sorted(mt_values)}")
        print(f"Expected mt_lookback: [30, 50, 70, 90]")

        # Test section-agnostic parameter detection
        print(f"\n" + "="*60)
        print("TESTING SECTION-AGNOSTIC PARAMETER DETECTION")
        print("="*60)

        from v4.settings.config_helper import ConfigHelper
        helper = ConfigHelper(V4_SETTINGS_FILE)

        print("All parameters found by ConfigHelper:")
        for param_name in sorted(helper._param_lookup.keys()):
            section_name, param_value = helper._param_lookup[param_name]
            if param_value.strip().startswith('(') and 'optimize=' in param_value:
                print(f"  {param_name} = {param_value} (in [{section_name}])")

        print(f"\nDirect parameter lookup:")
        st_lookback = helper.get('st_lookback')
        mt_lookback = helper.get('mt_lookback')
        print(f"  st_lookback: {st_lookback}")
        print(f"  mt_lookback: {mt_lookback}")
        
    except Exception as e:
        print(f"ERROR: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_optimization_detector()
