Q1: Parameter Display (Cell A1)
Which parameters from settings_parameters_v4.ini should be displayed in Cell A1? Should I show:
Show ALL optimizable parameters (we might later change that with a display variable - but set this as the default answer, and keep it simple for now)
 - The header of the column would show the name of the parameter
 - For the Benchmark row - that should show the base value - default value
 - Each strategy test output row would show the values actually used to generate that rows metrics; so If EMA_short optimized to 5 variations (6,8,10,12), then 4 rows shown 1 with each value of the parameter.

Q3: Performance Metrics Calculation
For CAGR, Sharpe ratio, etc., should I calculate from:
 - each output strategy results must calculate and store an equity curve = the full mark to market portfolio value after all trades completed for that market is open day.   I recommend we create a csv or xlsx output, with date rows, and columns of tested combinations from the strategy run including optimizations, which stores this equity curve combinations.   Those equity curves are then the single source of truth for all metric performance calcuations.
Benchmark Selection = strategy benchmark is selected by user from the configuration settings ini , current default is the equal weight strategy. You will need to create + store the equity curve from that strategy for several purposes - you can store aligned to same date structure as a column in the stored output of the strategy results.

Q5: Cash Component Handling = Cash is a core component of the marked to market daily portfolio value and equity curve.  For now, if easy, assume cash earns risk free rate - but set this as a future enhancement task in the plan - no return until future enhancement implemented.
How should I handle the cash component in performance calculations:

Q6: Trade Log Mapping
For the Trade Log tab, I need to map current columns to required format:
Use the existing csv format - can keep it without change, but 1 enhancements would be nice:
 - Add in a column for commission+slippage (this value should already be incorporated in the total $s formula column)
Recommned trade log: date, ticker, action, quantity, price per share executed, commission+slippage, total $s, pnl


Should trade_num is not really used current, can just order by row as it is sorted in current output.

Use commission+slippage in the settings_parameters_v4.ini 
commission_rate = 0.001
slippage_rate = 0.0005


Q7: File Output
Timestamp: Use report generation timestamp
Location: Use existing = Backtest_FinAsset_Alloc_Template\reporting