#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Test script for the unified portfolio file solution.

This script tests the new file collision prevention system:
1. Combination ID generation
2. Unified portfolio file creation
3. File mapping and retrieval

Author: AI Assistant
Date: 2025-07-27
"""

import sys
import os
from pathlib import Path
import pandas as pd
from datetime import datetime

# Add project root to path
_script_path = Path(__file__).resolve()
_project_root = _script_path.parent
if str(_project_root) not in sys.path:
    sys.path.insert(0, str(_project_root))

# Import the updated performance table generator
from v4.py_reporting.performance_table_generator import PerformanceTableGenerator
from v4.pipeline.modes import _create_unified_portfolio_data

def test_combination_id_generation():
    """Test the combination ID generation function."""
    print("="*60)
    print("TEST 1: Combination ID Generation")
    print("="*60)
    
    generator = PerformanceTableGenerator()
    
    # Test different parameter combinations
    test_combinations = [
        {'st_lookback': 15, 'mt_lookback': 70, 'top_n': 2, 'execution_delay': 1},
        {'st_lookback': 20, 'mt_lookback': 100, 'top_n': 3, 'execution_delay': 2},
        {'st_lookback': 10, 'mt_lookback': 50, 'top_n': 1, 'execution_delay': 0},
    ]
    
    for i, combo in enumerate(test_combinations):
        combo_id = generator.generate_combo_id(combo)
        print(f"Combination {i+1}: {combo}")
        print(f"Generated ID: {combo_id}")
        print()
    
    print("✅ Combination ID generation test completed")
    return True

def test_unified_portfolio_creation():
    """Test the unified portfolio file creation function."""
    print("="*60)
    print("TEST 2: Unified Portfolio File Creation")
    print("="*60)
    
    # Create mock pipeline results
    dates = pd.date_range(start='2020-01-01', periods=5, freq='D')
    portfolio_values = pd.Series([1000000, 1001000, 999500, 1002000, 1003500], index=dates)
    
    # Create mock allocation history
    allocation_data = {
        'Cash': [0.1, 0.05, 0.15, 0.08, 0.12],
        'SPY': [0.4, 0.45, 0.35, 0.42, 0.38],
        'TLT': [0.3, 0.25, 0.35, 0.28, 0.32],
        'SHV': [0.2, 0.25, 0.15, 0.22, 0.18]
    }
    allocation_history = pd.DataFrame(allocation_data, index=dates)
    
    # Create mock results
    mock_results = {
        'portfolio_values': portfolio_values,
        'allocation_history': allocation_history
    }
    
    # Test unified portfolio creation
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    unified_df = _create_unified_portfolio_data(mock_results, timestamp)
    
    print("Created unified portfolio DataFrame:")
    print(f"Shape: {unified_df.shape}")
    print(f"Columns: {list(unified_df.columns)}")
    print("\nFirst few rows:")
    print(unified_df.head())
    
    # Validate required columns
    required_cols = ['Date', 'Cash_USD', 'Portfolio_Value', 'Total_USD']
    missing_cols = [col for col in required_cols if col not in unified_df.columns]
    
    if missing_cols:
        print(f"❌ Missing required columns: {missing_cols}")
        return False
    else:
        print("✅ All required columns present")
    
    # Validate data integrity
    if len(unified_df) == len(dates):
        print("✅ Correct number of rows")
    else:
        print(f"❌ Row count mismatch: expected {len(dates)}, got {len(unified_df)}")
        return False
    
    print("✅ Unified portfolio creation test completed")
    return True

def test_file_mapping():
    """Test the file mapping and retrieval functionality."""
    print("="*60)
    print("TEST 3: File Mapping and Retrieval")
    print("="*60)
    
    generator = PerformanceTableGenerator()
    
    # Test combination
    test_combo = {'st_lookback': 15, 'mt_lookback': 70, 'top_n': 2, 'execution_delay': 1}
    combo_id = generator.generate_combo_id(test_combo)
    
    print(f"Test combination: {test_combo}")
    print(f"Generated combo ID: {combo_id}")
    
    # Test the mapping storage
    generator.combination_file_mapping[combo_id] = test_combo
    
    if combo_id in generator.combination_file_mapping:
        stored_combo = generator.combination_file_mapping[combo_id]
        if stored_combo == test_combo:
            print("✅ Combination mapping storage works correctly")
        else:
            print("❌ Combination mapping storage failed")
            return False
    else:
        print("❌ Combination not found in mapping")
        return False
    
    print("✅ File mapping test completed")
    return True

def test_csv_flag_logic():
    """Test the CSV flag logic."""
    print("="*60)
    print("TEST 4: CSV Flag Logic")
    print("="*60)
    
    generator = PerformanceTableGenerator()
    
    print(f"csv_flag_use: {generator.csv_flag_use}")
    print(f"csv_valid_det: {generator.csv_valid_det}")
    print(f"optimization_active: {generator.optimization_active}")
    
    # Test flag combinations
    test_cases = [
        (False, False, False, "No CSV generation"),
        (True, False, False, "Basic CSV generation"),
        (True, False, True, "Optimization mode - skip extra files"),
        (True, True, True, "Optimization mode - generate all files"),
    ]
    
    for csv_flag, csv_valid, opt_active, description in test_cases:
        should_generate_extra = csv_flag and (not opt_active or csv_valid)
        print(f"{description}: csv_flag={csv_flag}, csv_valid={csv_valid}, opt_active={opt_active}")
        print(f"  Should generate extra files: {should_generate_extra}")
    
    print("✅ CSV flag logic test completed")
    return True

def main():
    """Run all tests."""
    print("UNIFIED PORTFOLIO FILE SOLUTION - VALIDATION TESTS")
    print("="*60)
    
    tests = [
        test_combination_id_generation,
        test_unified_portfolio_creation,
        test_file_mapping,
        test_csv_flag_logic
    ]
    
    passed = 0
    failed = 0
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ Test {test_func.__name__} failed with exception: {e}")
            failed += 1
        print()
    
    print("="*60)
    print("TEST SUMMARY")
    print("="*60)
    print(f"Tests passed: {passed}")
    print(f"Tests failed: {failed}")
    print(f"Total tests: {passed + failed}")
    
    if failed == 0:
        print("🎉 ALL TESTS PASSED!")
        return True
    else:
        print("❌ Some tests failed")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
