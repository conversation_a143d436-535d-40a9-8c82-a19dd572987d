"""
Fix the execution delay parameter issue.
This script identifies and fixes the issue with execution_delay optimization.
"""

from pathlib import Path
import logging
import sys
import os
from config.paths import OUTPUT_DIR

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Define output file
output_file = OUTPUT_DIR / "debug" / "execution_delay_fix.txt"
os.makedirs(output_file.parent, exist_ok=True)

with open(output_file, 'w') as f:
    f.write("EXECUTION DELAY PARAMETER FIX\n")
    f.write("============================\n\n")
    
    # Import necessary modules
    try:
        from engine.backtest import BacktestEngine
        import inspect
        
        # Get the source code of the run_backtest method
        run_backtest_source = inspect.getsource(BacktestEngine.run_backtest)
        
        f.write("Step 1: Examining BacktestEngine.run_backtest method\n")
        f.write("First 500 characters of the method:\n")
        f.write(run_backtest_source[:500])
        f.write("\n...\n\n")
        
        # Check if get_param_value is called before the docstring
        docstring_pos = run_backtest_source.find('"""')
        pre_docstring = run_backtest_source[:docstring_pos]
        
        if "get_param_value" in pre_docstring:
            f.write("ISSUE FOUND: get_param_value is called BEFORE the docstring.\n")
            f.write("This causes Python to interpret the code incorrectly.\n")
            f.write("The fix is to move these calls AFTER the docstring.\n\n")
            
            f.write("Current code (problematic):\n")
            f.write(pre_docstring)
            f.write("\n\n")
            
            # Generate the fixed code
            fixed_code = run_backtest_source.replace(
                "    def run_backtest(self, price_data, signal_generator, \n                     rebalance_freq='monthly', \n                     execution_delay=0,\n                     **signal_params):\n        rebalance_freq = get_param_value(rebalance_freq)\n        execution_delay = get_param_value(execution_delay)",
                
                "    def run_backtest(self, price_data, signal_generator, \n                     rebalance_freq='monthly', \n                     execution_delay=0,\n                     **signal_params):"
            )
            
            # Add the get_param_value calls after the docstring
            docstring_end_pos = fixed_code.find('"""', docstring_pos + 3) + 3
            fixed_code = fixed_code[:docstring_end_pos] + "\n        # Extract parameter values\n        rebalance_freq = get_param_value(rebalance_freq)\n        execution_delay = get_param_value(execution_delay)" + fixed_code[docstring_end_pos:]
            
            f.write("Fixed code:\n")
            f.write(fixed_code[:500])
            f.write("\n...\n\n")
            
            # Write the fixed code to a new file
            fixed_file = project_root / "engine" / "backtest_fixed.py"
            with open(fixed_file, 'w') as ff:
                ff.write(fixed_code)
            
            f.write(f"Fixed code written to: {fixed_file}\n\n")
            
            # Generate a patch file
            import difflib
            diff = difflib.unified_diff(
                run_backtest_source.splitlines(),
                fixed_code.splitlines(),
                fromfile='backtest.py',
                tofile='backtest_fixed.py',
                lineterm=''
            )
            
            patch_file = project_root / "engine" / "backtest_fix.patch"
            with open(patch_file, 'w') as pf:
                pf.write('\n'.join(diff))
            
            f.write(f"Patch file written to: {patch_file}\n\n")
            
            # Instructions for applying the fix
            f.write("INSTRUCTIONS TO FIX THE ISSUE:\n")
            f.write("1. Open engine/backtest.py\n")
            f.write("2. Move the get_param_value calls AFTER the docstring\n")
            f.write("3. The fixed code is in engine/backtest_fixed.py\n")
            f.write("4. You can apply the patch with: patch engine/backtest.py < engine/backtest_fix.patch\n\n")
            
            # Direct fix instructions
            f.write("DIRECT FIX:\n")
            f.write("Replace the run_backtest method definition with:\n\n")
            f.write("    def run_backtest(self, price_data, signal_generator, \n")
            f.write("                     rebalance_freq='monthly', \n")
            f.write("                     execution_delay=0,\n")
            f.write("                     **signal_params):\n")
            f.write("        \"\"\"\n")
            f.write("        Run a backtest.\n")
            f.write("        \n")
            f.write("        Args:\n")
            f.write("            price_data (DataFrame): Historical price data\n")
            f.write("            signal_generator (function): Function that generates allocation signals\n")
            f.write("            rebalance_freq (str): Rebalance frequency ('daily', 'weekly', 'monthly', 'quarterly', 'yearly')\n")
            f.write("            execution_delay (int): Execution delay in days\n")
            f.write("            **signal_params: Additional parameters for the signal generator\n")
            f.write("            \n")
            f.write("        Returns:\n")
            f.write("            dict: Backtest results\n")
            f.write("        \"\"\"\n")
            f.write("        # Extract parameter values\n")
            f.write("        rebalance_freq = get_param_value(rebalance_freq)\n")
            f.write("        execution_delay = get_param_value(execution_delay)\n")
        else:
            f.write("No issue found with the docstring placement.\n")
            f.write("The error must be elsewhere in the code.\n\n")
    
    except Exception as e:
        import traceback
        f.write(f"ERROR: {e}\n")
        f.write(traceback.format_exc())
        f.write("\n")

print(f"Fix instructions saved to: {output_file}")
