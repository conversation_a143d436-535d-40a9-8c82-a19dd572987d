"""
Capture Tool for V3 GUI Testing

This script captures output from the V3 GUI test process and saves it to a log file.
You still run run_ema_v3_gui_test.bat directly, but this script helps capture its output.
"""

import os
import sys
import logging
import datetime
from pathlib import Path

# Configure logging
def setup_logging():
    log_dir = Path(__file__).parent / "logs"
    log_dir.mkdir(exist_ok=True)
    
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = log_dir / f"v3_gui_test_{timestamp}.txt"
    
    # Create a logger that writes to both console and file
    logger = logging.getLogger("v3_gui_test")
    logger.setLevel(logging.DEBUG)
    
    # File handler
    file_handler = logging.FileHandler(log_file)
    file_handler.setLevel(logging.DEBUG)
    file_format = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    file_handler.setFormatter(file_format)
    
    logger.addHandler(file_handler)
    
    # Also output to console
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(file_format)
    logger.addHandler(console_handler)
    
    return logger, log_file

# Enhance data flow logging
def enhance_data_flow_logging():
    """
    Add additional logging to key data flow points
    """
    # We won't modify files directly here - instead we'll add logging statements
    # to the enhanced tracker document with instructions on where to place them
    return

def main():
    logger, log_file = setup_logging()
    
    logger.info("=" * 80)
    logger.info("V3 GUI TEST OUTPUT CAPTURE")
    logger.info("=" * 80)
    logger.info(f"Time: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    logger.info(f"Log file: {log_file}")
    logger.info(f"BACKTEST_LOG_LEVEL: {os.environ.get('BACKTEST_LOG_LEVEL', 'Not set')}")
    logger.info("=" * 80)
    logger.info("")
    logger.info("INSTRUCTIONS:")
    logger.info("1. Run run_ema_v3_gui_test.bat in a separate terminal")
    logger.info("2. In the GUI:")
    logger.info("   - Check parameter settings")
    logger.info("   - Click Run Backtest")
    logger.info("3. When complete, check the log file for captured output")
    logger.info("")
    logger.info("=" * 80)
    
    # Display enhanced tracker document instructions
    logger.info("")
    logger.info("TRACKING DOCUMENT:")
    logger.info("- An updated v3_reporting_analysis.md file has been created")
    logger.info("- This document contains a task checklist for tracking progress")
    logger.info("- After each test run, update the Status column in the checklist")
    logger.info("")
    
    logger.info("=" * 80)
    logger.info("NOTE: This script doesn't run the bat file - you need to do that separately")
    logger.info("=" * 80)

if __name__ == "__main__":
    main()
