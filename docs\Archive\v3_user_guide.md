# V3 Parameter System User Guide

This guide provides step-by-step instructions for working with the V3 parameter system, including how to create new strategies, modify ticker lists, and run backtests with the new system.

## Table of Contents

1. [Creating a New Strategy](#creating-a-new-strategy)
2. [Modifying Ticker Lists](#modifying-ticker-lists)
3. [Running Backtests with V3](#running-backtests-with-v3)
4. [Parameter Type Reference](#parameter-type-reference)
5. [Common Tasks](#common-tasks)

## Creating a New Strategy

Creating a new strategy in the V3 system is straightforward. Simply create a new class that inherits from `StrategyParameterSet` and define your parameters.

### Step 1: Create a new strategy file

Create a new Python file in the `strategies` directory (e.g., `strategies/my_strategy.py`):

```python
from v3_engine import StrategyParameterSet, NumericParameter, CategoricalParameter

class MyStrategy(StrategyParameterSet):
    """
    My custom strategy implementation.
    
    This strategy uses XYZ approach for asset allocation.
    """
    def __init__(self, name="my_strategy"):
        super().__init__(name)
        
        # Register strategy-specific parameters
        self.register_parameters([
            # Define lookback periods
            NumericParameter(
                name="lookback_short", 
                default=20, 
                min_value=5, 
                max_value=100, 
                step=5,
                optimize=True,
                description="Short-term lookback period"
            ),
            NumericParameter(
                name="lookback_medium", 
                default=60, 
                min_value=20, 
                max_value=200, 
                step=10,
                optimize=False,
                description="Medium-term lookback period"
            ),
            NumericParameter(
                name="lookback_long", 
                default=120, 
                min_value=50, 
                max_value=300, 
                step=10,
                optimize=False,
                description="Long-term lookback period"
            ),
            
            # Define other strategy-specific parameters
            NumericParameter(
                name="threshold", 
                default=0.05, 
                min_value=0.01, 
                max_value=0.20, 
                step=0.01,
                optimize=True,
                description="Signal threshold for position changes"
            ),
            
            CategoricalParameter(
                name="signal_method",
                choices=["crossover", "momentum", "mean_reversion"],
                default="crossover",
                optimize=True,
                description="Method used to generate signals"
            )
        ])
        
    # Optionally implement strategy-specific methods
    def calculate_signal(self, prices, **kwargs):
        """
        Calculate signals based on strategy parameters.
        
        Args:
            prices: Price data
            **kwargs: Additional arguments
            
        Returns:
            dict: Signal data
        """
        # Access parameters via self.get_parameter()
        lookback_short = self.get_parameter("lookback_short").value
        lookback_medium = self.get_parameter("lookback_medium").value
        lookback_long = self.get_parameter("lookback_long").value
        threshold = self.get_parameter("threshold").value
        signal_method = self.get_parameter("signal_method").value
        
        # Implement your signal logic here
        # ...
        
        return signals
```

### Step 2: Register your strategy

Strategies need to be registered to be available in the system. Add the following code to the bottom of your strategy file:

```python
# Register the strategy with the system
from v3_engine import register_strategy_class
register_strategy_class(MyStrategy)
```

### Step 3: Import the strategy

Import your strategy in the appropriate module (e.g., `v3_engine/__init__.py` or in your main script):

```python
from strategies.my_strategy import MyStrategy
```

### Step 4: Use your strategy

Your strategy can now be created and used in backtests:

```python
from v3_engine import create_strategy

# Create strategy instance
my_strategy = create_strategy("mystrategy")

# Register with the system (makes it available to GUI and reports)
my_strategy.register_with_system()

# Run backtest with the strategy
# ...
```

## Modifying Ticker Lists

Ticker lists are defined in `config/ticker_lists.py` and are used by CategoricalListParameter to provide groups of tickers.

### Step 1: Open the ticker list file

Open `config/ticker_lists.py` in your preferred editor.

### Step 2: Add or modify ticker groups

The file contains a dictionary called `TICKER_LISTS` with ticker groups. Add or modify groups as needed:

```python
TICKER_LISTS = {
    # Existing groups
    'tech': ['AAPL', 'MSFT', 'GOOGL', 'META', 'AMZN', 'NVDA', 'INTC', 'CSCO', 'AMD', 'ORCL'],
    
    # Add your new group
    'my_portfolio': ['SPY', 'QQQ', 'TLT', 'GLD', 'SLV', 'VNQ'],
    
    # Modify existing group
    'finance': ['JPM', 'BAC', 'WFC', 'C', 'GS', 'MS', 'AXP', 'V', 'MA', 'BLK', 'SCHW', 'PYPL'],
}
```

### Step 3: Use the ticker group in your strategy

Use the ticker group in your strategy or configuration:

```python
from v3_engine import CategoricalListParameter

# In a strategy class
self.register_parameter(
    CategoricalListParameter(
        name="ticker_group",
        list_name="my_portfolio",  # Default group
        config_file="config/ticker_lists.py",
        optimize=True,
        description="Ticker group to use for backtesting"
    )
)
```

## Running Backtests with V3

Running backtests with the V3 system involves:

1. Setting up parameters
2. Creating and registering a strategy
3. Running the backtest with the parameter values

### Example: Basic Backtest

```python
from v3_engine import get_registry, create_strategy, PerformanceReporterAdapter

# Get registry and create strategy
registry = get_registry()
strategy = create_strategy("ema")
strategy.register_with_system()

# Set parameter values
registry.set_parameter_value("lookback_short", 20)
registry.set_parameter_value("lookback_long", 120)
registry.set_parameter_value("execution_delay", 1)
registry.set_parameter_value("ticker_group", "tech")

# Get parameter values for the backtest
params = registry.get_parameter_values()

# Run the backtest (adapt to your actual backtest function)
results = run_backtest(**params)

# Generate performance report
reporter = PerformanceReporterAdapter()
report_path = reporter.generate_performance_report(
    backtest_results=results,
    ticker_group=params["ticker_group"],
    output_dir="output/performance",
    strategy_name="ema"
)
```

### Example: Parameter Optimization

```python
from v3_engine import get_registry, create_strategy, ParameterOptimizer, PerformanceReporterAdapter

# Get registry and create strategy
registry = get_registry()
strategy = create_strategy("ema")
strategy.register_with_system()

# Set which parameters to optimize
registry.get_parameter("lookback_short").optimize = True
registry.get_parameter("lookback_long").optimize = True
registry.get_parameter("threshold").optimize = True

# Create optimizer
optimizer = ParameterOptimizer()

# Define backtest function
def run_backtest_with_params(params):
    # Your backtest implementation
    results = run_backtest(**params)
    return results

# Run optimization
best_params, best_result = optimizer.optimize_with_function(
    run_backtest_with_params,
    metric_key="sharpe",
    higher_is_better=True
)

# Generate optimization report
reporter = PerformanceReporterAdapter(optimizer=optimizer)
report_path = reporter.generate_optimization_report(
    optimization_results=optimizer.get_all_results(),
    ticker_group=best_params["ticker_group"],
    output_dir="output/optimization",
    strategy_name="ema",
    best_result=(best_params, best_result)
)
```

## Parameter Type Reference

The V3 system provides three types of parameters:

### NumericParameter

For numeric values (integers or floats) with minimum, maximum, and step sizes.

```python
NumericParameter(
    name="lookback",           # Parameter name
    default=20,                # Default value
    min_value=5,               # Minimum value
    max_value=100,             # Maximum value
    step=5,                    # Step size for optimization
    optimize=False,            # Whether to optimize this parameter
    description="Description"  # Optional description
)
```

### CategoricalParameter

For selection from a predefined list of choices.

```python
CategoricalParameter(
    name="rebalance_freq",                      # Parameter name
    choices=["monthly", "quarterly", "yearly"], # Available choices
    default="monthly",                          # Default choice
    optimize=False,                             # Whether to optimize this parameter
    description="Rebalancing frequency"         # Optional description
)
```

### CategoricalListParameter

For selection of user-defined groups from an external config file.

```python
CategoricalListParameter(
    name="ticker_group",                 # Parameter name
    list_name="tech",                    # Default group
    config_file="config/ticker_lists.py", # Config file containing groups
    optimize=False,                      # Whether to optimize this parameter
    description="Ticker group to use"    # Optional description
)
```

## Common Tasks

### Getting Parameter Values

```python
# Get a specific parameter
lookback = registry.get_parameter("lookback_short")
lookback_value = lookback.value

# Get all parameter values
all_values = registry.get_parameter_values()

# Get parameters for a specific group
strategy_params = registry.get_parameters("strategy_ema")
```

### Setting Parameter Values

```python
# Set a specific parameter value
registry.set_parameter_value("lookback_short", 30)

# Set a parameter's optimize flag
registry.get_parameter("lookback_short").optimize = True
```

### Creating GUI Controls

```python
# Create GUI controls for all parameters
gui_manager = GuiParameterManager()
controls = gui_manager.create_parameter_controls(parent_frame)

# Create a strategy selector
strategy_selector = gui_manager.create_strategy_selector(parent_frame, row=0)

# Apply GUI values to the registry
gui_manager.apply_gui_values_to_registry()
```

### Getting Available Strategies

```python
from v3_engine import get_available_strategies

strategies = get_available_strategies()
print(f"Available strategies: {strategies}")
```
