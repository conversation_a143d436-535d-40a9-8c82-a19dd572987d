@echo off
REM ============================================
REM Script: run_main_v4_prod2.bat
REM Description: Super-stable launcher for V4 decoupled pipeline with full + filtered logs
REM ============================================

SETLOCAL

REM --- Paths ---
SET "PYTHON_EXE=F:\AI_Library\my_quant_env\Scripts\python.exe"
SET "SCRIPT_DIR=%~dp0"
SET "PIPELINE_SCRIPT=%SCRIPT_DIR%run_v4_decoupled_pipeline.py"
SET "OUTPUT_DIR=%SCRIPT_DIR%v4_trace_outputs"

REM --- Ensure output directory exists ---
IF NOT EXIST "%OUTPUT_DIR%" (
    mkdir "%OUTPUT_DIR%"
)

REM --- Timestamp (YYYYMMDD_HHMMSS) ---
SET "TIMESTAMP=%DATE:~10,4%%DATE:~4,2%%DATE:~7,2%_%TIME:~0,2%%TIME:~3,2%%TIME:~6,2%"
SET "TIMESTAMP=%TIMESTAMP: =0%"

REM --- Log files ---
SET "FULL_LOG=%OUTPUT_DIR%\full_%TIMESTAMP%.txt"
SET "FILTERED_LOG=%OUTPUT_DIR%\filtered_%TIMESTAMP%.txt"

echo [%TIME%] Running V4 production pipeline (full log: "%FULL_LOG%" )

REM --- Run Python Orchestrator ---
echo [%TIME%] Starting V4 production pipeline...
(
    echo ===== V4 PRODUCTION PIPELINE =====
    "%PYTHON_EXE%" "%PIPELINE_SCRIPT%"
    SET "EXIT_CODE=%ERRORLEVEL%"
    echo V4 production pipeline completed with exit code %EXIT_CODE%
) > "%FULL_LOG%" 2>&1

REM --- Create filtered log ---
echo ===== FILTERED OUTPUT ===== > "%FILTERED_LOG%"
echo Run Time: %DATE% %TIME% >> "%FILTERED_LOG%"
echo. >> "%FILTERED_LOG%"
echo ===== MILESTONES ===== >> "%FILTERED_LOG%"
findstr /C:"[MILESTONE]" "%FULL_LOG%" >> "%FILTERED_LOG%" 2>nul
echo. >> "%FILTERED_LOG%"
echo ===== WARNINGS ^& ERRORS ===== >> "%FILTERED_LOG%"
findstr /C:"[ERROR]" /C:"[WARNING]" "%FULL_LOG%" >> "%FILTERED_LOG%" 2>nul
findstr /i "error warning exception traceback failed fatal" "%FULL_LOG%" >> "%FILTERED_LOG%" 2>nul

echo [%TIME%] V4 production pipeline finished with exit code %EXIT_CODE%
IF %EXIT_CODE% EQU 0 (
    echo Pipeline completed successfully.
) ELSE (
    echo Pipeline encountered errors. Check the logs for details.
)
echo Full log: "%FULL_LOG%"
echo Filtered log: "%FILTERED_LOG%"

ENDLOCAL
exit /b %EXIT_CODE%
