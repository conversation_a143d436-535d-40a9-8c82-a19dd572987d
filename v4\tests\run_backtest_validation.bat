@echo off
echo =====================================================
echo Backtest Engine Validation Test - %date% %time%
echo =====================================================

REM Set up virtual environment path
set VENV_PATH=F:\AI_Library\my_quant_env

REM Activate virtual environment
call %VENV_PATH%\Scripts\activate.bat

REM Set output directory
set OUTPUT_DIR=s:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\tests\output
echo Output directory path: %OUTPUT_DIR%

REM Check if output directory exists
echo Checking if output directory exists: %OUTPUT_DIR%
if not exist "%OUTPUT_DIR%" (
    echo Creating output directory...
    mkdir "%OUTPUT_DIR%" 2>nul
    if %errorlevel% neq 0 (
        echo ERROR: Failed to create output directory! Error code: %errorlevel%
        echo Current directory: %cd%
        echo Attempting to create parent directories...
        mkdir "s:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\tests" 2>nul
        mkdir "%OUTPUT_DIR%" 2>nul
    )
    
    if exist "%OUTPUT_DIR%" (
        echo Output directory created successfully.
    ) else (
        echo ERROR: Still failed to create output directory!
        echo Will attempt to use current directory for output.
        set OUTPUT_DIR=%cd%
        echo New output directory: %OUTPUT_DIR%
    )
) else (
    echo Output directory already exists.
)

REM Set log file
set LOG_FILE=%OUTPUT_DIR%\backtest_validation_%date:~-4,4%%date:~-7,2%%date:~-10,2%_%time:~0,2%%time:~3,2%%time:~6,2%.log
set LOG_FILE=%LOG_FILE: =0%

echo Starting validation tests...
echo Results will be saved to: %LOG_FILE%

REM Run the test script with output to both console and log file
echo Running test script...
echo Command: python s:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\tests\test_backtest_v4.py
echo Output will be saved to: %LOG_FILE%

REM Check if Python is available
python --version
if %errorlevel% neq 0 (
    echo ERROR: Python not found or not in PATH!
    goto :error
)

REM Run the script with output to log file
python s:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\tests\test_backtest_v4.py > "%LOG_FILE%" 2>&1
echo Python script completed with exit code: %errorlevel%

REM Display log file contents
echo.
echo Log file contents:
echo ==================
if exist "%LOG_FILE%" (
    type "%LOG_FILE%"
) else (
    echo ERROR: Log file not created!
)

REM Create a summary CSV file with test results
set CSV_FILE=%OUTPUT_DIR%\backtest_validation_summary_%date:~-4,4%%date:~-7,2%%date:~-10,2%_%time:~0,2%%time:~3,2%%time:~6,2%.csv
set CSV_FILE=%CSV_FILE: =0%

echo Test,Result,CAGR,Volatility,Sharpe,MaxDrawdown,Turnover > "%CSV_FILE%"

REM Extract key metrics from log file and append to CSV
findstr /C:"Equal Weight Strategy:" "%LOG_FILE%" > nul
if %errorlevel% equ 0 (
    findstr /C:"✅ Weights history differs from signal history" "%LOG_FILE%" > nul
    if %errorlevel% equ 0 (
        set EQ_RESULT=PASS
    ) else (
        set EQ_RESULT=FAIL
    )
    for /f "tokens=2 delims=:" %%a in ('findstr /C:"CAGR: " "%LOG_FILE%"') do set EQ_CAGR=%%a
    for /f "tokens=2 delims=:" %%a in ('findstr /C:"Volatility: " "%LOG_FILE%"') do set EQ_VOL=%%a
    for /f "tokens=2 delims=:" %%a in ('findstr /C:"Sharpe Ratio: " "%LOG_FILE%"') do set EQ_SHARPE=%%a
    for /f "tokens=2 delims=:" %%a in ('findstr /C:"Max Drawdown: " "%LOG_FILE%"') do set EQ_DD=%%a
    for /f "tokens=2 delims=:" %%a in ('findstr /C:"Turnover: " "%LOG_FILE%"') do set EQ_TO=%%a
    echo Equal Weight,%EQ_RESULT%,%EQ_CAGR%,%EQ_VOL%,%EQ_SHARPE%,%EQ_DD%,%EQ_TO% >> "%CSV_FILE%"
)

REM Momentum strategy removed to keep the codebase simple

findstr /C:"EMA with Delay Strategy:" "%LOG_FILE%" > nul
if %errorlevel% equ 0 (
    findstr /C:"✅ Weights history differs from signal history" "%LOG_FILE%" > nul
    if %errorlevel% equ 0 (
        set EMA_RESULT=PASS
    ) else (
        set EMA_RESULT=FAIL
    )
    for /f "tokens=2 delims=:" %%a in ('findstr /C:"CAGR: " "%LOG_FILE%"') do set EMA_CAGR=%%a
    for /f "tokens=2 delims=:" %%a in ('findstr /C:"Volatility: " "%LOG_FILE%"') do set EMA_VOL=%%a
    for /f "tokens=2 delims=:" %%a in ('findstr /C:"Sharpe Ratio: " "%LOG_FILE%"') do set EMA_SHARPE=%%a
    for /f "tokens=2 delims=:" %%a in ('findstr /C:"Max Drawdown: " "%LOG_FILE%"') do set EMA_DD=%%a
    for /f "tokens=2 delims=:" %%a in ('findstr /C:"Turnover: " "%LOG_FILE%"') do set EMA_TO=%%a
    echo EMA with Delay,%EMA_RESULT%,%EMA_CAGR%,%EMA_VOL%,%EMA_SHARPE%,%EMA_DD%,%EMA_TO% >> "%CSV_FILE%"
)

echo.
echo Tests completed!
echo Full log saved to: %LOG_FILE%
echo Summary saved to: %CSV_FILE%

REM Display output directory contents
echo.
echo Output directory contents:
dir "%OUTPUT_DIR%"

echo.

REM Check if all tests passed
findstr /C:"ALL TESTS PASSED" "%LOG_FILE%" > nul
if %errorlevel% equ 0 (
    echo All tests passed! Allocation history is preserved correctly.
) else (
    echo Some tests failed! Check the logs for details.
)

REM Deactivate virtual environment
echo Deactivating virtual environment...
call %VENV_PATH%\Scripts\deactivate.bat

goto :end

:error
echo Test execution failed!

:end

pause
