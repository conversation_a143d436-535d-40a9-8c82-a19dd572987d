# s:/Dropbox/Scott Only Internal/Quant_Python_24/Backtest_FinAsset_Alloc_Template/config/paths.py
"""
Centralized path configuration for the Quant Python 24 Backtesting Framework.
All critical paths should be defined here and imported by other modules.
This ensures consistency and ease of maintenance.
"""

import os
import sys
from pathlib import Path

# --- Core Project Structure ---
# Resolves to s:/Dropbox/Scott Only Internal/Quant_Python_24/Backtest_FinAsset_Alloc_Template
PROJECT_ROOT = Path(__file__).resolve().parent.parent

# --- Standard Subdirectories ---
CONFIG_DIR = PROJECT_ROOT / "config"
DATA_DIR = PROJECT_ROOT / "data"
LOG_DIR = PROJECT_ROOT / "logs"
OUTPUT_DIR = PROJECT_ROOT / "output"
DOCS_DIR = PROJECT_ROOT / "docs"
TESTS_DIR = PROJECT_ROOT / "v4" / "tests" # Specific to v4 tests for now

# --- CPS V4 Specific ---
CPS_V4_DIR = PROJECT_ROOT / "CPS_V4"
V4_DIR = PROJECT_ROOT / "v4"
V4_MODELS_DIR = V4_DIR / "models"
V4_ENGINE_DIR = V4_DIR / "engine"
V4_CONFIG_DIR = V4_DIR / "config" # If different from top-level config

# --- External Libraries & Resources ---
# Path to the user's virtual environment
VENV_PATH = Path(os.getenv("MY_QUANT_ENV_PATH", r"F:\AI_Library\my_quant_env")) # Uses env var, with a default

# Path to the Custom Function Library
CUSTOM_LIB_PATH = Path(os.getenv("CUSTOM_FUNCTION_LIB_PATH", r"S:\Dropbox\Scott Only Internal\Quant_Python_24\Custom Function Library")).resolve()

# --- Add Key Paths to sys.path ---
# Ensure PROJECT_ROOT is the first custom path to allow for unambiguous top-level imports
# (e.g., from CPS_V4 import ..., from v4 import ...)
if str(PROJECT_ROOT) not in sys.path:
    sys.path.insert(0, str(PROJECT_ROOT))

# Add Custom Function Library to sys.path
if CUSTOM_LIB_PATH.exists() and str(CUSTOM_LIB_PATH) not in sys.path:
    sys.path.insert(1, str(CUSTOM_LIB_PATH))

# Add CPS_V4_DIR to sys.path to allow direct import of modules within it
if CPS_V4_DIR.exists() and str(CPS_V4_DIR) not in sys.path:
    sys.path.insert(1, str(CPS_V4_DIR)) # Insert after PROJECT_ROOT, but before other custom libs if order matters
    # Optionally, add subdirectories of Custom Function Library if they act as packages
    # for subdir in CUSTOM_LIB_PATH.iterdir():
    #     if subdir.is_dir() and (subdir / "__init__.py").exists(): # Check if it's a package
    #         if str(subdir) not in sys.path:
    #             sys.path.append(str(subdir))

# --- Create Essential Directories if they don't exist ---
for dir_path in [LOG_DIR, OUTPUT_DIR, DATA_DIR]: # Add others as needed
    dir_path.mkdir(parents=True, exist_ok=True)

# --- For Debugging: Print effective paths (optional, can be commented out) ---
# print(f"DEBUG: PROJECT_ROOT set to: {PROJECT_ROOT}")
# print(f"DEBUG: sys.path includes:")
# for p in sys.path[:5]: # Print first 5 for brevity
#     print(f"DEBUG:   {p}")

# --- Backward Compatibility (if needed) ---
# Example: If older code used a different variable name for a path
# CUSTOM_FUNCTION_LIB_DIR = CUSTOM_LIB_PATH # As per MEMORY[bfb2ffde-da25-4d1a-8ca4-1258730a3c16]

# Add the specific backward compatibility from memory
CUSTOM_FUNCTION_LIB_DIR = CUSTOM_LIB_PATH


# --- Helper function to get a path (optional) ---
def get_path(path_name: str, default=None) -> Path | None:
    """
    Retrieves a defined path by its variable name.
    Example: get_path('DATA_DIR')
    """
    return globals().get(path_name, default)
