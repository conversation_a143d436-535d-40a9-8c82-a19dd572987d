## ACTIONABLE RENAMING PLAN (to avoid confusion in production)

| Path                                                       | Old Name(s)                                                       | New Name(s)                                                                   | In Use in Production? | Action |
| ---------------------------------------------------------- | ----------------------------------------------------------------- | ----------------------------------------------------------------------------- | --------------------- | ------ |
| Custom Function Library/reporting/performance_reporting.py | performance_reporting.py                                          | lib_performance_reporting.py                                                  | NO                    | Rename |
| Custom Function Library/config/parameter_optimization.py   | define_parameter, get_parameter_range, get_parameter_combinations | lib_define_parameter, lib_get_parameter_range, lib_get_parameter_combinations | NO                    | Rename |

**Details:**  

- Only modules/functions NOT used in the current Template production flow are to be renamed.
- Renaming uses the `lib_` prefix for Library items to minimize disruption and avoid confusion.
- No changes are required in the Template codebase.

---

# Module & Function Name Comparison: Template vs Library

## SUMMARY: Virtually Identical Names (≤2 character difference)

| Template Name                                                       | Library Name                                                  | Type     |
| ------------------------------------------------------------------- | ------------------------------------------------------------- | -------- |
| reporting/performance_reporting.py                                  | reporting/performance_reporting.py                            | module   |
| define_parameter (config/local_parameter_optimization.py)           | define_parameter (config/parameter_optimization.py)           | function |
| get_parameter_range (config/local_parameter_optimization.py)        | get_parameter_range (config/parameter_optimization.py)        | function |
| get_parameter_combinations (config/local_parameter_optimization.py) | get_parameter_combinations (config/parameter_optimization.py) | function |

> Only exact or ≤2 character differences are listed above. All other overlaps are detailed below for reference.

---

## 1. Modules & Submodules

| Template Path / Module                 | Library Path / Module                                                                     | Notes / Similarity                   |
| -------------------------------------- | ----------------------------------------------------------------------------------------- | ------------------------------------ |
| engine/backtest.py                     | backtesting/backtest_framework.py                                                         | Both: backtest engine/core           |
| engine/portfolio.py                    | portfolio/portfolio_backtesting.py                                                        | Both: portfolio logic                |
| engine/allocation.py                   | portfolio/minV_portfolio_optimization.py,<br>portfolio/advanced_portfolio_optimization.py | Both: allocation/optimization        |
| engine/orders.py                       | ---                                                                                       | Template only                        |
| engine/execution.py                    | ---                                                                                       | Template only                        |
| reporting/performance_reporting.py     | reporting/performance_reporting.py                                                        | **Identical name**                   |
| config/local_parameter_optimization.py | config/parameter_optimization.py                                                          | **Nearly identical** (local vs base) |
| config/config_v2.py                    | config/config.py                                                                          | Both: config                         |
| config/paths.py                        | ---                                                                                       | Template only                        |
| engine/benchmark.py                    | ---                                                                                       | Template only                        |
| run_backtest_v2_with_metrics.py        | ---                                                                                       | Template only (entry point)          |

---

## 2. Functions & Classes

| Template Function/Class (Module)                                                                                 | Library Function/Class (Module)                                                                            | Notes / Similarity              |
| ---------------------------------------------------------------------------------------------------------------- | ---------------------------------------------------------------------------------------------------------- | ------------------------------- |
| BacktestEngine, run_backtest(), _calculate_results() (engine/backtest.py)                                        | backtest_strategy() (backtesting/backtest_framework.py)                                                    | Both: main driver               |
| Portfolio, get_total_value(), get_returns_series() (engine/portfolio.py)                                         | port_backtest_strategy(), port_calculate_backtest_metrics() (portfolio/portfolio_backtesting.py)           | Both: portfolio logic           |
| Order, Trade, TradeLog (engine/orders.py)                                                                        | ---                                                                                                        | Template only                   |
| ExecutionEngine, execute_orders() (engine/execution.py)                                                          | ---                                                                                                        | Template only                   |
| compare_positions(), generate_orders(), calculate_rebalance_orders() (engine/allocation.py)                      | port_min_variance_portfolio(), port_hierarchical_risk_parity() (portfolio/*)                               | Both: allocation logic          |
| calculate_equal_weight_benchmark() (engine/benchmark.py)                                                         | ---                                                                                                        | Template only                   |
| generate_performance_report_local(), create_backtest_report() (reporting/performance_reporting.py)               | create_performance_tables(), create_optimization_results(), etc. (reporting/performance_reporting.py)      | **Similar reporting utilities** |
| define_parameter(), get_parameter_range(), get_parameter_combinations() (config/local_parameter_optimization.py) | define_parameter(), get_parameter_range(), get_parameter_combinations() (config/parameter_optimization.py) | **Identical names**             |
| config_v2 (config/config_v2.py)                                                                                  | ---                                                                                                        | Template only                   |

---

## 3. Additional Overlaps/Similarities

- **Performance Reporting**: Both have a `performance_reporting.py` module with similar function purposes, though function names may differ slightly.
- **Parameter Optimization**: Both have modules for parameter optimization with nearly identical function names (define_parameter, get_parameter_range, etc.).
- **Portfolio/Backtest Logic**: Both have modules and functions for portfolio management and backtesting, though the Template uses object-oriented classes (e.g., Portfolio, BacktestEngine), while the Library often uses functional approaches (e.g., port_backtest_strategy).

---

## 4. Example of Potential for Confusion

- `reporting/performance_reporting.py` exists in both with different (but overlapping) function sets.
- `config/local_parameter_optimization.py` (Template) vs `config/parameter_optimization.py` (Library) have nearly identical function names and purposes.
- Allocation and optimization logic appears in both, sometimes with similar or identical function names.

---

**This table is intended to help developers avoid confusion and accidental misuse when working with both codebases in the same environment.**
