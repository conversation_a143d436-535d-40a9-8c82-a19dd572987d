"""Compatibility shim.

Legacy modules still import `CPS_v4.settings_CPS_v4`.  The canonical
location is now `v4.settings.settings_CPS_v4`.  This shim forwards all
such imports without requiring invasive refactors across dozens of
modules.  Remove once all code is updated.
"""
from importlib import import_module
import sys as _sys

_mod = import_module("v4.settings.settings_CPS_v4")
# Expose submodule so `from CPS_v4.settings_CPS_v4 import ...` works
_sys.modules[__name__ + ".settings_CPS_v4"] = _mod
# Re-export public names
globals().update(_mod.__dict__)
