"""
Performance Table XLSX Generator for CPS V4
Generates comprehensive performance reports with all required tabs.
Integrates with existing unified pipeline to use real backtest data.

LOCATION: v4/py_reporting/performance_table_generator.py
This is the active version - moved from v4/reporting/ for better organization.

Requirements implemented:
- Q1: Show ALL optimizable parameters
- Q2: Simple parameter format (current value only)
- Q3: Calculate from equity curves using real price data
- Q4: Equal weight benchmark from config
- Q5: Cash component included in portfolio value
- Q6: Enhanced trade log with commission+slippage
- Q7: Report generation timestamp, save to \reporting
"""

import pandas as pd
import numpy as np
from datetime import datetime
import configparser
import os
from pathlib import Path
import openpyxl
from openpyxl.styles import Font, Alignment, PatternFill

# Import centralized path configuration
from v4.config.paths_v4 import (
    PROJECT_ROOT, OUTPUT_DIR, V4_TRACE_OUTPUTS_DIR, OPTIMIZATION_VALIDATION_DIR,
    V4_SETTINGS_FILE, DATA_DIR, get_validation_dir, get_reporting_file_path,
    get_v4_trace_file_path, EQUITY_CURVE_STRATEGY_LATEST, EQUITY_CURVE_BENCHMARK_LATEST
)
from openpyxl.utils.dataframe import dataframe_to_rows
import logging
import subprocess
import shutil
import tempfile
import json

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class PerformanceTableGenerator:
    """Generates Performance Table XLSX reports from backtest data."""
    
    def __init__(self, config_path=None, csv_flag_use=False):
        """Initialize with configuration file path."""
        # Use centralized path configuration
        self.config_path = config_path or str(V4_SETTINGS_FILE)
        self.config = self._load_config()
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.csv_flag_use = csv_flag_use  # Controls CSV file generation

        # Load new CSV validation detail control flag - USE SECTION-AGNOSTIC APPROACH
        from v4.settings.config_helper import get_param_boolean
        self.csv_valid_det = get_param_boolean('csv_valid_det', self.config_path, fallback=False)
        self.optimization_active = get_param_boolean('optimization_active', self.config_path, fallback=False)

        # EARLY GATEWAY SETUP: Set CPS_V4_OPTIMIZATION_ACTIVE environment variable
        # This is the EARLIEST point where we can determine if optimization should run
        import os
        if self.optimization_active:
            os.environ['CPS_V4_OPTIMIZATION_ACTIVE'] = 'true'
            print(f"[EARLY GATEWAY] Set CPS_V4_OPTIMIZATION_ACTIVE=true (optimization_active={self.optimization_active})")
        else:
            # Ensure it's not set if optimization is not active
            if 'CPS_V4_OPTIMIZATION_ACTIVE' in os.environ:
                del os.environ['CPS_V4_OPTIMIZATION_ACTIVE']
            print(f"[EARLY GATEWAY] CPS_V4_OPTIMIZATION_ACTIVE not set (optimization_active={self.optimization_active})")

        # Track combination to file mapping for optimization
        self.combination_file_mapping = {}

        # Validation mode for step-by-step optimization validation
        self.validation_mode = False
        self.validation_dir = OPTIMIZATION_VALIDATION_DIR

        # Create validation directories if in validation mode
        if getattr(self, 'validation_mode', False):
            self._setup_validation_directories()

        # Commission and slippage rates from config - ALLOW FALLBACKS FOR ESSENTIAL CONFIG
        self.commission_rate = float(self.config.get('Backtest', 'commission_rate', fallback=0.001))
        self.slippage_rate = float(self.config.get('Backtest', 'slippage_rate', fallback=0.0005))

        logger.info(f"Initialized PerformanceTableGenerator with timestamp: {self.timestamp}, CSV generation: {self.csv_flag_use}, CSV validation detail: {self.csv_valid_det}")

    def generate_combo_id(self, combination_params):
        """Generate unique, readable combination identifier.

        Args:
            combination_params: Dictionary of parameter values for this combination

        Returns:
            str: Readable combination ID like 'st15_mt70_top2_exec1'
        """
        # Sort parameters for consistent naming
        sorted_params = sorted(combination_params.items())

        # Create readable ID parts
        param_parts = []
        for key, value in sorted_params:
            if key == 'st_lookback':
                param_parts.append(f"st{value}")
            elif key == 'mt_lookback':
                param_parts.append(f"mt{value}")
            elif key == 'top_n':
                param_parts.append(f"top{value}")
            elif key == 'execution_delay':
                param_parts.append(f"exec{value}")
            else:
                # Generic parameter handling
                param_parts.append(f"{key}{value}")

        combo_id = "_".join(param_parts)
        logger.debug(f"[COMBO_ID] Generated ID: {combo_id} for params: {combination_params}")
        return combo_id

    def _load_unified_portfolio_for_combination(self, combo_id, timestamp):
        """Load unified portfolio file for specific optimization combination.

        Args:
            combo_id: Unique combination identifier
            timestamp: Timestamp for file matching

        Returns:
            pd.Series: Equity curve from Portfolio_Value column, or None if not found
        """
        try:
            # Construct expected filename pattern
            expected_pattern = f"unified_portfolio_combo_{combo_id}_*.csv"

            # Search for matching files in reporting directory
            reporting_dir = OUTPUT_DIR
            matching_files = list(reporting_dir.glob(expected_pattern))

            if not matching_files:
                # Try with exact timestamp
                exact_file = reporting_dir / f"unified_portfolio_combo_{combo_id}_{timestamp}.csv"
                if exact_file.exists():
                    matching_files = [exact_file]
                else:
                    logger.error(f"[MAPPING] No unified portfolio file found for combination {combo_id}")
                    logger.error(f"[MAPPING] Searched pattern: {expected_pattern}")
                    logger.error(f"[MAPPING] Searched directory: {reporting_dir}")
                    return None

            # Use most recent file if multiple found
            target_file = max(matching_files, key=lambda f: f.stat().st_mtime)

            # Load and validate file
            df = pd.read_csv(target_file, index_col='Date', parse_dates=True)

            # Validate required columns
            required_cols = ['Portfolio_Value']
            missing_cols = [col for col in required_cols if col not in df.columns]
            if missing_cols:
                logger.error(f"[MAPPING] Missing required columns in {target_file}: {missing_cols}")
                logger.error(f"[MAPPING] Available columns: {list(df.columns)}")
                return None

            # Extract equity curve for validation
            equity_curve = df['Portfolio_Value'].copy()

            logger.info(f"[MAPPING] Loaded unified portfolio for combo {combo_id}")
            logger.info(f"[MAPPING] File: {target_file}")
            logger.info(f"[MAPPING] Data points: {len(equity_curve)}")
            logger.info(f"[MAPPING] Final portfolio value: ${equity_curve.iloc[-1]:,.2f}")

            return equity_curve

        except Exception as e:
            logger.error(f"[MAPPING] Error loading unified portfolio for combo {combo_id}: {e}")
            return None

    def _setup_validation_directories(self):
        """Set up directories for validation artifacts with timestamps according to stepmappingopt.md."""
        if not self.validation_mode:
            return
            
        # If validation_dir is already set and exists, use it (from batch file)
        if hasattr(self, 'validation_dir') and isinstance(self.validation_dir, Path) and self.validation_dir.exists():
            # Initialize status file if it doesn't exist (flat structure)
            status_file = self.validation_dir / "status__current_step.txt"
            if not status_file.exists():
                with open(status_file, "w") as f:
                    f.write("VALIDATION_INITIALIZED")

            logger.info(f"Validation mode enabled. Using existing validation directory: {self.validation_dir}")
            return
            
        # Create main validation directory with timestamp subfolder
        self.validation_timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.validation_dir = get_validation_dir(self.validation_timestamp)

        # Create only the main directory (flat structure)
        self.validation_dir.mkdir(parents=True, exist_ok=True)

        # Initialize status file (flat structure with prefix)
        with open(self.validation_dir / "status__current_step.txt", "w") as f:
            f.write("VALIDATION_INITIALIZED")
            
        # Log validation setup (flat structure)
        validation_log_path = self.validation_dir / "validation__setup.log"
        with open(validation_log_path, "w") as f:
            f.write(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] Validation directories created\n")
            f.write(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] Timestamp: {self.validation_timestamp}\n")
            
        logger.info(f"Validation directories created: {self.validation_dir}")
    
    def _log_validation_step(self, step_number, step_name, status="RUNNING", details=None):
        """Log validation step information and update status with flat file structure."""
        if not self.validation_mode:
            return

        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # Update current step status file (flat structure)
        with open(self.validation_dir / "status__current_step.txt", "w") as f:
            f.write(f"STEP_{step_number}_OF_10_{status}")

        # Create step-specific checkpoint file (flat structure with prefix)
        step_checkpoint_file = self.validation_dir / f"step{step_number:02d}__checkpoint.txt"
        with open(step_checkpoint_file, "w") as f:
            f.write(f"STEP_{step_number}_OF_10_{status}\n")
            f.write(f"Step Name: {step_name}\n")
            f.write(f"Timestamp: {timestamp}\n")
            if details:
                f.write(f"Details: {details}\n")

        # Log to console and validation log
        log_message = f"[{timestamp}] [VALIDATION] STEP {step_number}: {step_name} - {status}"

        print(log_message)
        logger.info(log_message)

        # Append to step-specific log (flat structure with prefix)
        step_log_file = self.validation_dir / f"step{step_number:02d}__log.txt"
        with open(step_log_file, "a") as f:
            f.write(f"{log_message}\n")
            if details:
                f.write(f"[{timestamp}] [DETAILS] {details}\n")
        
        # Append to main validation log (flat structure)
        with open(self.validation_dir / "validation__main.log", "a") as f:
            f.write(f"{log_message}\n")
            
        # If step failed, stop immediately (no fallbacks)
        if status == "FAILED":
            error_msg = f"Validation STEP {step_number} FAILED: {step_name}"
            logger.error(f"[{timestamp}] [VALIDATION] {error_msg}")
            raise RuntimeError(error_msg)
            
    def _load_config(self):
        """Load configuration from INI file."""
        import os
        config = configparser.ConfigParser(interpolation=None)  # Disable interpolation to handle % symbols
        
        # Debug: Show the exact config file path being used
        abs_config_path = os.path.abspath(self.config_path)
        logger.info(f"[CONFIG DEBUG] Loading config from: {self.config_path}")
        logger.info(f"[CONFIG DEBUG] Absolute path: {abs_config_path}")
        logger.info(f"[CONFIG DEBUG] File exists: {os.path.exists(abs_config_path)}")
        
        config.read(self.config_path)
        
        # Debug: Show all sections found in the config
        sections = config.sections()
        logger.info(f"[CONFIG DEBUG] All sections found: {sections}")
        
        # Debug: Check specifically for System section
        if 'System' in sections:
            logger.info(f"[CONFIG DEBUG] System section found with keys: {list(config['System'].keys())}")
            for key in config['System']:
                logger.info(f"[CONFIG DEBUG] System.{key} = {config['System'][key]}")
        else:
            logger.error(f"[CONFIG DEBUG] System section NOT found in config file!")
        
        return config
    
    def _get_optimizable_parameters(self):
        """Extract all optimizable parameters from config file.
        
        Returns dict with parameter names and their current values.
        """
        optimizable_params = {}
        
        logger.info(f"[TRACE_OPT] Starting _get_optimizable_parameters scan")
        logger.info(f"[TRACE_OPT] Config sections: {list(self.config.sections())}")
        
        for section_name in self.config.sections():
            if section_name == 'Lists':  # Skip lists section
                continue
                
            logger.info(f"[TRACE_OPT] Scanning section [{section_name}]")
            section = self.config[section_name]
            
            for param_name, param_value in section.items():
                logger.info(f"[TRACE_OPT] Found param {param_name} = {param_value}")
                
                # Check if parameter is ComplexN type (optimizable)
                if param_value.strip().startswith('(') and 'optimize=' in param_value:
                    logger.info(f"[TRACE_OPT] Processing ComplexN param: {param_name}")
                    try:
                        # Parse ComplexN parameter format
                        # Format: (optimize=True/False, default_value=value, min_value=min, max_value=max, increment=step)
                        param_str = param_value.strip()[1:-1]  # Remove outer parentheses
                        param_dict = {}
                        
                        for item in param_str.split(','):
                            key, value = item.split('=', 1)
                            key = key.strip()
                            value = value.strip()
                            
                            if key == 'optimize':
                                param_dict[key] = str(value).lower() == 'true'
                            else:
                                try:
                                    param_dict[key] = float(value)
                                except ValueError:
                                    param_dict[key] = value
                        
                        logger.info(f"[TRACE_OPT] Parsed {param_name}: {param_dict}")
                        
                        # Add to optimizable params if optimize=True or if we want all ComplexN
                        # Per Q1: Show ALL optimizable parameters (ComplexN type)
                        if 'default_value' in param_dict:
                            optimizable_params[f"{section_name}.{param_name}"] = param_dict['default_value']
                            logger.info(f"[TRACE_OPT] Added optimizable param: {section_name}.{param_name} = {param_dict['default_value']}")
                            
                    except Exception as e:
                        logger.warning(f"Could not parse parameter {param_name}: {param_value}, error: {e}")
                else:
                    logger.info(f"[TRACE_OPT] Skipping non-ComplexN param: {param_name}")
        
        logger.info(f"[TRACE_OPT] Found {len(optimizable_params)} optimizable parameters: {optimizable_params}")
        return optimizable_params
    
    def _load_data_files(self, data_dir=None):
        """Load the most recent data files from trace outputs."""
        data_dir = data_dir or V4_TRACE_OUTPUTS_DIR
        
        # Log current working directory and absolute path
        logger.info(f"[TRACE_PATH] Current working directory: {Path.cwd()}")
        logger.info(f"[TRACE_PATH] Looking for data in: {data_dir.absolute()}")
        logger.info(f"[TRACE_PATH] Directory exists: {data_dir.exists()}")
        
        if data_dir.exists():
            logger.info(f"[TRACE_PATH] Directory contents: {list(data_dir.glob('*'))}")

        # Find most recent files - try unified files first, then fallback to regular
        signal_files = (list(data_dir.glob("unified_signals_*.csv")) +
                       list(data_dir.glob("signals_output_*.csv")))
        allocation_files = (list(data_dir.glob("unified_allocation_history_*.csv")) +
                            list(data_dir.glob("allocation_history_*.csv")))
        trade_files = (list(data_dir.glob("unified_trade_log_*.csv")) +
                      list(data_dir.glob("trade_log_*.csv")))

        logger.info(f"[TRACE_PATH] Found signal files: {len(signal_files)} - {signal_files}")
        logger.info(f"[TRACE_PATH] Found allocation files: {len(allocation_files)} - {allocation_files}")
        logger.info(f"[TRACE_PATH] Found trade files: {len(trade_files)} - {trade_files}")

        if not signal_files or not allocation_files or not trade_files:
            raise FileNotFoundError(f"Required data files not found in {data_dir}. "
                                  f"Signal: {len(signal_files)}, Allocation: {len(allocation_files)}, "
                                  f"Trade: {len(trade_files)}")

        # Get most recent files
        latest_signal = max(signal_files, key=os.path.getctime)
        latest_allocation = max(allocation_files, key=os.path.getctime)
        latest_trade = max(trade_files, key=os.path.getctime)

        logger.info(f"Loading data files:")
        logger.info(f"  Signals: {latest_signal}")
        logger.info(f"  Allocations: {latest_allocation}")
        logger.info(f"  Trades: {latest_trade}")

        # Load data with proper error handling
        try:
            signals_df = pd.read_csv(latest_signal, index_col=0, parse_dates=True)
            logger.info(f"Loaded signals: {signals_df.shape} - {signals_df.index[0]} to {signals_df.index[-1]}")
        except Exception as e:
            logger.error(f"Error loading signals file: {e}")
            raise

        try:
            allocation_df = pd.read_csv(latest_allocation, index_col=0, parse_dates=True)
            logger.info(f"Loaded allocations: {allocation_df.shape} - {allocation_df.index[0]} to {allocation_df.index[-1]}")
        except Exception as e:
            logger.error(f"Error loading allocation file: {e}")
            raise

        try:
            # Trade log might not have date index
            trade_df = pd.read_csv(latest_trade)
            if 'Unnamed: 0' in trade_df.columns:
                trade_df = trade_df.drop('Unnamed: 0', axis=1)
            logger.info(f"Loaded trades: {trade_df.shape}")
        except Exception as e:
            logger.error(f"Error loading trade file: {e}")
            raise

        return signals_df, allocation_df, trade_df
    
    def _enhance_trade_log(self, trade_df):
        """Enhance trade log with commission+slippage column per Q6."""
        enhanced_df = trade_df.copy()
        
        # Calculate commission + slippage
        enhanced_df['commission+slippage'] = (
            abs(enhanced_df['total $s']) * (self.commission_rate + self.slippage_rate)
        )
        
        # Reorder columns to match recommended format
        column_order = ['date', 'ticker', 'action', 'quantity', 
                       'price per share executed', 'commission+slippage', 'total $s', 'pnl']
        
        # Only reorder if all columns exist
        existing_cols = [col for col in column_order if col in enhanced_df.columns]
        other_cols = [col for col in enhanced_df.columns if col not in column_order]
        
        enhanced_df = enhanced_df[existing_cols + other_cols]
        
        logger.info(f"Enhanced trade log with {len(enhanced_df)} trades")
        return enhanced_df
    
    def _load_price_data(self, data_dir=None):
        """Load the most recent price data file, preferring main data directory."""
        # First try to load from main data directory (most current)
        main_data_dir = DATA_DIR
        if main_data_dir.exists():
            excel_files = list(main_data_dir.glob("tickerdata_SPY_SHV_EFA_TLT_PFF_*.xlsx"))
            if excel_files:
                # Get most recent Excel file by modification time
                latest_excel = max(excel_files, key=lambda f: f.stat().st_mtime)
                logger.info(f"Loading price data from main data directory: {latest_excel}")

                try:
                    price_df = pd.read_excel(latest_excel, index_col=0, parse_dates=True)
                    if len(price_df) > 0:  # Check if file has data
                        logger.info(f"Loaded price data: {price_df.shape}, {price_df.index[0]} to {price_df.index[-1]}")
                        return price_df
                    else:
                        logger.warning(f"Excel file {latest_excel} is empty, falling back to CSV")
                except Exception as e:
                    logger.warning(f"Failed to load Excel price data from {latest_excel}: {e}, falling back to CSV")

        # Use trace outputs directory if data_dir not found
        data_dir = data_dir or V4_TRACE_OUTPUTS_DIR

        # Find most recent price data file
        price_files = list(data_dir.glob("price_data_*.csv"))
        if not price_files:
            # Try initial price data
            price_files = list(data_dir.glob("00_initial_price_data.csv"))

        if not price_files:
            raise FileNotFoundError("No price data files found in v4_trace_outputs or v4/data")

        latest_price = max(price_files, key=lambda f: f.stat().st_mtime)
        logger.info(f"Loading price data from trace outputs: {latest_price}")

        price_df = pd.read_csv(latest_price, index_col=0, parse_dates=True)
        logger.info(f"Loaded price data: {price_df.shape}, {price_df.index[0]} to {price_df.index[-1]}")
        return price_df

    def _calculate_equity_curves(self, allocation_df):
        """Calculate equity curves from real allocation history and price data."""
        # Load real price data
        price_df = self._load_price_data()

        # Get initial capital from config - ALLOW FALLBACKS FOR ESSENTIAL CONFIG
        initial_capital = float(self.config.get('Backtest', 'initial_capital', fallback=1000000))

        # Align dates between allocation and price data
        common_dates = allocation_df.index.intersection(price_df.index)
        if len(common_dates) == 0:
            raise ValueError("No common dates between allocation and price data")

        allocation_aligned = allocation_df.loc[common_dates]
        price_aligned = price_df.loc[common_dates]

        logger.info(f"Aligned data: {len(common_dates)} common dates")
        logger.info(f"Date range: {common_dates[0]} to {common_dates[-1]}")

        # Calculate daily portfolio values
        equity_curve = pd.Series(index=common_dates, dtype=float)

        # Get ticker columns (exclude Cash and Total)
        ticker_cols = [col for col in allocation_aligned.columns
                      if col in price_aligned.columns and col not in ['Cash', 'Total']]

        logger.info(f"Calculating equity curve for tickers: {ticker_cols}")

        # Initialize with starting capital
        equity_curve.iloc[0] = initial_capital

        # Calculate daily returns and portfolio values
        for i in range(1, len(equity_curve)):
            prev_date = common_dates[i-1]
            curr_date = common_dates[i]

            # Get previous day's allocations and current day's prices
            prev_allocations = allocation_aligned.loc[prev_date]
            prev_prices = price_aligned.loc[prev_date]
            curr_prices = price_aligned.loc[curr_date]

            # Calculate portfolio value based on price changes
            portfolio_value = 0.0

            # Handle cash component (earns risk-free rate)
            cash_allocation = prev_allocations.get('Cash', 0.0)
            if cash_allocation > 0:
                # Get risk-free rate from config (annual rate) - ALLOW FALLBACKS FOR ESSENTIAL CONFIG
                risk_free_rate_annual = float(self.config.get('Performance', 'risk_free_rate', fallback=0.02))
                # Convert to daily rate
                daily_risk_free_rate = risk_free_rate_annual / 252  # 252 trading days per year
                # Cash earns risk-free rate
                portfolio_value += equity_curve.iloc[i-1] * cash_allocation * (1 + daily_risk_free_rate)

            # Handle ticker allocations
            for ticker in ticker_cols:
                if ticker in prev_allocations and ticker in prev_prices and ticker in curr_prices:
                    allocation = prev_allocations[ticker]
                    if allocation > 0 and prev_prices[ticker] > 0:
                        # Calculate return for this ticker
                        ticker_return = (curr_prices[ticker] - prev_prices[ticker]) / prev_prices[ticker]
                        # Add to portfolio value
                        portfolio_value += equity_curve.iloc[i-1] * allocation * (1 + ticker_return)

            equity_curve.iloc[i] = portfolio_value

        logger.info(f"Calculated strategy equity curve: ${equity_curve.iloc[0]:,.2f} -3e ${equity_curve.iloc[-1]:,.2f}")

        # Store strategy equity curve for reference
        self._store_equity_curve(equity_curve, "strategy")

        return equity_curve
    
    def _calculate_performance_metrics(self, equity_curve):
        """Calculate performance metrics from equity curve."""
        if len(equity_curve) < 2:
            return {}
        
        # Calculate returns
        returns = equity_curve.pct_change().dropna()
        
        # Basic metrics
        total_return = (equity_curve.iloc[-1] / equity_curve.iloc[0]) - 1
        
        # Annualized metrics (assuming daily data)
        trading_days = 252
        years = len(equity_curve) / trading_days
        cagr = (equity_curve.iloc[-1] / equity_curve.iloc[0]) ** (1/years) - 1 if years > 0 else 0
        
        volatility = returns.std() * np.sqrt(trading_days)

        # Risk-free rate for calculations - ALLOW FALLBACKS FOR ESSENTIAL CONFIG
        risk_free_rate = float(self.config.get('Performance', 'risk_free_rate', fallback='0.02'))
        daily_rf_rate = risk_free_rate / trading_days

        # Sharpe ratio
        excess_returns = returns - daily_rf_rate
        sharpe = (excess_returns.mean() * trading_days) / volatility if volatility > 0 else 0

        # Sortino ratio (downside deviation)
        downside_returns = excess_returns[excess_returns < 0]
        downside_deviation = downside_returns.std() * np.sqrt(trading_days) if len(downside_returns) > 0 else 0
        sortino = (excess_returns.mean() * trading_days) / downside_deviation if downside_deviation > 0 else 0

        # Drawdown calculation
        rolling_max = equity_curve.expanding().max()
        drawdown = (equity_curve - rolling_max) / rolling_max
        max_drawdown = drawdown.min()

        # UPI (Ulcer Performance Index) calculation
        # UPI = Annualized Return / Ulcer Index
        # Ulcer Index = sqrt(mean(drawdown^2))
        ulcer_index = np.sqrt((drawdown ** 2).mean()) if len(drawdown) > 0 else 0
        upi = (cagr * 100) / (ulcer_index * 100) if ulcer_index > 0 else 0

        metrics = {
            'Total Return': total_return,
            'CAGR': cagr,
            'Volatility': volatility,
            'Sharpe Ratio': sharpe,
            'Sortino Ratio': sortino,
            'UPI': upi,
            'Max Drawdown': max_drawdown,
            'Final Value': equity_curve.iloc[-1],
            'Start Date': equity_curve.index[0].strftime('%Y-%m-%d') if hasattr(equity_curve.index[0], 'strftime') else str(equity_curve.index[0]),
            'End Date': equity_curve.index[-1].strftime('%Y-%m-%d') if hasattr(equity_curve.index[-1], 'strftime') else str(equity_curve.index[-1]),
            'Trading Days': len(equity_curve)
        }
        
        logger.info("Calculated performance metrics including Sortino and UPI")
        return metrics

    def _create_parameter_header(self, worksheet, optimizable_params):
        """Create parameter header in Cell A1 per Q1."""
        if not optimizable_params:
            return

        # Format parameters as simple "Parameter: Value" format per Q2
        param_lines = []
        for param_name, param_value in optimizable_params.items():
            # Clean up parameter name (remove section prefix for display)
            display_name = param_name.split('.')[-1] if '.' in param_name else param_name
            param_lines.append(f"{display_name}: {param_value}")

        header_text = "Parameters: " + " | ".join(param_lines)

        # Set header in A1
        worksheet['A1'] = header_text
        worksheet['A1'].font = Font(bold=True, size=12)
        worksheet['A1'].alignment = Alignment(wrap_text=True)

        # Merge cells if header is long
        if len(header_text) > 50:
            worksheet.merge_cells('A1:F1')

        logger.info(f"Created parameter header with {len(optimizable_params)} parameters")

    def _create_signal_history_tab(self, workbook, signals_df):
        """Create Signal History tab."""
        ws = workbook.create_sheet("Signal History")

        # Convert percentages to display format (0.6 -> 60.00%)
        display_df = signals_df * 100

        # Add data to worksheet
        for r in dataframe_to_rows(display_df, index=True, header=True):
            ws.append(r)

        # Format as percentages
        for row in ws.iter_rows(min_row=2, min_col=2):  # Skip header and date column
            for cell in row:
                if cell.value is not None:
                    cell.number_format = '0.00%'
                    cell.value = cell.value / 100  # Convert back for Excel percentage format

        # Format date column
        for cell in ws['A'][1:]:  # Skip header
            if cell.value:
                cell.number_format = 'YYYY-MM-DD'

        logger.info(f"Created Signal History tab with {len(signals_df)} rows")
        return ws

    def _create_allocation_history_tab(self, workbook, allocation_df):
        """Create Allocation History tab."""
        ws = workbook.create_sheet("Allocation History")

        # Convert percentages to display format
        display_df = allocation_df.copy()
        for col in display_df.columns:
            if col != 'Total':  # Don't modify Total column if it exists
                display_df[col] = display_df[col] * 100

        # Add data to worksheet
        for r in dataframe_to_rows(display_df, index=True, header=True):
            ws.append(r)

        # Format as percentages
        for row in ws.iter_rows(min_row=2, min_col=2):  # Skip header and date column
            for cell in row:
                if cell.value is not None:
                    cell.number_format = '0.00%'
                    cell.value = cell.value / 100  # Convert back for Excel percentage format

        # Format date column
        for cell in ws['A'][1:]:  # Skip header
            if cell.value:
                cell.number_format = 'YYYY-MM-DD'

        logger.info(f"Created Allocation History tab with {len(allocation_df)} rows")
        return ws

    def _create_trade_log_tab(self, workbook, trade_df):
        """Create Trade Log tab with enhanced format per Q6."""
        ws = workbook.create_sheet("Trade Log")

        # Add data to worksheet
        for r in dataframe_to_rows(trade_df, index=False, header=True):
            ws.append(r)

        # Format currency columns
        currency_cols = ['price per share executed', 'commission+slippage', 'total $s', 'pnl']
        for col_name in currency_cols:
            if col_name in trade_df.columns:
                col_idx = list(trade_df.columns).index(col_name) + 1
                col_letter = openpyxl.utils.get_column_letter(col_idx)
                for cell in ws[col_letter][1:]:  # Skip header
                    if cell.value is not None:
                        cell.number_format = '$#,##0.00'

        # Format date column
        if 'date' in trade_df.columns:
            date_col_idx = list(trade_df.columns).index('date') + 1
            date_col_letter = openpyxl.utils.get_column_letter(date_col_idx)
            for cell in ws[date_col_letter][1:]:  # Skip header
                if cell.value:
                    cell.number_format = 'YYYY-MM-DD'

        logger.info(f"Created Trade Log tab with {len(trade_df)} trades")
        return ws

    def _calculate_benchmark_equity_curve(self, allocation_df):
        """Calculate equal weight benchmark equity curve per Q4."""
        # Load price data
        price_df = self._load_price_data()

        # Get initial capital - ALLOW FALLBACKS FOR ESSENTIAL CONFIG
        initial_capital = float(self.config.get('Backtest', 'initial_capital', fallback=1000000))

        # Align dates
        common_dates = allocation_df.index.intersection(price_df.index)
        price_aligned = price_df.loc[common_dates]

        # Get ticker columns (exclude Cash)
        ticker_cols = [col for col in price_aligned.columns if col not in ['Cash', 'Total']]

        # Create equal weight allocations (1/n for each ticker)
        equal_weight = 1.0 / len(ticker_cols)
        logger.info(f"Benchmark equal weight per ticker: {equal_weight:.1%} across {len(ticker_cols)} tickers: {ticker_cols}")

        # Calculate benchmark equity curve
        benchmark_curve = pd.Series(index=common_dates, dtype=float)
        benchmark_curve.iloc[0] = initial_capital

        for i in range(1, len(benchmark_curve)):
            prev_date = common_dates[i-1]
            curr_date = common_dates[i]

            prev_prices = price_aligned.loc[prev_date]
            curr_prices = price_aligned.loc[curr_date]

            portfolio_value = 0.0
            for ticker in ticker_cols:
                if ticker in prev_prices and ticker in curr_prices and prev_prices[ticker] > 0:
                    ticker_return = (curr_prices[ticker] - prev_prices[ticker]) / prev_prices[ticker]
                    portfolio_value += benchmark_curve.iloc[i-1] * equal_weight * (1 + ticker_return)

            benchmark_curve.iloc[i] = portfolio_value

        logger.info(f"Calculated benchmark equity curve: ${benchmark_curve.iloc[0]:,.2f} -> ${benchmark_curve.iloc[-1]:,.2f}")

        # Store benchmark equity curve for reference
        self._store_equity_curve(benchmark_curve, "benchmark")

        return benchmark_curve

    def _store_equity_curve(self, equity_curve, curve_type):
        """Store equity curve to reporting directory for reference and inspection."""
        # Handle empty equity curves
        if len(equity_curve) == 0:
            logger.warning(f"Cannot store empty equity curve for {curve_type}")
            return None
            
        # Create reporting directory if it doesn't exist (using centralized path)
        reporting_dir = OUTPUT_DIR
        reporting_dir.mkdir(exist_ok=True)

        # Create filename - using timestamp for uniqueness but keeping simple reference
        filename = f"equity_curve_{curve_type}_{self.timestamp}.csv"
        filepath = get_reporting_file_path(filename)

        # Convert to DataFrame with proper formatting
        equity_df = pd.DataFrame({
            'Date': equity_curve.index,
            'Portfolio_Value': equity_curve.values,
            'Daily_Return': equity_curve.pct_change(),
            'Cumulative_Return': (equity_curve / equity_curve.iloc[0]) - 1
        })

        # Save to CSV - HARD STOP if csv_flag_use is False
        if self.csv_flag_use:
            equity_df.to_csv(filepath, index=False)
            logger.info(f"Stored {curve_type} equity curve to: {filepath}")

            # Also create a simple reference file without timestamp for easy access
            simple_filename = f"equity_curve_{curve_type}_latest.csv"
            simple_filepath = get_reporting_file_path(simple_filename)
            try:
                equity_df.to_csv(simple_filepath, index=False)
                logger.info(f"Created latest reference: {simple_filepath}")
            except PermissionError:
                logger.warning(f"Permission denied writing to {simple_filepath}, skipping latest reference file")
        else:
            logger.info(f"Skipped storing {curve_type} equity curve CSV (csv_flag_use = False)")

        return filepath

    def _create_settings_tab(self, workbook):
        """Create Settings tab with actual values used in backtest."""
        ws = workbook.create_sheet("Settings")

        # Title
        ws['A1'] = "Backtest Settings Used"
        ws['A1'].font = openpyxl.styles.Font(bold=True, size=14)

        # Get actual strategy parameters used
        strategy_params = self._get_strategy_parameters()

        # Create settings list with actual values used - USE SECTION-AGNOSTIC APPROACH
        from v4.settings.config_helper import ConfigHelper
        helper = ConfigHelper(self.config_path)

        settings = [
            ('Risk Free Rate', helper.getfloat('risk_free_rate', fallback=0.02)),
            ('Strategy Algorithm', 'ema'),  # Actual algorithm used
            ('Benchmark Strategy', 'equal_weight'),  # Actual benchmark used
            ('Tickers Used', 'SPY, SHV, EFA, TLT, PFF'),  # Actual tickers
            ('Start Date', helper.get('start_date', fallback='2020-01-01')),
            ('End Date', helper.get('end_date', fallback='2025-07-21')),
            ('Initial Capital', helper.getfloat('initial_capital', fallback=1000000.0)),
            ('Commission Rate', helper.getfloat('commission_rate', fallback=0.001)),
            ('Slippage Rate', float(self.config.get('Backtest', 'slippage_rate', fallback='0.0005'))),
            ('Deviation Threshold', float(self.config.get('Backtest', 'deviation_threshold', fallback='0.02'))),
            ('Short-term Lookback', strategy_params.get('st_lookback', 15)),
            ('Medium-term Lookback', strategy_params.get('mt_lookback', 70)),
            ('Long-term Lookback', strategy_params.get('lt_lookback', 100)),
            ('Execution Delay', strategy_params.get('execution_delay', 1)),
            ('Top N Assets', strategy_params.get('top_n', 2)),
            ('EMA Short Period', 12),
            ('EMA Long Period', 26),
            ('Min Weight', 0.0),
            ('Max Weight', 1.0)
        ]

        # Write settings to worksheet
        row = 3
        for setting_name, setting_value in settings:
            ws[f'A{row}'] = setting_name
            ws[f'B{row}'] = setting_value
            row += 1

        # Auto-adjust column widths
        ws.column_dimensions['A'].width = 25
        ws.column_dimensions['B'].width = 20

        logger.info("Created Settings tab with actual backtest values used")
        return ws

    def _get_strategy_parameters(self):
        """Extract strategy parameters in exact order required by reference layout."""
        # Extract specific parameters in exact order from reference
        strategy_params = {}

        # Get st_lookback, mt_lookback, lt_lookback from Strategy section
        if 'Strategy' in self.config:
            strategy_section = self.config['Strategy']

            # Parse ComplexN parameters
            for param_name in ['st_lookback', 'mt_lookback', 'lt_lookback']:
                if param_name in strategy_section:
                    param_value = strategy_section[param_name]
                    if param_value.strip().startswith('('):
                        # Parse ComplexN format
                        try:
                            param_str = param_value.strip()[1:-1]
                            param_dict = {}
                            for item in param_str.split(','):
                                key, value = item.split('=', 1)
                                key = key.strip()
                                value = value.strip()
                                if key == 'default_value':
                                    strategy_params[param_name] = int(float(value))
                                    break
                        except:
                            pass

            # Get execution_delay (SimpleN parameter)
            if 'execution_delay' in strategy_section:
                strategy_params['execution_delay'] = int(strategy_section['execution_delay'])

        # Get top_n from System section (system_top_n)
        if 'System' in self.config:
            system_section = self.config['System']
            if 'system_top_n' in system_section:
                param_value = system_section['system_top_n']
                if param_value.strip().startswith('('):
                    # Parse ComplexN format
                    try:
                        param_str = param_value.strip()[1:-1]
                        for item in param_str.split(','):
                            key, value = item.split('=', 1)
                            key = key.strip()
                            value = value.strip()
                            if key == 'default_value':
                                strategy_params['top_n'] = int(float(value))
                                break
                    except:
                        pass

        logger.info(f"Extracted strategy parameters: {strategy_params}")
        return strategy_params

    def get_optimization_combinations(self):
        """Generate all combinations of optimization parameters - PUBLIC METHOD."""
        import itertools

        # Check if optimization debugging is enabled
        # NO FALLBACKS - hard fail if System section is missing for proper tracing
    
        # Debug: Show config state right before System access
        logger.info(f"[SYSTEM DEBUG] About to access System section")
        logger.info(f"[SYSTEM DEBUG] Available sections: {self.config.sections()}")
        logger.info(f"[SYSTEM DEBUG] System section exists: {'System' in self.config.sections()}")
    
        debug_mode = self.config['System']['optimization_debug_mode'] 
        # Direct dictionary access ensures hard fail on missing section/key
        if isinstance(debug_mode, bool):
            optimization_debug = debug_mode
        else:
            optimization_debug = str(debug_mode).lower() == 'true'
        
        if optimization_debug:
            logger.info(f"[TRACE_OPT] Starting get_optimization_combinations")
        
        optimization_params = {}
        fixed_params = {}

        # Get parameters from Strategy section
        if 'Strategy' in self.config:
            if optimization_debug:
                logger.info(f"[TRACE_OPT] Processing Strategy section")
            strategy_section = self.config['Strategy']

            # Parse ComplexN parameters
            for param_name in ['st_lookback', 'mt_lookback', 'lt_lookback']:
                if param_name in strategy_section:
                    param_value = strategy_section[param_name]
                    if optimization_debug:
                        logger.info(f"[TRACE_OPT] Found {param_name} = {param_value}")
                    
                    if param_value.strip().startswith('('):
                        # Parse ComplexN format
                        try:
                            param_str = param_value.strip()[1:-1]
                            param_dict = {}
                            for item in param_str.split(','):
                                key, value = item.split('=', 1)
                                key = key.strip()
                                value = value.strip()
                                param_dict[key] = value

                            if optimization_debug:
                                logger.info(f"[TRACE_OPT] Parsed {param_name}: {param_dict}")
                            
                            # Check if this parameter should be optimized
                            if str(param_dict.get('optimize', 'False')).lower() == 'true':
                                # Generate optimization range
                                min_val = int(float(param_dict.get('min_value', 0)))
                                max_val = int(float(param_dict.get('max_value', 100)))
                                increment = int(float(param_dict.get('increment', 1)))

                                values = list(range(min_val, max_val + 1, increment))
                                optimization_params[param_name] = values
                                if optimization_debug:
                                    logger.info(f"[TRACE_OPT] Added optimization param {param_name}: {values}")
                            else:
                                # Fixed parameter
                                fixed_params[param_name] = int(float(param_dict.get('default_value', 0)))
                                if optimization_debug:
                                    logger.info(f"[TRACE_OPT] Added fixed param {param_name}: {fixed_params[param_name]}")
                        except Exception as e:
                            logger.error(f"[TRACE_OPT] Error parsing {param_name}: {e}")
                            fixed_params[param_name] = 0

            # Get execution_delay (SimpleN parameter)
            if 'execution_delay' in strategy_section:
                fixed_params['execution_delay'] = int(strategy_section['execution_delay'])
                if optimization_debug:
                    logger.info(f"[TRACE_OPT] Added execution_delay: {fixed_params['execution_delay']}")

        # Get parameters from System section - scan ALL ComplexN parameters
        if 'System' in self.config:
            logger.info(f"[TRACE_OPT] Processing System section")
            system_section = self.config['System']
            
            # Scan all parameters in System section for ComplexN type
            for param_name, param_value in system_section.items():
                logger.info(f"[TRACE_OPT] Found system param {param_name} = {param_value}")
                
                if param_value.strip().startswith('(') and 'optimize=' in param_value:
                    logger.info(f"[TRACE_OPT] Processing ComplexN system param: {param_name}")
                    try:
                        param_str = param_value.strip()[1:-1]
                        param_dict = {}
                        for item in param_str.split(','):
                            key, value = item.split('=', 1)
                            key = key.strip()
                            value = value.strip()
                            param_dict[key] = value
                        
                        logger.info(f"[TRACE_OPT] Parsed system {param_name}: {param_dict}")
                        
                        # Check if this parameter should be optimized
                        if str(param_dict.get('optimize', 'False')).lower() == 'true':
                            # Generate optimization range
                            min_val = int(float(param_dict.get('min_value', 0)))
                            max_val = int(float(param_dict.get('max_value', 100)))
                            increment = int(float(param_dict.get('increment', 1)))
                            
                            values = list(range(min_val, max_val + 1, increment))
                            optimization_params[param_name] = values
                            logger.info(f"[TRACE_OPT] Added optimization param {param_name}: {values}")
                        else:
                            # Fixed parameter - use appropriate mapping
                            param_value = int(float(param_dict.get('default_value', 0)))
                            if param_name == 'system_top_n':
                                fixed_params['top_n'] = param_value
                                logger.info(f"[TRACE_OPT] Added fixed param top_n: {param_value}")
                            else:
                                fixed_params[param_name] = param_value
                                logger.info(f"[TRACE_OPT] Added fixed param {param_name}: {param_value}")
                    except Exception as e:
                        logger.error(f"[TRACE_OPT] Error parsing system {param_name}: {e}")
                        if param_name == 'system_top_n':
                            fixed_params['top_n'] = 2
                        else:
                            fixed_params[param_name] = 0
        else:
            logger.info(f"[TRACE_OPT] No System section found")

        logger.info(f"[TRACE_OPT] Final optimization_params: {optimization_params}")
        logger.info(f"[TRACE_OPT] Final fixed_params: {fixed_params}")
        
        # Generate all combinations
        if optimization_params:
            param_names = list(optimization_params.keys())
            param_values = list(optimization_params.values())
            combinations = list(itertools.product(*param_values))

            logger.info(f"[TRACE_OPT] Generated {len(combinations)} combinations from {len(optimization_params)} optimizable params")
            
            result = []
            for combo in combinations:
                param_set = fixed_params.copy()
                for i, param_name in enumerate(param_names):
                    param_set[param_name] = combo[i]
                result.append(param_set)
                
            # Log first few combinations for verification
            for i, combo in enumerate(result[:3]):
                logger.info(f"[TRACE_OPT] Combination {i}: {combo}")
                
            # STEP 1: Parameter Validation
            if self.validation_mode:
                self._log_validation_step(1, "Parameter Extraction")
                print(f"[VALIDATION] Found {len(result)} combinations from {len(optimization_params)} variables")
                
                # Save combinations to results file with timestamp
                validation_result = {
                    "step": 1,
                    "status": "SUCCESS",
                    "timestamp": datetime.now().strftime("%Y-%m-%dT%H:%M:%SZ"),
                    "total_combinations": len(result),
                    "parameters_found": list(optimization_params.keys()),
                    "combinations": result[:5],  # First 5 for brevity
                    "error": None
                }
                
                # Save to JSON file (flat structure with prefix)
                results_file = self.validation_dir / "step01__parameters.json"
                with open(results_file, "w") as f:
                    json.dump(validation_result, f, indent=2)
                    
                # Create human-readable log (flat structure)
                log_file = self.validation_dir / "step01__parameters.log"
                with open(log_file, "w") as f:
                    f.write(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] [VALIDATION] STEP 1: Parameter Extraction\n")
                    f.write(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] [SUCCESS] Found {len(result)} combinations from {len(optimization_params)} variables\n")
                    f.write(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] [INFO] Parameters: {', '.join(optimization_params.keys())}\n")
                    
                # Update status file (flat structure)
                with open(self.validation_dir / "status__current_step.txt", "w") as f:
                    f.write("STEP_1_OF_10_COMPLETED")

            return result
        else:
            # No optimization parameters, return single combination
            logger.info(f"[TRACE_OPT] No optimization parameters found, returning single fixed combination: {fixed_params}")
            return [fixed_params]

    def _validate_single_combination(self, combination):
        """Minimal validation - run single real backtest for validation according to stepmappingopt.md."""
        if not self.validation_mode:
            return True

        try:
            # STEP 3: Single Combination Test
            self._log_validation_step(3, "Single Combination Test", "RUNNING")

            # Check if optimization is active to determine validation approach
            optimization_active = self.config.getboolean('System', 'optimization_active', fallback=False)

            if optimization_active:
                # OPTIMIZATION MODE: Run subprocess for this specific combination
                equity_curve = self._run_pipeline_for_combination(combination)
            else:
                # SINGLE MODE: Use existing pipeline results (no subprocess needed)
                logger.info("[TRACE] Single mode validation - using existing pipeline results")
                equity_curve = self._load_existing_equity_curve(combination)
            
            if equity_curve is not None and len(equity_curve) > 0:
                # Create validation artifacts
                validation_result = {
                    "step": 3,
                    "status": "SUCCESS",
                    "timestamp": datetime.now().strftime("%Y-%m-%dT%H:%M:%SZ"),
                    "combination": combination,
                    "equity_curve_length": len(equity_curve),
                    "first_value": float(equity_curve.iloc[0]) if len(equity_curve) > 0 else None,
                    "last_value": float(equity_curve.iloc[-1]) if len(equity_curve) > 0 else None,
                    "mean_value": float(equity_curve.mean()) if len(equity_curve) > 0 else None,
                    "return_code": 0,
                    "error": None
                }
                
                # Save to JSON file (flat structure)
                results_file = self.validation_dir / "step03__single_result.json"
                with open(results_file, "w") as f:
                    json.dump(validation_result, f, indent=2)
                
                # Log to file (flat structure)
                log_file = self.validation_dir / "step03__single.log"
                with open(log_file, "w") as f:
                    f.write(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] [VALIDATION] STEP 3: Single Combination Test\n")
                    f.write(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] [SUCCESS] Single backtest completed successfully\n")
                    f.write(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] [INFO] Equity curve length: {len(equity_curve)}\n")
                    f.write(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] [INFO] First value: {validation_result['first_value']}\n")
                    f.write(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] [INFO] Last value: {validation_result['last_value']}\n")
                
                # Update status
                self._log_validation_step(3, "Single Combination Test", "COMPLETED")
                return True
            else:
                error_msg = f"Single backtest failed - no equity curve returned for combination: {combination}"
                self._log_validation_step(3, "Single Combination Test", "FAILED", error_msg)
                return False
                
        except Exception as e:
            error_msg = f"Single backtest failed with exception: {str(e)}"
            self._log_validation_step(3, "Single Combination Test", "FAILED", error_msg)
            return False

    def _backup_settings(self):
        """Backup current CPS_v4 settings file."""
        settings_file = V4_SETTINGS_FILE
        backup_file = settings_file.with_suffix('.ini.backup')
        shutil.copy2(settings_file, backup_file)
        return backup_file

    def _restore_settings(self, backup_file):
        """Restore CPS_v4 settings from backup."""
        settings_file = V4_SETTINGS_FILE
        shutil.copy2(backup_file, settings_file)
        backup_file.unlink()  # Delete backup file

    def _create_temp_settings_for_combination(self, combination):
        """Create a temporary settings file for this optimization combination."""
        import tempfile
        import shutil
        import re
        import time

        # Create a temporary settings file with proper cleanup
        original_settings = V4_SETTINGS_FILE
        
        # Verify the original settings file exists
        if not original_settings.exists():
            error_msg = f"Original settings file not found: {original_settings}"
            logger.error(error_msg)
            raise FileNotFoundError(error_msg)
        
        # Create temp file but close the file descriptor immediately
        temp_fd, temp_path = tempfile.mkstemp(suffix='.ini', prefix='temp_settings_')
        os.close(temp_fd)  # Close immediately to avoid file locks

        try:
            # Copy the original file
            shutil.copy2(original_settings, temp_path)

            # Read the temp file content
            with open(temp_path, 'r') as f:
                content = f.read()

            # Log for debugging
            logger.info(f"Creating temp settings for combination: {combination}")

            # CRITICAL FIX: Replace ComplexN parameters with simple values for subprocess execution
            # The pipeline code expects simple integer values, not ComplexN format

            if 'st_lookback' in combination:
                # Replace entire ComplexN parameter with simple value
                pattern = r'st_lookback\s*=\s*\([^)]*\)'
                replacement = f'st_lookback = {combination["st_lookback"]}'
                content = re.sub(pattern, replacement, content)

            if 'mt_lookback' in combination:
                # Replace entire ComplexN parameter with simple value
                pattern = r'mt_lookback\s*=\s*\([^)]*\)'
                replacement = f'mt_lookback = {combination["mt_lookback"]}'
                content = re.sub(pattern, replacement, content)

            if 'lt_lookback' in combination:
                # Replace entire ComplexN parameter with simple value
                pattern = r'lt_lookback\s*=\s*\([^)]*\)'
                replacement = f'lt_lookback = {combination["lt_lookback"]}'
                content = re.sub(pattern, replacement, content)

            if 'execution_delay' in combination:
                # Find and replace execution_delay (simple value)
                pattern = r'(execution_delay\s*=\s*)\d+'
                replacement = f'\g<1>{combination["execution_delay"]}'
                content = re.sub(pattern, replacement, content)

            if 'top_n' in combination:
                # Replace entire ComplexN parameter with simple value
                pattern = r'system_top_n\s*=\s*\([^)]*\)'
                replacement = f'system_top_n = {combination["top_n"]}'
                content = re.sub(pattern, replacement, content)

            if 'system_lookback' in combination:
                # Replace entire ComplexN parameter with simple value
                pattern = r'system_lookback\s*=\s*\([^)]*\)'
                replacement = f'system_lookback = {combination["system_lookback"]}'
                content = re.sub(pattern, replacement, content)

            # CRITICAL FIX: Set csv_flag_use = False for optimization runs
            # This ensures CSV generation is properly suppressed during optimization
            pattern = r'(csv_flag_use\s*=\s*)True'
            replacement = r'\g<1>False'
            content = re.sub(pattern, replacement, content)
            logger.info("Set csv_flag_use = False in temporary settings file")

            # Write the modified content back
            with open(temp_path, 'w') as f:
                f.write(content)
                
            # Track updated parameters for validation
            updated_params = {k: combination.get(k) for k in combination.keys()}
            
            # STEP 2: Settings File Validation
            if getattr(self, 'validation_mode', False):
                self._log_validation_step(2, "Settings File Creation")
                print(f"[VALIDATION] Created temporary settings file with {len(updated_params)} parameters")
                
                # Create validation artifacts
                validation_result = {
                    "step": 2,
                    "status": "SUCCESS",
                    "timestamp": datetime.now().strftime("%Y-%m-%dT%H:%M:%SZ"),
                    "settings_file": str(temp_path),
                    "parameters_modified": updated_params,
                    "error": None
                }
                
                # Save to JSON file (flat structure with prefix)
                results_file = self.validation_dir / "step02__settings.json"
                with open(results_file, "w") as f:
                    json.dump(validation_result, f, indent=2)
                    
                # Also save a copy of the settings file for validation (flat structure)
                settings_copy = self.validation_dir / f"step02__settings_combination_{self.timestamp}.ini"
                shutil.copy(temp_path, settings_copy)
                
                # Log to file (flat structure)
                log_file = self.validation_dir / "step02__settings.log"
                with open(log_file, "w") as f:
                    f.write(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] [VALIDATION] STEP 2: Settings File Creation\n")
                    f.write(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] [SUCCESS] Created settings file at {temp_path}\n")
                    f.write(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] [INFO] Modified parameters: {updated_params}\n")
                    f.write(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] [INFO] Settings copy saved to: {settings_copy}\n")
                    
                # Update status file (flat structure)
                with open(self.validation_dir / "status__current_step.txt", "w") as f:
                    f.write("STEP_2_OF_10_COMPLETED")

            return temp_path

        except Exception as e:
            # Clean up on error
            try:
                if os.path.exists(temp_path):
                    os.unlink(temp_path)
            except:
                pass
            raise e

    def _run_pipeline_for_combination(self, combination):
        """Run unified pipeline for a specific parameter combination and return equity curve (silent)."""
        temp_settings_path = None
        try:
            # Create unique identifier for this combination using the new method
            combo_id = self.generate_combo_id(combination)

            # Store combination mapping for later file retrieval
            self.combination_file_mapping[combo_id] = combination

            # Create temporary settings file for this combination
            temp_settings_path = self._create_temp_settings_for_combination(combination)
            
            # Validate the temp settings file was created successfully
            if not temp_settings_path or not os.path.exists(temp_settings_path):
                logger.error(f"Failed to create temp settings file for combination: {combination}")
                return None

            # Run unified pipeline with custom settings file - capture all output for debugging
            cmd = ["python", "v4/run_unified_pipeline.py", "--settings", temp_settings_path]
            logger.info(f"[TRACE] Running command: {' '.join(cmd)} (cwd=current)")

            # Set environment variables to pass optimization settings to subprocess
            env = os.environ.copy()
            env['CPS_V4_OPTIMIZATION_ACTIVE'] = 'true'  # Flag to indicate optimization mode
            env['CPS_V4_OPTIMIZATION_SETTINGS_FILE'] = temp_settings_path
            env['CPS_V4_COMBO_ID'] = combo_id  # Pass combination ID to pipeline

            # Set shared log file for all optimization subprocess calls
            if not hasattr(self, '_shared_log_file'):
                from v4.utils.tracing_utils import setup_trace_directory
                trace_dir = setup_trace_directory()
                self._shared_log_file = trace_dir / f"unified_pipeline_optimization_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
            env['CPS_V4_SHARED_LOG_FILE'] = str(self._shared_log_file)

            # Add current directory to Python path so subprocess can find v4.config module
            current_dir = str(PROJECT_ROOT)
            if 'PYTHONPATH' in env:
                env['PYTHONPATH'] = f"{current_dir};{env['PYTHONPATH']}"
            else:
                env['PYTHONPATH'] = current_dir

            # Use current working directory for consistency
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=600, env=env, cwd=current_dir)

            if result.returncode != 0:
                # Log error details to identify the problem
                logger.error(f"Subprocess failed for combination {combination}: return code {result.returncode}")
                logger.error(f"Command: {' '.join(cmd)}")
                logger.error(f"STDOUT:\n{result.stdout}")
                logger.error(f"STDERR:\n{result.stderr}")
                
                # Also log the temp settings content for debugging
                try:
                    with open(temp_settings_path, 'r') as f:
                        settings_content = f.read()
                    logger.error(f"Temp settings file content:\n{settings_content[:1000]}...")  # First 1000 chars
                except Exception as read_e:
                    logger.error(f"Could not read temp settings file: {read_e}")
                
                raise RuntimeError(f"Pipeline failed for combo {combination} with error code {result.returncode}")
            
            # Log successful pipeline execution
            logger.info(f"[TRACE] Pipeline completed successfully for combo {combo_id}")

            # Load equity curve from the unified portfolio file - NEW APPROACH
            equity_curve = self._load_unified_portfolio_for_combination(combo_id, self.timestamp)

            if equity_curve is not None:
                logger.info(f"[TRACE] Successfully loaded unified portfolio for combo {combo_id}")
                return equity_curve
            else:
                error_msg = f"Failed to load unified portfolio file for combo: {combination}"
                logger.error(f"[TRACE] {error_msg}")
                raise FileNotFoundError(error_msg)

        except Exception as e:
            logger.error(f"[TRACE] Exception in _run_pipeline_for_combination: {e}")
            raise RuntimeError(f"Pipeline execution failed for combination {combination}") from e
        finally:
            # Clean up temporary settings file
            if temp_settings_path:
                self._cleanup_temp_settings(temp_settings_path)

    def _get_parameter_value(self, section, parameter):
        """Extract the actual value from a parameter, handling both simple and ComplexN formats.

        NOTE: This method is deprecated - use section-agnostic ConfigHelper instead.
        Kept for backward compatibility only.
        """
        # Use section-agnostic approach
        from v4.settings.config_helper import ConfigHelper
        helper = ConfigHelper(self.config_path)

        try:
            # First try to get as simple integer
            return helper.getint(parameter)
        except ValueError:
            # If that fails, it might be a ComplexN parameter - extract default_value
            raw_value = helper.get(parameter)
            if raw_value and raw_value.startswith('(') and 'default_value=' in raw_value:
                # Parse ComplexN format: (optimize=True, default_value=15, ...)
                import re
                match = re.search(r'default_value=(\d+)', raw_value)
                if match:
                    return int(match.group(1))
            # If all else fails, raise the original error
            raise ValueError(f"Cannot parse parameter {parameter} = {raw_value}")

    def _cleanup_temp_settings(self, temp_settings_path):
        """Clean up temporary settings file with proper Windows file lock handling."""
        import time
        import gc
        
        if not temp_settings_path or not os.path.exists(temp_settings_path):
            return
            
        # Force garbage collection to release any file handles
        gc.collect()
        
        # Try multiple times with delay to handle Windows file locks
        max_attempts = 5
        for attempt in range(max_attempts):
            try:
                # Close any open file handles in this process
                if hasattr(os, 'sync'):
                    os.sync()
                    
                os.unlink(temp_settings_path)
                logger.info(f"Successfully cleaned up temporary settings file: {temp_settings_path}")
                return
            except (OSError, PermissionError, FileNotFoundError) as e:
                if attempt < max_attempts - 1:
                    # Exponential backoff with jitter
                    sleep_time = (0.1 * (2 ** attempt)) + (0.01 * attempt)
                    time.sleep(sleep_time)
                    continue
                else:
                    # Final attempt failed - just log warning, don't break the flow
                    logger.warning(f"Failed to clean up temp settings file: {temp_settings_path} after {max_attempts} attempts")
                    logger.warning(f"Error: {e}")
                    logger.warning("Temp file will be cleaned up automatically when Python exits")
                    return

    def _load_existing_equity_curve(self, combination):
        """Load equity curve from existing pipeline results (single mode)."""
        try:
            # In single mode, the pipeline already ran and created equity curve files
            # Look for the most recent equity curve file in the reporting directory
            reporting_dir = self.config.get('Paths', 'reporting_directory', fallback='v4_reporting_outputs')

            # Look for equity curve files (CSV format)
            equity_files = []
            if os.path.exists(reporting_dir):
                for file in os.listdir(reporting_dir):
                    if 'equity_curve' in file.lower() and file.endswith('.csv'):
                        equity_files.append(os.path.join(reporting_dir, file))

            if not equity_files:
                # Try alternative location - v4_trace_outputs
                trace_dir = 'v4_trace_outputs'
                if os.path.exists(trace_dir):
                    for file in os.listdir(trace_dir):
                        if 'allocation_history' in file.lower() and file.endswith('.csv'):
                            equity_files.append(os.path.join(trace_dir, file))

            if not equity_files:
                error_msg = f"No equity curve files found in {reporting_dir} or v4_trace_outputs for single mode"
                logger.critical(f"[TRACE] {error_msg}")
                raise ValueError(error_msg)

            # Use the most recent file
            latest_file = max(equity_files, key=os.path.getmtime)
            logger.info(f"[TRACE] Loading existing equity curve from: {latest_file}")

            # Load the equity curve
            df = pd.read_csv(latest_file)

            # Look for Portfolio_Value or Total column (different files have different column names)
            if 'Portfolio_Value' in df.columns:
                equity_curve = df['Portfolio_Value'].dropna()
                logger.info(f"[TRACE] Loaded equity curve from Portfolio_Value column with {len(equity_curve)} data points, final value: {equity_curve.iloc[-1] if len(equity_curve) > 0 else 'EMPTY'}")
                return equity_curve
            elif 'Total' in df.columns:
                equity_curve = df['Total'].dropna()
                logger.info(f"[TRACE] Loaded equity curve from Total column with {len(equity_curve)} data points, final value: {equity_curve.iloc[-1] if len(equity_curve) > 0 else 'EMPTY'}")
                return equity_curve
            else:
                error_msg = f"Neither Portfolio_Value nor Total column found in {latest_file}. Available columns: {list(df.columns)}"
                logger.critical(f"[TRACE] {error_msg}")
                raise ValueError(error_msg)

        except Exception as e:
            error_msg = f"Failed to load existing equity curve for combination {combination}: {str(e)}"
            logger.critical(f"[TRACE] {error_msg}")
            raise ValueError(error_msg)

    def _run_matrix_optimization(self, combinations):
        """Run matrix-based optimization using EquityCurvesManager for robust data handling."""
        import time
        from .equity_curves_manager import EquityCurvesManager
        
        start_time = time.time()

        # INSTRUMENTATION: Entry logging
        logger.info(f"[TRACE] _run_matrix_optimization ENTRY - combinations count: {len(combinations)}")
        logger.info(f"[TRACE] Parameter grid size: {len(combinations)} combinations")
        for i, combo in enumerate(combinations[:3]):  # Log first 3 for verification
            logger.info(f"[TRACE] Sample combo {i}: {combo}")
        
        # MILESTONE: Start optimization
        logger.info(f"[OPTIMIZATION] Starting {len(combinations)} combinations...")

        # Initialize EquityCurvesManager
        equity_manager = EquityCurvesManager(output_dir=str(OUTPUT_DIR))

        # Get date range from base case allocation data or price data
        if hasattr(self, 'allocation_df') and self.allocation_df is not None:
            date_index = self.allocation_df.index
        else:
            # Fallback to price data date range for validation tests
            price_df = self._load_price_data()
            date_index = price_df.index
        equity_manager.initialize_with_date_index(date_index)

        # Progress tracking
        completed = 0
        failed = 0
        progress_interval = max(1, len(combinations) // 10)  # Report every 10%

        # Process each combination
        for i, combo in enumerate(combinations):
            try:
                # INSTRUMENTATION: Strategy calculation entry
                logger.info(f"[TRACE] Starting strategy calculation for combo {i}: {combo}")
                
                # STEP 4: Equity Curve Validation
                if self.validation_mode:
                    self._log_validation_step(4, "Equity Curve Processing")
                    print(f"[VALIDATION] Processing combination {i+1}/{len(combinations)}")
                
                # Run pipeline for this combination (silent)
                equity_curve = self._run_pipeline_for_combination(combo)

                if equity_curve is not None:
                    # INSTRUMENTATION: Check numeric values immediately after strategy calc
                    logger.info(f"[TRACE] Strategy calc result - equity curve length: {len(equity_curve)}")
                    logger.info(f"[TRACE] Strategy calc result - first value: {equity_curve.iloc[0] if len(equity_curve) > 0 else 'EMPTY'}")
                    logger.info(f"[TRACE] Strategy calc result - last value: {equity_curve.iloc[-1] if len(equity_curve) > 0 else 'EMPTY'}")
                    logger.info(f"[TRACE] Strategy calc result - mean value: {equity_curve.mean() if len(equity_curve) > 0 else 'EMPTY'}")
                    logger.info(f"[TRACE] Strategy calc result - any zeros?: {(equity_curve == 0).any() if len(equity_curve) > 0 else 'EMPTY'}")
                    
                    # Add to equity curves manager
                    column_name = equity_manager.add_combination_result(combo, equity_curve)
                    completed += 1
                    
                    logger.info(f"[TRACE] Successfully stored combo {i} as {column_name}")
                    
                    # STEP 4: Additional Equity Curve Validation
                    if self.validation_mode:
                        # Save equity curve data for validation
                        validation_result = {
                            "step": 4,
                            "status": "SUCCESS",
                            "timestamp": datetime.now().strftime("%Y-%m-%dT%H:%M:%SZ"),
                            "combination_index": i,
                            "combination_params": combo,
                            "equity_curve_length": len(equity_curve),
                            "first_value": float(equity_curve.iloc[0]) if len(equity_curve) > 0 else None,
                            "last_value": float(equity_curve.iloc[-1]) if len(equity_curve) > 0 else None,
                            "mean_value": float(equity_curve.mean()) if len(equity_curve) > 0 else None,
                            "has_zeros": bool((equity_curve == 0).any()) if len(equity_curve) > 0 else False,
                            "error": None
                        }
                        
                        # Save to CSV file - HARD STOP if csv_flag_use is False
                        if self.csv_flag_use:
                            results_file = self.validation_dir / "step04__equity_curves.csv"
                            df_row = pd.DataFrame([{
                                "combination_index": i,
                                "equity_curve_length": len(equity_curve),
                                "first_value": validation_result["first_value"],
                                "last_value": validation_result["last_value"],
                                "mean_value": validation_result["mean_value"],
                                "has_zeros": validation_result["has_zeros"]
                            }])

                            # Append to existing file or create new
                            if results_file.exists():
                                df_row.to_csv(results_file, mode='a', header=False, index=False)
                            else:
                                df_row.to_csv(results_file, index=False)
                        else:
                            logger.info(f"Skipped validation CSV save for combo {i} (CSV generation disabled during optimization)")
                        
                        # Log to file (flat structure)
                        log_file = self.validation_dir / "step04__equity_curves.log"
                        with open(log_file, "a") as f:
                            f.write(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] [VALIDATION] Combination {i+1}/{len(combinations)} processed successfully\n")
                            f.write(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] [INFO] Equity curve length: {len(equity_curve)}\n")
                            f.write(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] [INFO] First value: {validation_result['first_value']}\n")
                            f.write(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] [INFO] Last value: {validation_result['last_value']}\n")
                else:
                    error_msg = f"Strategy calculation returned None for combo {i}: {combo}"
                    logger.error(f"[TRACE] {error_msg}")
                    raise RuntimeError(error_msg)

            except Exception as e:
                error_msg = f"Exception in strategy calculation for combo {i}: {e}"
                logger.error(f"[TRACE] {error_msg}")
                raise RuntimeError(error_msg) from e

            # MILESTONE: Progress reporting and periodic saves
            if (i + 1) % progress_interval == 0 or (i + 1) == len(combinations):
                elapsed = time.time() - start_time
                pct = ((i + 1) / len(combinations)) * 100

                if i + 1 < len(combinations):
                    eta_seconds = (elapsed / (i + 1)) * (len(combinations) - (i + 1))
                    eta = f"{eta_seconds/60:.0f}min" if eta_seconds > 60 else f"{eta_seconds:.0f}s"
                    logger.info(f"[PROGRESS] {i+1}/{len(combinations)} ({pct:.0f}%) | Success: {completed} | Failed: {failed} | ETA: {eta}")
                else:
                    duration = f"{elapsed/60:.0f}min" if elapsed > 60 else f"{elapsed:.0f}s"
                    logger.info(f"[PROGRESS] {i+1}/{len(combinations)} (100%) | Success: {completed} | Failed: {failed} | Duration: {duration}")
                
                # Save progress
                equity_manager.save_to_disk()
                logger.info(f"Progress saved after {i+1} combinations")

        # MILESTONE: Final save and create summary
        equity_file, metadata_file = equity_manager.save_to_disk()
        
        # Create performance summary
        summary_df = equity_manager.create_performance_summary()
        if not summary_df.empty:
            summary_file = get_reporting_file_path(f"optimization_performance_summary_{equity_manager.timestamp}.csv")
            summary_df.to_csv(summary_file, index=False)
            logger.info(f"Performance summary saved: {summary_file}")
        
        # Convert back to legacy format for compatibility (optional)
        equity_matrix = equity_manager.equity_curves_df.copy()
        combination_metadata = {}
        
        # Create legacy metadata format
        for column_name, metadata in equity_manager.combination_metadata.items():
            if 'parameters' in metadata:
                combination_metadata[column_name] = metadata['parameters']

        # MILESTONE: Complete
        matrix_size = f"{len(combinations)} combinations × {len(date_index)} days"
        logger.info(f"[MATRIX] Saved equity matrix: {matrix_size} → {equity_file}")
        
        # INSTRUMENTATION: Exit logging
        logger.info(f"[TRACE] _run_matrix_optimization EXIT - completed: {completed}, failed: {failed}")
        logger.info(f"[TRACE] Final matrix shape: {equity_matrix.shape}")
        logger.info(f"[TRACE] Equity file saved to: {equity_file}")
        logger.info(f"[TRACE] Metadata file saved to: {metadata_file}")

        return equity_matrix, combination_metadata

    def _run_backtest_for_combination(self, param_combo, save_csv=False):
        """Run real optimization using validated unified pipeline process - NO FALLBACKS."""
        logger.info(f"Running pipeline optimization for combination: {param_combo}")

        # Run pipeline and get equity curve
        equity_curve = self._run_pipeline_for_combination(param_combo)

        if equity_curve is None:
            raise RuntimeError(f"Pipeline failed to generate equity curve for combination: {param_combo}")

        # Calculate performance metrics
        metrics = self._calculate_performance_metrics(equity_curve)
        annual_returns = self._calculate_annual_returns(equity_curve)

        # Only save CSV for base case (when save_csv=True)
        if save_csv:
            self._store_equity_curve(equity_curve, 'strategy')

        logger.info(f"Pipeline optimization completed. CAGR: {metrics.get('CAGR', 0):.2%}")
        return equity_curve, metrics, annual_returns

    def _convert_signals_to_allocations(self, signal_results, date_index):
        """Convert signal generation results to allocation DataFrame format - NO FALLBACKS."""
        # signal_results can be either a DataFrame or dict
        if isinstance(signal_results, pd.DataFrame):
            allocation_df = signal_results.copy()
        elif isinstance(signal_results, dict):
            # Convert dict to DataFrame - dict has date keys and allocation values
            allocation_data = []
            for date, allocations in signal_results.items():
                if isinstance(allocations, dict):
                    row = {'Date': date}
                    row.update(allocations)
                    allocation_data.append(row)
                elif isinstance(allocations, pd.Series):
                    row = {'Date': date}
                    row.update(allocations.to_dict())
                    allocation_data.append(row)

            if not allocation_data:
                raise ValueError("No valid allocation data found in signal_results dict")
                
            allocation_df = pd.DataFrame(allocation_data)
            allocation_df['Date'] = pd.to_datetime(allocation_df['Date'])
            allocation_df = allocation_df.set_index('Date')
        else:
            raise TypeError(f"Unexpected signal_results format: {type(signal_results)}")

        # Ensure all tickers are present with 0.0 default
        tickers = ['SPY', 'SHV', 'EFA', 'TLT', 'PFF']
        for ticker in tickers:
            if ticker not in allocation_df.columns:
                allocation_df[ticker] = 0.0

        # Add Cash column (remaining allocation)
        allocation_df['Cash'] = 1.0 - allocation_df[tickers].sum(axis=1)

        # Ensure Cash is non-negative
        allocation_df['Cash'] = allocation_df['Cash'].clip(lower=0.0)

        # Reindex to match price data dates
        allocation_df = allocation_df.reindex(date_index, method='ffill').fillna(0.0)

        logger.info(f"Converted signals to allocations: {allocation_df.shape}")
        return allocation_df



    def _calculate_parameter_variation(self, param_combo):
        """Calculate a variation factor based on parameter combination.
        This is a placeholder for real optimization - in practice, each combination
        would run separate signal generation and produce genuinely different results."""

        # Get base case parameters
        base_st = 15  # default st_lookback
        base_mt = 70  # default mt_lookback

        # Get current parameters
        current_st = param_combo.get('st_lookback', base_st)
        current_mt = param_combo.get('mt_lookback', base_mt)

        # Calculate simple variation based on parameter differences
        # This creates realistic but artificial differences between combinations
        st_factor = 1.0 + (current_st - base_st) * 0.002  # 0.2% per unit difference
        mt_factor = 1.0 + (current_mt - base_mt) * 0.001  # 0.1% per unit difference

        # Combine factors (multiplicative effect)
        variation_factor = st_factor * mt_factor

        # Ensure reasonable bounds (±10% variation)
        variation_factor = max(0.90, min(1.10, variation_factor))

        logger.info(f"Parameter variation: st_lookback={current_st} (vs {base_st}), mt_lookback={current_mt} (vs {base_mt}) -> factor={variation_factor:.4f}")

        return variation_factor



    def _calculate_annual_returns(self, equity_curve):
        """Calculate annual returns for each year."""
        annual_returns = {}

        # Group by year - handle both datetime and non-datetime indices
        if hasattr(equity_curve.index, 'year'):
            yearly_data = equity_curve.groupby(equity_curve.index.year)
        else:
            # For non-datetime indices, create a dummy grouping
            yearly_data = [(2024, equity_curve)]  # Single group with dummy year

        for year, year_data in yearly_data:
            if len(year_data) > 1:
                year_return = (year_data.iloc[-1] / year_data.iloc[0]) - 1
                annual_returns[str(year)] = year_return

        # Keep the current year calculation as-is (it was working)
        # We'll just change the column header display later

        return annual_returns

    def _create_performance_tab(self, workbook, strategy_equity_curve, allocation_df, benchmark_equity_curve=None):
        """Create Performance tab with exact reference layout."""
        ws = workbook.create_sheet("Performance")

        # Get strategy parameters in exact order
        strategy_params = self._get_strategy_parameters()

        # Calculate performance metrics
        strategy_metrics = self._calculate_performance_metrics(strategy_equity_curve)
        strategy_annual_returns = self._calculate_annual_returns(strategy_equity_curve)

        # Calculate benchmark metrics if not provided
        if benchmark_equity_curve is None:
            # FIXED: Pass allocation_df to calculate independent benchmark equity curve
            benchmark_equity_curve = self._calculate_benchmark_equity_curve(allocation_df)
            benchmark_metrics = self._calculate_performance_metrics(benchmark_equity_curve)
            benchmark_annual_returns = self._calculate_annual_returns(benchmark_equity_curve)
        else:
            benchmark_metrics = self._calculate_performance_metrics(benchmark_equity_curve)
            benchmark_annual_returns = self._calculate_annual_returns(benchmark_equity_curve)

        # EXACT COLUMN ORDER from reference layout
        headers = [
            'Type',
            'st_lookback',
            'mt_lookback',
            'lt_lookback',
            'top_n',
            'execution_delay',
            'CAGR',
            'Sharpe',
            'Sortino',
            'UPI',
            'Max Drawdown'
        ]

        # Add YTD and annual return columns
        current_year = strategy_equity_curve.index[-1].year
        ytd_col = f"YTD '{str(current_year)[-2:]}'"

        # Add annual return columns (most recent first)
        years = sorted([int(year) for year in strategy_annual_returns.keys()
                       if year.isdigit()], reverse=True)

        # Replace current year header with YTD format, keep others as year
        for year in years:
            if year == current_year:
                headers.append(ytd_col)
            else:
                headers.append(str(year))

        # Write headers (Row 1)
        for col_idx, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col_idx, value=header)
            cell.font = Font(bold=True)
            cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")

        # BENCHMARK ROW (Row 2) - Must be first per reference
        benchmark_row = ['Benchmark (Equal Weight)']

        # Add parameter values for benchmark (use strategy defaults)
        benchmark_row.extend([
            strategy_params.get('st_lookback', ''),
            strategy_params.get('mt_lookback', ''),
            strategy_params.get('lt_lookback', ''),
            strategy_params.get('top_n', ''),
            strategy_params.get('execution_delay', '')
        ])

        # Add benchmark performance metrics
        benchmark_row.extend([
            benchmark_metrics.get('CAGR', 0),
            benchmark_metrics.get('Sharpe Ratio', 0),
            benchmark_metrics.get('Sortino Ratio', 0),
            benchmark_metrics.get('UPI', 0),
            benchmark_metrics.get('Max Drawdown', 0)
        ])

        # Add benchmark YTD and annual returns
        for year in years:
            if year == current_year:
                # Use current year data for YTD column
                benchmark_row.append(benchmark_annual_returns.get(str(year), 0))
            else:
                benchmark_row.append(benchmark_annual_returns.get(str(year), 0))

        # Write benchmark row
        for col_idx, value in enumerate(benchmark_row, 1):
            cell = ws.cell(row=2, column=col_idx, value=value)
            # Format numbers
            if col_idx >= 7:  # Performance metrics columns
                if col_idx in [7, 11]:  # CAGR, Max Drawdown
                    cell.number_format = '0.00%'
                elif col_idx in [8, 9, 10]:  # Sharpe, Sortino, UPI
                    cell.number_format = '0.00'
                elif col_idx >= 12:  # YTD and annual returns
                    cell.number_format = '0.00%'

        # STRATEGY ROWS - Check if optimization is active using GATEWAY APPROACH
        import os
        optimization_active_env = os.environ.get('CPS_V4_OPTIMIZATION_ACTIVE')
        optimization_active = optimization_active_env and optimization_active_env.lower() == 'true'

        print(f"🚪 [PERFORMANCE TABLE] CPS_V4_OPTIMIZATION_ACTIVE: {optimization_active_env}")
        print(f"🚪 [PERFORMANCE TABLE] Using optimization mode: {optimization_active}")

        if optimization_active:
            # OPTIMIZATION MODE: Use matrix-based optimization
            optimization_combinations = self.get_optimization_combinations()
            current_row = 3  # Start after benchmark row

            # Run matrix optimization for all combinations (lean logging)
            equity_matrix, combination_metadata = self._run_matrix_optimization(optimization_combinations)
        else:
            # SINGLE MODE: Use existing pipeline results (no optimization)
            logger.info("[TRACE] Single mode detected - using existing pipeline results instead of optimization")

            # Create single combination from base case settings
            base_combination = {
                'st_lookback': self._get_parameter_value('Strategy', 'st_lookback'),
                'mt_lookback': self._get_parameter_value('Strategy', 'mt_lookback'),
                'lt_lookback': self._get_parameter_value('Strategy', 'lt_lookback'),
                'execution_delay': self._get_parameter_value('Strategy', 'execution_delay'),
                'system_lookback': self._get_parameter_value('System', 'system_lookback'),
                'top_n': self._get_parameter_value('System', 'system_top_n')
            }

            optimization_combinations = [base_combination]
            current_row = 3  # Start after benchmark row

            # Use existing equity curve from the pipeline run that already completed
            equity_matrix = None  # Will be loaded from existing files
            combination_metadata = [{'combination': base_combination, 'index': 0}]

        # Determine base case combination (for CSV saving)
        base_case_params = {
            'st_lookback': 15,  # default_value from settings
            'mt_lookback': 70,  # default_value from settings
            'lt_lookback': 100,
            'execution_delay': 1,
            'top_n': 2
        }

        # INSTRUMENTATION: Result aggregation before .xlsx write
        logger.info(f"[TRACE] Starting result aggregation for {len(optimization_combinations)} combinations before XLSX write")

        if equity_matrix is not None:
            logger.info(f"[TRACE] Equity matrix shape: {equity_matrix.shape}")
            logger.info(f"[TRACE] Equity matrix columns: {list(equity_matrix.columns)}")

            # Check for zeros in the equity matrix before processing
            for col in equity_matrix.columns:
                col_data = equity_matrix[col].dropna()
                if len(col_data) > 0:
                    has_zeros = (col_data == 0).any()
                    logger.info(f"[TRACE] Matrix column {col}: length={len(col_data)}, has_zeros={has_zeros}, first={col_data.iloc[0] if len(col_data) > 0 else 'EMPTY'}, last={col_data.iloc[-1] if len(col_data) > 0 else 'EMPTY'}")
                else:
                    logger.info(f"[TRACE] Matrix column {col}: EMPTY after dropna()")
        else:
            logger.info("[TRACE] Single mode - no equity matrix, will load from existing files")
        
        # Process each combination from the matrix (silent processing)
        for combo_idx, param_combo in enumerate(optimization_combinations):
            if equity_matrix is not None:
                # OPTIMIZATION MODE: Get equity curve from matrix
                # Create descriptive column name from parameters
                param_key_parts = []
                for param, value in param_combo.items():
                    # Use abbreviated parameter names for compact column names
                    param_abbrev = {
                        'st_lookback': 'ST',
                        'mt_lookback': 'MT',
                        'lt_lookback': 'LT',
                        'top_n': 'TOP',
                        'execution_delay': 'ED'
                    }.get(param, param[:3].upper())

                    param_key_parts.append(f"{param_abbrev}{value}")

                param_key = "_".join(param_key_parts)

                # Try to find the column by parameter key or by generic combo_name
                column_names = list(equity_matrix.columns)
                matching_cols = [col for col in column_names if param_key in col]

                if matching_cols:
                    # Use the first matching column based on parameter key
                    combo_name = matching_cols[0]
                    logger.info(f"[TRACE] Found parameter-based equity curve column: {combo_name}")
                else:
                    # CRITICAL ERROR: Parameter-based column not found - NO FALLBACKS ALLOWED
                    error_msg = f"Parameter-based equity curve column not found for {param_key}. Available columns: {column_names}. NO FALLBACKS ALLOWED."
                    logger.critical(f"[TRACE] {error_msg}")
                    raise ValueError(error_msg)

                # Make sure the column exists before accessing
                if combo_name not in equity_matrix.columns:
                    error_msg = f"Column {combo_name} not found in equity matrix! Available columns: {list(equity_matrix.columns)}"
                    logger.critical(f"[TRACE] {error_msg}")
                    raise ValueError(error_msg)
                else:
                    combo_equity_curve = equity_matrix[combo_name].dropna()
            else:
                # SINGLE MODE: Load equity curve from existing pipeline results
                combo_equity_curve = self._load_existing_equity_curve(param_combo)
            
            # INSTRUMENTATION: Check values from matrix before metrics calculation
            logger.info(f"[TRACE] Processing combo {combo_idx} for XLSX - curve length: {len(combo_equity_curve)}")
            if len(combo_equity_curve) > 0:
                logger.info(f"[TRACE] Combo {combo_idx} values: first={combo_equity_curve.iloc[0]}, last={combo_equity_curve.iloc[-1]}, mean={combo_equity_curve.mean()}, zeros={(combo_equity_curve == 0).any()}")
            else:
                logger.warning(f"[TRACE] Combo {combo_idx} has EMPTY equity curve from matrix!")

            # Check if this is the base case (save CSV only for base case)
            is_base_case = (param_combo.get('st_lookback') == base_case_params['st_lookback'] and
                           param_combo.get('mt_lookback') == base_case_params['mt_lookback'])

            # Save CSV for base case (only if CSV flag is enabled)
            if is_base_case and self.csv_flag_use:
                self._store_equity_curve(combo_equity_curve, 'strategy')

            # Calculate metrics from equity curve (silent)
            combo_metrics = self._calculate_performance_metrics(combo_equity_curve)
            combo_annual_returns = self._calculate_annual_returns(combo_equity_curve)
            
            # INSTRUMENTATION: Check calculated metrics before writing to XLSX
            logger.info(f"[TRACE] Combo {combo_idx} metrics: CAGR={combo_metrics.get('CAGR', 'MISSING')}, Sharpe={combo_metrics.get('Sharpe Ratio', 'MISSING')}")

            # Create strategy row label
            if len(optimization_combinations) == 1:
                strategy_label = 'Strategy'
            else:
                strategy_label = f'Strategy_{combo_idx + 1}'

            strategy_row = [strategy_label]

            # Add parameter values for this combination
            strategy_row.extend([
                param_combo.get('st_lookback', ''),
                param_combo.get('mt_lookback', ''),
                param_combo.get('lt_lookback', ''),
                param_combo.get('top_n', ''),
                param_combo.get('execution_delay', '')
            ])

            # Add actual performance metrics for this combination
            strategy_row.extend([
                combo_metrics.get('CAGR', 0),
                combo_metrics.get('Sharpe Ratio', 0),
                combo_metrics.get('Sortino Ratio', 0),
                combo_metrics.get('UPI', 0),
                combo_metrics.get('Max Drawdown', 0)
            ])

            # Add YTD and annual returns for this combination
            for year in years:
                if year == current_year:
                    # Use current year data for YTD column
                    strategy_row.append(combo_annual_returns.get(str(year), 0))
                else:
                    strategy_row.append(combo_annual_returns.get(str(year), 0))

            # Write strategy row
            for col_idx, value in enumerate(strategy_row, 1):
                cell = ws.cell(row=current_row, column=col_idx, value=value)
                # Format numbers
                if col_idx >= 7:  # Performance metrics columns
                    if col_idx in [7, 11]:  # CAGR, Max Drawdown
                        cell.number_format = '0.00%'
                    elif col_idx in [8, 9, 10]:  # Sharpe, Sortino, UPI
                        cell.number_format = '0.00'
                    elif col_idx >= 12:  # YTD and annual returns
                        cell.number_format = '0.00%'

            current_row += 1

        # Auto-adjust column widths
        for column in ws.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 20)
            ws.column_dimensions[column_letter].width = adjusted_width

        logger.info(f"Created Performance tab with {len(headers)} columns, 1 benchmark + {len(optimization_combinations)} strategy rows")
        return ws

    def generate_performance_table(self, output_dir=None):
        """Generate complete Performance Table XLSX report."""
        try:
            logger.info("Starting Performance Table generation...")

            # Load data
            signals_df, allocation_df, trade_df = self._load_data_files()

            # Get optimizable parameters
            optimizable_params = self._get_optimizable_parameters()

            # Enhance trade log
            enhanced_trade_df = self._enhance_trade_log(trade_df)

            # Calculate equity curves
            strategy_equity_curve = self._calculate_equity_curves(allocation_df)

            # Create Excel workbook
            workbook = openpyxl.Workbook()

            # Remove default sheet
            workbook.remove(workbook.active)

            # Create all tabs
            signal_ws = self._create_signal_history_tab(workbook, signals_df)
            allocation_ws = self._create_allocation_history_tab(workbook, allocation_df)
            trade_ws = self._create_trade_log_tab(workbook, enhanced_trade_df)
            # Store allocation_df as instance variable for optimization
            self.allocation_df = allocation_df
            performance_ws = self._create_performance_tab(workbook, strategy_equity_curve, allocation_df)
            settings_ws = self._create_settings_tab(workbook)

            # Add parameter header to first sheet (Signal History)
            self._create_parameter_header(signal_ws, optimizable_params)

            # Set Signal History as active sheet
            workbook.active = signal_ws

            # Save file
            output_path = Path(output_dir) if output_dir else OUTPUT_DIR
            output_path.mkdir(exist_ok=True)

            filename = f"EMA_V3_1_performance_tables_{self.timestamp}.xlsx"
            filepath = output_path / filename

            workbook.save(filepath)

            logger.info(f"Performance Table saved to: {filepath}")
            return filepath

        except Exception as e:
            logger.error(f"Error generating Performance Table: {e}")
            raise


def generate_performance_table_from_pipeline_results(results: dict,
                                                    signals_df: pd.DataFrame = None,
                                                    output_dir: str = "reporting") -> Path:
    """
    Generate Performance Table XLSX from unified pipeline results.

    This function is designed to be called from the unified pipeline
    with real backtest data and results.

    Args:
        results: Results dictionary from unified pipeline
        signals_df: Signals DataFrame from pipeline
        output_dir: Output directory for the report

    Returns:
        Path to generated XLSX file
    """
    logger = logging.getLogger('performance_table_pipeline')
    logger.info("Generating Performance Table XLSX from pipeline results...")

    try:
        # Initialize generator
        generator = PerformanceTableGenerator()

        # Use pipeline data directly instead of loading from files
        allocation_df = results.get('allocation_history')
        trade_df = results.get('trade_log')

        # Store allocation data in generator for optimization calculations
        generator.allocation_df = allocation_df

        if allocation_df is None or trade_df is None:
            raise ValueError("Required data (allocation_history, trade_log) not found in pipeline results")

        if signals_df is None:
            raise ValueError("Signals DataFrame is required")

        logger.info(f"Using pipeline data:")
        logger.info(f"  Signals: {signals_df.shape}")
        logger.info(f"  Allocations: {allocation_df.shape}")
        logger.info(f"  Trades: {trade_df.shape}")

        # Get optimizable parameters
        optimizable_params = generator._get_optimizable_parameters()

        # Enhance trade log
        enhanced_trade_df = generator._enhance_trade_log(trade_df)

        # Calculate equity curves using real data
        strategy_equity_curve = generator._calculate_equity_curves(allocation_df)

        # Create Excel workbook
        workbook = openpyxl.Workbook()
        workbook.remove(workbook.active)  # Remove default sheet

        # Create all tabs
        signal_ws = generator._create_signal_history_tab(workbook, signals_df)
        allocation_ws = generator._create_allocation_history_tab(workbook, allocation_df)
        trade_ws = generator._create_trade_log_tab(workbook, enhanced_trade_df)
        performance_ws = generator._create_performance_tab(workbook, strategy_equity_curve, allocation_df)
        settings_ws = generator._create_settings_tab(workbook)

        # Add parameter header to first sheet
        generator._create_parameter_header(signal_ws, optimizable_params)

        # Set Signal History as active sheet
        workbook.active = signal_ws

        # Save file
        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True)

        filename = f"EMA_V3_1_performance_tables_{generator.timestamp}.xlsx"
        filepath = output_path / filename

        workbook.save(filepath)

        logger.info(f"Performance Table XLSX saved to: {filepath}")
        return filepath

    except Exception as e:
        logger.error(f"Error generating Performance Table from pipeline results: {e}")
        raise
