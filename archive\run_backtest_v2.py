"""
Main entry point for running backtests with the improved v2 engine.
"""

import sys
import os
import pandas as pd
from datetime import date, datetime
import logging
from pathlib import Path

# Configure logging first
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Import local modules
from config.config_v2 import config_v2 as config
from config.paths import *  # All paths are handled here
from data.data_loader import load_data_for_backtest
from models.ema_allocation_model import ema_allocation_model_single
from engine.backtest import BacktestEngine
from reporting.performance_reporting import generate_performance_report_local

def adapter_ema_allocation(price_data, **params):
    """
    Adapter function to convert from price_data to the format expected by ema_allocation_model.
    
    Args:
        price_data (DataFrame): Historical price data
        **params: Additional parameters for the model
        
    Returns:
        dict: Asset weights dictionary
    """
    # Extract parameters
    st_lookback = params.get('st_lookback', 10)
    mt_lookback = params.get('mt_lookback', 50)
    lt_lookback = params.get('lt_lookback', 150)
    
    # Call the existing model
    weights = ema_allocation_model_single(
        price_data=price_data,
        returns_data=None,  # Not needed for EMA model
        st_lookback=st_lookback,
        mt_lookback=mt_lookback,
        lt_lookback=lt_lookback
    )
    
    return weights

def run_backtest_v2(config, debug=False):
    """
    Run a backtest using the improved v2 engine.
    
    Args:
        config (dict): Configuration dictionary
        debug (bool): If True, output debug information
        
    Returns:
        dict: Dictionary containing backtest results
    """
    # Configure logging
    if debug:
        # Create a file handler that logs everything
        log_file = os.path.join(OUTPUT_PATH, "debug", "debug_log_v2.txt")
        file_handler = logging.FileHandler(log_file)
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))
        logger.addHandler(file_handler)

    # Log configuration
    logger.info("Starting backtest with v2 engine:")
    logger.info(f"Strategy: {config['backtest_params']['strategy']}")
    logger.info(f"Rebalance frequency: {config['backtest_params']['rebalance_freq']}")
    logger.info(f"Execution delay: {config['backtest_params']['execution_delay']}")
    
    # Load data
    logger.info("Loading data...")
    data = load_data_for_backtest(config)
    price_data = data['price_data']
    returns_data = data['returns_data']
    risk_free_rate = data['risk_free_rate']
    
    # Create backtest engine
    engine = BacktestEngine(
        initial_capital=config['backtest_params']['initial_capital'],
        commission_rate=config['backtest_params']['commission_rate'],
        slippage_rate=config['backtest_params']['slippage_rate']
    )
    
    # Run backtest
    results = engine.run_backtest(
        price_data=price_data,
        signal_generator=adapter_ema_allocation,
        rebalance_freq=config['backtest_params']['rebalance_freq'],
        execution_delay=config['backtest_params']['execution_delay'],
        st_lookback=config['strategy_params']['st_lookback'],
        mt_lookback=config['strategy_params']['mt_lookback'],
        lt_lookback=config['strategy_params']['lt_lookback']
    )
    
    # Generate performance report
    try:
        # Extract strategy parameters for the performance table
        strategy_params = {
            'st_lookback': config['strategy_params'].get('st_lookback', ''),
            'mt_lookback': config['strategy_params'].get('mt_lookback', ''),
            'lt_lookback': config['strategy_params'].get('lt_lookback', ''),
            'top_n': config['strategy_params'].get('top_n', ''),
            'execution_delay': config['backtest_params'].get('execution_delay', '')
        }
        
        # Generate performance report with all data directly
        timestamp = datetime.now().strftime('%Y-%m-%d_%H%M%S')
        report_path = generate_performance_report_local(
            data_dict={
                'returns': results['strategy_returns'],
                'weights_history': results['weights_history'],
                'signal_history': results['signal_history'],
                'benchmark_returns': None,  # No benchmark in this test
                'strategy_params': strategy_params,
                # Create portfolio value data for the report
                'portfolio_value': pd.DataFrame((1 + results['strategy_returns']).cumprod()),
                'metrics': pd.DataFrame.from_dict(results['performance'], orient='index', columns=['Value'])
            }, 
            output_dir=OUTPUT_DIR,
            filename_prefix=f"v2_{config['backtest_params']['strategy']}_{timestamp}"
        )
        
        logger.info(f"Generated performance report: {report_path}")
        
    except Exception as e:
        logger.error(f"Error generating performance report: {e}")
        import traceback
        traceback.print_exc()
    
    # Print summary
    print_backtest_summary(results, config)
    
    return results

def print_backtest_summary(results, config):
    """
    Print a summary of backtest results.
    
    Args:
        results (dict): Backtest results
        config (dict): Configuration dictionary
    """
    print("\n" + "="*80)
    print(f"BACKTEST SUMMARY (V2 ENGINE)")
    print("="*80)
    
    # Strategy info
    print(f"Strategy: {config['backtest_params']['strategy']}")
    print(f"Rebalance Frequency: {config['backtest_params']['rebalance_freq']}")
    print(f"Execution Delay: {config['backtest_params']['execution_delay']} days")
    
    # Parameters
    if config['backtest_params']['strategy'] == 'ema':
        print(f"Short-term EMA: {config['strategy_params']['st_lookback']} days")
        print(f"Medium-term EMA: {config['strategy_params']['mt_lookback']} days")
        print(f"Long-term EMA: {config['strategy_params']['lt_lookback']} days")
    
    # Performance metrics
    print("\nPERFORMANCE METRICS:")
    print(f"Initial Capital: ${results['initial_capital']:,.2f}")
    print(f"Final Value: ${results['final_value']:,.2f}")
    print(f"Total Return: {results['total_return']:.2%}")
    print(f"CAGR: {results['performance']['cagr']:.2%}")
    print(f"Volatility: {results['performance']['volatility']:.2%}")
    print(f"Sharpe Ratio: {results['performance']['sharpe']:.2f}")
    print(f"Max Drawdown: {results['performance']['max_drawdown']:.2%}")
    
    # Trade statistics
    if 'trade_log' in results and not results['trade_log'].empty:
        trades_df = results['trade_log']
        print("\nTRADE STATISTICS:")
        print(f"Total Trades: {len(trades_df)}")
        
        # Buy/sell breakdown
        buys = trades_df[trades_df['direction'] == 'BUY']
        sells = trades_df[trades_df['direction'] == 'SELL']
        print(f"Buy Trades: {len(buys)}")
        print(f"Sell Trades: {len(sells)}")
        
        # Commission costs
        total_commission = trades_df['commission'].sum()
        print(f"Total Commission: ${total_commission:.2f}")
    
    print("="*80 + "\n")

def update_config_from_args():
    """
    Update configuration based on command line arguments.
    """
    import argparse
    
    parser = argparse.ArgumentParser(description='Run a backtest with the v2 engine.')
    
    # Strategy selection
    parser.add_argument('--strategy', type=str, help='Strategy to use (ema)')
    
    # Date range
    parser.add_argument('--start-date', type=str, help='Start date (YYYY-MM-DD)')
    parser.add_argument('--end-date', type=str, help='End date (YYYY-MM-DD)')
    
    # Rebalance frequency
    parser.add_argument('--rebalance', type=str, help='Rebalance frequency (daily, weekly, monthly, quarterly, yearly)')
    
    # Execution delay
    parser.add_argument('--delay', type=int, help='Execution delay in days')
    
    # Strategy parameters
    parser.add_argument('--st-lookback', type=int, help='Short-term EMA lookback period')
    parser.add_argument('--mt-lookback', type=int, help='Medium-term EMA lookback period')
    parser.add_argument('--lt-lookback', type=int, help='Long-term EMA lookback period')
    
    # Debug mode
    parser.add_argument('--debug', action='store_true', help='Enable debug mode')
    
    args = parser.parse_args()
    
    # Update config based on arguments
    if args.strategy:
        config['backtest_params']['strategy'] = args.strategy
    
    if args.start_date:
        config['data_params']['start_date'] = args.start_date
    
    if args.end_date:
        config['data_params']['end_date'] = args.end_date
    
    if args.rebalance:
        config['backtest_params']['rebalance_freq'] = args.rebalance
    
    if args.delay is not None:
        config['backtest_params']['execution_delay'] = args.delay
    
    if args.st_lookback:
        config['strategy_params']['st_lookback'] = args.st_lookback
    
    if args.mt_lookback:
        config['strategy_params']['mt_lookback'] = args.mt_lookback
    
    if args.lt_lookback:
        config['strategy_params']['lt_lookback'] = args.lt_lookback
    
    logger.info(f"Updated config: strategy={config['backtest_params']['strategy']}, "
                f"rebalance={config['backtest_params']['rebalance_freq']}, "
                f"start_date={config['data_params']['start_date']}, "
                f"end_date={config['data_params']['end_date']}")
    
    return args.debug

def main():
    """
    Main function to run the backtest.
    """
    # Check for debug flag
    debug_mode = '--debug' in sys.argv or update_config_from_args()
    if debug_mode:
        logger.info("Running in DEBUG mode")
    
    try:
        # Run backtest using our enhanced v2 backtest engine
        logger.info("Running backtest with v2 engine...")
        
        # Run the backtest
        backtest_results = run_backtest_v2(config, debug=debug_mode)
        
    except Exception as e:
        logger.error(f"Error in main: {e}")
        raise

if __name__ == "__main__":
    main()
