#!/usr/bin/env python
# -*- coding: utf-8 -*-
# config_reader_test.py
"""
Extremely simplified script to test basic print functionality.
"""

import sys

# Attempt a single, direct print operation.
# No custom functions, no PYTHONUNBUFFERED env var, minimal imports.

try:
    print("CONFIG_READER_TEST.PY: MINIMAL PRINT ATTEMPT 1")
    sys.stdout.flush()
    print("CONFIG_READER_TEST.PY: MINIMAL PRINT ATTEMPT 2 AFTER FLUSH")
    sys.stdout.flush()
except Exception as e:
    # If printing itself fails, we can't print the error.
    # This is a last resort to try and get *any* error info out,
    # though it's unlikely to be seen if basic prints fail.
    with open("config_reader_error.log", "w") as f:
        f.write(f"Error during minimal print attempt: {str(e)}\n")
        import traceback
        traceback.print_exc(file=f)

# Even if prints work, let's write a marker to a file
# to confirm the script ran to completion.
try:
    with open("config_reader_script_completed.marker", "w") as marker_file:
        marker_file.write("config_reader_test.py completed its execution.")
except:
    pass # If we can't write this, we have bigger issues than prints
