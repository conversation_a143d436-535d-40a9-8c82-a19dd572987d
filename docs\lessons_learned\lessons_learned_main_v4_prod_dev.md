# Lessons Learned: main_v4_production_run.py Development

This document captures key lessons learned during the development and debugging of `main_v4_production_run.py`, particularly by comparing its evolution against the existing `trace_ema_signal_breakdown.py` script. Adhering to these lessons can improve development efficiency and code quality.

## 1. Path Setup Consistency

* **Observation:** `trace_ema_signal_breakdown.py` utilizes a robust method (`Path(__file__).resolve().parent`) to define `PROJECT_ROOT` and correctly adds it to `sys.path`. This pattern was eventually adopted in `main_v4_production_run.py` after initial pathing issues.
* **Lesson:** When creating new main/runner scripts, **always refer to existing, functional examples** within the project (like `trace_ema_signal_breakdown.py`) for established conventions, especially path setup. This avoids re-solving known problems and ensures operational consistency across scripts.

## 2. Scope and Parameter Passing to Functions

* **Observation:** A `NameError: name 'PROJECT_ROOT' is not defined` occurred during CSV saving in `main_v4_production_run.py`. This was because a globally defined variable (`_project_root`) was not correctly accessible within the `main()` function's local scope where it was referenced as `PROJECT_ROOT`.
* **Lesson:** Be meticulous about variable scope. If a function requires access to a variable defined outside its immediate local scope, **explicitly pass it as an argument**. This was resolved by changing `def main():` to `def main(project_root_path):` and calling `main(_project_root)`.

## 3. Configuration and Parameter Propagation for Models

* **Observation:** The backtest log for `main_v4_production_run.py` showed `Signal params: {}` when the `BacktestEngine` called the `ema_signal_generator`. The `ema_allocation_model_updated` function (the signal generator) expects parameters like EMA lookbacks and `system_top_n` to be sourced from the CPS_v4 settings system (e.g., via `settings.get_param_value_by_key()`). The current issue of zero weights/signals likely stems from parameters not being correctly interpreted or utilized by the model when called by the engine.
* **Lesson:** When a component (e.g., `BacktestEngine`) invokes another configurable component (e.g., a signal model), ensure that necessary parameters are correctly propagated from the global settings system or explicitly passed via `**params` if the model is designed to accept them that way. The signal model itself should clearly document how it retrieves its parameters. **Do not assume global settings are automatically available to nested calls without a clear mechanism.**

## 4. V4 Module Imports and Project Structure

* **Observation:** Initial `ImportError`s in `main_v4_production_run.py` were due to incorrect assumptions about module paths within the V4 project structure.
* **Lesson:** The V4 system has a defined directory structure (e.g., `v4/engine/`, `v4/models/`, `v4/settings/`). All Python imports must align with this structure (e.g., `from v4.engine.data_loader_v4 import ...`). `trace_ema_signal_breakdown.py` served as a reliable example for correct V4 import statements.

## 5. Pandas DataFrame Boolean Evaluation

* **Observation:** A `ValueError: The truth value of a DataFrame is ambiguous` was encountered when checking `if not weights_history:`. This was resolved by changing the condition to `if weights_history.empty:`.
* **Lesson:** Always use specific Pandas methods like `.empty`, `.bool()`, `.item()`, `.any()`, or `.all()` for boolean checks on DataFrames or Series. Avoid direct boolean evaluation of these objects.

## 6. Leveraging Existing Code and Efficient Iterative Debugging

* **Observation:** Several issues addressed during the development of `main_v4_production_run.py` (e.g., pathing, some V4 import patterns) had already been solved or demonstrated in `trace_ema_signal_breakdown.py`.
* **Lesson:** Before extensive debugging or implementing new functionality that mirrors existing scripts, **thoroughly review existing, related code** for patterns, solutions, and best practices. This can significantly speed up development and reduce redundant effort. While the iterative run-log-fix cycle is effective, its efficiency is greatly enhanced by learning from prior art within the project.

By internalizing these lessons and proactively referring to established code like `trace_ema_signal_breakdown.py`, future development on similar scripts can be more streamlined and robust.
