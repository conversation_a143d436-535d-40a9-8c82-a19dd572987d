"""
Report generation module for CPS v4.
Contains functions for creating backtest reports and Excel output.
"""

import os
import pandas as pd
import numpy as np
import logging
from pathlib import Path
from datetime import datetime
from typing import Dict, Optional, Union
from openpyxl import load_workbook

from .performance_metrics import calculate_performance_metrics
from .performance_tables import create_performance_table
from visualization.performance_charts import (
    create_cumulative_returns_chart,
    create_drawdown_chart,
    create_return_distribution_chart,
    create_monthly_returns_chart,
    create_portfolio_weights_chart
)

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_backtest_report(
    backtest_results: Dict,
    config: Dict,
    output_dir: str = 'output',
    prefix: str = ''
) -> Dict[str, str]:
    """
    Generate a comprehensive performance report from backtest results.
    
    Args:
        backtest_results: Results from backtest including:
            - returns: Series of portfolio returns
            - weights_history: DataFrame of portfolio weights
            - benchmark_returns: Series of benchmark returns
        config: Configuration dictionary
        output_dir: Directory to save report
        prefix: Prefix for filenames
        
    Returns:
        Dict: Paths to generated files
    """
    # Extract data from backtest results
    returns = backtest_results.get('returns')
    weights_history = backtest_results.get('weights_history')
    benchmark_returns = backtest_results.get('benchmark_returns')
    
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)
    
    # Generate charts
    output_files = {}
    
    if returns is not None and not returns.empty:
        # Cumulative returns chart
        cum_returns_path = os.path.join(output_dir, f"{prefix}cumulative_returns.png")
        create_cumulative_returns_chart(returns, benchmark_returns, cum_returns_path)
        output_files['cumulative_returns'] = cum_returns_path
        
        # Drawdown chart
        drawdown_path = os.path.join(output_dir, f"{prefix}drawdown.png")
        create_drawdown_chart(returns, benchmark_returns, drawdown_path)
        output_files['drawdown'] = drawdown_path
        
        # Return distribution chart
        dist_path = os.path.join(output_dir, f"{prefix}return_distribution.png")
        create_return_distribution_chart(returns, benchmark_returns, dist_path)
        output_files['return_distribution'] = dist_path
        
        # Monthly returns chart
        monthly_path = os.path.join(output_dir, f"{prefix}monthly_returns.png")
        create_monthly_returns_chart(returns, monthly_path)
        output_files['monthly_returns'] = monthly_path
    
    # Portfolio weights chart if available
    if weights_history is not None and not weights_history.empty:
        weights_path = os.path.join(output_dir, f"{prefix}portfolio_weights.png")
        create_portfolio_weights_chart(weights_history, weights_path)
        output_files['portfolio_weights'] = weights_path
    
    # Generate Excel report
    excel_path = os.path.join(output_dir, f"{prefix}performance_report.xlsx")
    generate_performance_report_local(
        {
            'returns': returns,
            'weights_history': weights_history,
            'benchmark_returns': benchmark_returns,
            'config': config
        },
        output_dir=output_dir,
        filename_prefix=prefix
    )
    output_files['excel_report'] = excel_path
    
    return output_files

def generate_performance_report_local(
    data_dict: Dict,
    output_dir: Optional[str] = None,
    filename_prefix: Optional[str] = None
) -> str:
    """
    Generate a performance report in Excel format.
    
    Args:
        data_dict: Dictionary containing:
            - returns: Series of strategy returns
            - weights_history: DataFrame of asset weights over time
            - benchmark_returns: Series of benchmark returns
            - config: Configuration dictionary
        output_dir: Output directory for the report
        filename_prefix: Prefix for the output filename
        
    Returns:
        str: Path to the generated report
    """
    # Extract data
    returns = data_dict.get('returns')
    weights_history = data_dict.get('weights_history')
    benchmark_returns = data_dict.get('benchmark_returns')
    config = data_dict.get('config', {})
    
    # Create output directory if needed
    if output_dir:
        os.makedirs(output_dir, exist_ok=True)
    
    # Generate filename
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    prefix = f"{filename_prefix}_" if filename_prefix else ""
    output_path = os.path.join(output_dir or '.', f"{prefix}performance_report_{timestamp}.xlsx")
    
    # Create Excel writer
    with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
        # Create and write performance table
        perf_table = create_performance_table(returns, benchmark_returns, config.get('strategy_params'))
        perf_table.to_excel(writer, sheet_name='Performance', index=False)
        
        # Add allocation history if available
        if weights_history is not None and not weights_history.empty:
            allocation_history = weights_history.copy()
            allocation_history = allocation_history.reset_index().rename(columns={'index': 'Date'})
            allocation_history.to_excel(writer, sheet_name='Allocation History', index=False)
            logger.info(f"Added Allocation History tab with {len(allocation_history)} rows")
        
        # Format Excel workbook
        workbook = writer.book
        for sheet_name in workbook.sheetnames:
            worksheet = workbook[sheet_name]
            for column in worksheet.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(cell.value)
                    except:
                        pass
                adjusted_width = (max_length + 2)
                worksheet.column_dimensions[column_letter].width = adjusted_width
    
    logger.info(f"Performance report generated: {output_path}")
    return output_path
