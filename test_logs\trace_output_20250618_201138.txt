2025-06-18 20:11:39,492 - INFO - Successfully loaded settings for ema_allocation_model_v4.
Initial PYTHONPATH: None
Initial sys.path: ['S:\\Dropbox\\Scott Only Internal\\Quant_Python_24\\Backtest_FinAsset_Alloc_Template', 'C:\\Python313\\python313.zip', 'C:\\Python313\\DLLs', 'C:\\Python313\\Lib', 'C:\\Python313', 'F:\\AI_Library\\my_quant_env', 'F:\\AI_Library\\my_quant_env\\Lib\\site-packages', 'F:\\GitHub_Clone\\ai-sdlc', 'F:\\AI_Library\\my_quant_env\\Lib\\site-packages\\win32', 'F:\\AI_Library\\my_quant_env\\Lib\\site-packages\\win32\\lib', 'F:\\AI_Library\\my_quant_env\\Lib\\site-packages\\Pythonwin']
sys.path after ensuring PROJECT_ROOT: ['S:\\Dropbox\\Scott Only Internal\\Quant_Python_24\\Backtest_FinAsset_Alloc_Template', 'C:\\Python313\\python313.zip', 'C:\\Python313\\DLLs', 'C:\\Python313\\Lib', 'C:\\Python313', 'F:\\AI_Library\\my_quant_env', 'F:\\AI_Library\\my_quant_env\\Lib\\site-packages', 'F:\\GitHub_Clone\\ai-sdlc', 'F:\\AI_Library\\my_quant_env\\Lib\\site-packages\\win32', 'F:\\AI_Library\\my_quant_env\\Lib\\site-packages\\win32\\lib', 'F:\\AI_Library\\my_quant_env\\Lib\\site-packages\\Pythonwin']
Successfully imported config.paths. PROJECT_ROOT according to paths.py: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template
Loaded settings from: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\settings\settings_parameters_v4.ini
Found 11 parameter sections
Found 55 named lists for parameter references
Successfully loaded settings.
Python version: 3.13.1 (tags/v3.13.1:0671451, Dec  3 2024, 19:06:28) [MSC v.1942 64 bit (AMD64)]
Python paths:
  S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4
  S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template
  S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\CPS_V4
  S:\Dropbox\Scott Only Internal\Quant_Python_24\Custom Function Library
  C:\Python313\python313.zip
  C:\Python313\DLLs
  C:\Python313\Lib
  C:\Python313
  F:\AI_Library\my_quant_env
  F:\AI_Library\my_quant_env\Lib\site-packages
Loaded settings from: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\settings\settings_parameters_v4.ini
Found 11 parameter sections
Found 55 named lists for parameter references
Loaded settings from: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\settings\settings_parameters_v4.ini
Found 11 parameter sections
Found 55 named lists for parameter references
Loaded settings from: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\settings\settings_parameters_v4.ini
Found 11 parameter sections
Found 55 named lists for parameter references
Loaded settings from: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\settings\settings_parameters_v4.ini
Found 11 parameter sections
Found 55 named lists for parameter references
Loaded settings from: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\settings\settings_parameters_v4.ini
Found 11 parameter sections
Found 55 named lists for parameter references
Loaded settings from: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\settings\settings_parameters_v4.ini
Found 11 parameter sections
Found 55 named lists for parameter references
2025-06-18 20:11:39,524 - INFO - Script setup complete. Settings loaded and loggers configured.
2025-06-18 20:11:39,524 - INFO - Script setup complete. Settings loaded and loggers configured.
2025-06-18 20:11:39,546 - INFO - Script setup complete. Settings loaded and loggers configured.
2025-06-18 20:11:39,546 - INFO - Starting EMA Signal Breakdown trace.
2025-06-18 20:11:39,546 - INFO - Starting EMA Signal Breakdown trace.
2025-06-18 20:11:39,546 - INFO - EMA Signal Breakdown Trace Log - 20250618_201139
2025-06-18 20:11:39,547 - INFO - Period: 2021-01-20 to 2021-02-28
2025-06-18 20:11:39,547 - INFO - Initializing Backtest Engine components...
2025-06-18 20:11:39,547 - INFO - Initializing Backtest Engine components...
2025-06-18 20:11:39,547 - INFO - Initializing Backtest Engine components...
2025-06-18 20:11:39,547 - INFO - Initialized backtest engine with $1,000,000.00 capital
2025-06-18 20:11:39,547 - INFO - Commission rate: 0.10%
2025-06-18 20:11:39,547 - INFO - Slippage rate: 0.05%
2025-06-18 20:11:39,547 - INFO - Backtest Engine initialized with initial capital: $1,000,000.00 and execution delay: 0 days.
2025-06-18 20:11:39,547 - INFO - Backtest Engine initialized with initial capital: $1,000,000.00 and execution delay: 0 days.
2025-06-18 20:11:39,547 - INFO - Backtest Engine initialized with initial capital: $1,000,000.00 and execution delay: 0 days.
2025-06-18 20:11:39,547 - INFO - Loading price data...
2025-06-18 20:11:39,547 - INFO - Loading price data...
2025-06-18 20:11:39,548 - INFO - [TickerData] Mode=Read, file=S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\data\tickerdata_SPY_SHV_EFA_TLT_PFF_20200101_20250615.xlsx
2025-06-18 20:11:39,935 - INFO - Price data loaded: 1370 rows, 5 assets.
2025-06-18 20:11:39,935 - INFO - Price data loaded: 1370 rows, 5 assets.
2025-06-18 20:11:39,935 - INFO - Price data loaded. Shape: (1370, 5)
2025-06-18 20:11:39,935 - INFO - Loading CPS V4 settings...
2025-06-18 20:11:39,935 - INFO - Loading CPS V4 settings...
2025-06-18 20:11:39,939 - INFO - --- Effective Settings for Trace ---
2025-06-18 20:11:39,960 - INFO - EMA Lookbacks: ST=15, MT=70, LT=100
2025-06-18 20:11:39,960 - INFO - EMA Weights: Min=0.0, Max=1.0
2025-06-18 20:11:39,960 - INFO - EMA Strategy Top N: 3
2025-06-18 20:11:39,960 - INFO - System Top N (for allocation rules): 2
2025-06-18 20:11:39,960 - INFO - Signal Algorithm for Rules: ema
2025-06-18 20:11:39,960 - INFO - -----------------------------------

Loaded settings from: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\settings\settings_parameters_v4.ini
Found 11 parameter sections
Found 55 named lists for parameter references
2025-06-18 20:11:39,963 - INFO - Tracing 27 business days from 2021-01-20 to 2021-02-28.
2025-06-18 20:11:39,963 - INFO - Tracing 27 business days from 2021-01-20 to 2021-02-28.
2025-06-18 20:11:39,963 - INFO - 
========== Tracing for Date: 2021-01-20 ==========
2025-06-18 20:11:39,964 - INFO - --- Calling Production Model with trace_mode=True ---
2025-06-18 20:11:39,971 - INFO - Applied ema allocation weights
2025-06-18 20:11:39,973 - INFO - --- Step 1: Ratios (EMAXAvg) ---
2025-06-18 20:11:39,974 - INFO - Ratios received:
SPY    1.031513
SHV    1.000027
EFA    1.039898
TLT    0.985320
PFF    1.014675
2025-06-18 20:11:39,975 - INFO - --- Step 2: Ranks ---
2025-06-18 20:11:39,977 - INFO - Ranks received:
  Asset  EMAXAvg_Value  Rank_Ordinal
0   EFA       1.039898             1
1   SPY       1.031513             2
2   PFF       1.014675             3
3   SHV       1.000027             4
4   TLT       0.985320             5
2025-06-18 20:11:39,977 - INFO - --- Step 3: Signal (Allocation) ---
2025-06-18 20:11:39,978 - INFO - Signal received:
SPY    0.4
SHV    0.0
EFA    0.6
TLT    0.0
PFF    0.0
2025-06-18 20:11:39,978 - INFO - --- Processing Backtest Engine for date: 2021-01-20 ---
2025-06-18 20:11:39,978 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine IMMEDIATELY AFTER conversion: <class 'dict'>
2025-06-18 20:11:39,978 - INFO - No orders from previous signals to execute for 2021-01-20
2025-06-18 20:11:39,979 - INFO - Generated 2 new orders based on today's signal for 2021-01-20
2025-06-18 20:11:39,979 - INFO - T+0 Execution: Processing 2 orders generated from today's signal.
2025-06-18 20:11:39,979 - INFO - Executed 2 T+0 trades from today's signal for 2021-01-20
2025-06-18 20:11:39,979 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine BEFORE mark_to_market: <class 'dict'>
2025-06-18 20:11:39,979 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine BEFORE direct get_weights call: <class 'dict'>
2025-06-18 20:11:39,980 - INFO - Final post-trade allocations for 2021-01-20:
EFA     0.599269
SPY     0.399664
Cash    0.001067
2025-06-18 20:11:39,980 - INFO - 
========== Tracing for Date: 2021-01-21 ==========
2025-06-18 20:11:39,980 - INFO - --- Calling Production Model with trace_mode=True ---
2025-06-18 20:11:39,987 - INFO - Applied ema allocation weights
2025-06-18 20:11:39,989 - INFO - --- Step 1: Ratios (EMAXAvg) ---
2025-06-18 20:11:39,989 - INFO - Ratios received:
SPY    1.031953
SHV    1.000024
EFA    1.039822
TLT    0.984949
PFF    1.014642
2025-06-18 20:11:39,989 - INFO - --- Step 2: Ranks ---
2025-06-18 20:11:39,991 - INFO - Ranks received:
  Asset  EMAXAvg_Value  Rank_Ordinal
0   EFA       1.039822             1
1   SPY       1.031953             2
2   PFF       1.014642             3
3   SHV       1.000024             4
4   TLT       0.984949             5
2025-06-18 20:11:39,991 - INFO - --- Step 3: Signal (Allocation) ---
2025-06-18 20:11:39,992 - INFO - Signal received:
SPY    0.4
SHV    0.0
EFA    0.6
TLT    0.0
PFF    0.0
2025-06-18 20:11:39,992 - INFO - --- Processing Backtest Engine for date: 2021-01-21 ---
2025-06-18 20:11:39,992 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine IMMEDIATELY AFTER conversion: <class 'dict'>
2025-06-18 20:11:39,992 - INFO - No orders from previous signals to execute for 2021-01-21
2025-06-18 20:11:39,993 - INFO - Generated 2 new orders based on today's signal for 2021-01-21
2025-06-18 20:11:39,993 - INFO - T+0 Execution: Processing 2 orders generated from today's signal.
2025-06-18 20:11:39,993 - INFO - Executed 2 T+0 trades from today's signal for 2021-01-21
2025-06-18 20:11:39,993 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine BEFORE mark_to_market: <class 'dict'>
2025-06-18 20:11:39,993 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine BEFORE direct get_weights call: <class 'dict'>
2025-06-18 20:11:39,994 - INFO - Final post-trade allocations for 2021-01-21:
EFA     0.599912
SPY     0.400055
Cash    0.000032
2025-06-18 20:11:39,994 - INFO - 
========== Tracing for Date: 2021-01-22 ==========
2025-06-18 20:11:39,994 - INFO - --- Calling Production Model with trace_mode=True ---
2025-06-18 20:11:40,000 - INFO - Applied ema allocation weights
2025-06-18 20:11:40,002 - INFO - --- Step 1: Ratios (EMAXAvg) ---
2025-06-18 20:11:40,003 - INFO - Ratios received:
SPY    1.032048
SHV    1.000022
EFA    1.039386
TLT    0.984831
PFF    1.014674
2025-06-18 20:11:40,003 - INFO - --- Step 2: Ranks ---
2025-06-18 20:11:40,004 - INFO - Ranks received:
  Asset  EMAXAvg_Value  Rank_Ordinal
0   EFA       1.039386             1
1   SPY       1.032048             2
2   PFF       1.014674             3
3   SHV       1.000022             4
4   TLT       0.984831             5
2025-06-18 20:11:40,004 - INFO - --- Step 3: Signal (Allocation) ---
2025-06-18 20:11:40,005 - INFO - Signal received:
SPY    0.4
SHV    0.0
EFA    0.6
TLT    0.0
PFF    0.0
2025-06-18 20:11:40,005 - INFO - --- Processing Backtest Engine for date: 2021-01-22 ---
2025-06-18 20:11:40,005 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine IMMEDIATELY AFTER conversion: <class 'dict'>
2025-06-18 20:11:40,005 - INFO - No orders from previous signals to execute for 2021-01-22
2025-06-18 20:11:40,005 - INFO - Generated 2 new orders based on today's signal for 2021-01-22
2025-06-18 20:11:40,006 - INFO - T+0 Execution: Processing 2 orders generated from today's signal.
2025-06-18 20:11:40,006 - INFO - Executed 2 T+0 trades from today's signal for 2021-01-22
2025-06-18 20:11:40,006 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine BEFORE mark_to_market: <class 'dict'>
2025-06-18 20:11:40,006 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine BEFORE direct get_weights call: <class 'dict'>
2025-06-18 20:11:40,007 - INFO - Final post-trade allocations for 2021-01-22:
EFA     0.599951
SPY     0.399989
Cash    0.000060
2025-06-18 20:11:40,007 - INFO - 
========== Tracing for Date: 2021-01-25 ==========
2025-06-18 20:11:40,007 - INFO - --- Calling Production Model with trace_mode=True ---
2025-06-18 20:11:40,014 - INFO - Applied ema allocation weights
2025-06-18 20:11:40,015 - INFO - --- Step 1: Ratios (EMAXAvg) ---
2025-06-18 20:11:40,016 - INFO - Ratios received:
SPY    1.032261
SHV    1.000019
EFA    1.038844
TLT    0.985371
PFF    1.014524
2025-06-18 20:11:40,016 - INFO - --- Step 2: Ranks ---
2025-06-18 20:11:40,017 - INFO - Ranks received:
  Asset  EMAXAvg_Value  Rank_Ordinal
0   EFA       1.038844             1
1   SPY       1.032261             2
2   PFF       1.014524             3
3   SHV       1.000019             4
4   TLT       0.985371             5
2025-06-18 20:11:40,018 - INFO - --- Step 3: Signal (Allocation) ---
2025-06-18 20:11:40,019 - INFO - Signal received:
SPY    0.4
SHV    0.0
EFA    0.6
TLT    0.0
PFF    0.0
2025-06-18 20:11:40,020 - INFO - --- Processing Backtest Engine for date: 2021-01-25 ---
2025-06-18 20:11:40,020 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine IMMEDIATELY AFTER conversion: <class 'dict'>
2025-06-18 20:11:40,020 - INFO - No orders from previous signals to execute for 2021-01-25
2025-06-18 20:11:40,021 - INFO - Generated 2 new orders based on today's signal for 2021-01-25
2025-06-18 20:11:40,021 - INFO - T+0 Execution: Processing 2 orders generated from today's signal.
2025-06-18 20:11:40,021 - INFO - Executed 2 T+0 trades from today's signal for 2021-01-25
2025-06-18 20:11:40,021 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine BEFORE mark_to_market: <class 'dict'>
2025-06-18 20:11:40,021 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine BEFORE direct get_weights call: <class 'dict'>
2025-06-18 20:11:40,022 - INFO - Final post-trade allocations for 2021-01-25:
EFA     0.599855
SPY     0.400132
Cash    0.000013
2025-06-18 20:11:40,022 - INFO - 
========== Tracing for Date: 2021-01-26 ==========
2025-06-18 20:11:40,023 - INFO - --- Calling Production Model with trace_mode=True ---
2025-06-18 20:11:40,030 - INFO - Applied ema allocation weights
2025-06-18 20:11:40,032 - INFO - --- Step 1: Ratios (EMAXAvg) ---
2025-06-18 20:11:40,033 - INFO - Ratios received:
SPY    1.032272
SHV    1.000018
EFA    1.038357
TLT    0.985772
PFF    1.014220
2025-06-18 20:11:40,033 - INFO - --- Step 2: Ranks ---
2025-06-18 20:11:40,034 - INFO - Ranks received:
  Asset  EMAXAvg_Value  Rank_Ordinal
0   EFA       1.038357             1
1   SPY       1.032272             2
2   PFF       1.014220             3
3   SHV       1.000018             4
4   TLT       0.985772             5
2025-06-18 20:11:40,034 - INFO - --- Step 3: Signal (Allocation) ---
2025-06-18 20:11:40,035 - INFO - Signal received:
SPY    0.4
SHV    0.0
EFA    0.6
TLT    0.0
PFF    0.0
2025-06-18 20:11:40,035 - INFO - --- Processing Backtest Engine for date: 2021-01-26 ---
2025-06-18 20:11:40,035 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine IMMEDIATELY AFTER conversion: <class 'dict'>
2025-06-18 20:11:40,035 - INFO - No orders from previous signals to execute for 2021-01-26
2025-06-18 20:11:40,035 - INFO - Generated 2 new orders based on today's signal for 2021-01-26
2025-06-18 20:11:40,036 - INFO - T+0 Execution: Processing 2 orders generated from today's signal.
2025-06-18 20:11:40,036 - INFO - Executed 2 T+0 trades from today's signal for 2021-01-26
2025-06-18 20:11:40,036 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine BEFORE mark_to_market: <class 'dict'>
2025-06-18 20:11:40,036 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine BEFORE direct get_weights call: <class 'dict'>
2025-06-18 20:11:40,037 - INFO - Final post-trade allocations for 2021-01-26:
EFA     0.599979
SPY     0.399769
Cash    0.000252
2025-06-18 20:11:40,037 - INFO - 
========== Tracing for Date: 2021-01-27 ==========
2025-06-18 20:11:40,037 - INFO - --- Calling Production Model with trace_mode=True ---
2025-06-18 20:11:40,044 - INFO - Applied ema allocation weights
2025-06-18 20:11:40,045 - INFO - --- Step 1: Ratios (EMAXAvg) ---
2025-06-18 20:11:40,046 - INFO - Ratios received:
SPY    1.030859
SHV    1.000021
EFA    1.036585
TLT    0.986282
PFF    1.013185
2025-06-18 20:11:40,046 - INFO - --- Step 2: Ranks ---
2025-06-18 20:11:40,047 - INFO - Ranks received:
  Asset  EMAXAvg_Value  Rank_Ordinal
0   EFA       1.036585             1
1   SPY       1.030859             2
2   PFF       1.013185             3
3   SHV       1.000021             4
4   TLT       0.986282             5
2025-06-18 20:11:40,047 - INFO - --- Step 3: Signal (Allocation) ---
2025-06-18 20:11:40,048 - INFO - Signal received:
SPY    0.4
SHV    0.0
EFA    0.6
TLT    0.0
PFF    0.0
2025-06-18 20:11:40,048 - INFO - --- Processing Backtest Engine for date: 2021-01-27 ---
2025-06-18 20:11:40,049 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine IMMEDIATELY AFTER conversion: <class 'dict'>
2025-06-18 20:11:40,049 - INFO - No orders from previous signals to execute for 2021-01-27
2025-06-18 20:11:40,049 - INFO - Generated 2 new orders based on today's signal for 2021-01-27
2025-06-18 20:11:40,049 - INFO - T+0 Execution: Processing 2 orders generated from today's signal.
2025-06-18 20:11:40,050 - INFO - Executed 2 T+0 trades from today's signal for 2021-01-27
2025-06-18 20:11:40,050 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine BEFORE mark_to_market: <class 'dict'>
2025-06-18 20:11:40,050 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine BEFORE direct get_weights call: <class 'dict'>
2025-06-18 20:11:40,051 - INFO - Final post-trade allocations for 2021-01-27:
EFA     0.600024
SPY     0.399746
Cash    0.000230
2025-06-18 20:11:40,051 - INFO - 
========== Tracing for Date: 2021-01-28 ==========
2025-06-18 20:11:40,051 - INFO - --- Calling Production Model with trace_mode=True ---
2025-06-18 20:11:40,057 - INFO - Applied ema allocation weights
2025-06-18 20:11:40,059 - INFO - --- Step 1: Ratios (EMAXAvg) ---
2025-06-18 20:11:40,060 - INFO - Ratios received:
SPY    1.030028
SHV    1.000019
EFA    1.035317
TLT    0.986466
PFF    1.012752
2025-06-18 20:11:40,060 - INFO - --- Step 2: Ranks ---
2025-06-18 20:11:40,061 - INFO - Ranks received:
  Asset  EMAXAvg_Value  Rank_Ordinal
0   EFA       1.035317             1
1   SPY       1.030028             2
2   PFF       1.012752             3
3   SHV       1.000019             4
4   TLT       0.986466             5
2025-06-18 20:11:40,062 - INFO - --- Step 3: Signal (Allocation) ---
2025-06-18 20:11:40,062 - INFO - Signal received:
SPY    0.4
SHV    0.0
EFA    0.6
TLT    0.0
PFF    0.0
2025-06-18 20:11:40,062 - INFO - --- Processing Backtest Engine for date: 2021-01-28 ---
2025-06-18 20:11:40,062 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine IMMEDIATELY AFTER conversion: <class 'dict'>
2025-06-18 20:11:40,063 - INFO - No orders from previous signals to execute for 2021-01-28
2025-06-18 20:11:40,063 - INFO - Generated 2 new orders based on today's signal for 2021-01-28
2025-06-18 20:11:40,063 - INFO - T+0 Execution: Processing 2 orders generated from today's signal.
2025-06-18 20:11:40,063 - INFO - Executed 2 T+0 trades from today's signal for 2021-01-28
2025-06-18 20:11:40,063 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine BEFORE mark_to_market: <class 'dict'>
2025-06-18 20:11:40,063 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine BEFORE direct get_weights call: <class 'dict'>
2025-06-18 20:11:40,064 - INFO - Final post-trade allocations for 2021-01-28:
EFA     0.599970
SPY     0.399908
Cash    0.000122
2025-06-18 20:11:40,064 - INFO - 
========== Tracing for Date: 2021-01-29 ==========
2025-06-18 20:11:40,064 - INFO - --- Calling Production Model with trace_mode=True ---
2025-06-18 20:11:40,071 - INFO - Applied ema allocation weights
2025-06-18 20:11:40,072 - INFO - --- Step 1: Ratios (EMAXAvg) ---
2025-06-18 20:11:40,073 - INFO - Ratios received:
SPY    1.028162
SHV    1.000022
EFA    1.033025
TLT    0.986345
PFF    1.012140
2025-06-18 20:11:40,073 - INFO - --- Step 2: Ranks ---
2025-06-18 20:11:40,074 - INFO - Ranks received:
  Asset  EMAXAvg_Value  Rank_Ordinal
0   EFA       1.033025             1
1   SPY       1.028162             2
2   PFF       1.012140             3
3   SHV       1.000022             4
4   TLT       0.986345             5
2025-06-18 20:11:40,075 - INFO - --- Step 3: Signal (Allocation) ---
2025-06-18 20:11:40,075 - INFO - Signal received:
SPY    0.4
SHV    0.0
EFA    0.6
TLT    0.0
PFF    0.0
2025-06-18 20:11:40,076 - INFO - --- Processing Backtest Engine for date: 2021-01-29 ---
2025-06-18 20:11:40,076 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine IMMEDIATELY AFTER conversion: <class 'dict'>
2025-06-18 20:11:40,076 - INFO - No orders from previous signals to execute for 2021-01-29
2025-06-18 20:11:40,076 - INFO - Generated 1 new orders based on today's signal for 2021-01-29
2025-06-18 20:11:40,076 - INFO - T+0 Execution: Processing 1 orders generated from today's signal.
2025-06-18 20:11:40,076 - INFO - Executed 1 T+0 trades from today's signal for 2021-01-29
2025-06-18 20:11:40,076 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine BEFORE mark_to_market: <class 'dict'>
2025-06-18 20:11:40,077 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine BEFORE direct get_weights call: <class 'dict'>
2025-06-18 20:11:40,077 - INFO - Final post-trade allocations for 2021-01-29:
EFA     0.599869
SPY     0.400073
Cash    0.000058
2025-06-18 20:11:40,078 - INFO - 
========== Tracing for Date: 2021-02-01 ==========
2025-06-18 20:11:40,078 - INFO - --- Calling Production Model with trace_mode=True ---
2025-06-18 20:11:40,084 - INFO - Applied ema allocation weights
2025-06-18 20:11:40,086 - INFO - --- Step 1: Ratios (EMAXAvg) ---
2025-06-18 20:11:40,087 - INFO - Ratios received:
SPY    1.027370
SHV    1.000019
EFA    1.031605
TLT    0.986334
PFF    1.011879
2025-06-18 20:11:40,087 - INFO - --- Step 2: Ranks ---
2025-06-18 20:11:40,088 - INFO - Ranks received:
  Asset  EMAXAvg_Value  Rank_Ordinal
0   EFA       1.031605             1
1   SPY       1.027370             2
2   PFF       1.011879             3
3   SHV       1.000019             4
4   TLT       0.986334             5
2025-06-18 20:11:40,088 - INFO - --- Step 3: Signal (Allocation) ---
2025-06-18 20:11:40,089 - INFO - Signal received:
SPY    0.4
SHV    0.0
EFA    0.6
TLT    0.0
PFF    0.0
2025-06-18 20:11:40,089 - INFO - --- Processing Backtest Engine for date: 2021-02-01 ---
2025-06-18 20:11:40,089 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine IMMEDIATELY AFTER conversion: <class 'dict'>
2025-06-18 20:11:40,090 - INFO - No orders from previous signals to execute for 2021-02-01
2025-06-18 20:11:40,090 - INFO - Generated 2 new orders based on today's signal for 2021-02-01
2025-06-18 20:11:40,090 - INFO - T+0 Execution: Processing 2 orders generated from today's signal.
2025-06-18 20:11:40,090 - INFO - Executed 2 T+0 trades from today's signal for 2021-02-01
2025-06-18 20:11:40,090 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine BEFORE mark_to_market: <class 'dict'>
2025-06-18 20:11:40,090 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine BEFORE direct get_weights call: <class 'dict'>
2025-06-18 20:11:40,091 - INFO - Final post-trade allocations for 2021-02-01:
EFA     0.599876
SPY     0.400110
Cash    0.000013
2025-06-18 20:11:40,091 - INFO - 
========== Tracing for Date: 2021-02-02 ==========
2025-06-18 20:11:40,092 - INFO - --- Calling Production Model with trace_mode=True ---
2025-06-18 20:11:40,100 - INFO - Applied ema allocation weights
2025-06-18 20:11:40,102 - INFO - --- Step 1: Ratios (EMAXAvg) ---
2025-06-18 20:11:40,103 - INFO - Ratios received:
SPY    1.027380
SHV    1.000017
EFA    1.030846
TLT    0.986024
PFF    1.011821
2025-06-18 20:11:40,103 - INFO - --- Step 2: Ranks ---
2025-06-18 20:11:40,105 - INFO - Ranks received:
  Asset  EMAXAvg_Value  Rank_Ordinal
0   EFA       1.030846             1
1   SPY       1.027380             2
2   PFF       1.011821             3
3   SHV       1.000017             4
4   TLT       0.986024             5
2025-06-18 20:11:40,106 - INFO - --- Step 3: Signal (Allocation) ---
2025-06-18 20:11:40,106 - INFO - Signal received:
SPY    0.4
SHV    0.0
EFA    0.6
TLT    0.0
PFF    0.0
2025-06-18 20:11:40,107 - INFO - --- Processing Backtest Engine for date: 2021-02-02 ---
2025-06-18 20:11:40,107 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine IMMEDIATELY AFTER conversion: <class 'dict'>
2025-06-18 20:11:40,107 - INFO - No orders from previous signals to execute for 2021-02-02
2025-06-18 20:11:40,107 - INFO - Generated 2 new orders based on today's signal for 2021-02-02
2025-06-18 20:11:40,107 - INFO - T+0 Execution: Processing 2 orders generated from today's signal.
2025-06-18 20:11:40,108 - INFO - Executed 2 T+0 trades from today's signal for 2021-02-02
2025-06-18 20:11:40,108 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine BEFORE mark_to_market: <class 'dict'>
2025-06-18 20:11:40,108 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine BEFORE direct get_weights call: <class 'dict'>
2025-06-18 20:11:40,109 - INFO - Final post-trade allocations for 2021-02-02:
EFA     0.599985
SPY     0.399974
Cash    0.000041
2025-06-18 20:11:40,109 - INFO - 
========== Tracing for Date: 2021-02-03 ==========
2025-06-18 20:11:40,109 - INFO - --- Calling Production Model with trace_mode=True ---
2025-06-18 20:11:40,118 - INFO - Applied ema allocation weights
2025-06-18 20:11:40,120 - INFO - --- Step 1: Ratios (EMAXAvg) ---
2025-06-18 20:11:40,121 - INFO - Ratios received:
SPY    1.027359
SHV    1.000025
EFA    1.030249
TLT    0.985335
PFF    1.011546
2025-06-18 20:11:40,121 - INFO - --- Step 2: Ranks ---
2025-06-18 20:11:40,123 - INFO - Ranks received:
  Asset  EMAXAvg_Value  Rank_Ordinal
0   EFA       1.030249             1
1   SPY       1.027359             2
2   PFF       1.011546             3
3   SHV       1.000025             4
4   TLT       0.985335             5
2025-06-18 20:11:40,123 - INFO - --- Step 3: Signal (Allocation) ---
2025-06-18 20:11:40,124 - INFO - Signal received:
SPY    0.4
SHV    0.0
EFA    0.6
TLT    0.0
PFF    0.0
2025-06-18 20:11:40,124 - INFO - --- Processing Backtest Engine for date: 2021-02-03 ---
2025-06-18 20:11:40,125 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine IMMEDIATELY AFTER conversion: <class 'dict'>
2025-06-18 20:11:40,125 - INFO - No orders from previous signals to execute for 2021-02-03
2025-06-18 20:11:40,125 - INFO - Generated 2 new orders based on today's signal for 2021-02-03
2025-06-18 20:11:40,125 - INFO - T+0 Execution: Processing 2 orders generated from today's signal.
2025-06-18 20:11:40,125 - INFO - Executed 2 T+0 trades from today's signal for 2021-02-03
2025-06-18 20:11:40,126 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine BEFORE mark_to_market: <class 'dict'>
2025-06-18 20:11:40,126 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine BEFORE direct get_weights call: <class 'dict'>
2025-06-18 20:11:40,127 - INFO - Final post-trade allocations for 2021-02-03:
EFA     0.599980
SPY     0.399944
Cash    0.000076
2025-06-18 20:11:40,127 - INFO - 
========== Tracing for Date: 2021-02-04 ==========
2025-06-18 20:11:40,127 - INFO - --- Calling Production Model with trace_mode=True ---
2025-06-18 20:11:40,136 - INFO - Applied ema allocation weights
2025-06-18 20:11:40,138 - INFO - --- Step 1: Ratios (EMAXAvg) ---
2025-06-18 20:11:40,139 - INFO - Ratios received:
SPY    1.027886
SHV    1.000027
EFA    1.029716
TLT    0.984642
PFF    1.011421
2025-06-18 20:11:40,139 - INFO - --- Step 2: Ranks ---
2025-06-18 20:11:40,141 - INFO - Ranks received:
  Asset  EMAXAvg_Value  Rank_Ordinal
0   EFA       1.029716             1
1   SPY       1.027886             2
2   PFF       1.011421             3
3   SHV       1.000027             4
4   TLT       0.984642             5
2025-06-18 20:11:40,141 - INFO - --- Step 3: Signal (Allocation) ---
2025-06-18 20:11:40,142 - INFO - Signal received:
SPY    0.4
SHV    0.0
EFA    0.6
TLT    0.0
PFF    0.0
2025-06-18 20:11:40,142 - INFO - --- Processing Backtest Engine for date: 2021-02-04 ---
2025-06-18 20:11:40,142 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine IMMEDIATELY AFTER conversion: <class 'dict'>
2025-06-18 20:11:40,143 - INFO - No orders from previous signals to execute for 2021-02-04
2025-06-18 20:11:40,143 - INFO - Generated 2 new orders based on today's signal for 2021-02-04
2025-06-18 20:11:40,143 - INFO - T+0 Execution: Processing 2 orders generated from today's signal.
2025-06-18 20:11:40,143 - INFO - Executed 2 T+0 trades from today's signal for 2021-02-04
2025-06-18 20:11:40,143 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine BEFORE mark_to_market: <class 'dict'>
2025-06-18 20:11:40,144 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine BEFORE direct get_weights call: <class 'dict'>
2025-06-18 20:11:40,145 - INFO - Final post-trade allocations for 2021-02-04:
EFA     0.599983
SPY     0.399833
Cash    0.000183
2025-06-18 20:11:40,145 - INFO - 
========== Tracing for Date: 2021-02-05 ==========
2025-06-18 20:11:40,145 - INFO - --- Calling Production Model with trace_mode=True ---
2025-06-18 20:11:40,154 - INFO - Applied ema allocation weights
2025-06-18 20:11:40,156 - INFO - --- Step 1: Ratios (EMAXAvg) ---
2025-06-18 20:11:40,157 - INFO - Ratios received:
SPY    1.028478
SHV    1.000033
EFA    1.029518
TLT    0.983655
PFF    1.011410
2025-06-18 20:11:40,157 - INFO - --- Step 2: Ranks ---
2025-06-18 20:11:40,158 - INFO - Ranks received:
  Asset  EMAXAvg_Value  Rank_Ordinal
0   EFA       1.029518             1
1   SPY       1.028478             2
2   PFF       1.011410             3
3   SHV       1.000033             4
4   TLT       0.983655             5
2025-06-18 20:11:40,159 - INFO - --- Step 3: Signal (Allocation) ---
2025-06-18 20:11:40,160 - INFO - Signal received:
SPY    0.4
SHV    0.0
EFA    0.6
TLT    0.0
PFF    0.0
2025-06-18 20:11:40,160 - INFO - --- Processing Backtest Engine for date: 2021-02-05 ---
2025-06-18 20:11:40,160 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine IMMEDIATELY AFTER conversion: <class 'dict'>
2025-06-18 20:11:40,160 - INFO - No orders from previous signals to execute for 2021-02-05
2025-06-18 20:11:40,160 - INFO - Generated 2 new orders based on today's signal for 2021-02-05
2025-06-18 20:11:40,161 - INFO - T+0 Execution: Processing 2 orders generated from today's signal.
2025-06-18 20:11:40,161 - INFO - Executed 2 T+0 trades from today's signal for 2021-02-05
2025-06-18 20:11:40,161 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine BEFORE mark_to_market: <class 'dict'>
2025-06-18 20:11:40,161 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine BEFORE direct get_weights call: <class 'dict'>
2025-06-18 20:11:40,162 - INFO - Final post-trade allocations for 2021-02-05:
EFA     0.599991
SPY     0.399661
Cash    0.000348
2025-06-18 20:11:40,162 - INFO - 
========== Tracing for Date: 2021-02-08 ==========
2025-06-18 20:11:40,162 - INFO - --- Calling Production Model with trace_mode=True ---
2025-06-18 20:11:40,171 - INFO - Applied ema allocation weights
2025-06-18 20:11:40,173 - INFO - --- Step 1: Ratios (EMAXAvg) ---
2025-06-18 20:11:40,174 - INFO - Ratios received:
SPY    1.029305
SHV    1.000033
EFA    1.029648
TLT    0.983068
PFF    1.011428
2025-06-18 20:11:40,174 - INFO - --- Step 2: Ranks ---
2025-06-18 20:11:40,176 - INFO - Ranks received:
  Asset  EMAXAvg_Value  Rank_Ordinal
0   EFA       1.029648             1
1   SPY       1.029305             2
2   PFF       1.011428             3
3   SHV       1.000033             4
4   TLT       0.983068             5
2025-06-18 20:11:40,176 - INFO - --- Step 3: Signal (Allocation) ---
2025-06-18 20:11:40,177 - INFO - Signal received:
SPY    0.4
SHV    0.0
EFA    0.6
TLT    0.0
PFF    0.0
2025-06-18 20:11:40,177 - INFO - --- Processing Backtest Engine for date: 2021-02-08 ---
2025-06-18 20:11:40,177 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine IMMEDIATELY AFTER conversion: <class 'dict'>
2025-06-18 20:11:40,177 - INFO - No orders from previous signals to execute for 2021-02-08
2025-06-18 20:11:40,177 - INFO - Generated 1 new orders based on today's signal for 2021-02-08
2025-06-18 20:11:40,177 - INFO - T+0 Execution: Processing 1 orders generated from today's signal.
2025-06-18 20:11:40,178 - INFO - Executed 1 T+0 trades from today's signal for 2021-02-08
2025-06-18 20:11:40,178 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine BEFORE mark_to_market: <class 'dict'>
2025-06-18 20:11:40,178 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine BEFORE direct get_weights call: <class 'dict'>
2025-06-18 20:11:40,179 - INFO - Final post-trade allocations for 2021-02-08:
EFA     0.599997
SPY     0.399724
Cash    0.000279
2025-06-18 20:11:40,179 - INFO - 
========== Tracing for Date: 2021-02-09 ==========
2025-06-18 20:11:40,179 - INFO - --- Calling Production Model with trace_mode=True ---
2025-06-18 20:11:40,185 - INFO - Applied ema allocation weights
2025-06-18 20:11:40,191 - INFO - --- Step 1: Ratios (EMAXAvg) ---
2025-06-18 20:11:40,191 - INFO - Ratios received:
SPY    1.029898
SHV    1.000039
EFA    1.029979
TLT    0.982640
PFF    1.011457
2025-06-18 20:11:40,191 - INFO - --- Step 2: Ranks ---
2025-06-18 20:11:40,192 - INFO - Ranks received:
  Asset  EMAXAvg_Value  Rank_Ordinal
0   EFA       1.029979             1
1   SPY       1.029898             2
2   PFF       1.011457             3
3   SHV       1.000039             4
4   TLT       0.982640             5
2025-06-18 20:11:40,193 - INFO - --- Step 3: Signal (Allocation) ---
2025-06-18 20:11:40,194 - INFO - Signal received:
SPY    0.4
SHV    0.0
EFA    0.6
TLT    0.0
PFF    0.0
2025-06-18 20:11:40,194 - INFO - --- Processing Backtest Engine for date: 2021-02-09 ---
2025-06-18 20:11:40,194 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine IMMEDIATELY AFTER conversion: <class 'dict'>
2025-06-18 20:11:40,194 - INFO - No orders from previous signals to execute for 2021-02-09
2025-06-18 20:11:40,194 - INFO - Generated 2 new orders based on today's signal for 2021-02-09
2025-06-18 20:11:40,194 - INFO - T+0 Execution: Processing 2 orders generated from today's signal.
2025-06-18 20:11:40,195 - INFO - Executed 2 T+0 trades from today's signal for 2021-02-09
2025-06-18 20:11:40,195 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine BEFORE mark_to_market: <class 'dict'>
2025-06-18 20:11:40,195 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine BEFORE direct get_weights call: <class 'dict'>
2025-06-18 20:11:40,196 - INFO - Final post-trade allocations for 2021-02-09:
EFA     0.599995
SPY     0.399723
Cash    0.000282
2025-06-18 20:11:40,196 - INFO - 
========== Tracing for Date: 2021-02-10 ==========
2025-06-18 20:11:40,196 - INFO - --- Calling Production Model with trace_mode=True ---
2025-06-18 20:11:40,202 - INFO - Applied ema allocation weights
2025-06-18 20:11:40,204 - INFO - --- Step 1: Ratios (EMAXAvg) ---
2025-06-18 20:11:40,205 - INFO - Ratios received:
SPY    1.030300
SHV    1.000043
EFA    1.029994
TLT    0.982652
PFF    1.011425
2025-06-18 20:11:40,205 - INFO - --- Step 2: Ranks ---
2025-06-18 20:11:40,206 - INFO - Ranks received:
  Asset  EMAXAvg_Value  Rank_Ordinal
0   SPY       1.030300             1
1   EFA       1.029994             2
2   PFF       1.011425             3
3   SHV       1.000043             4
4   TLT       0.982652             5
2025-06-18 20:11:40,206 - INFO - --- Step 3: Signal (Allocation) ---
2025-06-18 20:11:40,207 - INFO - Signal received:
SPY    0.6
SHV    0.0
EFA    0.4
TLT    0.0
PFF    0.0
2025-06-18 20:11:40,207 - INFO - --- Processing Backtest Engine for date: 2021-02-10 ---
2025-06-18 20:11:40,207 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine IMMEDIATELY AFTER conversion: <class 'dict'>
2025-06-18 20:11:40,208 - INFO - No orders from previous signals to execute for 2021-02-10
2025-06-18 20:11:40,208 - INFO - Generated 2 new orders based on today's signal for 2021-02-10
2025-06-18 20:11:40,208 - INFO - T+0 Execution: Processing 2 orders generated from today's signal.
2025-06-18 20:11:40,208 - INFO - Executed 2 T+0 trades from today's signal for 2021-02-10
2025-06-18 20:11:40,208 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine BEFORE mark_to_market: <class 'dict'>
2025-06-18 20:11:40,209 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine BEFORE direct get_weights call: <class 'dict'>
2025-06-18 20:11:40,210 - INFO - Final post-trade allocations for 2021-02-10:
EFA     0.400105
SPY     0.599139
Cash    0.000756
2025-06-18 20:11:40,210 - INFO - 
========== Tracing for Date: 2021-02-11 ==========
2025-06-18 20:11:40,210 - INFO - --- Calling Production Model with trace_mode=True ---
2025-06-18 20:11:40,217 - INFO - Applied ema allocation weights
2025-06-18 20:11:40,218 - INFO - --- Step 1: Ratios (EMAXAvg) ---
2025-06-18 20:11:40,219 - INFO - Ratios received:
SPY    1.030653
SHV    1.000042
EFA    1.030297
TLT    0.982423
PFF    1.011242
2025-06-18 20:11:40,219 - INFO - --- Step 2: Ranks ---
2025-06-18 20:11:40,220 - INFO - Ranks received:
  Asset  EMAXAvg_Value  Rank_Ordinal
0   SPY       1.030653             1
1   EFA       1.030297             2
2   PFF       1.011242             3
3   SHV       1.000042             4
4   TLT       0.982423             5
2025-06-18 20:11:40,220 - INFO - --- Step 3: Signal (Allocation) ---
2025-06-18 20:11:40,221 - INFO - Signal received:
SPY    0.6
SHV    0.0
EFA    0.4
TLT    0.0
PFF    0.0
2025-06-18 20:11:40,221 - INFO - --- Processing Backtest Engine for date: 2021-02-11 ---
2025-06-18 20:11:40,221 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine IMMEDIATELY AFTER conversion: <class 'dict'>
2025-06-18 20:11:40,221 - INFO - No orders from previous signals to execute for 2021-02-11
2025-06-18 20:11:40,222 - INFO - Generated 2 new orders based on today's signal for 2021-02-11
2025-06-18 20:11:40,222 - INFO - T+0 Execution: Processing 2 orders generated from today's signal.
2025-06-18 20:11:40,222 - INFO - Executed 2 T+0 trades from today's signal for 2021-02-11
2025-06-18 20:11:40,222 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine BEFORE mark_to_market: <class 'dict'>
2025-06-18 20:11:40,222 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine BEFORE direct get_weights call: <class 'dict'>
2025-06-18 20:11:40,223 - INFO - Final post-trade allocations for 2021-02-11:
EFA     0.400003
SPY     0.599736
Cash    0.000261
2025-06-18 20:11:40,223 - INFO - 
========== Tracing for Date: 2021-02-12 ==========
2025-06-18 20:11:40,223 - INFO - --- Calling Production Model with trace_mode=True ---
2025-06-18 20:11:40,230 - INFO - Applied ema allocation weights
2025-06-18 20:11:40,232 - INFO - --- Step 1: Ratios (EMAXAvg) ---
2025-06-18 20:11:40,232 - INFO - Ratios received:
SPY    1.031144
SHV    1.000041
EFA    1.030778
TLT    0.981632
PFF    1.010905
2025-06-18 20:11:40,232 - INFO - --- Step 2: Ranks ---
2025-06-18 20:11:40,233 - INFO - Ranks received:
  Asset  EMAXAvg_Value  Rank_Ordinal
0   SPY       1.031144             1
1   EFA       1.030778             2
2   PFF       1.010905             3
3   SHV       1.000041             4
4   TLT       0.981632             5
2025-06-18 20:11:40,234 - INFO - --- Step 3: Signal (Allocation) ---
2025-06-18 20:11:40,234 - INFO - Signal received:
SPY    0.6
SHV    0.0
EFA    0.4
TLT    0.0
PFF    0.0
2025-06-18 20:11:40,235 - INFO - --- Processing Backtest Engine for date: 2021-02-12 ---
2025-06-18 20:11:40,235 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine IMMEDIATELY AFTER conversion: <class 'dict'>
2025-06-18 20:11:40,235 - INFO - No orders from previous signals to execute for 2021-02-12
2025-06-18 20:11:40,235 - INFO - Generated 2 new orders based on today's signal for 2021-02-12
2025-06-18 20:11:40,235 - INFO - T+0 Execution: Processing 2 orders generated from today's signal.
2025-06-18 20:11:40,235 - INFO - Executed 2 T+0 trades from today's signal for 2021-02-12
2025-06-18 20:11:40,235 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine BEFORE mark_to_market: <class 'dict'>
2025-06-18 20:11:40,236 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine BEFORE direct get_weights call: <class 'dict'>
2025-06-18 20:11:40,236 - INFO - Final post-trade allocations for 2021-02-12:
EFA     0.400012
SPY     0.599960
Cash    0.000028
2025-06-18 20:11:40,236 - INFO - 
========== Tracing for Date: 2021-02-16 ==========
2025-06-18 20:11:40,237 - INFO - --- Calling Production Model with trace_mode=True ---
2025-06-18 20:11:40,243 - INFO - Applied ema allocation weights
2025-06-18 20:11:40,245 - INFO - --- Step 1: Ratios (EMAXAvg) ---
2025-06-18 20:11:40,245 - INFO - Ratios received:
SPY    1.031433
SHV    1.000039
EFA    1.031503
TLT    0.980223
PFF    1.010323
2025-06-18 20:11:40,246 - INFO - --- Step 2: Ranks ---
2025-06-18 20:11:40,247 - INFO - Ranks received:
  Asset  EMAXAvg_Value  Rank_Ordinal
0   EFA       1.031503             1
1   SPY       1.031433             2
2   PFF       1.010323             3
3   SHV       1.000039             4
4   TLT       0.980223             5
2025-06-18 20:11:40,247 - INFO - --- Step 3: Signal (Allocation) ---
2025-06-18 20:11:40,248 - INFO - Signal received:
SPY    0.4
SHV    0.0
EFA    0.6
TLT    0.0
PFF    0.0
2025-06-18 20:11:40,248 - INFO - --- Processing Backtest Engine for date: 2021-02-16 ---
2025-06-18 20:11:40,248 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine IMMEDIATELY AFTER conversion: <class 'dict'>
2025-06-18 20:11:40,248 - INFO - No orders from previous signals to execute for 2021-02-16
2025-06-18 20:11:40,248 - INFO - Generated 2 new orders based on today's signal for 2021-02-16
2025-06-18 20:11:40,248 - INFO - T+0 Execution: Processing 2 orders generated from today's signal.
2025-06-18 20:11:40,249 - INFO - Executed 2 T+0 trades from today's signal for 2021-02-16
2025-06-18 20:11:40,249 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine BEFORE mark_to_market: <class 'dict'>
2025-06-18 20:11:40,249 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine BEFORE direct get_weights call: <class 'dict'>
2025-06-18 20:11:40,249 - INFO - Final post-trade allocations for 2021-02-16:
EFA     0.599244
SPY     0.400219
Cash    0.000538
2025-06-18 20:11:40,250 - INFO - 
========== Tracing for Date: 2021-02-17 ==========
2025-06-18 20:11:40,250 - INFO - --- Calling Production Model with trace_mode=True ---
2025-06-18 20:11:40,256 - INFO - Applied ema allocation weights
2025-06-18 20:11:40,258 - INFO - --- Step 1: Ratios (EMAXAvg) ---
2025-06-18 20:11:40,258 - INFO - Ratios received:
SPY    1.031610
SHV    1.000038
EFA    1.031716
TLT    0.979374
PFF    1.009770
2025-06-18 20:11:40,258 - INFO - --- Step 2: Ranks ---
2025-06-18 20:11:40,259 - INFO - Ranks received:
  Asset  EMAXAvg_Value  Rank_Ordinal
0   EFA       1.031716             1
1   SPY       1.031610             2
2   PFF       1.009770             3
3   SHV       1.000038             4
4   TLT       0.979374             5
2025-06-18 20:11:40,260 - INFO - --- Step 3: Signal (Allocation) ---
2025-06-18 20:11:40,260 - INFO - Signal received:
SPY    0.4
SHV    0.0
EFA    0.6
TLT    0.0
PFF    0.0
2025-06-18 20:11:40,260 - INFO - --- Processing Backtest Engine for date: 2021-02-17 ---
2025-06-18 20:11:40,261 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine IMMEDIATELY AFTER conversion: <class 'dict'>
2025-06-18 20:11:40,261 - INFO - No orders from previous signals to execute for 2021-02-17
2025-06-18 20:11:40,261 - INFO - Generated 2 new orders based on today's signal for 2021-02-17
2025-06-18 20:11:40,261 - INFO - T+0 Execution: Processing 2 orders generated from today's signal.
2025-06-18 20:11:40,261 - INFO - Executed 2 T+0 trades from today's signal for 2021-02-17
2025-06-18 20:11:40,261 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine BEFORE mark_to_market: <class 'dict'>
2025-06-18 20:11:40,261 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine BEFORE direct get_weights call: <class 'dict'>
2025-06-18 20:11:40,262 - INFO - Final post-trade allocations for 2021-02-17:
EFA     0.599989
SPY     0.399858
Cash    0.000153
2025-06-18 20:11:40,262 - INFO - 
========== Tracing for Date: 2021-02-18 ==========
2025-06-18 20:11:40,262 - INFO - --- Calling Production Model with trace_mode=True ---
2025-06-18 20:11:40,269 - INFO - Applied ema allocation weights
2025-06-18 20:11:40,270 - INFO - --- Step 1: Ratios (EMAXAvg) ---
2025-06-18 20:11:40,271 - INFO - Ratios received:
SPY    1.031444
SHV    1.000038
EFA    1.031542
TLT    0.978488
PFF    1.009272
2025-06-18 20:11:40,271 - INFO - --- Step 2: Ranks ---
2025-06-18 20:11:40,272 - INFO - Ranks received:
  Asset  EMAXAvg_Value  Rank_Ordinal
0   EFA       1.031542             1
1   SPY       1.031444             2
2   PFF       1.009272             3
3   SHV       1.000038             4
4   TLT       0.978488             5
2025-06-18 20:11:40,272 - INFO - --- Step 3: Signal (Allocation) ---
2025-06-18 20:11:40,273 - INFO - Signal received:
SPY    0.4
SHV    0.0
EFA    0.6
TLT    0.0
PFF    0.0
2025-06-18 20:11:40,273 - INFO - --- Processing Backtest Engine for date: 2021-02-18 ---
2025-06-18 20:11:40,273 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine IMMEDIATELY AFTER conversion: <class 'dict'>
2025-06-18 20:11:40,273 - INFO - No orders from previous signals to execute for 2021-02-18
2025-06-18 20:11:40,273 - INFO - Generated 1 new orders based on today's signal for 2021-02-18
2025-06-18 20:11:40,273 - INFO - T+0 Execution: Processing 1 orders generated from today's signal.
2025-06-18 20:11:40,274 - INFO - Executed 1 T+0 trades from today's signal for 2021-02-18
2025-06-18 20:11:40,274 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine BEFORE mark_to_market: <class 'dict'>
2025-06-18 20:11:40,274 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine BEFORE direct get_weights call: <class 'dict'>
2025-06-18 20:11:40,275 - INFO - Final post-trade allocations for 2021-02-18:
EFA     0.599949
SPY     0.400031
Cash    0.000020
2025-06-18 20:11:40,275 - INFO - 
========== Tracing for Date: 2021-02-19 ==========
2025-06-18 20:11:40,275 - INFO - --- Calling Production Model with trace_mode=True ---
2025-06-18 20:11:40,282 - INFO - Applied ema allocation weights
2025-06-18 20:11:40,283 - INFO - --- Step 1: Ratios (EMAXAvg) ---
2025-06-18 20:11:40,284 - INFO - Ratios received:
SPY    1.031123
SHV    1.000037
EFA    1.031468
TLT    0.977106
PFF    1.008752
2025-06-18 20:11:40,284 - INFO - --- Step 2: Ranks ---
2025-06-18 20:11:40,285 - INFO - Ranks received:
  Asset  EMAXAvg_Value  Rank_Ordinal
0   EFA       1.031468             1
1   SPY       1.031123             2
2   PFF       1.008752             3
3   SHV       1.000037             4
4   TLT       0.977106             5
2025-06-18 20:11:40,285 - INFO - --- Step 3: Signal (Allocation) ---
2025-06-18 20:11:40,286 - INFO - Signal received:
SPY    0.4
SHV    0.0
EFA    0.6
TLT    0.0
PFF    0.0
2025-06-18 20:11:40,286 - INFO - --- Processing Backtest Engine for date: 2021-02-19 ---
2025-06-18 20:11:40,286 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine IMMEDIATELY AFTER conversion: <class 'dict'>
2025-06-18 20:11:40,286 - INFO - No orders from previous signals to execute for 2021-02-19
2025-06-18 20:11:40,287 - INFO - Generated 2 new orders based on today's signal for 2021-02-19
2025-06-18 20:11:40,287 - INFO - T+0 Execution: Processing 2 orders generated from today's signal.
2025-06-18 20:11:40,287 - INFO - Executed 2 T+0 trades from today's signal for 2021-02-19
2025-06-18 20:11:40,287 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine BEFORE mark_to_market: <class 'dict'>
2025-06-18 20:11:40,287 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine BEFORE direct get_weights call: <class 'dict'>
2025-06-18 20:11:40,288 - INFO - Final post-trade allocations for 2021-02-19:
EFA     0.599994
SPY     0.399643
Cash    0.000364
2025-06-18 20:11:40,288 - INFO - 
========== Tracing for Date: 2021-02-22 ==========
2025-06-18 20:11:40,288 - INFO - --- Calling Production Model with trace_mode=True ---
2025-06-18 20:11:40,295 - INFO - Applied ema allocation weights
2025-06-18 20:11:40,297 - INFO - --- Step 1: Ratios (EMAXAvg) ---
2025-06-18 20:11:40,298 - INFO - Ratios received:
SPY    1.030350
SHV    1.000036
EFA    1.031186
TLT    0.975590
PFF    1.008061
2025-06-18 20:11:40,298 - INFO - --- Step 2: Ranks ---
2025-06-18 20:11:40,299 - INFO - Ranks received:
  Asset  EMAXAvg_Value  Rank_Ordinal
0   EFA       1.031186             1
1   SPY       1.030350             2
2   PFF       1.008061             3
3   SHV       1.000036             4
4   TLT       0.975590             5
2025-06-18 20:11:40,299 - INFO - --- Step 3: Signal (Allocation) ---
2025-06-18 20:11:40,300 - INFO - Signal received:
SPY    0.4
SHV    0.0
EFA    0.6
TLT    0.0
PFF    0.0
2025-06-18 20:11:40,300 - INFO - --- Processing Backtest Engine for date: 2021-02-22 ---
2025-06-18 20:11:40,300 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine IMMEDIATELY AFTER conversion: <class 'dict'>
2025-06-18 20:11:40,300 - INFO - No orders from previous signals to execute for 2021-02-22
2025-06-18 20:11:40,300 - INFO - Generated 2 new orders based on today's signal for 2021-02-22
2025-06-18 20:11:40,301 - INFO - T+0 Execution: Processing 2 orders generated from today's signal.
2025-06-18 20:11:40,301 - INFO - Executed 2 T+0 trades from today's signal for 2021-02-22
2025-06-18 20:11:40,301 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine BEFORE mark_to_market: <class 'dict'>
2025-06-18 20:11:40,301 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine BEFORE direct get_weights call: <class 'dict'>
2025-06-18 20:11:40,302 - INFO - Final post-trade allocations for 2021-02-22:
EFA     0.599970
SPY     0.399842
Cash    0.000188
2025-06-18 20:11:40,302 - INFO - 
========== Tracing for Date: 2021-02-23 ==========
2025-06-18 20:11:40,302 - INFO - --- Calling Production Model with trace_mode=True ---
2025-06-18 20:11:40,309 - INFO - Applied ema allocation weights
2025-06-18 20:11:40,310 - INFO - --- Step 1: Ratios (EMAXAvg) ---
2025-06-18 20:11:40,311 - INFO - Ratios received:
SPY    1.029675
SHV    1.000035
EFA    1.030913
TLT    0.974197
PFF    1.007406
2025-06-18 20:11:40,311 - INFO - --- Step 2: Ranks ---
2025-06-18 20:11:40,312 - INFO - Ranks received:
  Asset  EMAXAvg_Value  Rank_Ordinal
0   EFA       1.030913             1
1   SPY       1.029675             2
2   PFF       1.007406             3
3   SHV       1.000035             4
4   TLT       0.974197             5
2025-06-18 20:11:40,313 - INFO - --- Step 3: Signal (Allocation) ---
2025-06-18 20:11:40,313 - INFO - Signal received:
SPY    0.4
SHV    0.0
EFA    0.6
TLT    0.0
PFF    0.0
2025-06-18 20:11:40,313 - INFO - --- Processing Backtest Engine for date: 2021-02-23 ---
2025-06-18 20:11:40,313 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine IMMEDIATELY AFTER conversion: <class 'dict'>
2025-06-18 20:11:40,314 - INFO - No orders from previous signals to execute for 2021-02-23
2025-06-18 20:11:40,314 - INFO - Generated 1 new orders based on today's signal for 2021-02-23
2025-06-18 20:11:40,314 - INFO - T+0 Execution: Processing 1 orders generated from today's signal.
2025-06-18 20:11:40,314 - INFO - Executed 1 T+0 trades from today's signal for 2021-02-23
2025-06-18 20:11:40,314 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine BEFORE mark_to_market: <class 'dict'>
2025-06-18 20:11:40,314 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine BEFORE direct get_weights call: <class 'dict'>
2025-06-18 20:11:40,315 - INFO - Final post-trade allocations for 2021-02-23:
EFA     0.599967
SPY     0.399913
Cash    0.000121
2025-06-18 20:11:40,315 - INFO - 
========== Tracing for Date: 2021-02-24 ==========
2025-06-18 20:11:40,315 - INFO - --- Calling Production Model with trace_mode=True ---
2025-06-18 20:11:40,322 - INFO - Applied ema allocation weights
2025-06-18 20:11:40,323 - INFO - --- Step 1: Ratios (EMAXAvg) ---
2025-06-18 20:11:40,324 - INFO - Ratios received:
SPY    1.029613
SHV    1.000034
EFA    1.030769
TLT    0.972731
PFF    1.006756
2025-06-18 20:11:40,324 - INFO - --- Step 2: Ranks ---
2025-06-18 20:11:40,326 - INFO - Ranks received:
  Asset  EMAXAvg_Value  Rank_Ordinal
0   EFA       1.030769             1
1   SPY       1.029613             2
2   PFF       1.006756             3
3   SHV       1.000034             4
4   TLT       0.972731             5
2025-06-18 20:11:40,326 - INFO - --- Step 3: Signal (Allocation) ---
2025-06-18 20:11:40,327 - INFO - Signal received:
SPY    0.4
SHV    0.0
EFA    0.6
TLT    0.0
PFF    0.0
2025-06-18 20:11:40,327 - INFO - --- Processing Backtest Engine for date: 2021-02-24 ---
2025-06-18 20:11:40,327 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine IMMEDIATELY AFTER conversion: <class 'dict'>
2025-06-18 20:11:40,327 - INFO - No orders from previous signals to execute for 2021-02-24
2025-06-18 20:11:40,328 - INFO - Generated 2 new orders based on today's signal for 2021-02-24
2025-06-18 20:11:40,328 - INFO - T+0 Execution: Processing 2 orders generated from today's signal.
2025-06-18 20:11:40,328 - INFO - Executed 2 T+0 trades from today's signal for 2021-02-24
2025-06-18 20:11:40,328 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine BEFORE mark_to_market: <class 'dict'>
2025-06-18 20:11:40,328 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine BEFORE direct get_weights call: <class 'dict'>
2025-06-18 20:11:40,329 - INFO - Final post-trade allocations for 2021-02-24:
EFA     0.599968
SPY     0.399965
Cash    0.000067
2025-06-18 20:11:40,329 - INFO - 
========== Tracing for Date: 2021-02-25 ==========
2025-06-18 20:11:40,329 - INFO - --- Calling Production Model with trace_mode=True ---
2025-06-18 20:11:40,336 - INFO - Applied ema allocation weights
2025-06-18 20:11:40,337 - INFO - --- Step 1: Ratios (EMAXAvg) ---
2025-06-18 20:11:40,338 - INFO - Ratios received:
SPY    1.028172
SHV    1.000029
EFA    1.029640
TLT    0.970723
PFF    1.005763
2025-06-18 20:11:40,338 - INFO - --- Step 2: Ranks ---
2025-06-18 20:11:40,339 - INFO - Ranks received:
  Asset  EMAXAvg_Value  Rank_Ordinal
0   EFA       1.029640             1
1   SPY       1.028172             2
2   PFF       1.005763             3
3   SHV       1.000029             4
4   TLT       0.970723             5
2025-06-18 20:11:40,340 - INFO - --- Step 3: Signal (Allocation) ---
2025-06-18 20:11:40,341 - INFO - Signal received:
SPY    0.4
SHV    0.0
EFA    0.6
TLT    0.0
PFF    0.0
2025-06-18 20:11:40,341 - INFO - --- Processing Backtest Engine for date: 2021-02-25 ---
2025-06-18 20:11:40,341 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine IMMEDIATELY AFTER conversion: <class 'dict'>
2025-06-18 20:11:40,341 - INFO - No orders from previous signals to execute for 2021-02-25
2025-06-18 20:11:40,342 - INFO - Generated 2 new orders based on today's signal for 2021-02-25
2025-06-18 20:11:40,342 - INFO - T+0 Execution: Processing 2 orders generated from today's signal.
2025-06-18 20:11:40,342 - INFO - Executed 2 T+0 trades from today's signal for 2021-02-25
2025-06-18 20:11:40,342 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine BEFORE mark_to_market: <class 'dict'>
2025-06-18 20:11:40,342 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine BEFORE direct get_weights call: <class 'dict'>
2025-06-18 20:11:40,343 - INFO - Final post-trade allocations for 2021-02-25:
EFA     0.600020
SPY     0.399687
Cash    0.000293
2025-06-18 20:11:40,343 - INFO - 
========== Tracing for Date: 2021-02-26 ==========
2025-06-18 20:11:40,343 - INFO - --- Calling Production Model with trace_mode=True ---
2025-06-18 20:11:40,349 - INFO - Applied ema allocation weights
2025-06-18 20:11:40,351 - INFO - --- Step 1: Ratios (EMAXAvg) ---
2025-06-18 20:11:40,352 - INFO - Ratios received:
SPY    1.026591
SHV    1.000028
EFA    1.027841
TLT    0.970668
PFF    1.005204
2025-06-18 20:11:40,352 - INFO - --- Step 2: Ranks ---
2025-06-18 20:11:40,353 - INFO - Ranks received:
  Asset  EMAXAvg_Value  Rank_Ordinal
0   EFA       1.027841             1
1   SPY       1.026591             2
2   PFF       1.005204             3
3   SHV       1.000028             4
4   TLT       0.970668             5
2025-06-18 20:11:40,353 - INFO - --- Step 3: Signal (Allocation) ---
2025-06-18 20:11:40,354 - INFO - Signal received:
SPY    0.4
SHV    0.0
EFA    0.6
TLT    0.0
PFF    0.0
2025-06-18 20:11:40,354 - INFO - --- Processing Backtest Engine for date: 2021-02-26 ---
2025-06-18 20:11:40,354 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine IMMEDIATELY AFTER conversion: <class 'dict'>
2025-06-18 20:11:40,354 - INFO - No orders from previous signals to execute for 2021-02-26
2025-06-18 20:11:40,354 - INFO - Generated 2 new orders based on today's signal for 2021-02-26
2025-06-18 20:11:40,354 - INFO - T+0 Execution: Processing 2 orders generated from today's signal.
2025-06-18 20:11:40,355 - INFO - Executed 2 T+0 trades from today's signal for 2021-02-26
2025-06-18 20:11:40,355 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine BEFORE mark_to_market: <class 'dict'>
2025-06-18 20:11:40,355 - INFO - Trace Script DIAGNOSTIC: Type of current_prices_for_engine BEFORE direct get_weights call: <class 'dict'>
2025-06-18 20:11:40,356 - INFO - Final post-trade allocations for 2021-02-26:
EFA     0.599921
SPY     0.400034
Cash    0.000045
2025-06-18 20:11:40,356 - INFO - 
Aggregating and saving trace files...
2025-06-18 20:11:40,356 - INFO - 
Aggregating and saving trace files...
2025-06-18 20:11:40,362 - INFO - Full history ratios saved to: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\test_logs\trace_ratios_20210120_20210228_20250618_201139.csv
2025-06-18 20:11:40,362 - INFO - Full history ratios saved to: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\test_logs\trace_ratios_20210120_20210228_20250618_201139.csv
2025-06-18 20:11:40,362 - INFO - Full history ratios saved to: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\test_logs\trace_ratios_20210120_20210228_20250618_201139.csv
2025-06-18 20:11:40,405 - INFO - Ranks matrix saved to: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\test_logs\trace_ranks_20210120_20210228_20250618_201139.csv
2025-06-18 20:11:40,405 - INFO - Ranks matrix saved to: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\test_logs\trace_ranks_20210120_20210228_20250618_201139.csv
2025-06-18 20:11:40,405 - INFO - Ranks matrix saved to: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\test_logs\trace_ranks_20210120_20210228_20250618_201139.csv
2025-06-18 20:11:40,410 - INFO - Signal allocations saved to: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\test_logs\trace_signal_allocation_20210120_20210228_20250618_201139.csv
2025-06-18 20:11:40,410 - INFO - Signal allocations saved to: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\test_logs\trace_signal_allocation_20210120_20210228_20250618_201139.csv
2025-06-18 20:11:40,410 - INFO - Signal allocations saved to: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\test_logs\trace_signal_allocation_20210120_20210228_20250618_201139.csv
2025-06-18 20:11:40,414 - INFO - Post-trade allocations saved to: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\test_logs\trace_post_trade_allocations_20210120_20210228_20250618_201139.csv
2025-06-18 20:11:40,414 - INFO - Post-trade allocations saved to: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\test_logs\trace_post_trade_allocations_20210120_20210228_20250618_201139.csv
2025-06-18 20:11:40,414 - INFO - Post-trade allocations saved to: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\test_logs\trace_post_trade_allocations_20210120_20210228_20250618_201139.csv
2025-06-18 20:11:40,414 - INFO - 
Detailed trace log saved to: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\test_logs\signal_breakdown_trace_20210120_20210228_20250618_201139.txt
2025-06-18 20:11:40,414 - INFO - 
Detailed trace log saved to: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\test_logs\signal_breakdown_trace_20210120_20210228_20250618_201139.txt
2025-06-18 20:11:40,414 - INFO - Trace script finished.
2025-06-18 20:11:40,414 - INFO - EMA signal breakdown trace finished.
2025-06-18 20:11:40,414 - INFO - EMA signal breakdown trace finished.
2025-06-18 20:11:40,414 - INFO - EMA signal breakdown trace finished.
