#!/usr/bin/env python
# -*- coding: utf-8 -*-
# debug_allocation_flow.py
"""
Debug script for tracing allocation history data flow in the backtest engine.
This script explicitly logs each step of the allocation data flow process.

Author: AI Assistant
Date: 2025-06-14
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
from pathlib import Path
import json

# Configure logging with more detailed format
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Add parent directory to path to import modules
project_root = Path(__file__).parent.parent.parent
if str(project_root) not in sys.path:
    sys.path.append(str(project_root))

# Import backtest engine and related modules
from v4.engine.backtest_v4 import BacktestEngine
from v4.engine.signal_generator_v4 import generate_signals, create_signal_generator
from v4.engine.portfolio_v4 import Portfolio

def generate_test_data(start_date='2022-01-01', end_date='2022-12-31', symbols=None):
    """Generate synthetic price data for testing."""
    if symbols is None:
        symbols = ['AAPL', 'MSFT', 'AMZN', 'GOOGL']
    
    # Create date range
    dates = pd.date_range(start=start_date, end=end_date, freq='B')
    
    # Create price data with random walks
    np.random.seed(42)  # For reproducibility
    price_data = pd.DataFrame(index=dates)
    
    for symbol in symbols:
        # Start with 100
        prices = [100]
        # Generate random daily returns
        for _ in range(1, len(dates)):
            daily_return = np.random.normal(0.0005, 0.015)  # Mean and std dev
            prices.append(prices[-1] * (1 + daily_return))
        
        price_data[symbol] = prices
    
    return price_data

def create_debug_output_dir():
    """Create output directory for debug files."""
    output_dir = Path(__file__).parent / "debug_output"
    output_dir.mkdir(exist_ok=True)
    return output_dir

def save_to_json(data, filename, output_dir):
    """Save data to JSON file."""
    filepath = output_dir / filename
    
    # Convert data to serializable format if needed
    if isinstance(data, pd.DataFrame):
        data_dict = data.to_dict(orient='index')
        # Convert index to strings if they're dates
        if isinstance(data.index, pd.DatetimeIndex):
            data_dict = {str(k): v for k, v in data_dict.items()}
    elif isinstance(data, pd.Series):
        data_dict = data.to_dict()
    else:
        data_dict = data
    
    with open(filepath, 'w') as f:
        json.dump(data_dict, f, indent=2, default=str)
    
    logger.info(f"Saved {filename} to {filepath}")
    return filepath

def debug_backtest_run():
    """Run a backtest with explicit tracing of allocation history."""
    logger.info("Starting debug backtest run with allocation history tracing")
    
    # Create output directory
    output_dir = create_debug_output_dir()
    
    # Generate test data
    price_data = generate_test_data()
    logger.info(f"Generated test data with {len(price_data)} dates and {len(price_data.columns)} symbols")
    
    # Save price data for reference
    save_to_json(price_data, "price_data.json", output_dir)
    
    # Override settings for testing
    from v4.settings.settings_CPS_v4 import load_settings, save_settings
    settings = load_settings()
    
    # Update settings for test
    settings['backtest']['rebalance_freq'] = 'M'  # Monthly rebalancing
    settings['backtest']['execution_delay'] = 1    # 1-day execution delay
    save_settings(settings)
    
    # Create backtest engine with debug logging
    engine = BacktestEngine()
    
    # Create a signal generator function that logs signals
    def debug_signal_generator(price_data, **kwargs):
        logger.info("Generating signals with debug logging")
        signals = generate_signals(price_data, strategy='ema', **kwargs)
        logger.info(f"Generated signals shape: {signals.shape}")
        logger.info(f"Signal sample (first 3 dates):\n{signals.head(3)}")
        save_to_json(signals, "generated_signals.json", output_dir)
        return signals
    
    # Run backtest with signal tracking
    logger.info("Running backtest with signal tracking")
    results = engine.run_backtest(
        price_data=price_data,
        signal_generator=debug_signal_generator,
        strategy='ema'
    )
    
    # Extract and log key components
    logger.info("Extracting and logging key components from results")
    
    # 1. Signal History
    signal_history = results['signal_history']
    logger.info(f"Signal history shape: {signal_history.shape}")
    logger.info(f"Signal history sample:\n{signal_history.head(3)}")
    save_to_json(signal_history, "signal_history.json", output_dir)
    
    # 2. Weights History
    weights_history = results['weights_history']
    logger.info(f"Weights history shape: {weights_history.shape}")
    logger.info(f"Weights history sample:\n{weights_history.head(3)}")
    save_to_json(weights_history, "weights_history.json", output_dir)
    
    # 3. Position History
    position_history = results['position_history']
    logger.info(f"Position history shape: {position_history.shape}")
    logger.info(f"Position history sample:\n{position_history.head(3)}")
    save_to_json(position_history, "position_history.json", output_dir)
    
    # 4. Trade Log
    trade_log = results['trade_log']
    logger.info(f"Trade log shape: {trade_log.shape}")
    logger.info(f"Trade log sample:\n{trade_log.head(3)}")
    save_to_json(trade_log, "trade_log.json", output_dir)
    
    # Compare signal vs weights history
    logger.info("Comparing signal history vs weights history")
    
    # Check if weights_history and signal_history are different
    if weights_history.equals(signal_history):
        logger.warning("⚠️ ISSUE DETECTED: Weights history equals signal history")
        logger.warning("This suggests allocation history is not being properly preserved")
    else:
        logger.info("✅ Weights history differs from signal history")
        logger.info("This suggests allocation history is being properly preserved")
    
    # Check specific dates for detailed comparison
    sample_dates = weights_history.index[::20]  # Every 20th date
    comparison_data = []
    
    for date in sample_dates:
        weights = weights_history.loc[date] if date in weights_history.index else pd.Series()
        signals = signal_history.loc[date] if date in signal_history.index else pd.Series()
        
        comparison = {
            'date': str(date),
            'weights': weights.to_dict() if not weights.empty else {},
            'signals': signals.to_dict() if not signals.empty else {},
            'match': weights.equals(signals) if not weights.empty and not signals.empty else False
        }
        comparison_data.append(comparison)
        
        logger.info(f"\nDate: {date}")
        logger.info(f"Weights: {weights.to_dict() if not weights.empty else 'No weights'}")
        logger.info(f"Signals: {signals.to_dict() if not signals.empty else 'No signals'}")
        
    # Save comparison data
    save_to_json(comparison_data, "signal_vs_weights_comparison.json", output_dir)
    
    # Check for allocation history in _calculate_results method
    logger.info("Checking allocation history handling in _calculate_results method")
    
    # Extract the relevant code section for reference
    code_section = """
    # From BacktestEngine._calculate_results:
    
    # If weights_history is None, use signal_history as a fallback
    if weights_history is None:
        logger.warning("No weights_history provided, using signal_history as fallback")
        print("WARNING: No weights_history provided, using signal_history as fallback")
        weights_history = signal_history.copy() if signal_history is not None else None
    else:
        logger.info("Using provided weights_history for results")
        print("Using provided weights_history for results")
        print(f"Weights history shape: {weights_history.shape}")
        print(f"Weights history sample:\\n{weights_history.head()}")
        
    # Ensure weights_history has the same dates as trades for proper alignment
    if weights_history is not None and len(trades) > 0:
        print(f"Processing weights_history for {len(trades)} trades")
        executed_weights = weights_history.copy()
        
        # Forward fill values to show continuous allocation
        executed_weights = executed_weights.ffill()
        print("Forward-filled weights_history to show continuous allocation")
        
        # Store current weights in weights_history
        current_weights = portfolio.get_weights()
        weights_history.loc[current_date] = current_weights
    """
    
    logger.info("Reference code section for _calculate_results method:")
    logger.info(code_section)
    
    # Return results for further analysis
    return results, output_dir

if __name__ == "__main__":
    print("=====================================================")
    print("ALLOCATION HISTORY DEBUG TRACE")
    print(f"Run Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=====================================================\n")
    
    results, output_dir = debug_backtest_run()
    
    print("\n=====================================================")
    print("DEBUG TRACE COMPLETED")
    print(f"Output files saved to: {output_dir}")
    print("=====================================================\n")
