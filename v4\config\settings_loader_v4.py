# s:/Dropbox/Scott Only Internal/Quant_Python_24/Backtest_FinAsset_Alloc_Template/v4/config/settings_loader_v4.py
"""
Centralized settings loader for CPS V4.

This module provides functions to load, parse, and cache settings 
from the settings_parameters_v4.ini file.
"""

import configparser
import os
from ast import literal_eval

# Assuming paths.py is in the parent directory of v4 (i.e., project_root/config/paths.py)
# and that project_root is added to sys.path by the calling script.
from config.paths import get_config_path # To be confirmed if this path is correct

_cached_settings = None

def load_settings(ini_file_name: str = 'settings_parameters_v4.ini', force_reload: bool = False) -> configparser.ConfigParser:
    """
    Loads settings from the specified INI file.

    Args:
        ini_file_name (str): The name of the INI file to load.
        force_reload (bool): If True, forces a reload of the settings, bypassing the cache.

    Returns:
        configparser.ConfigParser: The loaded settings.

    Raises:
        FileNotFoundError: If the INI file cannot be found.
        configparser.Error: If there's an error parsing the INI file.
    """
    global _cached_settings

    # DISABLE CACHING DURING OPTIMIZATION - ALWAYS FORCE RELOAD TO PREVENT FALLBACK TO CACHED VALUES
    # This ensures each optimization combination gets fresh settings
    if False:  # Disabled: not force_reload and _cached_settings is not None:
        return _cached_settings

    try:
        # Use paths.py to get the project root and construct the path to the ini file.
        from config.paths import get_project_root
        project_root = get_project_root()
        # The location is confirmed to be 'v4/settings/settings_parameters_v4.ini'
        ini_file_path = os.path.join(project_root, 'v4', 'settings', ini_file_name)
    except ImportError as e:
        raise ImportError(f"Could not import get_project_root from config.paths. Ensure sys.path is correct. Original error: {e}")
    except Exception as e:
        raise RuntimeError(f"An unexpected error occurred while constructing the settings file path: {e}")

    if not os.path.exists(ini_file_path):
        raise FileNotFoundError(f"Settings file not found at the expected path: '{ini_file_path}'. "
                                f"Please ensure '{ini_file_name}' exists in the 'v4/settings/' directory.")

    config = configparser.ConfigParser()
    try:
        config.read(ini_file_path)
        if not config.sections():
            raise configparser.Error(f"The settings file '{ini_file_path}' is empty or not a valid INI file.")
    except configparser.Error as e:
        raise configparser.Error(f"Error parsing settings file '{ini_file_path}': {e}")

    _cached_settings = config
    return config

def get_setting(section: str, key: str, default=None, expected_type=None, settings_file: str = 'settings_parameters_v4.ini'):
    """
    Retrieves a specific setting value, with type conversion and default value handling.

    Args:
        section (str): The section in the INI file.
        key (str): The key within the section.
        default: The default value to return if the key is not found.
        expected_type (type): The expected type of the value (e.g., int, float, bool, list, tuple).
                               If None, the value is returned as a string.
        settings_file (str): The name of the settings INI file.

    Returns:
        The setting value, converted to expected_type if specified, or the default value.
    """
    config = load_settings(settings_file)

    if not config.has_section(section):
        if default is not None:
            return default
        raise KeyError(f"Section '{section}' not found in settings file '{settings_file}'.")

    if not config.has_option(section, key):
        if default is not None:
            return default
        raise KeyError(f"Key '{key}' not found in section '{section}' of settings file '{settings_file}'.")

    value_str = config.get(section, key)

    if expected_type is None:
        return value_str

    try:
        if expected_type == bool:
            if value_str.lower() in ['true', 'yes', '1', 'on']:
                return True
            elif value_str.lower() in ['false', 'no', '0', 'off']:
                return False
            else:
                raise ValueError(f"Cannot convert '{value_str}' to bool for {section}.{key}")
        elif expected_type in [list, tuple]:
            # literal_eval can safely evaluate strings containing Python literals
            # (strings, numbers, tuples, lists, dicts, booleans, None)
            return expected_type(literal_eval(value_str))
        else:
            return expected_type(value_str)
    except ValueError as e:
        raise ValueError(f"Error converting value '{value_str}' for {section}.{key} to {expected_type}: {e}")
    except SyntaxError as e:
        # This can happen if literal_eval tries to parse something not a valid literal, e.g. unquoted string in a list
        raise ValueError(f"Error parsing value '{value_str}' for {section}.{key} as {expected_type} (likely malformed list/tuple string): {e}")


if __name__ == '__main__':
    # Example usage and basic test
    try:
        # This assumes the script is run in an environment where paths.py can be found
        # and settings_parameters_v4.ini is accessible.
        # Adjust sys.path if running this file directly for testing and paths.py is not found.
        import sys
        # Example: Add project root to sys.path if not already there
        # This path adjustment is for direct script execution testing only.
        # The main application should handle sys.path configuration.
        current_dir = os.path.dirname(os.path.abspath(__file__))
        project_root_dir = os.path.abspath(os.path.join(current_dir, '..', '..')) 
        if project_root_dir not in sys.path:
            sys.path.insert(0, project_root_dir)
        
        # Re-check import after path adjustment if necessary
        from config.paths import get_config_path, get_project_root
        print(f"Project root (from paths.py): {get_project_root()}")
        print(f"Config path (from paths.py): {get_config_path()}")

        settings = load_settings()
        print("Successfully loaded settings.")
        print("Sections found:", settings.sections())

        # Example: Retrieve some common parameters
        # These are examples, actual parameters might differ
        initial_capital = get_setting('CORE_PARAMETERS', 'initial_capital', default=100000, expected_type=float)
        print(f"Initial Capital: {initial_capital} (type: {type(initial_capital)})")

        tickers_str = get_setting('DATA_PARAMETERS', 'tickers', default='("SPY",)', expected_type=str)
        tickers_tuple = get_setting('DATA_PARAMETERS', 'tickers', default=("SPY",), expected_type=tuple)
        print(f"Tickers (str): {tickers_str} (type: {type(tickers_str)})")
        print(f"Tickers (tuple): {tickers_tuple} (type: {type(tickers_tuple)})")
        
        st_lookback = get_setting('EMA_STRATEGY_PARAMETERS', 'st_lookback', default=15, expected_type=int)
        print(f"ST Lookback: {st_lookback} (type: {type(st_lookback)})")

        # Test a boolean
        # Assuming a hypothetical boolean parameter for testing
        # Add this to your INI for testing: [GUI_PARAMETERS] use_dark_mode = True
        # use_dark_mode = get_setting('GUI_PARAMETERS', 'use_dark_mode', default=False, expected_type=bool)
        # print(f"Use Dark Mode: {use_dark_mode} (type: {type(use_dark_mode)})")

        # Test non-existent section/key with default
        non_existent_val = get_setting('NON_EXISTENT_SECTION', 'some_key', default='default_value')
        print(f"Non-existent value with default: {non_existent_val}")

        # Test non-existent key without default (should raise KeyError)
        try:
            get_setting('CORE_PARAMETERS', 'non_existent_key_no_default')
        except KeyError as e:
            print(f"Caught expected error for non-existent key: {e}")

    except FileNotFoundError as e:
        print(f"ERROR - FileNotFoundError: {e}")
        print("Please ensure 'settings_parameters_v4.ini' is correctly placed and paths.py is configured.")
    except (configparser.Error, ValueError, KeyError) as e:
        print(f"ERROR: {e}")
    except ImportError as e:
        print(f"ERROR - ImportError: {e}. This might be due to paths.py not being found. Ensure correct sys.path.")

