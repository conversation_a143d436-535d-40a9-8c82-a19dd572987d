import chardet

# Read the file with proper encoding detection
with open('docs/CPS_v4/optimization_flow.md', 'rb') as f:
    raw_data = f.read()
    encoding = chardet.detect(raw_data)['encoding']
    print(f'Detected encoding: {encoding}')
    
# Read content
with open('docs/CPS_v4/optimization_flow.md', 'r', encoding=encoding) as f:
    content = f.read()

# Apply fixes for MarkText compatibility
fixes = [
    ('graph TB', 'graph TD'),
    ('-- ', '--> '),
    ('--|', '-->|'),
]

for old, new in fixes:
    content = content.replace(old, new)

# Fix subgraph syntax
content = content.replace('subgraph "Example Parameter"', 'subgraph Example["Example Parameter"]')
content = content.replace('subgraph "For Each Combination"', 'subgraph Combo["For Each Combination"]')  
content = content.replace('subgraph "Parameter Replacement Process"', 'subgraph Params["Parameter Replacement Process"]')
content = content.replace('subgraph "Parameter Resolution"', 'subgraph Resolution["Parameter Resolution"]')
content = content.replace('subgraph "Performance Metrics per Combination"', 'subgraph Metrics["Performance Metrics per Combination"]')
content = content.replace("r'default_value=\\\\g<1>'", "r'default_value=value'")  # Simplify regex example

# Write back with UTF-8 encoding
with open('docs/CPS_v4/optimization_flow.md', 'w', encoding='utf-8') as f:
    f.write(content)
    
print('Mermaid charts fixed for MarkText compatibility')
