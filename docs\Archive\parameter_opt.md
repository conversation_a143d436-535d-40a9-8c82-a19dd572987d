# Parameter Format Standards

Parameters for optimization must be defined using a standardized tuple format to ensure compatibility, auditability, and ease of use across the backtest engine and optimization modules.

### Standard Parameter Definition

Use the `define_parameter` function to declare each parameter:

```python
define_parameter(optimize, default_value, min_value, max_value, increment)
```

- **optimize**: Set to `True` to enable optimization for this parameter, or `False` to use only the default value.
- **default_value**: The value used if optimization is disabled.
- **min_value**: The minimum value for the optimization range.
- **max_value**: The maximum value for the optimization range.
- **increment**: The step size between values in the optimization range.

**Returns:**  
A tuple in the format: `('Y' or 'N', default_value, min_value, max_value, increment)`

**Example:**
```python
momentum_period = define_parameter(True, 30, 20, 40, 5)
# Result: ('Y', 30, 20, 40, 5)
```

### Validation and Usage

- Use `validate_parameter(param, param_name)` to ensure each parameter tuple is correctly specified.
- Generate value ranges for optimization with `get_parameter_range(param, param_name)`.
- Use `get_parameter_combinations(parameters)` to create all possible combinations for grid search.

**Note:**  
All parameter definitions should be clear, explicit, and include comments or docstrings where appropriate for maintainability.

---

# Parameter Optimization Functions Matrix

This document summarizes the key functions in `config/local_parameter_optimization.py` used for parameter optimization in the backtest engine. The matrix below indicates whether each function is currently used, where it is used, and its primary role.

**Legend:**
- **Used:** Y = Yes, N = No
- **Where Used:** File or module where the function is called
- **Role:** Brief description of the function's purpose

---

| Function Name                  | Used | Where Used                                 | Role                                                                 |
|-------------------------------|------|--------------------------------------------|----------------------------------------------------------------------|
| `define_parameter`             | Y    | `run_backtest_v2_with_metrics.py`          | Defines a parameter for optimization (optimize flag, default, range).|
| `get_parameter_combinations`   | Y    | `run_backtest_v2_with_metrics.py`          | Generates all parameter value combinations for grid search.           |
| `validate_parameters`          | N    | N/A                                        | Validates parameter input and ranges.                                 |
| `ParameterOptimizer`           | Y    | `run_backtest_v2_with_metrics.py`          | Class for running parameter optimization (grid/random search, etc.).  |
| `random_parameter_combination` | N    | N/A                                        | Generates a random combination of parameters.                         |
| `save_optimization_results`    | N    | N/A                                        | Saves optimization results to disk.                                   |
| `load_optimization_results`    | N    | N/A                                        | Loads optimization results from disk.                                 |
| `plot_optimization_surface`    | N    | N/A                                        | Plots 2D/3D optimization surfaces for parameter analysis.             |

---

## Notes
- Only the functions marked as **Used: Y** are actively called in the current backtest engine integration.
- Unused functions remain for future extensibility or may be removed after further review.
- For more details on each function, see the docstrings in `config/local_parameter_optimization.py`.

---

_Last updated: 2025-04-15_
