@echo off
echo Testing Report Generation Fixes (Following Established Pattern)
echo ==============================================================
echo.

cd /d "S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template"

REM Create output directory if missing
if not exist "output" mkdir "output"

echo Testing PerformanceTableGenerator fixes...
echo.

REM Test using established pattern - single Python execution
python -c "
import sys
import os
from pathlib import Path
import logging

# Add project root to path
sys.path.insert(0, str(Path.cwd()))

# Set up logging to file
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('output/test_report_fix.log'),
        logging.StreamHandler()
    ]
)

from v4.reporting.performance_table_generator import PerformanceTableGenerator

try:
    print('Testing PerformanceTableGenerator fixes...')
    
    # Test 1: Generator initialization
    generator = PerformanceTableGenerator()
    print('✅ Generator initialized')
    
    # Test 2: Optimizable parameters (this was failing with .lower() error)
    params = generator._get_optimizable_parameters()
    print(f'✅ Found {len(params)} optimizable parameters')
    
    # Test 3: Data file loading (this was failing with file not found)
    print('Testing data file loading...')
    signals, allocations, trades = generator._load_data_files()
    print(f'✅ Data loaded - Signals: {signals.shape}, Allocations: {allocations.shape}, Trades: {trades.shape}')
    
    # Test 4: Full report generation
    print('Testing report generation...')
    filepath = generator.generate_performance_table()
    print(f'✅ Report generated: {filepath}')
    
    print('\\nAll tests passed! Fixes are working.')
    
except Exception as e:
    print(f'❌ Test failed: {e}')
    import traceback
    traceback.print_exc()
    exit(1)
"

echo.
echo Test complete - check output/test_report_fix.log for details
echo.
if %ERRORLEVEL% EQU 0 (
    echo ✅ All tests passed!
) else (
    echo ❌ Tests failed - see log file
)
pause
