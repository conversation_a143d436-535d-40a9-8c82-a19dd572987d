2025-06-18 11:58:56,779 - INFO - Script setup complete. Settings loaded and loggers configured.
2025-06-18 11:58:56,779 - INFO - EMA Production Flow Trace Log - 20250618_115856
2025-06-18 11:58:56,779 - INFO - Period: 2021-01-20 to 2021-02-28
2025-06-18 11:58:57,165 - INFO - Price data loaded. Shape: (1370, 5)
2025-06-18 11:58:57,169 - INFO - --- Effective Settings for Trace ---
2025-06-18 11:58:57,169 - INFO - EMA Lookbacks: ST=15, MT=70, LT=100
2025-06-18 11:58:57,170 - INFO - EMA Weights: Min=0.0, Max=1.0
2025-06-18 11:58:57,170 - INFO - EMA Strategy Top N: 3
2025-06-18 11:58:57,170 - INFO - System Top N (for allocation rules): 2
2025-06-18 11:58:57,198 - INFO - Signal Algorithm for Rules: {'default': "'ema'", 'picklist': ['Strategy_EMA', 'Strategy_SMA', 'Strategy_MACD', 'Strategy_RSI', 'Strategy_BB']}
2025-06-18 11:58:57,198 - INFO - -----------------------------------
2025-06-18 11:58:57,202 - INFO - 
========== Tracing for Date: 2021-01-20 ==========
2025-06-18 11:58:57,203 - INFO - --- Step 1: Calculate EMA Metrics ---
2025-06-18 11:58:57,203 - INFO - Input price_data_slice shape: (265, 5)
2025-06-18 11:58:57,203 - INFO - Setting module-level ema_params in ema_model_module: {'st_lookback': 15, 'mt_lookback': 70, 'lt_lookback': 100, 'min_weight': 0.0, 'max_weight': 1.0, 'top_n_strat_specific': 3}
2025-06-18 11:58:57,220 - INFO - Output short_ema_df tail(1):
                   SPY  ...        PFF
Date                    ...           
2021-01-20  354.597918  ...  29.691865

[1 rows x 5 columns]
2025-06-18 11:58:57,223 - INFO - Output med_ema_df tail (1):
                   SPY        SHV        EFA         TLT        PFF
Date                                                               
2021-01-20  339.835007  97.173336  62.416445  137.835581  29.151148
2025-06-18 11:58:57,225 - INFO - Output long_ema_df tail (1):
                   SPY        SHV        EFA         TLT        PFF
Date                                                               
2021-01-20  333.307234  97.166897  61.070301  138.596178  28.839638
2025-06-18 11:58:57,225 - INFO - --- Step 2: Calculate EMA Ratios ---
2025-06-18 11:58:57,230 - INFO - Output stmtemax_series (latest):
                 SPY       SHV       EFA       TLT       PFF
Date                                                        
2021-01-20  1.043441  0.999987  1.057753  0.976128  1.018549
2025-06-18 11:58:57,232 - INFO - Output mtltemax_series (latest):
                 SPY       SHV       EFA       TLT       PFF
Date                                                        
2021-01-20  1.019585  1.000066  1.022043  0.994512  1.010801
2025-06-18 11:58:57,234 - INFO - Output emaxavg_series (latest for ranking):
                 SPY       SHV       EFA      TLT       PFF
Date                                                       
2021-01-20  1.031513  1.000027  1.039898  0.98532  1.014675
2025-06-18 11:58:57,235 - INFO - --- Step 3: Determine Allocation Weights ---
2025-06-18 11:58:57,235 - INFO - Calling ema_model_module.ema_allocation_model_updated with price_data up to 2021-01-20
2025-06-18 11:58:57,236 - INFO - Attempting to call ema_model_module.ema_allocation_model_updated...
2025-06-18 11:58:57,236 - INFO -   ema_model_module.st_lookback=15, mt_lookback=70, lt_lookback=100
2025-06-18 11:58:57,236 - INFO -   ema_model_module.min_weight=0.0, ema_model_module.max_weight=1.0
2025-06-18 11:58:57,236 - INFO -   Trace script's signal_algo: {'default': "'ema'", 'picklist': ['Strategy_EMA', 'Strategy_SMA', 'Strategy_MACD', 'Strategy_RSI', 'Strategy_BB']}, system_top_n: 2 (Note: production module uses its own settings for these)
2025-06-18 11:58:57,237 - INFO - EMA production flow trace finished (finally block).
