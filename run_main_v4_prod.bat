@echo off
REM ============================================
REM Script: run_main_v4_prod.bat
REM Enhanced version with robust output handling
REM ============================================

SETLOCAL
pushd "%~dp0"

REM --- Configuration ---
SET PYTHON_EXE=F:\AI_Library\my_quant_env\Scripts\python.exe
SET SCRIPT_PATH=%~dp0main_v4_production_run.py
SET OUTPUT_LOG_DIR=v4_trace_outputs

REM Create timestamp
FOR /f "tokens=2 delims==" %%G IN ('wmic os get localdatetime /value') DO SET TIMESTAMP=%%G
SET TIMESTAMP=%TIMESTAMP:~0,8%_%TIMESTAMP:~8,6%
SET OUTPUT_FILE=%OUTPUT_LOG_DIR%\run_main_v4_prod_%TIMESTAMP%.txt

REM --- Ensure output directory exists ---
echo [%TIME%] Preparing output directory...
IF NOT EXIST "%OUTPUT_LOG_DIR%" (
    mkdir "%OUTPUT_LOG_DIR%"
    IF ERRORLEVEL 1 (
        echo ERROR: Failed to create output directory
        exit /b 1
    )
)

REM --- Execute Python Script ---
echo [%TIME%] Running Python script...
"%PYTHON_EXE%" "%SCRIPT_PATH%" 1> "%OUTPUT_FILE%" 2>&1
SET PYTHON_EXIT=%ERRORLEVEL%

REM --- Process Results ---
echo [%TIME%] Processing results...
IF EXIST "%OUTPUT_FILE%" (
    echo [%TIME%] Output file created: "%OUTPUT_FILE%"
    echo.
    echo === IMPORTANT MESSAGES ===
    type "%OUTPUT_FILE%" | findstr /i "error warning traceback fatal exception milestone"
    echo.
    echo Script completed with exit code: %PYTHON_EXIT%
    echo Full output saved to: "%OUTPUT_FILE%"
) ELSE (
    echo ERROR: No output file was created
    exit /b 1
)

popd
ENDLOCAL
