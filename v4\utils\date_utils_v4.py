# utils/date_utils.py
"""
Standardized date utilities for the backtesting framework.
This module provides a consistent interface for all date operations.

Rules:
1. Internally use pandas Timestamps for calculations (timezone-naive)
2. Always display dates in YYYY-MM-DD format without time components
3. All functions accept flexible inputs and return standardized outputs
4. Time components are suppressed in all output formats
"""

import logging
import pandas as pd
from datetime import date, datetime
from typing import Union, Optional, List, Tuple

logger = logging.getLogger(__name__)

# Type definitions for improved clarity
DateInput = Union[str, date, datetime, pd.Timestamp, pd.DatetimeIndex]
DateRange = Tuple[pd.Timestamp, pd.Timestamp]

# Override pandas display options to only show date components
pd.set_option('display.date_dayfirst', False)  # Use YYYY-MM-DD format
pd.set_option('display.date_yearfirst', True)  # Year first in display

def standardize_date(date_input: DateInput) -> pd.Timestamp:
    """
    Convert any date-like input to a standardized pandas Timestamp.
    Internal use only - for display formatting use date_to_str().
    
    Args:
        date_input: Any date-like object (string, date, datetime, Timestamp)
        
    Returns:
        pandas Timestamp (timezone-naive, normalized to midnight)
    """
    if date_input is None:
        return None
    # Accept integer date inputs (YYYYMMDD ints)
    if isinstance(date_input, int):
        date_input = str(date_input)
    
    try:
        # Convert to pandas Timestamp
        if isinstance(date_input, str):
            ts = pd.Timestamp(date_input)
        elif isinstance(date_input, (date, datetime)):
            ts = pd.Timestamp(date_input)
        elif isinstance(date_input, pd.Timestamp):
            ts = date_input
        elif isinstance(date_input, pd.DatetimeIndex):
            if len(date_input) == 0:
                return None
            ts = date_input[0]
        else:
            raise ValueError(f"Unsupported date type: {type(date_input)}")
        
        # Ensure timezone-naive
        if ts.tz is not None:
            ts = ts.tz_localize(None)
        
        # Normalize to midnight (remove time component)
        ts = ts.normalize()
        
        return ts
    
    except Exception as e:
        logger.error(f"Error standardizing date {date_input}: {e}")
        return None

def standardize_date_range(start_date: DateInput, end_date: Optional[DateInput] = None) -> DateRange:
    """
    Convert any date-like inputs to a standardized date range.
    Internal use only - for display formatting use date_to_str().
    
    Args:
        start_date: Start date in any supported format
        end_date: End date in any supported format (default: today)
        
    Returns:
        Tuple of (start_date, end_date) as pandas Timestamps
    """
    start_ts = standardize_date(start_date)
    
    # Default end_date to today if not provided
    if end_date is None:
        end_ts = standardize_date(pd.Timestamp.today())
    else:
        end_ts = standardize_date(end_date)
    
    return (start_ts, end_ts)

def create_date_range(start_date: DateInput, end_date: Optional[DateInput] = None, freq: str = 'D') -> pd.DatetimeIndex:
    """
    Create a pandas DatetimeIndex with daily dates between start and end.
    
    Args:
        start_date: Start date in any supported format
        end_date: End date in any supported format (default: today)
        freq: Frequency for the date range (default: daily)
        
    Returns:
        DatetimeIndex with dates in the specified range (time components hidden)
    """
    start_ts, end_ts = standardize_date_range(start_date, end_date)
    
    if start_ts is None or end_ts is None:
        logger.error(f"Invalid date range: {start_date} to {end_date}")
        return pd.DatetimeIndex([])
    
    # Create date range (will have normalized time components)
    return pd.date_range(start=start_ts, end=end_ts, freq=freq)

def standardize_dataframe_index(df: pd.DataFrame) -> pd.DataFrame:
    """
    Ensure a DataFrame has a timezone-naive DatetimeIndex.
    
    Args:
        df: DataFrame with dates as index
        
    Returns:
        DataFrame with standardized DatetimeIndex (time components hidden)
    """
    if df is None or len(df) == 0:
        return df
    
    # Convert index to DatetimeIndex if it's not already
    if not isinstance(df.index, pd.DatetimeIndex):
        try:
            df.index = pd.DatetimeIndex(df.index)
        except Exception as e:
            logger.warning(f"Could not convert index to DatetimeIndex: {e}")
            return df
    
    # Ensure timezone-naive index
    if df.index.tz is not None:
        df.index = df.index.tz_localize(None)
    
    # Normalize to remove time component
    df.index = df.index.normalize()
    
    return df

def date_to_str(date_input: DateInput, fmt: str = '%Y-%m-%d') -> str:
    """
    Convert any date input to a formatted string.
    This should be used for all display purposes.
    
    Args:
        date_input: Any date-like object
        fmt: String format (default: YYYY-MM-DD)
        
    Returns:
        Formatted date string WITHOUT time components
    """
    ts = standardize_date(date_input)
    if ts is None:
        return None
    
    return ts.strftime(fmt)

def display_date(date_input: DateInput) -> str:
    """
    Standard function for displaying dates in reports or UI.
    Always returns YYYY-MM-DD format without time components.
    
    Args:
        date_input: Any date-like object
        
    Returns:
        Formatted date string (YYYY-MM-DD)
    """
    return date_to_str(date_input)

def filter_dataframe_by_dates(df: pd.DataFrame, 
                           start_date: Optional[DateInput] = None, 
                           end_date: Optional[DateInput] = None) -> pd.DataFrame:
    """
    Filter a DataFrame by date range.
    
    Args:
        df: DataFrame with DatetimeIndex
        start_date: Start date (optional)
        end_date: End date (optional)
        
    Returns:
        Filtered DataFrame
    """
    if df is None or len(df) == 0:
        return df
    
    # Standardize the DataFrame index
    df = standardize_dataframe_index(df)
    
    # Filter by start date if provided
    if start_date is not None:
        start_ts = standardize_date(start_date)
        if start_ts is not None:
            df = df[df.index >= start_ts]
    
    # Filter by end date if provided
    if end_date is not None:
        end_ts = standardize_date(end_date)
        if end_ts is not None:
            df = df[df.index <= end_ts]
    
    return df

def get_business_days(start_date: DateInput, end_date: Optional[DateInput] = None) -> pd.DatetimeIndex:
    """
    Get business days between start and end dates.
    
    Args:
        start_date: Start date in any supported format
        end_date: End date in any supported format (default: today)
        
    Returns:
        DatetimeIndex with business days (time components hidden)
    """
    return create_date_range(start_date, end_date, freq='B')

def get_rebalance_dates(start_date: Union[str, pd.Timestamp, date],
                       end_date: Union[str, pd.Timestamp, date],
                       freq: str = 'weekly') -> pd.DatetimeIndex:
    """
    Get rebalance dates for a strategy.
    
    Args:
        start_date: Start date in any supported format
        end_date: End date in any supported format (default: today)
        freq: Rebalance frequency (default: weekly)
        
    Returns:
        DatetimeIndex with rebalance dates (time components hidden)
    """
    return create_date_range(start_date, end_date, freq=map_rebalance_frequency(freq))

def map_rebalance_frequency(freq_str: str) -> str:
    """
    Map common frequency terms to pandas frequency strings.
    
    Args:
        freq_str: Frequency string (e.g., 'daily', 'weekly', 'monthly')
        
    Returns:
        Pandas frequency string
    """
    freq_map = {
        'daily': 'B',         # Business days
        'weekly': 'W',        # Weekly (standard pandas weekly frequency)
        'monthly': 'MS',      # Month start
        'quarterly': 'QS',    # Quarter start
        'yearly': 'AS',       # Year start
        
        # Already in pandas format
        'D': 'D',
        'B': 'B',
        'W': 'W',
        'W-MON': 'W-MON',
        'W-TUE': 'W-TUE',
        'W-WED': 'W-WED',
        'W-THU': 'W-THU',
        'W': 'W',
        'MS': 'MS',
        'QS': 'QS',
        'AS': 'AS',
    }
    
    return freq_map.get(freq_str.lower(), freq_str)

# Function to convert DataFrame index to date strings for display
def format_dataframe_index_for_display(df: pd.DataFrame) -> pd.DataFrame:
    """
    Format a DataFrame's DatetimeIndex to date strings for display.
    Use this function before displaying DataFrames in reports or UI.
    
    Args:
        df: DataFrame with DatetimeIndex
        
    Returns:
        DataFrame with index formatted as 'YYYY-MM-DD' strings
    """
    if df is None or len(df) == 0:
        return df
    
    # Make a copy to avoid modifying the original
    df_display = df.copy()
    
    # Convert index to strings in YYYY-MM-DD format
    if isinstance(df_display.index, pd.DatetimeIndex):
        df_display.index = df_display.index.strftime('%Y-%m-%d')
    
    return df_display
