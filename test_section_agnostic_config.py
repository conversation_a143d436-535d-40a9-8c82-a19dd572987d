#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Test script to verify section-agnostic configuration approach is working correctly.

This script tests the ConfigHelper class and convenience functions to ensure
they can find parameters regardless of which section they're in.
"""

import sys
import os
from pathlib import Path

# Add v4 to path for imports
sys.path.append('v4')

from v4.settings.config_helper import <PERSON>fig<PERSON><PERSON><PERSON>, get_param, get_param_boolean
from v4.config.paths_v4 import V4_SETTINGS_FILE

def test_section_agnostic_config():
    """Test the section-agnostic configuration approach."""
    print("=" * 60)
    print("TESTING SECTION-AGNOSTIC CONFIGURATION APPROACH")
    print("=" * 60)
    
    # Test 1: Initialize ConfigHelper
    print("\n1. Testing ConfigHelper initialization...")
    try:
        helper = ConfigHelper(V4_SETTINGS_FILE)
        print("✓ ConfigHelper initialized successfully")
    except Exception as e:
        print(f"✗ ConfigHelper initialization failed: {e}")
        return False
    
    # Test 2: Test key parameters that were causing issues
    print("\n2. Testing key parameters...")
    test_params = [
        ('optimization_active', 'boolean'),
        ('csv_flag_use', 'boolean'),
        ('csv_valid_det', 'boolean'),
        ('commission_rate', 'float'),
        ('slippage_rate', 'float'),
        ('initial_capital', 'float'),
        ('start_date', 'string'),
        ('end_date', 'string'),
        ('tickers', 'string'),
        ('strategy_algorithm', 'string'),
        ('risk_free_rate', 'float')
    ]
    
    for param_name, param_type in test_params:
        try:
            if param_type == 'boolean':
                value = helper.getboolean(param_name)
            elif param_type == 'float':
                value = helper.getfloat(param_name)
            elif param_type == 'int':
                value = helper.getint(param_name)
            else:
                value = helper.get(param_name)
            
            section = helper.find_param_section(param_name)
            print(f"✓ {param_name}: {value} (found in [{section}])")
        except Exception as e:
            print(f"✗ {param_name}: ERROR - {e}")
    
    # Test 3: Test convenience functions
    print("\n3. Testing convenience functions...")
    try:
        opt_active = get_param_boolean('optimization_active')
        csv_flag = get_param_boolean('csv_flag_use')
        commission = get_param('commission_rate')
        
        print(f"✓ get_param_boolean('optimization_active'): {opt_active}")
        print(f"✓ get_param_boolean('csv_flag_use'): {csv_flag}")
        print(f"✓ get_param('commission_rate'): {commission}")
    except Exception as e:
        print(f"✗ Convenience functions failed: {e}")
    
    # Test 4: Test fallback behavior
    print("\n4. Testing fallback behavior...")
    try:
        # Test with non-existent parameter
        missing_param = helper.get('non_existent_parameter', fallback='DEFAULT_VALUE')
        print(f"✓ Non-existent parameter with fallback: {missing_param}")
        
        missing_bool = helper.getboolean('non_existent_bool', fallback=True)
        print(f"✓ Non-existent boolean with fallback: {missing_bool}")
    except Exception as e:
        print(f"✗ Fallback behavior failed: {e}")
    
    # Test 5: Compare with old section-specific approach
    print("\n5. Testing compatibility with old approach...")
    try:
        import configparser
        config = configparser.ConfigParser()
        config.read(V4_SETTINGS_FILE)
        
        # Find optimization_active in config manually
        old_value = None
        for section_name in config.sections():
            if 'optimization_active' in config[section_name]:
                old_value = config.getboolean(section_name, 'optimization_active')
                break
        
        new_value = helper.getboolean('optimization_active')
        
        if old_value == new_value:
            print(f"✓ Values match: old={old_value}, new={new_value}")
        else:
            print(f"✗ Values differ: old={old_value}, new={new_value}")
    except Exception as e:
        print(f"✗ Compatibility test failed: {e}")
    
    print("\n" + "=" * 60)
    print("SECTION-AGNOSTIC CONFIGURATION TEST COMPLETE")
    print("=" * 60)
    
    return True

if __name__ == "__main__":
    success = test_section_agnostic_config()
    if success:
        print("\n✅ All tests completed successfully!")
    else:
        print("\n❌ Some tests failed!")
        sys.exit(1)
