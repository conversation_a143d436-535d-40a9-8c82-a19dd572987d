# CPS v4 Optimization Pipeline Flow Documentation

## Overview

This document illustrates the complete optimization pipeline flow in the CPS v4 backtest system, showing how data flows through every module and function during multi-parameter optimization runs.

## High-Level Architecture

```mermaid
graph TD
    A[Start Optimization] --> B[Load Parameters from INI]
    B --> C[Generate Parameter Combinations]
    C --> D[For Each Combination]
    D --> E[Create Temporary Settings]
    E --> F[Run Backtest Pipeline]
    F --> G[Generate Equity Curve]
    G --> H[Store Results]
    H --> I{More Combinations?}
    I -->|Yes| D
    I -->|No| J[Generate Performance Tables]
    J --> K[Create Optimization Report]
    K --> L[End Optimization]
```

## Detailed Optimization Flow

### 1. Entry Point and Mode Detection

```mermaid
flowchart TD
    A[run_unified_pipeline.py:main] --> B[parse_cli_arguments]
    B --> C[run_unified_pipeline]
    C --> D[determine_pipeline_mode]
    D --> E[load_settings]
    E --> F[PerformanceTableGenerator.get_optimization_combinations]
    F --> G{Combinations > 1?}
    G -->|Yes| H[Return 'optimization' mode]
    G -->|No| I[Return 'single' mode]
```

**Natural Language Flow:**

1. User executes `python v4/run_unified_pipeline.py`
2. CLI arguments are parsed (if any)
3. The system loads settings from `v4/settings/settings_parameters_v4.ini`
4. `PerformanceTableGenerator` scans the config for ComplexN parameters with `optimize=True`
5. If multiple parameter combinations exist, optimization mode is triggered

### 2. Parameter Combination Generation

```mermaid
flowchart TD
    A[PerformanceTableGenerator.get_optimization_combinations] --> B[Scan Strategy Section]
    B --> C[Parse ComplexN Parameters]
    C --> D{Parameter has optimize=True?}
    D -->|Yes| E[Generate Range: min_value to max_value by increment]
    D -->|No| F[Use default_value as fixed parameter]
    E --> G[Store in optimization_params dict]
    F --> H[Store in fixed_params dict]
    G --> I[Generate Cartesian Product]
    H --> I
    I --> J[Return List of Parameter Combinations]

    subgraph "Example Parameter"
        K["st_lookback = (optimize=True, default_value=15, min_value=10, max_value=25, increment=5)"]
        K --> L["Values: [10, 15, 20, 25]"]
    end
```

**Natural Language Flow:**

1. The system scans the `[Strategy]` section of the settings file
2. For each parameter, it checks if the value starts with `(` and contains `optimize=`
3. ComplexN parameters are parsed to extract optimization ranges
4. Parameters with `optimize=True` generate value ranges (min to max by increment)
5. Parameters with `optimize=False` use only their default values
6. A Cartesian product generates all possible parameter combinations
7. Each combination is a dictionary with parameter names and specific values

### 3. Matrix Optimization Execution

```mermaid
flowchart TD
    A[run_optimization_pipeline] --> B[Initialize PerformanceTableGenerator]
    B --> C[Get Parameter Combinations]
    C --> D[Initialize EquityCurvesManager]
    D --> E[Matrix Optimization Loop]

    subgraph "For Each Combination"
        E --> F[_run_pipeline_for_combination]
        F --> G[Create Temporary Settings File]
        G --> H[Replace Parameter Values using Regex]
        H --> I[Execute Subprocess: python v4/run_unified_pipeline.py --settings temp.ini]
        I --> J[Load Generated Equity Curve CSV]
        J --> K[Store in EquityCurvesManager]
    end

    K --> L[Generate Performance Summary]
    L --> M[Save Equity Matrix CSV]
    M --> N[Generate XLSX Report]
```

**Natural Language Flow:**

1. The optimization pipeline initializes an `EquityCurvesManager` to collect results
2. For each parameter combination (e.g., 12 combinations from st_lookback=[10,15,20,25] × mt_lookback=[65,70,75]):
   - A temporary settings file is created
   - Parameter values are replaced using regex substitution (e.g., `default_value=15` → `default_value=20`)
   - A subprocess runs the single pipeline with the temporary settings
   - The generated equity curve is loaded and stored with a unique identifier
3. All equity curves are compiled into a matrix (dates × combinations)
4. Performance metrics are calculated for each combination
5. Results are saved to CSV files and compiled into an XLSX report

### 4. Temporary Settings File Management

```mermaid
flowchart TD
    A[_create_temp_settings] --> B[Read Original Settings File]
    B --> C[Create Temporary Copy]
    C --> D[Apply Parameter Replacements]

    subgraph "Parameter Replacement Process"
        D --> E["Find: default_value=15"]
        E --> F["Replace: default_value=20"]
        F --> G["Pattern: r'default_value=([^,)]+)'"]
        G --> H["Replacement: r'default_value=\\g<1>'"]
    end

    H --> I[Write Updated Settings to Temp File]
    I --> J[Return Temp File Path]
    J --> K[Subprocess Uses Temp Settings]
    K --> L[_cleanup_temp_settings]
```

**Natural Language Flow:**

1. A temporary copy of the settings file is created for each combination
2. Parameter values are replaced using regex patterns that match the ComplexN format
3. The key fix was correcting the regex backreference from `\g<1>` to `\g<1>`
4. Each subprocess receives its own temporary settings file with the correct parameter values
5. After the subprocess completes, the temporary file is cleaned up

### 5. EMA Model Parameter Loading

```mermaid
flowchart TD
    A[Subprocess: EMA Model Execution] --> B[ema_allocation_model_v4.py]
    B --> C[load_parameter_metadata]
    C --> D[get_effective_parameter]

    subgraph "Parameter Resolution"
        D --> E{Parameter has optimize=True?}
        E -->|Yes| F[Use Override Value from Function Args]
        E -->|No| G[Use default_value from Settings]
        F --> H[Effective Parameter Value]
        G --> H
    end

    H --> I[calculate_ema_metrics_with_params]
    I --> J[EMA Calculations with Correct Parameters]
```

**Natural Language Flow:**

1. When the subprocess runs, the EMA model loads parameters fresh from the temporary settings file
2. The model checks each parameter's `optimize` flag
3. For parameters with `optimize=True`, it uses override values passed to the function
4. For parameters with `optimize=False`, it always uses the default_value from settings
5. This ensures optimization parameters are properly applied while keeping non-optimized parameters stable

### 6. Pipeline Subprocess Execution

```mermaid
flowchart TD
    A[subprocess.run Command] --> B[python v4/run_unified_pipeline.py --settings temp.ini]
    B --> C[Signal Generation Phase]
    C --> D[EMA Model with Updated Parameters]
    D --> E[Trading Phase]
    E --> F[Backtest Execution]
    F --> G[Generate Equity Curve]
    G --> H[Save to reporting/equity_curve_strategy_latest.csv]
    H --> I[Parent Process Reads Result]
```

**Natural Language Flow:**

1. Each optimization combination runs as a separate Python subprocess
2. The subprocess executes the full unified pipeline (signal generation + trading)
3. The EMA model reads parameters from the temporary settings file
4. Signal generation produces allocations based on the specific parameter values
5. The trading phase executes the backtest using those signals
6. An equity curve is generated and saved to a standardized location
7. The parent optimization process immediately reads and stores the result

### 7. Result Aggregation and Reporting

```mermaid
flowchart TD
    A[EquityCurvesManager] --> B[Collect All Equity Curves]
    B --> C[Create Equity Matrix DataFrame]
    C --> D[Calculate Performance Metrics]

    subgraph "Performance Metrics per Combination"
        D --> E[CAGR Calculation]
        D --> F[Sharpe Ratio]
        D --> G[Sortino Ratio]
        D --> H[UPI Index]
        D --> I[Max Drawdown]
    end

    E --> J[Performance Summary CSV]
    F --> J
    G --> J
    H --> J
    I --> J

    C --> K[Equity Matrix CSV]
    J --> L[XLSX Report Generation]
    K --> L
    L --> M[Final Performance Report]
```

**Natural Language Flow:**

1. All equity curves are collected into a single DataFrame matrix
2. Each column represents one parameter combination's performance over time
3. Performance metrics are calculated for each combination using the equity curves
4. A summary CSV shows the comparative performance of all combinations
5. An equity matrix CSV contains the raw daily portfolio values
6. A comprehensive XLSX report presents the results in formatted tables

## Key Data Structures

### Parameter Combination Example

```python
{
    'st_lookback': 15,
    'mt_lookback': 70,
    'lt_lookback': 100,
    'execution_delay': 1,
    'top_n': 2
}
```

### Equity Matrix Structure

```
Date          combo_0    combo_1    combo_2    ...
2020-01-01    1000000    1000000    1000000    ...
2020-01-02    1001250    1000800    1002100    ...
2020-01-03    999800     1001200    998750     ...
...
```

### Performance Summary Structure

```
Combination    st_lookback    mt_lookback    CAGR      Sharpe    Max_Drawdown
combo_0        10            65            0.0847     1.23      -0.1456
combo_1        10            70            0.0821     1.19      -0.1523
combo_2        10            75            0.0798     1.15      -0.1601
...
```

## File Flow During Optimization

```mermaid
flowchart LR
    A[settings_parameters_v4.ini] --> B[temp_settings_combo_0.ini]
    A --> C[temp_settings_combo_1.ini]
    A --> D[temp_settings_combo_N.ini]

    B --> E[equity_curve_combo_0.csv]
    C --> F[equity_curve_combo_1.csv]
    D --> G[equity_curve_combo_N.csv]

    E --> H[equity_curves_matrix.csv]
    F --> H
    G --> H

    H --> I[optimization_performance_summary.csv]
    H --> J[Performance_Table_Report.xlsx]
```

## Critical Success Factors

1. **Correct Regex Backreferences**: The fix from `\g<1>` to `\g<1>` ensures parameter values are properly updated in temporary settings files.

2. **Fresh Parameter Loading**: The EMA model loads parameters on each function call rather than caching globally, allowing optimization overrides to work.

3. **Subprocess Isolation**: Each parameter combination runs in its own subprocess to ensure clean parameter environments.

4. **Immediate Result Capture**: Equity curves are read immediately after each subprocess completes to avoid overwrites.

5. **Proper File Management**: Temporary settings files are created and cleaned up properly for each combination.

This optimization pipeline enables testing multiple parameter combinations efficiently while ensuring each combination receives the correct parameter values and produces distinct, meaningful results.
