"""
v4/engine/portfolio_manager.py
Portfolio management module for the backtest engine.
Part of the backtest engine system (CPS v4 compliant).
"""

import pandas as pd
import numpy as np
import logging
from datetime import date, datetime

logger = logging.getLogger(__name__)

class PortfolioManager:
    """
    Manages portfolio state, positions, and valuation throughout a backtest.
    Separates portfolio management logic from the main backtest engine.
    """
    
    def __init__(self, initial_capital=100000.0):
        """
        Initialize the portfolio manager.
        
        Args:
            initial_capital (float): Initial capital for the portfolio
        """
        self.initial_capital = initial_capital
        self.cash = initial_capital
        self.positions = {}  # {symbol: {'quantity': qty, 'cost': cost, 'value': value}}
        
        # History tracking
        self.value_history = pd.Series()
        self.position_history = {}  # {date: {symbol: {'quantity': qty, 'value': value}}}
        self.cash_history = pd.Series()
        
        logger.info(f"Initialized portfolio with {initial_capital:,.2f} capital")
    
    def get_cash(self):
        """Get current cash balance."""
        return self.cash
    
    def get_total_value(self):
        """Get total portfolio value (cash + positions)."""
        position_value = sum(pos['value'] for pos in self.positions.values())
        return self.cash + position_value
    
    def get_positions(self):
        """Get current positions."""
        return self.positions.copy()
    
    def add_cash(self, amount):
        """Add cash to the portfolio."""
        if amount <= 0:
            logger.warning(f"Attempted to add invalid cash amount: {amount}")
            return False
        
        self.cash += amount
        return True
    
    def remove_cash(self, amount):
        """Remove cash from the portfolio."""
        if amount <= 0:
            logger.warning(f"Attempted to remove invalid cash amount: {amount}")
            return False
        
        if amount > self.cash:
            logger.warning(f"Insufficient cash: needed {amount}, have {self.cash}")
            return False
        
        self.cash -= amount
        return True
    
    def add_position(self, symbol, quantity, price):
        """
        Add to a position or create a new one.
        
        Args:
            symbol (str): Symbol of the asset
            quantity (float): Quantity to add
            price (float): Price per unit
        """
        if quantity <= 0 or price <= 0:
            logger.warning(f"Invalid quantity ({quantity}) or price ({price})")
            return False
        
        value = quantity * price
        
        # Update existing position or create new one
        if symbol in self.positions:
            # Update existing position
            current = self.positions[symbol]
            new_quantity = current['quantity'] + quantity
            new_cost = current['cost'] + value
            
            self.positions[symbol] = {
                'quantity': new_quantity,
                'cost': new_cost,
                'value': new_quantity * price,
                'price': price  # Current price
            }
        else:
            # Create new position
            self.positions[symbol] = {
                'quantity': quantity,
                'cost': value,
                'value': value,
                'price': price
            }
        
        logger.debug(f"Added {quantity} of {symbol} at {price:.2f}")
        return True
    
    def remove_position(self, symbol, quantity, price):
        """
        Remove from a position.
        
        Args:
            symbol (str): Symbol of the asset
            quantity (float): Quantity to remove
            price (float): Price per unit
        """
        if symbol not in self.positions:
            logger.warning(f"Position {symbol} does not exist")
            return False
        
        if quantity <= 0 or price <= 0:
            logger.warning(f"Invalid quantity ({quantity}) or price ({price})")
            return False
        
        current = self.positions[symbol]
        
        if quantity > current['quantity']:
            logger.warning(f"Insufficient quantity of {symbol}: needed {quantity}, have {current['quantity']}")
            return False
        
        # Calculate new position
        new_quantity = current['quantity'] - quantity
        
        # Calculate cost basis (proportional)
        cost_ratio = quantity / current['quantity']
        removed_cost = current['cost'] * cost_ratio
        new_cost = current['cost'] - removed_cost
        
        # Update or remove position
        if new_quantity > 0:
            self.positions[symbol] = {
                'quantity': new_quantity,
                'cost': new_cost,
                'value': new_quantity * price,
                'price': price
            }
        else:
            # Remove position completely
            del self.positions[symbol]
        
        logger.debug(f"Removed {quantity} of {symbol} at {price:.2f}")
        return True
    
    def mark_to_market(self, prices, current_date=None):
        """
        Mark portfolio positions to market using current prices.
        
        Args:
            prices: Current prices (dict, Series, or DataFrame)
            current_date: Current date (optional)
        """
        if current_date is None:
            current_date = date.today()
        
        # Standardize prices to a dictionary
        price_dict = {}
        if isinstance(prices, dict):
            price_dict = prices
        elif isinstance(prices, pd.Series):
            price_dict = prices.to_dict()
        elif isinstance(prices, pd.DataFrame):
            # Assume the most recent prices in each column
            price_dict = prices.iloc[-1].to_dict()
        
        # Update position values
        for symbol, position in self.positions.items():
            if symbol in price_dict:
                price = price_dict[symbol]
                if price > 0:
                    position['value'] = position['quantity'] * price
                    position['price'] = price
        
        # Calculate total value
        total_value = self.get_total_value()
        
        # Store history
        if isinstance(current_date, pd.Timestamp):
            current_date = current_date.to_pydatetime()
        
        # Update value history
        if not isinstance(self.value_history.index, pd.DatetimeIndex):
            self.value_history = pd.Series(dtype=float)
        
        self.value_history.loc[current_date] = total_value
        
        # Update cash history
        if not isinstance(self.cash_history.index, pd.DatetimeIndex):
            self.cash_history = pd.Series(dtype=float)
        
        self.cash_history.loc[current_date] = self.cash
        
        # Update position history
        position_snapshot = {}
        for symbol, position in self.positions.items():
            position_snapshot[symbol] = position.copy()
        
        self.position_history[current_date] = {
            'positions': position_snapshot,
            'cash': self.cash,
            'total_value': total_value
        }
        
        logger.debug(f"Marked portfolio to market on {current_date}: Total value = {total_value:.2f}")
        return total_value
    
    def get_value_history(self):
        """Get portfolio value history."""
        return self.value_history.copy()
    
    def get_cash_history(self):
        """Get cash balance history."""
        return self.cash_history.copy()
    
    def get_position_history(self):
        """
        Get position history as a DataFrame.
        
        Returns:
            DataFrame: Position values over time
        """
        if not self.position_history:
            return pd.DataFrame()
        
        # Extract all symbols from position history
        all_symbols = set()
        for date_data in self.position_history.values():
            all_symbols.update(date_data['positions'].keys())
        
        # Create DataFrame with all dates and symbols
        dates = sorted(self.position_history.keys())
        position_df = pd.DataFrame(index=dates, columns=list(all_symbols) + ['Cash'])
        
        # Fill with position values
        for date in dates:
            date_data = self.position_history[date]
            position_df.loc[date, 'Cash'] = date_data['cash']
            
            for symbol in all_symbols:
                if symbol in date_data['positions']:
                    position_df.loc[date, symbol] = date_data['positions'][symbol]['value']
                else:
                    position_df.loc[date, symbol] = 0.0
        
        return position_df
    
    def get_weights_history(self):
        """
        Calculate portfolio weights history.
        
        Returns:
            DataFrame: Portfolio weights over time
        """
        position_df = self.get_position_history()
        if position_df.empty:
            return pd.DataFrame()
        
        # Calculate total value for each date
        total_values = position_df.sum(axis=1)
        
        # Calculate weights
        weights_df = position_df.div(total_values, axis=0)
        
        return weights_df
    
    def reset(self):
        """Reset the portfolio to initial state."""
        self.cash = self.initial_capital
        self.positions = {}
        self.value_history = pd.Series()
        self.position_history = {}
        self.cash_history = pd.Series()
        
        logger.info(f"Reset portfolio to initial state with {self.initial_capital:,.2f} capital")
        return True
