"""
Verification script to check if the generated reports meet the V3 Performance Reporting Standards.
This script performs a thorough evaluation of the exact output against the requirements.
"""

import os
import sys
import logging
import pandas as pd
import matplotlib.pyplot as plt
from PIL import Image
import glob
import re
from datetime import datetime

# Set up logging
logging.basicConfig(level=logging.INFO, 
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Add project root to path
project_root = os.path.abspath(os.path.dirname(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

def check_file_exists(filepath):
    """Check if a file exists."""
    exists = os.path.isfile(filepath)
    if exists:
        logger.info(f"✅ File exists: {filepath}")
    else:
        logger.error(f"❌ File does not exist: {filepath}")
    return exists

def check_filename_format(filepath, pattern):
    """Check if a filename matches the required pattern."""
    filename = os.path.basename(filepath)
    matches = re.match(pattern, filename)
    if matches:
        logger.info(f"✅ Filename format is correct: {filename}")
    else:
        logger.error(f"❌ Filename format is incorrect: {filename}, should match pattern: {pattern}")
    return bool(matches)

def check_excel_sheets(filepath, required_sheets):
    """Check if an Excel file contains the required sheets."""
    try:
        xl = pd.ExcelFile(filepath)
        sheets = xl.sheet_names
        
        missing_sheets = [sheet for sheet in required_sheets if sheet not in sheets]
        
        if not missing_sheets:
            logger.info(f"✅ Excel file contains all required sheets: {', '.join(required_sheets)}")
            return True
        else:
            logger.error(f"❌ Excel file is missing required sheets: {', '.join(missing_sheets)}")
            logger.info(f"   Found sheets: {', '.join(sheets)}")
            return False
    except Exception as e:
        logger.error(f"❌ Error checking Excel sheets: {e}")
        return False

def check_excel_sheet_columns(filepath, sheet_name, required_columns):
    """Check if an Excel sheet contains the required columns."""
    try:
        df = pd.read_excel(filepath, sheet_name=sheet_name)
        columns = list(df.columns)
        
        missing_columns = [col for col in required_columns if col not in columns]
        
        if not missing_columns:
            logger.info(f"✅ Sheet '{sheet_name}' contains all required columns: {', '.join(required_columns)}")
            return True
        else:
            logger.error(f"❌ Sheet '{sheet_name}' is missing required columns: {', '.join(missing_columns)}")
            logger.info(f"   Found columns: {', '.join(columns)}")
            return False
    except Exception as e:
        logger.error(f"❌ Error checking Excel sheet columns: {e}")
        return False

def check_image_dimensions_and_dpi(filepath, min_dpi=300):
    """Check if an image meets the minimum DPI requirement."""
    try:
        with Image.open(filepath) as img:
            width, height = img.size
            
            # Check if image has DPI information
            if 'dpi' in img.info:
                dpi_x, dpi_y = img.info['dpi']
                logger.info(f"   Image dimensions: {width}x{height}, DPI: {dpi_x}x{dpi_y}")
                
                if dpi_x >= min_dpi and dpi_y >= min_dpi:
                    logger.info(f"✅ Image meets minimum DPI requirement of {min_dpi}")
                    return True
                else:
                    logger.error(f"❌ Image does not meet minimum DPI requirement of {min_dpi}, found {dpi_x}x{dpi_y}")
                    return False
            else:
                # If DPI information is not available, we can't verify
                logger.warning(f"⚠️ Image does not contain DPI information, dimensions: {width}x{height}")
                return None
    except Exception as e:
        logger.error(f"❌ Error checking image dimensions and DPI: {e}")
        return False

def verify_performance_table_report(reports_dir):
    """Verify the Performance Table Report against the standards."""
    logger.info("\n=== Verifying Performance Table Report ===")
    
    # Find the performance table report
    pattern = r"EMA_V3_1_performance_tables_\d{8}_\d{6}\.xlsx"
    performance_files = []
    
    for file in os.listdir(reports_dir):
        if re.match(pattern, file):
            performance_files.append(os.path.join(reports_dir, file))
    
    # Also check for files with the old naming convention
    old_pattern = r"ema_performance_tables_\d{4}-\d{2}-\d{2}_\d{6}\.xlsx"
    for file in os.listdir(reports_dir):
        if re.match(old_pattern, file):
            performance_files.append(os.path.join(reports_dir, file))
    
    if not performance_files:
        logger.error("❌ No Performance Table Report found")
        return False
    
    # Use the most recent file
    performance_file = max(performance_files, key=os.path.getmtime)
    logger.info(f"Found Performance Table Report: {os.path.basename(performance_file)}")
    
    # Check filename format
    check_filename_format(performance_file, pattern)
    
    # Check required sheets
    required_sheets = ['Signal History', 'Allocation History', 'Trade Log']
    sheets_exist = check_excel_sheets(performance_file, required_sheets)
    
    if sheets_exist:
        # Check Signal History sheet
        signal_columns = ['Date'] + ['SPY', 'TLT', 'EFA', 'SHV', 'PFF', 'Cash']  # Example tickers
        check_excel_sheet_columns(performance_file, 'Signal History', ['Date'])
        
        # Check Allocation History sheet
        check_excel_sheet_columns(performance_file, 'Allocation History', ['Date'])
        
        # Check Trade Log sheet
        trade_log_columns = ['trade_num', 'symbol', 'quantity', 'execution_date', 
                            'execution_price', 'commission+slippage', 'amount', 'pnl']
        check_excel_sheet_columns(performance_file, 'Trade Log', trade_log_columns)
    
    return True

def verify_allocation_weights_graphic(reports_dir):
    """Verify the Allocation Weights Graphic against the standards."""
    logger.info("\n=== Verifying Allocation Weights Graphic ===")
    
    # Find the allocation weights graphic
    allocation_dir = os.path.join(reports_dir, 'allocation_reports')
    if not os.path.isdir(allocation_dir):
        logger.error(f"❌ Allocation reports directory not found: {allocation_dir}")
        return False
    
    pattern = r"EMA_V3_1_allocation_weights_\d{8}_\d{6}\.png"
    allocation_files = []
    
    for root, dirs, files in os.walk(allocation_dir):
        for file in files:
            if re.match(pattern, file):
                allocation_files.append(os.path.join(root, file))
    
    if not allocation_files:
        logger.error("❌ No Allocation Weights Graphic found")
        return False
    
    # Use the most recent file
    allocation_file = max(allocation_files, key=os.path.getmtime)
    logger.info(f"Found Allocation Weights Graphic: {os.path.basename(allocation_file)}")
    
    # Check filename format
    check_filename_format(allocation_file, pattern)
    
    # Check image dimensions and DPI
    check_image_dimensions_and_dpi(allocation_file, min_dpi=300)
    
    return True

def verify_monthly_returns_graphic(reports_dir):
    """Verify the Monthly Returns Graphic against the standards."""
    logger.info("\n=== Verifying Monthly Returns Graphic ===")
    
    # Find the monthly returns graphic
    pattern = r"EMA_V3_1_monthly_returns_\d{8}_\d{6}\.png"
    monthly_files = []
    
    for file in os.listdir(reports_dir):
        if re.match(pattern, file):
            monthly_files.append(os.path.join(reports_dir, file))
    
    if not monthly_files:
        logger.error("❌ No Monthly Returns Graphic found")
        return False
    
    # Use the most recent file
    monthly_file = max(monthly_files, key=os.path.getmtime)
    logger.info(f"Found Monthly Returns Graphic: {os.path.basename(monthly_file)}")
    
    # Check filename format
    check_filename_format(monthly_file, pattern)
    
    # Check image dimensions and DPI
    check_image_dimensions_and_dpi(monthly_file, min_dpi=300)
    
    return True

def verify_combined_returns_drawdown_graphic(reports_dir):
    """Verify the Combined Returns & Drawdown Graphic against the standards."""
    logger.info("\n=== Verifying Combined Returns & Drawdown Graphic ===")
    
    # Find the combined returns & drawdown graphic
    pattern = r"EMA_V3_1_cumulative_returns_drawdown_\d{8}_\d{6}\.png"
    combined_files = []
    
    for file in os.listdir(reports_dir):
        if re.match(pattern, file):
            combined_files.append(os.path.join(reports_dir, file))
    
    if not combined_files:
        logger.error("❌ No Combined Returns & Drawdown Graphic found")
        return False
    
    # Use the most recent file
    combined_file = max(combined_files, key=os.path.getmtime)
    logger.info(f"Found Combined Returns & Drawdown Graphic: {os.path.basename(combined_file)}")
    
    # Check filename format
    check_filename_format(combined_file, pattern)
    
    # Check image dimensions and DPI
    check_image_dimensions_and_dpi(combined_file, min_dpi=300)
    
    return True

def run_verification():
    """Run verification of all reports against the standards."""
    logger.info("Starting verification of V3 reports against standards")
    
    # Define reports directory
    reports_dir = os.path.join('output', 'v3_test_reports')
    if not os.path.isdir(reports_dir):
        logger.error(f"❌ Reports directory not found: {reports_dir}")
        return False
    
    # Verify each report type
    performance_ok = verify_performance_table_report(reports_dir)
    allocation_ok = verify_allocation_weights_graphic(reports_dir)
    monthly_ok = verify_monthly_returns_graphic(reports_dir)
    combined_ok = verify_combined_returns_drawdown_graphic(reports_dir)
    
    # Summary
    logger.info("\n=== Verification Summary ===")
    logger.info(f"Performance Table Report: {'✅ PASS' if performance_ok else '❌ FAIL'}")
    logger.info(f"Allocation Weights Graphic: {'✅ PASS' if allocation_ok else '❌ FAIL'}")
    logger.info(f"Monthly Returns Graphic: {'✅ PASS' if monthly_ok else '❌ FAIL'}")
    logger.info(f"Combined Returns & Drawdown Graphic: {'✅ PASS' if combined_ok else '❌ FAIL'}")
    
    all_ok = performance_ok and allocation_ok and monthly_ok and combined_ok
    logger.info(f"\nOverall Verification: {'✅ PASS' if all_ok else '❌ FAIL'}")
    
    return all_ok

if __name__ == "__main__":
    run_verification()
