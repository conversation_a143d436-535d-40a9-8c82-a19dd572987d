# CPS V4 Project Status & Active Development Plan

## Project Overview

CPS V4 is a configuration-driven backtesting and trading system for financial asset allocation strategies. All major infrastructure work is complete - file reorganization, module refactoring, and path centralization are done.

**Current Focus**: Fix optimization loop execution and resolve configuration architecture issues.

**Core Process Plan**: Follows the detailed optimization plan in `memory-bank/optimization_plan.md` using the `EquityCurvesManager` class for matrix-based parameter combination testing.

## 🎯 CURRENT STATUS: OPTIMIZATION XLSX INTEGRATION COMPLETED ✅

### **MAJOR ACHIEVEMENT: Optimization System Fully Functional (August 2, 2025)**

**Date**: 2025-08-02
**Status**: OPTIMIZATION SYSTEM FULLY OPERATIONAL

**CRITICAL SUCCESSES ACHIEVED**:
- ✅ **Optimization produces 12 UNIQUE equity curves with realistic portfolio values ($1.18M to $1.44M)**
- ✅ **XLSX Integration**: EMA_V3_1_performance_tables_*.xlsx reports now properly display optimization results
- ✅ **Real Market Data**: Fixed hardcoded benchmark returns (0.0002 daily) to use actual price data
- ✅ **Excel Formatting**: Metrics now recognized as numbers, not text, with proper percentage display
- ✅ **UPI Calculation**: Implemented real Ulcer Performance Index calculation
- ✅ **Dynamic ComboID**: Only includes parameters with optimize=True (S{st}_M{mt} format)

**CRITICAL RULE VIOLATION DISCOVERED & FIXED**:
- ❌ **Found**: Hardcoded fallback values in `_get_risk_free_rate()` function violating "NEVER use mock data" rule
- ✅ **Fixed**: Replaced fallback logic with hard-fail behavior to enforce proper configuration

**Previously Resolved Issues**:
- ✅ **Architectural Violation**: Optimization flow no longer calls single-mode functions
- ✅ **Function Naming Convention**: All single mode functions renamed with "_single" suffix
- ✅ **Section Dependency Bug**: Migrated to section-agnostic parameter detection
- ✅ **Centralized Logging**: Replaced ad-hoc logging with smart_logging.py system
- ✅ **Configuration Management**: All settings centralized in settings_parameters_v4.ini
- ✅ **Output Reduction**: Reduced optimization output from 80,000+ to under 20,000 lines
- ✅ **Pipeline Separation**: Single mode uses direct path, optimization uses validation framework

**URGENT PRIORITY**: Debug and fix optimization system to generate genuinely unique equity curves

## 📋 IMMEDIATE TASKS & EXECUTION PLAN

### **Task 1: Fix Architectural Violation - Separate Function Implementation** 🔥 **PRIORITY 1**

**Objective**: Implement separate function architecture to eliminate optimization flow calling single-mode functions.

**Current Status**: SUBPROCESS ELIMINATION COMPLETED - ARCHITECTURAL VIOLATION IDENTIFIED

**Recent Progress**:

1. ✅ **Optimization Loop Fixed**: Pipeline now calls actual matrix optimization instead of baseline iteration
2. ✅ **Configuration Architecture**: Implemented section-agnostic parameter lookup via `ConfigHelper`
3. ✅ **CSV Flag Issues**: Fixed parameter propagation from validation to pipeline execution
4. ✅ **File Output Control**: Implemented proper file creation control per specification
5. ✅ **Subprocess Elimination**: Replaced subprocess calls with direct function calls (no more infinite recursion)
6. ❌ **Architectural Violation**: Optimization flow still calls `run_single_pipeline()` (single-mode function)

**Current Action Steps**:

- **Implement**: `run_optimization_combination()` - pure optimization function (no single-mode logic)
- **Clean**: `run_single_pipeline()` - remove all optimization-aware logic and environment variable checking
- **Update**: Optimization flow to call optimization-specific functions only
- **Test**: Both flows independently to ensure no cross-contamination
- **Validate**: Confirm clean separation and no architectural violations

**Success Criteria**:

- No optimization function calls single-mode functions
- No single-mode function checks optimization flags
- Validation passes all 10 steps with clean architecture
- Both flows work independently without mixed logic

### **Task 2: Standardize Configuration Architecture** 🔥 **PRIORITY 2**

**Objective**: Complete codebase-wide standardization to section-agnostic configuration approach.

**Action Steps**:

1. **Audit**: Find all instances of section-specific config calls (`config.getboolean('Section', 'param')`)
2. **Replace**: Convert to section-agnostic calls using `ConfigHelper` pattern
3. **Validate**: Ensure all parameters are unique across INI file sections
4. **Test**: Verify no configuration-related bugs remain

**Success Criteria**:

- All config access uses section-agnostic approach
- No duplicate parameter names across sections
- Configuration bugs eliminated permanently
- Simplified parameter access for developers

### **Task 3: Complete XLSX Chart Generation** 🔥 **PRIORITY 3**

**Objective**: Implement remaining chart generation features (Q8-Q9 specifications).

**Action Steps**:

1. **Implement**: Monthly returns heatmap with color scheme
2. **Add**: Benchmark selection approach
3. **Generate**: Monthly returns & drawdown PNG charts (≥300 DPI)
4. **Integrate**: Charts into XLSX report

**Success Criteria**:

- All charts generate correctly
- Charts meet DPI and formatting requirements
- Charts integrate seamlessly with XLSX tabs

### **Task 4: GUI Parameter Management Integration** 🔥 **PRIORITY 4**

**Objective**: Integrate GUI with CPS v4 parameter system for optimization management.

**Action Steps**:

1. **Analyze**: Current `v4/app/gui` structure
2. **Design**: Integration with optimization parameter system
3. **Implement**: Parameter management interface
4. **Test**: GUI-driven optimization execution

**Success Criteria**:

- GUI can manage optimization parameters
- Parameters reflect in report outputs
- Seamless integration with existing workflow

## 🔧 VALIDATION FRAMEWORK STATUS

### **Critical Fixes Applied** ✅

- **Optimization Loop**: Fixed `modes.py` to call actual `_run_matrix_optimization()` instead of baseline iteration
- **Section-Agnostic Config**: Implemented `ConfigHelper` class to eliminate section mismatch bugs
- **CSV Flag Propagation**: Fixed parameter passing from validation script to pipeline execution
- **File Output Control**: Implemented proper CSV control flags per `allowed_output.md` specification
- **INI Syntax**: Fixed date format escaping issue (`%%Y%%m%%d`)
- **Prohibited Files**: Blocked creation of allocation_history and trade_log during optimization
- **Pipeline Mode Detection**: Simplified logic to properly detect optimization mode when ANY parameter set to optimize
- **Simple Optimization Detection**: Added `has_any_optimization_parameters()` function for super simple detection

### **Current Validation Status**

**Step 1**: Parameter Extraction ✅ WORKING
**Step 2**: Settings File Creation ✅ WORKING
**Step 3**: Single Combination Test ❌ FAILING (OSError: [Errno 22] Invalid argument)
**Step 4-10**: Not reached due to Step 3 failure

### **Known Issues**

- ✅ **FIXED**: Subprocess infinite recursion eliminated with direct function calls
- ❌ **ARCHITECTURAL VIOLATION**: Optimization flow calls `run_single_pipeline()` (single-mode function)
- ❌ **Current Error**: OSError: [Errno 22] Invalid argument in `run_single_pipeline()` execution
- **Root Cause**: Mixed-logic functions violate clean branch gate architecture
- **Solution**: Implement separate function architecture per `docs/CPS_v4/separate_function_plan.md`

## 🚀 EXECUTION WORKFLOW

### **Current Production Entry Point**

```bash
test_optimization_fix_simple.bat
└── test_optimization_validation.py
    └── v4/py_reporting/performance_table_generator.py
        └── Matrix Optimization Loop:
            ├── v4/run_unified_pipeline.py (per combination)
            └── v4/py_reporting/report_modules/ (XLSX generation)
```

### **Key Implementation Rules**

- **No Fallbacks**: Hard fail on missing configurations
- **Real Data Only**: No synthetic/mock data allowed
- **Matrix-Based**: Use `EquityCurvesManager` for parameter combinations
- **Config Path**: `v4/settings/settings_parameters_v4.ini` (canonical)

## 📚 REFERENCE DOCUMENTATION

**Core Process**: See `memory-bank/optimization_plan.md` for complete matrix-based optimization architecture details.

**Key Files**:

- `v4/py_reporting/performance_table_generator.py` - Main optimization orchestration
- `v4/py_reporting/equity_curves_manager.py` - Matrix-based equity curve management
- `test_optimization_validation.py` - 10-step validation framework

---

## 📋 IMPLEMENTATION ROADMAP

### **Phase 1: Separate Function Architecture** (IMMEDIATE)
**Document**: `docs/CPS_v4/separate_function_plan.md`
**Timeline**: 4-6 hours
**Priority**: CRITICAL - Fixes architectural violation

1. Create `run_optimization_combination()` - pure optimization function
2. Clean `run_single_pipeline()` - remove optimization logic
3. Update optimization flow callers
4. Test both flows independently

### **Phase 2: Complete Optimization Validation** (NEXT)
**Timeline**: 2-3 hours
**Priority**: HIGH

1. Validate separate function architecture works
2. Confirm all 10 validation steps pass
3. Verify unique equity curves for each combination

### **Phase 3: XLSX Chart Generation** (FOLLOW-UP)
**Timeline**: 4-6 hours
**Priority**: MEDIUM

1. Implement Q8-Q9 chart specifications
2. Generate monthly returns & drawdown PNG charts
3. Complete Performance Table XLSX

---

**Next Action**: Implement separate function architecture per `docs/CPS_v4/separate_function_plan.md` to fix architectural violation.

*Last Updated: July 29, 2025*
*Status: Subprocess elimination completed - architectural violation identified and planned for fix*
