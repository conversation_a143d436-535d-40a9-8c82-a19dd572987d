"""
v4/engine/performance_metrics.py
Module for calculating performance metrics for backtests.
Part of the backtest engine system (CPS v4 compliant).
"""

import pandas as pd
import numpy as np
import logging

logger = logging.getLogger(__name__)

def calculate_performance_metrics(portfolio_values, trades=None):
    """
    Calculate performance metrics from portfolio values and trades.
    
    Args:
        portfolio_values (Series): Historical portfolio values
        trades (list): List of trade objects
        
    Returns:
        dict: Performance metrics
    """
    metrics = {}
    
    # Return early if not enough data
    if portfolio_values is None or len(portfolio_values) <= 1:
        logger.warning("Cannot calculate performance metrics: Insufficient data")
        return {
            'cagr': 0,
            'volatility': 0,
            'sharpe': 0,
            'max_drawdown': 0,
            'turnover': 0,
            'win_rate': 0
        }
    
    # Calculate returns
    strategy_returns = portfolio_values.pct_change().fillna(0)
    total_return = (portfolio_values.iloc[-1] / portfolio_values.iloc[0]) - 1
    
    # CAGR
    days = (portfolio_values.index[-1] - portfolio_values.index[0]).days
    years = days / 365
    if years > 0:
        cagr = (1 + total_return) ** (1 / years) - 1
    else:
        cagr = 0
    
    # Volatility (annualized)
    volatility = strategy_returns.std() * np.sqrt(252)
    
    # Sharpe ratio (assuming risk-free rate of 0)
    sharpe = cagr / volatility if volatility > 0 else 0
    
    # Maximum drawdown
    rolling_max = portfolio_values.cummax()
    drawdowns = (portfolio_values / rolling_max) - 1
    max_drawdown = drawdowns.min()
    
    # Turnover
    turnover = 0
    if trades:
        total_traded_value = sum(abs(trade.value) for trade in trades)
        avg_portfolio_value = portfolio_values.mean()
        if avg_portfolio_value > 0:
            turnover = total_traded_value / (2 * avg_portfolio_value)
    
    # Win rate
    win_rate = 0
    if trades:
        wins = sum(1 for trade in trades if trade.profit > 0)
        win_rate = wins / len(trades) if len(trades) > 0 else 0
    
    # Monthly returns
    monthly_returns = strategy_returns.resample('M').apply(lambda x: (1 + x).prod() - 1)
    
    # Yearly returns
    yearly_returns = strategy_returns.resample('Y').apply(lambda x: (1 + x).prod() - 1)
    
    return {
        'cagr': cagr,
        'volatility': volatility,
        'sharpe': sharpe,
        'max_drawdown': max_drawdown,
        'turnover': turnover,
        'win_rate': win_rate,
        'monthly_returns': monthly_returns,
        'yearly_returns': yearly_returns,
        'strategy_returns': strategy_returns,
        'total_return': total_return
    }

def calculate_drawdowns(portfolio_values):
    """
    Calculate drawdown series from portfolio values.
    
    Args:
        portfolio_values (Series): Historical portfolio values
        
    Returns:
        Series: Drawdowns series
    """
    if portfolio_values is None or len(portfolio_values) <= 1:
        return pd.Series()
        
    rolling_max = portfolio_values.cummax()
    drawdowns = (portfolio_values / rolling_max) - 1
    return drawdowns

def calculate_rolling_metrics(returns, window=252):
    """
    Calculate rolling performance metrics.
    
    Args:
        returns (Series): Returns series
        window (int): Rolling window size
        
    Returns:
        dict: Dictionary of rolling metrics
    """
    if returns is None or len(returns) < window:
        return {}
        
    # Rolling volatility (annualized)
    rolling_vol = returns.rolling(window=window).std() * np.sqrt(252)
    
    # Rolling Sharpe ratio (annualized, assuming risk-free rate of 0)
    rolling_ret = returns.rolling(window=window).mean() * 252
    rolling_sharpe = rolling_ret / rolling_vol
    
    # Rolling max drawdown
    rolling_drawdown = pd.Series(index=returns.index)
    for i in range(window, len(returns) + 1):
        window_returns = returns.iloc[i-window:i]
        cum_returns = (1 + window_returns).cumprod()
        drawdown = (cum_returns / cum_returns.cummax()) - 1
        rolling_drawdown.iloc[i-1] = drawdown.min()
    
    return {
        'rolling_volatility': rolling_vol,
        'rolling_sharpe': rolling_sharpe,
        'rolling_drawdown': rolling_drawdown
    }

def calculate_risk_metrics(returns, benchmark_returns=None):
    """
    Calculate risk metrics including beta, alpha, and information ratio.
    
    Args:
        returns (Series): Strategy returns
        benchmark_returns (Series): Benchmark returns
        
    Returns:
        dict: Risk metrics
    """
    if returns is None or len(returns) <= 1:
        return {
            'beta': 0,
            'alpha': 0,
            'information_ratio': 0,
            'tracking_error': 0
        }
        
    # If no benchmark provided, return default values
    if benchmark_returns is None or len(benchmark_returns) <= 1:
        return {
            'beta': 0,
            'alpha': 0,
            'information_ratio': 0,
            'tracking_error': 0
        }
    
    # Align dates
    common_idx = returns.index.intersection(benchmark_returns.index)
    if len(common_idx) <= 1:
        return {
            'beta': 0,
            'alpha': 0,
            'information_ratio': 0,
            'tracking_error': 0
        }
        
    aligned_returns = returns.loc[common_idx]
    aligned_benchmark = benchmark_returns.loc[common_idx]
    
    # Calculate beta
    covariance = np.cov(aligned_returns, aligned_benchmark)[0, 1]
    benchmark_variance = np.var(aligned_benchmark)
    beta = covariance / benchmark_variance if benchmark_variance > 0 else 0
    
    # Calculate alpha (annualized)
    avg_return = aligned_returns.mean() * 252
    avg_benchmark = aligned_benchmark.mean() * 252
    alpha = avg_return - (beta * avg_benchmark)
    
    # Calculate tracking error
    tracking_diff = aligned_returns - aligned_benchmark
    tracking_error = tracking_diff.std() * np.sqrt(252)
    
    # Calculate information ratio
    information_ratio = (avg_return - avg_benchmark) / tracking_error if tracking_error > 0 else 0
    
    return {
        'beta': beta,
        'alpha': alpha,
        'information_ratio': information_ratio,
        'tracking_error': tracking_error
    }
