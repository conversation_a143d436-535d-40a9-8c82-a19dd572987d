#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
settings_utils.py

Utility functions for settings management, moved from settings_CPS_v4.py
to maintain the 450-line limit.

Author: AI Assistant
Date: 2025-01-18
"""

import datetime
import configparser
from pathlib import Path
from typing import Dict, Any

PARAM_TYPES = {
    'SimpleA': 'simple_alphanumeric',
    'SimpleN': 'simple_numeric',
    'ComplexN': 'complex_numeric',
    'AlphaList': 'alpha_list'
}

def create_settings_template(settings_file_path: Path) -> None:
    """
    Create a template settings_parameters_v4.ini file if one doesn't exist yet.
    This function is primarily for new project setup.
    
    Args:
        settings_file_path: Path to the settings file
    """
    if settings_file_path.exists():
        print(f"Settings file already exists at {settings_file_path}")
        return
        
    # Create a minimal template with examples of all parameter types
    with open(settings_file_path, 'w') as f:
        f.write("; settings_parameters_v4.ini\n")
        f.write("; Central Parameter System v4 - Single Source of Truth\n")
        f.write("; Date: " + datetime.datetime.now().strftime('%Y-%m-%d') + "\n")
        f.write("; This file contains all parameters for the CPS v4 system\n\n")
        
        f.write("; =======================================================================\n")
        f.write("; PARAMETER STRUCTURE TEMPLATES\n")
        f.write("; =======================================================================\n")
        f.write("; Below are templates for different parameter types. Copy, paste, and modify\n")
        f.write("; as needed to change or add new parameters.\n")
        f.write(";\n")
        f.write("; Type 1: SimpleA - Simple Alphanumeric Value\n")
        f.write("; Format: variable_name = value\n")
        f.write("; Example: risk_free_ticker = ^IRX\n")
        f.write(";\n")
        f.write("; Type 2: SimpleN - Simple Numeric Value\n")
        f.write("; Format: variable_name = numeric_value\n")
        f.write("; Example: initial_capital = 1000000\n")
        f.write(";\n")
        f.write("; Type 3: ComplexN - Optimizable Numeric Value\n")
        f.write("; Format: variable_name = (optimize=True/False, default_value=value, min_value=min, max_value=max, increment=step)\n")
        f.write("; Example: ema_short_period = (optimize=True, default_value=12, min_value=5, max_value=20, increment=1)\n")
        f.write(";\n")
        f.write("; Type 4: AlphaList - List of Values with Pick List\n")
        f.write("; Format: variable_name = (default_list, picklist_variable)\n")
        f.write("; Example: tickers = (Group1ETFBase, ETFpicklist)\n")
        f.write(";\n")
        
        # Add minimal working sections
        f.write("\n; =====================================================\n")
        f.write("; PARAMETER LISTS\n")
        f.write("; =====================================================\n\n")
        f.write("[Lists]\n")
        f.write("; Example lists\n")
        f.write("Group1ETFBase = ('SPY', 'SHV', 'EFA')\n")
        f.write("ETFpicklist = Group1ETFBase\n\n")
        
        f.write("\n; =====================================================\n")
        f.write("; CORE PARAMETERS\n")
        f.write("; =====================================================\n\n")
        f.write("[Core]\n")
        f.write("initial_capital = 1000000\n")
        f.write("tickers = (Group1ETFBase, ETFpicklist)\n\n")
        
    print(f"Created settings template at: {settings_file_path}")
    print("Please edit this file to add your specific parameters.")
    print("Use the template examples at the top as a guide for parameter formats.")


def get_parameter_type(param_value: Any) -> str:
    """
    Identify the parameter type based on its structure and format.
    
    Args:
        param_value: The parameter value to identify
        
    Returns:
        String indicating the parameter type (SimpleA, SimpleN, ComplexN, AlphaList)
    """
    # Check for dictionary-based complex types
    if isinstance(param_value, dict):
        # Check for ComplexN structure (optimization parameters)
        if any(key in param_value for key in ['optimize', 'default_value', 'min_value']):
            return PARAM_TYPES['ComplexN']
        # Check for AlphaList structure (picklists)
        elif any(key in param_value for key in ['default', 'picklist']):
            return PARAM_TYPES['AlphaList']
    
    # Check for SimpleN (numeric values)
    elif isinstance(param_value, (int, float)):
        return PARAM_TYPES['SimpleN']
    
    # Check for AlphaList as tuple or list
    elif isinstance(param_value, (list, tuple)):
        return PARAM_TYPES['AlphaList']
        
    # Default to SimpleA (strings, booleans, etc.)
    return PARAM_TYPES['SimpleA']


def print_parameters_summary(settings: Dict[str, Any]) -> None:
    """
    Print a summary of the loaded parameters grouped by type.
    
    Args:
        settings: The settings dictionary as loaded by load_settings
    """
    # Count parameters by type
    type_counts = {
        'SimpleA': 0,
        'SimpleN': 0,
        'ComplexN': 0,
        'AlphaList': 0
    }
    
    # Collect parameter names by type
    param_by_type = {
        'SimpleA': [],
        'SimpleN': [],
        'ComplexN': [],
        'AlphaList': []
    }
    
    # Process each section
    for section, params in settings.items():
        if section.lower() == 'lists':
            continue
            
        for param_name, value in params.items():
            # Use our standard function to identify parameter type
            param_type_full = get_parameter_type(value)
            param_type = next(k for k, v in PARAM_TYPES.items() if v == param_type_full)
            
            # Update counts
            type_counts[param_type] += 1
            param_by_type[param_type].append(f"{section}.{param_name}")
    
    # Print summary
    print("\nParameter Type Summary:")
    for param_type, count in type_counts.items():
        print(f"  {param_type}: {count} parameters")
        
    # Print examples
    print("\nExample parameters by type:")
    for param_type, params in param_by_type.items():
        if params:
            example_count = min(3, len(params))
            examples = params[:example_count]
            print(f"  {param_type}: {', '.join(examples)}")


def _deep_update(target: Dict, source: Dict) -> None:
    """
    Recursively update a nested dictionary structure.
    
    Args:
        target: The dictionary to update
        source: The dictionary with new values
    """
    for key, value in source.items():
        if key in target and isinstance(target[key], dict) and isinstance(value, dict):
            _deep_update(target[key], value)
        else:
            target[key] = value


def _extract_value_from_complex_dict(param_value_from_settings):
    """If param_value_from_settings is a ComplexN dict, return its 'default_value'.
    Otherwise, return param_value_from_settings as is.
    Raises ValueError if it's a dict but not a valid ComplexN structure (missing 'default_value').
    
    This is a utility function for refactoring code to use the new parameter paths.
    """
    if isinstance(param_value_from_settings, dict):
        if 'default_value' not in param_value_from_settings:
            # This implies a malformed ComplexN dictionary if parsing didn't catch it,
            # or a non-ComplexN dictionary was unexpectedly passed.
            raise ValueError(
                f"Input dictionary '{param_value_from_settings}' for ComplexN extraction "
                f"is missing the required 'default_value' key."
            )
        return param_value_from_settings['default_value']
    return param_value_from_settings

def create_default_settings(settings_file_path: Path) -> None:
    """
    Create a default settings file if it doesn't exist.
    This is used during initial setup.
    """
    if settings_file_path.exists():
        return
        
    config = configparser.ConfigParser()
    
    # System section
    config['System'] = {
        'log_level': 'INFO',
        'log_file': 'reporting/logs/cps_v4.log'
    }
    
    # Report section
    config['Report'] = {
        'create_excel': 'True',
        'excel_format': 'xlsx',
        'save_trade_log': 'True',
        'include_summary': 'True'
    }
    
    with open(settings_file_path, 'w') as f:
        config.write(f)
