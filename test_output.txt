2025-06-03 17:10:11,977 - INFO - V3 reporter adapter modules loaded successfully
2025-06-03 17:10:11,978 - INFO - V3 reporting modules loaded successfully
2025-06-03 17:10:12,006 - v3_engine.parameter_registry - DEBUG - Module-level logger configured by ParameterRegistry.__init__.
2025-06-03 17:10:12,006 - v3_engine.parameter_registry - INFO - Parameter registry initialized
2025-06-03 17:10:12,006 - v3_engine.parameter_registry - INFO - Parameter registry has been reset.
2025-06-03 17:10:12,006 - v3_engine.parameter_registry - DEBUG - Added parameter 'create_excel' to optimizable parameters
2025-06-03 17:10:12,006 - v3_engine.parameter_registry - DEBUG - Added parameter 'save_trade_log' to optimizable parameters
2025-06-03 17:10:12,006 - v3_engine.parameter_registry - DEBUG - Added parameter 'metrics' to optimizable parameters
2025-06-03 17:10:12,006 - v3_engine.parameter_registry - DEBUG - Added parameter 'actual_execution_delay' to optimizable parameters
2025-06-03 17:10:12,006 - INFO - Reporting parameters registered successfully
2025-06-03 17:10:12,006 - v3_engine.parameter_registry - DEBUG - Added parameter 'create_charts' to optimizable parameters
2025-06-03 17:10:12,006 - v3_engine.parameter_registry - DEBUG - Added parameter 'chart_types' to optimizable parameters
2025-06-03 17:10:12,006 - v3_engine.parameter_registry - DEBUG - Added parameter 'chart_format' to optimizable parameters
2025-06-03 17:10:12,006 - v3_engine.parameter_registry - DEBUG - Added parameter 'chart_dpi' to optimizable parameters
2025-06-03 17:10:12,006 - v3_engine.parameter_registry - DEBUG - Added parameter 'colorblind_friendly' to optimizable parameters
2025-06-03 17:10:12,006 - INFO - Visualization parameters registered successfully
2025-06-03 17:10:12,428 - v3_engine.parameter_registry - INFO - Parameter registry initialized
2025-06-03 17:10:12,429 - v3_engine.V3_perf_repadapt_paramconvert - PARAMCONVERT_DEBUG - WARNING - Error processing parameter create_excel. Exception: KeyError - "Parameter 'create_excel' not found in any group". Defaulting to strategy_params.
2025-06-03 17:10:12,429 - v3_engine.V3_perf_repadapt_paramconvert - PARAMCONVERT_DEBUG - WARNING - Error processing parameter save_trade_log. Exception: KeyError - "Parameter 'save_trade_log' not found in any group". Defaulting to strategy_params.
2025-06-03 17:10:12,429 - v3_engine.V3_perf_repadapt_paramconvert - PARAMCONVERT_DEBUG - WARNING - Error processing parameter metrics. Exception: KeyError - "Parameter 'metrics' not found in any group". Defaulting to strategy_params.
2025-06-03 17:10:12,429 - v3_engine.V3_perf_repadapt_paramconvert - PARAMCONVERT_DEBUG - WARNING - Error processing parameter create_charts. Exception: KeyError - "Parameter 'create_charts' not found in any group". Defaulting to strategy_params.
2025-06-03 17:10:12,429 - v3_engine.V3_perf_repadapt_paramconvert - PARAMCONVERT_DEBUG - WARNING - Error processing parameter chart_types. Exception: KeyError - "Parameter 'chart_types' not found in any group". Defaulting to strategy_params.
2025-06-03 17:10:12,429 - v3_engine.V3_perf_repadapt_paramconvert - PARAMCONVERT_DEBUG - WARNING - Error processing parameter chart_format. Exception: KeyError - "Parameter 'chart_format' not found in any group". Defaulting to strategy_params.
2025-06-03 17:10:12,429 - v3_engine.V3_perf_repadapt_paramconvert - PARAMCONVERT_DEBUG - WARNING - Error processing parameter chart_dpi. Exception: KeyError - "Parameter 'chart_dpi' not found in any group". Defaulting to strategy_params.
2025-06-03 17:10:12,429 - v3_engine.V3_perf_repadapt_paramconvert - PARAMCONVERT_DEBUG - WARNING - Error processing parameter colorblind_friendly. Exception: KeyError - "Parameter 'colorblind_friendly' not found in any group". Defaulting to strategy_params.
2025-06-03 17:10:12,429 - v3_engine.V3_perf_repadapt_paramconvert - PARAMCONVERT_DEBUG - WARNING - Error processing parameter st_lookback. Exception: KeyError - "Parameter 'st_lookback' not found in any group". Defaulting to strategy_params.
2025-06-03 17:10:12,429 - v3_engine.V3_perf_repadapt_paramconvert - PARAMCONVERT_DEBUG - WARNING - Error processing parameter mt_lookback. Exception: KeyError - "Parameter 'mt_lookback' not found in any group". Defaulting to strategy_params.
2025-06-03 17:10:12,429 - v3_engine.V3_perf_repadapt_paramconvert - PARAMCONVERT_DEBUG - WARNING - Error processing parameter lt_lookback. Exception: KeyError - "Parameter 'lt_lookback' not found in any group". Defaulting to strategy_params.
2025-06-03 17:10:12,429 - v3_engine.V3_perf_repadapt_paramconvert - PARAMCONVERT_DEBUG - WARNING - Error processing parameter top_n. Exception: KeyError - "Parameter 'top_n' not found in any group". Defaulting to strategy_params.
2025-06-03 17:10:12,429 - v3_engine.V3_perf_repadapt_legacybridge - ERROR - Missing required EMA parameters in report: ['st_lookback', 'mt_lookback', 'lt_lookback', 'top_n']
2025-06-03 17:10:12,429 - ERROR - Missing required EMA parameters in report: ['st_lookback', 'mt_lookback', 'lt_lookback', 'top_n']
2025-06-03 17:10:12,429 - v3_engine.V3_perf_repadapt_legacybridge - INFO - Generating performance report for strategy 'test_strategy' with ticker group 'default'
2025-06-03 17:10:12,429 - INFO - Generating performance report for strategy 'test_strategy' with ticker group 'default'
2025-06-03 17:10:12,429 - v3_engine.V3_perf_repadapt_legacybridge - WARNING - Error processing parameter reporting: "Parameter 'reporting' not found in any group"
2025-06-03 17:10:12,429 - WARNING - Error processing parameter reporting: "Parameter 'reporting' not found in any group"
2025-06-03 17:10:12,429 - v3_engine.V3_perf_repadapt_legacybridge - WARNING - Error processing parameter visualization: "Parameter 'visualization' not found in any group"
2025-06-03 17:10:12,429 - WARNING - Error processing parameter visualization: "Parameter 'visualization' not found in any group"
2025-06-03 17:10:12,430 - v3_engine.V3_perf_repadapt_legacybridge - INFO - Successfully calculated benchmark metrics
2025-06-03 17:10:12,430 - INFO - Successfully calculated benchmark metrics
2025-06-03 17:10:12,430 - v3_engine.V3_perf_repadapt_legacybridge - INFO - Added benchmark metrics: ['annualized_return', 'cagr', 'volatility', 'max_drawdown', 'sharpe', 'total_return', 'annual_returns', 'beta', 'alpha', 'sortino', 'win_rate', 'calmar_ratio', 'information_ratio', 'consistent_return', 'drawdown_recovery_time']
2025-06-03 17:10:12,430 - INFO - Added benchmark metrics: ['annualized_return', 'cagr', 'volatility', 'max_drawdown', 'sharpe', 'total_return', 'annual_returns', 'beta', 'alpha', 'sortino', 'win_rate', 'calmar_ratio', 'information_ratio', 'consistent_return', 'drawdown_recovery_time']
F:\AI_Library\my_quant_env\Lib\site-packages\pandas\core\tools\numeric.py:173: FutureWarning: errors='ignore' is deprecated and will raise in a future version. Use to_numeric without passing `errors` and catch exceptions explicitly instead
  warnings.warn(
2025-06-03 17:10:12,433 - v3_engine.V3_perf_repadapt_legacybridge - INFO - Generating performance charts with format=png, dpi=300, types=standard
2025-06-03 17:10:12,433 - INFO - Generating performance charts with format=png, dpi=300, types=standard
2025-06-03 17:10:12,433 - v3_engine.V3_perf_repadapt_legacybridge - INFO - Generated 1 charts in S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\tests\test_output\charts
2025-06-03 17:10:12,433 - INFO - Generated 1 charts in S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\tests\test_output\charts
2025-06-03 17:10:12,434 - v3_engine.V3_perf_repadapt_legacybridge - INFO -   - cumulative_returns: test_report.xlsx
2025-06-03 17:10:12,434 - INFO -   - cumulative_returns: test_report.xlsx
2025-06-03 17:10:12,434 - v3_engine.V3_perf_repadapt_legacybridge - INFO - Test environment detected, using mock-compatible Excel writing
2025-06-03 17:10:12,434 - INFO - Test environment detected, using mock-compatible Excel writing
2025-06-03 17:10:12,434 - v3_engine.V3_perf_repadapt_legacybridge - INFO - Would write performance_df to 'Performance' sheet
2025-06-03 17:10:12,434 - INFO - Would write performance_df to 'Performance' sheet
2025-06-03 17:10:12,434 - v3_engine.V3_perf_repadapt_legacybridge - INFO - Would write Signal History data to sheet
2025-06-03 17:10:12,434 - INFO - Would write Signal History data to sheet
2025-06-03 17:10:12,434 - v3_engine.V3_perf_repadapt_legacybridge - INFO - Performance report generated at S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\output/test_strategy_performance_tables_2025-06-03_171012.xlsx
2025-06-03 17:10:12,434 - INFO - Performance report generated at S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\output/test_strategy_performance_tables_2025-06-03_171012.xlsx
.2025-06-03 17:10:12,434 - v3_engine.parameter_registry - INFO - Parameter registry has been reset.
2025-06-03 17:10:12,434 - v3_engine.parameter_registry - DEBUG - Added parameter 'create_excel' to optimizable parameters
2025-06-03 17:10:12,434 - v3_engine.parameter_registry - DEBUG - Added parameter 'save_trade_log' to optimizable parameters
2025-06-03 17:10:12,434 - v3_engine.parameter_registry - DEBUG - Added parameter 'metrics' to optimizable parameters
2025-06-03 17:10:12,434 - v3_engine.parameter_registry - DEBUG - Added parameter 'actual_execution_delay' to optimizable parameters
2025-06-03 17:10:12,434 - INFO - Reporting parameters registered successfully
2025-06-03 17:10:12,434 - v3_engine.parameter_registry - DEBUG - Added parameter 'create_charts' to optimizable parameters
2025-06-03 17:10:12,434 - v3_engine.parameter_registry - DEBUG - Added parameter 'chart_types' to optimizable parameters
2025-06-03 17:10:12,434 - v3_engine.parameter_registry - DEBUG - Added parameter 'chart_format' to optimizable parameters
2025-06-03 17:10:12,434 - v3_engine.parameter_registry - DEBUG - Added parameter 'chart_dpi' to optimizable parameters
2025-06-03 17:10:12,434 - v3_engine.parameter_registry - DEBUG - Added parameter 'colorblind_friendly' to optimizable parameters
2025-06-03 17:10:12,434 - INFO - Visualization parameters registered successfully
2025-06-03 17:10:12,858 - v3_engine.parameter_registry - INFO - Parameter registry initialized
2025-06-03 17:10:12,858 - v3_engine.parameter_registry - DEBUG - Added parameter 'create_excel' to optimizable parameters
2025-06-03 17:10:12,858 - v3_engine.parameter_registry - DEBUG - Added parameter 'save_trade_log' to optimizable parameters
2025-06-03 17:10:12,858 - v3_engine.parameter_registry - DEBUG - Added parameter 'metrics' to optimizable parameters
2025-06-03 17:10:12,858 - v3_engine.parameter_registry - DEBUG - Added parameter 'actual_execution_delay' to optimizable parameters
2025-06-03 17:10:12,858 - INFO - Reporting parameters registered successfully
2025-06-03 17:10:12,858 - v3_engine.parameter_registry - DEBUG - Added parameter 'create_charts' to optimizable parameters
2025-06-03 17:10:12,858 - v3_engine.parameter_registry - DEBUG - Added parameter 'chart_types' to optimizable parameters
2025-06-03 17:10:12,858 - v3_engine.parameter_registry - DEBUG - Added parameter 'chart_format' to optimizable parameters
2025-06-03 17:10:12,858 - v3_engine.parameter_registry - DEBUG - Added parameter 'chart_dpi' to optimizable parameters
2025-06-03 17:10:12,858 - v3_engine.parameter_registry - DEBUG - Added parameter 'colorblind_friendly' to optimizable parameters
2025-06-03 17:10:12,858 - INFO - Visualization parameters registered successfully
2025-06-03 17:10:12,858 - INFO - All reporting parameters registered successfully
.2025-06-03 17:10:12,858 - v3_engine.parameter_registry - INFO - Parameter registry has been reset.
2025-06-03 17:10:12,858 - v3_engine.parameter_registry - DEBUG - Added parameter 'create_excel' to optimizable parameters
2025-06-03 17:10:12,858 - v3_engine.parameter_registry - DEBUG - Added parameter 'save_trade_log' to optimizable parameters
2025-06-03 17:10:12,858 - v3_engine.parameter_registry - DEBUG - Added parameter 'metrics' to optimizable parameters
2025-06-03 17:10:12,858 - v3_engine.parameter_registry - DEBUG - Added parameter 'actual_execution_delay' to optimizable parameters
2025-06-03 17:10:12,858 - INFO - Reporting parameters registered successfully
2025-06-03 17:10:12,858 - v3_engine.parameter_registry - DEBUG - Added parameter 'create_charts' to optimizable parameters
2025-06-03 17:10:12,858 - v3_engine.parameter_registry - DEBUG - Added parameter 'chart_types' to optimizable parameters
2025-06-03 17:10:12,858 - v3_engine.parameter_registry - DEBUG - Added parameter 'chart_format' to optimizable parameters
2025-06-03 17:10:12,858 - v3_engine.parameter_registry - DEBUG - Added parameter 'chart_dpi' to optimizable parameters
2025-06-03 17:10:12,858 - v3_engine.parameter_registry - DEBUG - Added parameter 'colorblind_friendly' to optimizable parameters
2025-06-03 17:10:12,858 - INFO - Visualization parameters registered successfully
2025-06-03 17:10:13,290 - v3_engine.parameter_registry - INFO - Parameter registry initialized
2025-06-03 17:10:13,290 - INFO - Generating charts with format=png, dpi=300, types=standard, colorblind=True
.2025-06-03 17:10:13,300 - v3_engine.parameter_registry - INFO - Parameter registry has been reset.
2025-06-03 17:10:13,300 - v3_engine.parameter_registry - DEBUG - Added parameter 'create_excel' to optimizable parameters
2025-06-03 17:10:13,300 - v3_engine.parameter_registry - DEBUG - Added parameter 'save_trade_log' to optimizable parameters
2025-06-03 17:10:13,300 - v3_engine.parameter_registry - DEBUG - Added parameter 'metrics' to optimizable parameters
2025-06-03 17:10:13,300 - v3_engine.parameter_registry - DEBUG - Added parameter 'actual_execution_delay' to optimizable parameters
2025-06-03 17:10:13,300 - INFO - Reporting parameters registered successfully
2025-06-03 17:10:13,300 - v3_engine.parameter_registry - DEBUG - Added parameter 'create_charts' to optimizable parameters
2025-06-03 17:10:13,300 - v3_engine.parameter_registry - DEBUG - Added parameter 'chart_types' to optimizable parameters
2025-06-03 17:10:13,300 - v3_engine.parameter_registry - DEBUG - Added parameter 'chart_format' to optimizable parameters
2025-06-03 17:10:13,300 - v3_engine.parameter_registry - DEBUG - Added parameter 'chart_dpi' to optimizable parameters
2025-06-03 17:10:13,300 - v3_engine.parameter_registry - DEBUG - Added parameter 'colorblind_friendly' to optimizable parameters
2025-06-03 17:10:13,300 - INFO - Visualization parameters registered successfully
2025-06-03 17:10:13,747 - v3_engine.parameter_registry - INFO - Parameter registry initialized
2025-06-03 17:10:13,747 - v3_engine.parameter_registry - DEBUG - Added parameter 'create_excel' to optimizable parameters
2025-06-03 17:10:13,747 - v3_engine.parameter_registry - DEBUG - Added parameter 'save_trade_log' to optimizable parameters
2025-06-03 17:10:13,747 - v3_engine.parameter_registry - DEBUG - Added parameter 'metrics' to optimizable parameters
2025-06-03 17:10:13,747 - v3_engine.parameter_registry - DEBUG - Added parameter 'actual_execution_delay' to optimizable parameters
2025-06-03 17:10:13,747 - INFO - Reporting parameters registered successfully
2025-06-03 17:10:13,747 - v3_engine.parameter_registry - DEBUG - Added parameter 'create_charts' to optimizable parameters
2025-06-03 17:10:13,747 - v3_engine.parameter_registry - DEBUG - Added parameter 'chart_types' to optimizable parameters
2025-06-03 17:10:13,747 - v3_engine.parameter_registry - DEBUG - Added parameter 'chart_format' to optimizable parameters
2025-06-03 17:10:13,747 - v3_engine.parameter_registry - DEBUG - Added parameter 'chart_dpi' to optimizable parameters
2025-06-03 17:10:13,747 - v3_engine.parameter_registry - DEBUG - Added parameter 'colorblind_friendly' to optimizable parameters
2025-06-03 17:10:13,747 - INFO - Visualization parameters registered successfully
2025-06-03 17:10:13,747 - INFO - All reporting parameters registered successfully
.2025-06-03 17:10:13,747 - v3_engine.parameter_registry - INFO - Parameter registry has been reset.
2025-06-03 17:10:13,747 - v3_engine.parameter_registry - DEBUG - Added parameter 'create_excel' to optimizable parameters
2025-06-03 17:10:13,747 - v3_engine.parameter_registry - DEBUG - Added parameter 'save_trade_log' to optimizable parameters
2025-06-03 17:10:13,747 - v3_engine.parameter_registry - DEBUG - Added parameter 'metrics' to optimizable parameters
2025-06-03 17:10:13,747 - v3_engine.parameter_registry - DEBUG - Added parameter 'actual_execution_delay' to optimizable parameters
2025-06-03 17:10:13,747 - INFO - Reporting parameters registered successfully
2025-06-03 17:10:13,747 - v3_engine.parameter_registry - DEBUG - Added parameter 'create_charts' to optimizable parameters
2025-06-03 17:10:13,747 - v3_engine.parameter_registry - DEBUG - Added parameter 'chart_types' to optimizable parameters
2025-06-03 17:10:13,747 - v3_engine.parameter_registry - DEBUG - Added parameter 'chart_format' to optimizable parameters
2025-06-03 17:10:13,748 - v3_engine.parameter_registry - DEBUG - Added parameter 'chart_dpi' to optimizable parameters
2025-06-03 17:10:13,748 - v3_engine.parameter_registry - DEBUG - Added parameter 'colorblind_friendly' to optimizable parameters
2025-06-03 17:10:13,748 - INFO - Visualization parameters registered successfully
2025-06-03 17:10:14,160 - v3_engine.parameter_registry - INFO - Parameter registry initialized
2025-06-03 17:10:14,160 - v3_engine.parameter_registry - DEBUG - Added parameter 'create_excel' to optimizable parameters
2025-06-03 17:10:14,160 - v3_engine.parameter_registry - DEBUG - Added parameter 'save_trade_log' to optimizable parameters
2025-06-03 17:10:14,160 - v3_engine.parameter_registry - DEBUG - Added parameter 'metrics' to optimizable parameters
2025-06-03 17:10:14,160 - v3_engine.parameter_registry - DEBUG - Added parameter 'actual_execution_delay' to optimizable parameters
2025-06-03 17:10:14,160 - INFO - Reporting parameters registered successfully
.2025-06-03 17:10:14,161 - v3_engine.parameter_registry - INFO - Parameter registry has been reset.
2025-06-03 17:10:14,161 - v3_engine.parameter_registry - DEBUG - Added parameter 'create_excel' to optimizable parameters
2025-06-03 17:10:14,161 - v3_engine.parameter_registry - DEBUG - Added parameter 'save_trade_log' to optimizable parameters
2025-06-03 17:10:14,161 - v3_engine.parameter_registry - DEBUG - Added parameter 'metrics' to optimizable parameters
2025-06-03 17:10:14,161 - v3_engine.parameter_registry - DEBUG - Added parameter 'actual_execution_delay' to optimizable parameters
2025-06-03 17:10:14,161 - INFO - Reporting parameters registered successfully
2025-06-03 17:10:14,161 - v3_engine.parameter_registry - DEBUG - Added parameter 'create_charts' to optimizable parameters
2025-06-03 17:10:14,161 - v3_engine.parameter_registry - DEBUG - Added parameter 'chart_types' to optimizable parameters
2025-06-03 17:10:14,161 - v3_engine.parameter_registry - DEBUG - Added parameter 'chart_format' to optimizable parameters
2025-06-03 17:10:14,161 - v3_engine.parameter_registry - DEBUG - Added parameter 'chart_dpi' to optimizable parameters
2025-06-03 17:10:14,161 - v3_engine.parameter_registry - DEBUG - Added parameter 'colorblind_friendly' to optimizable parameters
2025-06-03 17:10:14,161 - INFO - Visualization parameters registered successfully
2025-06-03 17:10:14,575 - v3_engine.parameter_registry - INFO - Parameter registry initialized
2025-06-03 17:10:14,575 - v3_engine.parameter_registry - DEBUG - Added parameter 'create_charts' to optimizable parameters
2025-06-03 17:10:14,575 - v3_engine.parameter_registry - DEBUG - Added parameter 'chart_types' to optimizable parameters
2025-06-03 17:10:14,575 - v3_engine.parameter_registry - DEBUG - Added parameter 'chart_format' to optimizable parameters
2025-06-03 17:10:14,575 - v3_engine.parameter_registry - DEBUG - Added parameter 'chart_dpi' to optimizable parameters
2025-06-03 17:10:14,575 - v3_engine.parameter_registry - DEBUG - Added parameter 'colorblind_friendly' to optimizable parameters
2025-06-03 17:10:14,575 - INFO - Visualization parameters registered successfully
.
----------------------------------------------------------------------
Ran 6 tests in 2.570s

OK
Python version: 3.13.1 (tags/v3.13.1:0671451, Dec  3 2024, 19:06:28) [MSC v.1942 64 bit (AMD64)]
Python paths:
  S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template
  S:\Dropbox\Scott Only Internal\Quant_Python_24\Custom Function Library
  C:\Python313\python313.zip
  C:\Python313\DLLs
  C:\Python313\Lib
  C:\Python313
  F:\AI_Library\my_quant_env
  F:\AI_Library\my_quant_env\Lib\site-packages
  F:\AI_Library\my_quant_env\Lib\site-packages\win32
  F:\AI_Library\my_quant_env\Lib\site-packages\win32\lib
