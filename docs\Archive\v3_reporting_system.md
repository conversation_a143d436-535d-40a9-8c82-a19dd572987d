# V3 Reporting System Documentation

This document explains how the V3 reporting system works and how to use it. The V3 reporting system integrates with the V3 parameter system to ensure all reports are generated consistently with the parameters used in the backtest.

## Current V3 Reporting System Architecture (Updated May 2025)

### Core Modules

1. **Parameter Registration** (`v3_engine/reporting_parameters.py`)
   
   - Registers all reporting parameters with the V3 registry
   - Handles parameter types and default values

2. **Reporting Modules** (`v3_reporting/`)
   
   - `v3_performance_report.py`: Wrapper for performance reporting
   - `v3_allocation_report.py`: Wrapper for allocation reporting
   - `v3_visualization.py`: Wrapper for chart generation

3. **Adapter** (`v3_engine/performance_reporter_adapter.py`)
   
   - Bridges V3 parameters to legacy reporting code
   - <PERSON><PERSON> parameter conversion and validation

### Key Improvements

- Proper handling of execution_delay parameter optimization
- Signal history recovery fallback to weights_history
- Clear separation between parameter registration and usage
- Preserved all existing reporting functionality

### File Structure

```
backtest_template/
├── v3_engine/
│   ├── reporting_parameters.py   # Parameter registration
│   └── performance_reporter_adapter.py  # Adapter
├── v3_reporting/
│   ├── v3_performance_report.py  # Performance report wrapper
│   ├── v3_allocation_report.py   # Allocation report wrapper
│   └── v3_visualization.py       # Chart generation wrapper
└── run_backtest_v3.py            # Main runner
```

### File Structure

```text
backtest_template/
├── v3_engine/
│   ├── reporting_parameters.py   # Parameter registration
│   └── performance_reporter_adapter.py  # Adapter
├── v3_reporting/
│   ├── v3_performance_report.py  # Performance report wrapper
│   ├── v3_allocation_report.py   # Allocation report wrapper
│   └── v3_visualization.py       # Chart generation wrapper
└── run_backtest_v3.py            # Main runner
```

## Parameter Flow

1. **Registration**: Reporting parameters are registered in `v3_engine/reporting_parameters.py`
2. **Retrieval**: Parameters are retrieved from the registry when generating reports
3. **Conversion**: The `PerformanceReporterAdapter` converts parameters to the format expected by the reporting system
4. **Generation**: Reports are generated using the V3-specific wrapper modules

## Available Reporting Parameters

| Parameter      | Type | Description                     | Default                                                                                                          |
| -------------- | ---- | ------------------------------- | ---------------------------------------------------------------------------------------------------------------- |
| create_excel   | bool | Whether to create Excel reports | True                                                                                                             |
| save_trade_log | bool | Whether to save trade logs      | True                                                                                                             |
| metrics        | list | Performance metrics to include  | ['total_return', 'annualized_return', 'sharpe_ratio', 'sortino_ratio', 'max_drawdown', 'win_rate', 'volatility'] |
| create_charts  | bool | Whether to create charts        | True                                                                                                             |
| chart_types    | list | Types of charts to create       | ['cumulative_returns', 'drawdown', 'return_distribution', 'monthly_returns', 'portfolio_weights']                |
| chart_format   | str  | Chart format (png, jpg, svg)    | 'png'                                                                                                            |
| chart_dpi      | int  | Chart resolution (DPI)          | 300                                                                                                              |

## How to Use

### Running a Backtest with V3 Parameters

The simplest way to run a backtest with V3 parameters is to use the `run_backtest_v3.bat` batch file, which sets up the environment and runs the `run_backtest_v3.py` script.

```
run_backtest_v3.bat
```

### Programmatic Usage

For more control, you can use the V3 reporting modules directly in your code:

```python
from v3_reporting.v3_performance_report import generate_v3_performance_report
from v3_reporting.v3_allocation_report import generate_v3_allocation_report
from v3_reporting.v3_visualization import generate_v3_performance_charts

# Run backtest and get results
results = engine.run_backtest(parameters)

# Generate performance report
report_path = generate_v3_performance_report(
    backtest_results=results,
    output_path='output/reports/performance.xlsx',
    strategy_name='ema',
    parameters=parameters
)

# Generate allocation report
allocation_report_path = generate_v3_allocation_report(
    signal_history=results['signal_history'],
    output_dir='output/reports',
    ticker_group='default'
)

# Generate charts
chart_paths = generate_v3_performance_charts(
    backtest_results=results,
    output_dir='output/reports',
    strategy_name='ema'
)
```

### Advanced Usage: Customizing Parameters

You can customize the reporting parameters by modifying the registry before running the backtest:

```python
from v3_engine.parameter_registry import get_registry

# Get the registry
registry = get_registry()

# Modify reporting parameters
registry.set_parameter_value('create_excel', True)
registry.set_parameter_value('save_trade_log', True)
registry.set_parameter_value('metrics', ['total_return', 'sharpe_ratio', 'max_drawdown'])
registry.set_parameter_value('create_charts', True)
registry.set_parameter_value('chart_types', ['cumulative_returns', 'drawdown'])
registry.set_parameter_value('chart_format', 'png')
registry.set_parameter_value('chart_dpi', 600)

# Run backtest with modified parameters
results = run_v3_backtest()
```

## Integration with GUI

The V3 reporting system is designed to integrate seamlessly with the V3 GUI. When a backtest is run from the GUI, the parameters selected in the GUI are passed to the backtest engine and then to the reporting system.

## Testing

A test script is provided to verify that the V3 reporting system works correctly:

```
run_v3_reporting_test.bat
```

This script creates dummy backtest results and generates reports using the V3 reporting system.

## Report Formats and Standards

All reports generated by the V3 reporting system follow the standards defined in `docs/performance_reporting_standards.md`. This ensures consistency and usability across all reports.

## Troubleshooting

If you encounter issues with the V3 reporting system, check the following:

1. **Parameter Registration**: Ensure that all reporting parameters are registered correctly
2. **Parameter Values**: Check that the parameter values are valid and within expected ranges
3. **Backtest Results**: Verify that the backtest results contain all required data for report generation
4. **Output Directory**: Ensure that the output directory exists and is writable

## Log Level Control

The log level for the V3 reporting system can be controlled using the `BACKTEST_LOG_LEVEL` environment variable. This can be set in the batch file or before running the script:

```
set BACKTEST_LOG_LEVEL=DEBUG
```

Valid values are: DEBUG, INFO, WARNING, ERROR, CRITICAL. The default is INFO.

---

*Last updated: 2025-05-01*
