@echo OFF
SET SCRIPT_PATH=".\trace_ema_signal_breakdown.py"
SET PYTHON_EXE="F:\AI_Library\my_quant_env\Scripts\python.exe"
SET OUTPUT_LOG_DIR=".\test_logs"
SET TIMESTAMP=%date:~-4,4%%date:~-10,2%%date:~-7,2%_%time:~0,2%%time:~3,2%%time:~6,2%
SET OUTPUT_FILE="%OUTPUT_LOG_DIR%\trace_output_%TIMESTAMP%.txt"

REM Create output directory if it doesn't exist
IF NOT EXIST "%OUTPUT_LOG_DIR%" MKDIR "%OUTPUT_LOG_DIR%"

ECHO Running %SCRIPT_PATH%...
ECHO Output will be saved to %OUTPUT_FILE%

%PYTHON_EXE% %SCRIPT_PATH% > %OUTPUT_FILE% 2>&1

ECHO Script execution finished.
ECHO Check %OUTPUT_FILE% for results.
