@echo off
REM Run Memory Tool with Virtual Environment
setlocal enabledelayedexpansion

REM Set paths
set VENV_PATH=F:\AI_Library\my_quant_env
set PROJECT_DIR=%~dp0
set PYTHON_SCRIPT=%PROJECT_DIR%docs\memory_tool\ai_context_gen_v2.py
set OUTPUT_DIR=%PROJECT_DIR%memory_tool_output

REM Create output directory if it doesn't exist
if not exist "%OUTPUT_DIR%" (
    mkdir "%OUTPUT_DIR%"
    if errorlevel 1 (
        echo Failed to create output directory: %OUTPUT_DIR%
        pause
        exit /b 1
    )
)

echo Setting up environment...
echo Project Directory: %PROJECT_DIR%
echo Output Directory: %OUTPUT_DIR%

REM Activate virtual environment
if exist "%VENV_PATH%\Scripts\activate.bat" (
    call "%VENV_PATH%\Scripts\activate.bat"
) else (
    echo Virtual environment not found at: %VENV_PATH%
    pause
    exit /b 1
)

REM Change to project directory and run the Python script
cd /d "%PROJECT_DIR%"

echo Starting memory tool analysis...
python "%PYTHON_SCRIPT%"
set EXIT_CODE=!errorlevel!

REM Display completion message
echo.
if !EXIT_CODE! EQU 0 (
    echo Analysis completed successfully!
    echo Output saved to: %OUTPUT_DIR%
) else (
    echo Analysis completed with errors (Exit code: !EXIT_CODE!)
)

REM Keep the window open
pause
