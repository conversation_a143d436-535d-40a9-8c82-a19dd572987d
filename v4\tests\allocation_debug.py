# allocation_debug.py
"""
Debug script for allocation history issue
"""

import sys
from pathlib import Path
import pandas as pd
import numpy as np

# Add project root to sys.path
project_root = Path(__file__).parent.parent.parent
if str(project_root) not in sys.path:
    sys.path.append(str(project_root))

print("\n=== ALLOCATION DEBUG STARTED ===\n")
# Debug: working directory and paths
print(f"[DEBUG] CWD: {Path.cwd()}")
print(f"[DEBUG] Script path: {Path(__file__).resolve()}")
print(f"[DEBUG] project_root: {project_root}")

# Import required modules
from v4.settings.settings_CPS_v4 import load_settings
from v4.engine.data_loader_v4 import load_data_for_backtest
from v4.engine.backtest_v4 import BacktestEngine
from v4.engine.signal_generator_v4 import generate_signals, EqualWeightSignalGenerator, EMASignalGenerator

def debug_signal_generation(price_data):
    """Debug signal generation with both strategies"""
    print("\n=== DEBUG: SIGNAL GENERATION ===")
    
    # Test equal weight strategy
    print("\n--- Testing Equal Weight Strategy ---")
    equal_gen = EqualWeightSignalGenerator()
    equal_signals = equal_gen.generate_signals(price_data)
    print(f"Equal weight signals: {equal_signals}")
    
    # Test EMA strategy
    print("\n--- Testing EMA Strategy ---")
    ema_gen = EMASignalGenerator()
    ema_signals = ema_gen.generate_signals(price_data)
    print(f"EMA signals: {ema_signals}")
    
    # Test wrapper function with equal_weight
    print("\n--- Testing generate_signals wrapper with equal_weight ---")
    wrapper_equal = generate_signals(price_data, strategy='equal_weight')
    print(f"Wrapper equal_weight signals: {wrapper_equal}")
    
    # Test wrapper function with ema
    print("\n--- Testing generate_signals wrapper with ema ---")
    wrapper_ema = generate_signals(price_data, strategy='ema')
    print(f"Wrapper ema signals: {wrapper_ema}")
    
    return {
        'equal_weight_direct': equal_signals,
        'ema_direct': ema_signals,
        'equal_weight_wrapper': wrapper_equal,
        'ema_wrapper': wrapper_ema
    }

def debug_backtest_with_strategy(price_data, strategy_name):
    """Run backtest with specified strategy and debug output"""
    print(f"\n=== DEBUG: BACKTEST WITH {strategy_name.upper()} STRATEGY ===")
    
    # Create backtest engine
    engine = BacktestEngine()
    
    # Define a wrapper function that uses the specified strategy
    def signal_wrapper(price_data, **params):
        return generate_signals(price_data, strategy=strategy_name, **params)
    
    # Run backtest with the wrapper
    results = engine.run_backtest(
        price_data=price_data,
        signal_generator=signal_wrapper
    )
    
    # Check allocation history
    print("\n--- Allocation History Analysis ---")
    if 'weights_history' in results:
        weights = results['weights_history']
        print(f"Weights history shape: {weights.shape}")
        print(f"Weights history sample:\n{weights.head()}")
        print(f"Non-zero weights: {(weights > 0).sum().sum()}")
        print(f"Max weight: {weights.max().max()}")
    else:
        print("No weights_history in results")
    
    # Check signal history
    print("\n--- Signal History Analysis ---")
    if 'signal_history' in results:
        signals = results['signal_history']
        print(f"Signal history shape: {signals.shape}")
        print(f"Signal history sample:\n{signals.head()}")
        print(f"Non-zero signal entries: {(signals != 0).sum().sum()}")
    else:
        print("No signal_history in results")
    
    return results

def fix_signal_generation_issue():
    """Fix the signal generation issue in backtest_v4.py"""
    print("\n=== ATTEMPTING TO FIX SIGNAL GENERATION ISSUE ===")
    
    # The issue is in the backtest_v4.py file where it checks for signal_generator.__name__ == 'ema_allocation_model'
    # but our function is named generate_signals
    
    # Since we're not modifying the file directly, we'll create a patched version of the signal generator
    
    def patched_signal_generator(price_data, strategy='equal_weight', **params):
        """Patched version of generate_signals that ensures proper strategy selection"""
        print(f"\n=== PATCHED SIGNAL GENERATOR ===")
        print(f"Strategy: {strategy}")
        print(f"Price data shape: {price_data.shape}")
        
        # Force equal weight for testing
        generator = EqualWeightSignalGenerator()
        signals = generator.generate_signals(price_data, **params)
        
        print(f"Generated signals: {signals}")
        return signals
    
    # Set a recognizable name for debugging
    patched_signal_generator.__name__ = 'patched_signal_generator'
    
    return patched_signal_generator

def main():
    """Main debug function"""
    print("\n=== ALLOCATION HISTORY DEBUG STARTED ===")
    
    # Load settings and data
    settings = load_settings()
    print(f"Loaded settings: {settings.get('data_params', {})}")
    
    data = load_data_for_backtest()
    print(f"Loaded data keys: {list(data.keys())}")
    print(f"price_data: {data['price_data'].shape}")
    
    # Debug signal generation
    signal_results = debug_signal_generation(data['price_data'])
    
    # Debug backtest with equal weight strategy
    # Debug: define and show output directory for equal results
    output_dir = Path(f"{project_root}/v4/tests/output")
    print(f"[DEBUG] EqualResults CSV output_dir: {output_dir}")

    equal_results = debug_backtest_with_strategy(data['price_data'], 'equal_weight')
    # Export equal weight signal history
    # Debug: path to equal signal CSV
    print(f"[DEBUG] Writing equal_signal_history_debug.csv to: {output_dir}/equal_signal_history_debug.csv")
    output_dir = Path(f"{project_root}/v4/tests/output")
    output_dir.mkdir(exist_ok=True, parents=True)
    if 'signal_history' in equal_results and not equal_results['signal_history'].empty:
        equal_results['signal_history'].to_csv(f"{output_dir}/equal_signal_history_debug.csv")
        print(f"\nExported equal signal history to {output_dir}/equal_signal_history_debug.csv")
    
    # Debug backtest with patched signal generator
    print("\n=== TESTING WITH PATCHED SIGNAL GENERATOR ===")
    patched_generator = fix_signal_generation_issue()
    
    # Create backtest engine
    engine = BacktestEngine()
    
    # Run backtest with patched generator
    patched_results = engine.run_backtest(
        price_data=data['price_data'],
        signal_generator=patched_generator
    )
    
    # Check allocation history from patched version
    print("\n--- Patched Allocation History Analysis ---")
    if 'weights_history' in patched_results:
        weights = patched_results['weights_history']
        print(f"Weights history shape: {weights.shape}")
        print(f"Weights history sample:\n{weights.head()}")
        print(f"Non-zero weights: {(weights > 0).sum().sum()}")
        print(f"Max weight: {weights.max().max()}")
    else:
        print("No weights_history in patched results")
    
    # Compare results
    print("\n=== COMPARISON OF RESULTS ===")
    print(f"Equal weight strategy - final value: ${equal_results.get('final_value', 0):,.2f}")
    print(f"Patched generator - final value: ${patched_results.get('final_value', 0):,.2f}")
    
    # Export allocation history for verification
    if 'weights_history' in patched_results and not patched_results['weights_history'].empty:
        output_dir = Path(f"{project_root}/v4/tests/output")
        output_dir.mkdir(exist_ok=True, parents=True)
        patched_results['weights_history'].to_csv(f"{output_dir}/allocation_history_debug.csv")
        print(f"\nExported allocation history to {output_dir}/allocation_history_debug.csv")
        
        # Export signal history
        if 'signal_history' in patched_results and not patched_results['signal_history'].empty:
            patched_results['signal_history'].to_csv(f"{output_dir}/signal_history_debug.csv")
            print(f"\nExported signal history to {output_dir}/signal_history_debug.csv")
    
    print("\n=== ALLOCATION HISTORY DEBUG COMPLETED ===")
    return patched_results

if __name__ == "__main__":
    try:
        print("Starting allocation debug test")
        results = main()
        print("\n=== DEBUG COMPLETED SUCCESSFULLY ===")
        print(f"Final value: ${results.get('final_value', 0):,.2f}")
        print(f"Total return: {results.get('total_return', 0):.2%}")
    except Exception as e:
        print(f"\nError during debug: {str(e)}")
        import traceback
        traceback.print_exc()
