"""
debug_backtest_flow.py
Simple direct test script to trace data flow through the backtest engine
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime
import logging
from pathlib import Path

# Set up direct file logging
log_dir = Path(__file__).parent / "debug_logs"
log_dir.mkdir(exist_ok=True)
log_file = log_dir / f"debug_flow_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"

# Configure logging to both console and file
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# Print and log initial startup message
print(f"DEBUG FLOW TEST STARTED: {datetime.now()}")
logger.info(f"DEBUG FLOW TEST STARTED: {datetime.now()}")
logger.info(f"Log file: {log_file}")
logger.info(f"Python version: {sys.version}")
logger.info(f"Current directory: {os.getcwd()}")

# Add parent directory to path to import modules
parent_dir = str(Path(__file__).parent.parent.parent)
sys.path.append(parent_dir)
logger.info(f"Added to path: {parent_dir}")

try:
    # Import backtest engine components
    logger.info("Importing backtest engine components...")
    from v4.engine.backtest_v4 import BacktestEngine
    from v4.engine.signal_generator_v4 import generate_signals, create_signal_generator, EqualWeightSignalGenerator, EMASignalGenerator
    logger.info("Successfully imported backtest engine components")
except Exception as e:
    logger.error(f"Failed to import modules: {e}")
    sys.exit(1)

def generate_test_data(start_date='2020-01-01', end_date='2022-12-31', symbols=None):
    """Generate synthetic price data for testing."""
    logger.info(f"Generating test data from {start_date} to {end_date}")
    
    if symbols is None:
        symbols = ['AAPL', 'MSFT', 'AMZN', 'GOOGL']
    
    logger.info(f"Using symbols: {symbols}")
    
    # Create date range
    dates = pd.date_range(start=start_date, end=end_date, freq='B')
    logger.info(f"Generated {len(dates)} dates")
    
    # Create price data with random walks
    np.random.seed(42)  # For reproducibility
    price_data = pd.DataFrame(index=dates)
    
    for symbol in symbols:
        # Start with 100
        prices = [100]
        # Generate random daily returns
        for _ in range(1, len(dates)):
            daily_return = np.random.normal(0.0005, 0.015)  # Mean and std dev
            prices.append(prices[-1] * (1 + daily_return))
        
        price_data[symbol] = prices
    
    logger.info(f"Price data shape: {price_data.shape}")
    logger.info(f"Price data head:\n{price_data.head()}")
    logger.info(f"Price data tail:\n{price_data.tail()}")
    
    return price_data

def test_signal_generation(price_data):
    """Test signal generation directly."""
    logger.info("\n=== Testing Signal Generation ===")
    
    # Test Equal Weight
    logger.info("Testing Equal Weight signal generation...")
    eq_generator = EqualWeightSignalGenerator()
    eq_signals = eq_generator.generate_signals(price_data)
    logger.info(f"Equal Weight signals: {eq_signals}")
    
    # Test EMA
    logger.info("Testing EMA signal generation...")
    ema_generator = EMASignalGenerator()
    ema_signals = ema_generator.generate_signals(price_data)
    logger.info(f"EMA signals: {ema_signals}")
    
    # Test factory function
    logger.info("Testing signal generator factory...")
    eq_factory = create_signal_generator('equal_weight')
    eq_factory_signals = eq_factory.generate_signals(price_data)
    logger.info(f"Factory Equal Weight signals: {eq_factory_signals}")
    
    # Test wrapper function
    logger.info("Testing generate_signals wrapper...")
    wrapper_signals = generate_signals(price_data, strategy='equal_weight')
    logger.info(f"Wrapper signals: {wrapper_signals}")
    
    return {
        'equal_weight': eq_signals,
        'ema': ema_signals,
        'factory': eq_factory_signals,
        'wrapper': wrapper_signals
    }

def run_backtest_test(price_data, strategy='equal_weight', rebalance_freq='M', execution_delay=0):
    """Run a backtest with the specified parameters."""
    logger.info(f"\n=== Running backtest with strategy: {strategy}, rebalance_freq: {rebalance_freq}, execution_delay: {execution_delay} ===")
    
    # Override settings for testing
    try:
        from v4.settings.settings_CPS_v4 import load_settings, save_settings
        logger.info("Loading settings...")
        settings = load_settings()
        logger.info(f"Original settings: {settings}")
        
        # Update settings for test
        settings['backtest']['rebalance_freq'] = rebalance_freq
        settings['backtest']['execution_delay'] = execution_delay
        logger.info(f"Updated settings: {settings}")
        save_settings(settings)
        logger.info("Settings saved")
    except Exception as e:
        logger.error(f"Error with settings: {e}")
        # Continue with default settings
    
    # Create backtest engine
    logger.info("Creating backtest engine...")
    engine = BacktestEngine()
    logger.info("Backtest engine created")
    
    # Run backtest
    logger.info("Running backtest...")
    try:
        results = engine.run_backtest(
            price_data=price_data,
            signal_generator=generate_signals,
            strategy=strategy
        )
        logger.info("Backtest completed successfully")
        
        # Log key results
        logger.info(f"Results keys: {list(results.keys())}")
        if 'weights_history' in results:
            logger.info(f"Weights history shape: {results['weights_history'].shape}")
            logger.info(f"Weights history sample:\n{results['weights_history'].head()}")
        if 'signal_history' in results:
            logger.info(f"Signal history shape: {results['signal_history'].shape}")
            logger.info(f"Signal history sample:\n{results['signal_history'].head()}")
        if 'portfolio_history' in results:
            logger.info(f"Portfolio history shape: {results['portfolio_history'].shape}")
            logger.info(f"Portfolio history sample:\n{results['portfolio_history'].head()}")
        if 'performance' in results:
            logger.info(f"Performance metrics: {results['performance']}")
        
        return results
    except Exception as e:
        logger.error(f"Backtest failed: {e}", exc_info=True)
        return None

def validate_weights_history(results):
    """Validate that weights history is preserved correctly."""
    logger.info("\n=== Validating weights history ===")
    
    if results is None:
        logger.error("No results to validate")
        return False
    
    if 'weights_history' not in results or 'signal_history' not in results:
        logger.error("Missing weights_history or signal_history in results")
        return False
    
    weights_history = results['weights_history']
    signal_history = results['signal_history']
    
    logger.info(f"Weights history shape: {weights_history.shape}")
    logger.info(f"Signal history shape: {signal_history.shape}")
    
    # Check if weights_history and signal_history are different
    # (they should be if weights_history is preserved correctly)
    if weights_history.equals(signal_history):
        logger.warning("⚠️ Weights history equals signal history - this suggests allocation history is not preserved!")
        return False
    else:
        logger.info("✅ Weights history differs from signal history - allocation history is preserved!")
        return True

def compare_weights_vs_signals(results):
    """Compare weights history vs signal history."""
    logger.info("\n=== Comparing weights vs signals ===")
    
    if results is None:
        logger.error("No results to compare")
        return
    
    if 'weights_history' not in results or 'signal_history' not in results:
        logger.error("Missing weights_history or signal_history in results")
        return
    
    weights_history = results['weights_history']
    signal_history = results['signal_history']
    
    # Get a sample of dates to compare
    sample_dates = weights_history.index[::20]  # Every 20th date
    
    for date in sample_dates:
        weights = weights_history.loc[date]
        signals = signal_history.loc[date] if date in signal_history.index else pd.Series()
        
        logger.info(f"\nDate: {date}")
        logger.info(f"Weights: {weights.to_dict()}")
        logger.info(f"Signals: {signals.to_dict() if not signals.empty else 'No signals'}")

def main():
    """Main test function."""
    try:
        logger.info("=== STARTING BACKTEST ENGINE DEBUG FLOW TEST ===")
        
        # Generate test data
        price_data = generate_test_data()
        
        # Test signal generation directly
        signal_results = test_signal_generation(price_data)
        
        # Test Equal Weight backtest
        logger.info("\n=== Test 1: Equal Weight Strategy with Monthly Rebalancing ===")
        results_eq = run_backtest_test(price_data, strategy='equal_weight', rebalance_freq='M')
        if results_eq:
            valid_eq = validate_weights_history(results_eq)
            compare_weights_vs_signals(results_eq)
            logger.info(f"Equal Weight allocation history preserved: {valid_eq}")
        
        # Test EMA backtest with execution delay
        logger.info("\n=== Test 2: EMA Strategy with Execution Delay ===")
        results_ema = run_backtest_test(price_data, strategy='ema', rebalance_freq='M', execution_delay=1)
        if results_ema:
            valid_ema = validate_weights_history(results_ema)
            compare_weights_vs_signals(results_ema)
            logger.info(f"EMA allocation history preserved: {valid_ema}")
        
        # Print summary
        logger.info("\n=== SUMMARY ===")
        if results_eq and results_ema:
            eq_preserved = validate_weights_history(results_eq)
            ema_preserved = validate_weights_history(results_ema)
            
            if eq_preserved and ema_preserved:
                logger.info("✅ ALL TESTS PASSED! Allocation history is preserved correctly.")
            else:
                logger.info("❌ SOME TESTS FAILED! Check the logs for details.")
                if not eq_preserved:
                    logger.info("❌ Equal Weight allocation history not preserved")
                if not ema_preserved:
                    logger.info("❌ EMA allocation history not preserved")
        else:
            logger.info("❌ TESTS FAILED! One or more backtests did not complete.")
        
        logger.info(f"=== DEBUG FLOW TEST COMPLETED: {datetime.now()} ===")
        logger.info(f"Log file: {log_file}")
        
    except Exception as e:
        logger.error(f"Test failed with exception: {e}", exc_info=True)
        print(f"Test failed with exception: {e}")

if __name__ == "__main__":
    main()
    print(f"DEBUG FLOW TEST COMPLETED: {datetime.now()}")
    print(f"Log file: {log_file}")
