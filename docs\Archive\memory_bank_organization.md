# Memory-Bank Organization Guidelines

## Overview

This document provides recommendations for organizing the memory-bank directory to optimize AI context attention and improve documentation structure. The goal is to help the AI focus on the most relevant information while maintaining access to detailed reference documentation when needed.

## File Categorization

### 1. Keep in Memory-Bank (Essential Context)

These files provide critical context for the AI to understand the project's current state, decisions, and evolution:

| File | Purpose | Reason to Keep |
|------|---------|----------------|
| **projectbrief.md** | Project overview | Foundation document that shapes all other files |
| **activeContext.md** | Current work focus | Critical for understanding current state and next steps |
| **progress.md** | Status tracking | Provides up-to-date information on what works and what's left |
| **productContext.md** | Business context | Explains why the project exists and its goals |
| **systemPatterns.md** | Architecture patterns | Documents key technical decisions and patterns |
| **techContext.md** | Technical context | Describes the technology stack and constraints |
| **v3_recap.md** | Recent V3 work | Summarizes recent V3 development activities |
| **core_project_tasks_priority.md** | Task priorities | Helps AI understand what to focus on |
| **future_improvement_priority.md** | Future roadmap | Provides context for long-term direction |

### 2. Move to Docs Folder (Reference Documentation)

These files are stable reference documentation that should be moved to the docs folder:

| File | Purpose | Recommended Location |
|------|---------|---------------------|
| **md_usage_guidelines.md** | Markdown standards | docs/guidelines/md_usage_guidelines.md |
| **md_usage_mermaid_guidelines.md** | Mermaid chart standards | docs/guidelines/md_usage_mermaid_guidelines.md |
| **full_Process Flow_Full Architecture_v3.md** | System flow | docs/architecture/v3_process_flow.md |
| **transition_mappings.md** | V2-V3 mapping | docs/migration/v2_v3_mapping.md |
| **Status_Reporting.md** | Reporting standards | docs/reporting/status_reporting_standards.md |

### 3. Replace with AI-Optimized Versions

These files have already been optimized for AI usage and should replace their original counterparts:

| Original File | AI-Optimized Replacement | Status |
|---------------|--------------------------|--------|
| **systemFiles+Flow.md** | **systemFiles+Flow_AI.md** | ✅ Created |
| **v3_module+functions_list.md** | **v3_module+functions_list_AI.md** | ✅ Created |
| **parameters.md** (if exists) | **parameter_management_AI.md** | ✅ Created |

### 4. Low Priority / Consider Archiving

These files may be less relevant for current AI context or could be consolidated:

| File | Reason | Recommendation |
|------|--------|----------------|
| **v3_reporting_setup_briefing.md** | May be superseded by newer docs | Review content and either update or archive |
| **knowledge_base/** | AI-managed knowledge | Contains knowledge graph and search index |

## Implementation Recommendations

### 1. Create an AI Reference Index

Add a new file called `ai_reference_index.md` in the memory-bank root that explicitly tells the AI which files to check for specific types of questions:

```markdown
# AI Reference Index

## Module Questions
- For module relationships → See [systemFiles+Flow_AI.md]
- For function listings → See [v3_module+functions_list_AI.md]

## Parameter Questions
- For parameter system → See [parameter_management_AI.md]
- For parameter grouping → See [parameterGroups.md]

## Project Status
- For current focus → See [activeContext.md]
- For progress tracking → See [progress.md]
```

### 2. Implement Hierarchical Structure

Consider organizing the memory-bank into subdirectories for better navigation:

```text
memory-bank/
├── core/                 # Core project context
│   ├── projectbrief.md
│   ├── productContext.md
│   └── techContext.md
├── current/              # Current state and progress
│   ├── activeContext.md
│   ├── progress.md
│   └── v3_recap.md
└── architecture/         # Key decisions and patterns
    ├── systemPatterns.md
    ├── systemFiles+Flow_AI.md
    └── parameter_management_AI.md
```

### 3. Add AI Memory Creation Headers

Add explicit memory creation instructions to all key files:

```markdown
<!-- AI: CREATE MEMORY FROM THIS FILE
Title: Project Brief
Tags: [project_overview, goals, requirements]
-->
```

This helps the AI understand which information should be prioritized for memory creation.

### 4. Update Cross-References

Ensure all files properly cross-reference each other, especially after moving files to the docs folder. For example:

```markdown
For technical details, see [Module Mapping](../docs/v3_module_mapping.md)
```

## Benefits of This Organization

1. **Focused AI Context**: The AI can quickly understand the project's current state and priorities
2. **Reduced Relearning**: Essential context is preserved in memory-bank, while reference material is in docs
3. **Clearer Navigation**: Hierarchical structure makes it easier to find specific information
4. **Improved Maintenance**: Separation of concerns makes it easier to update documentation

## Knowledge Base Integration

The project includes an automated knowledge management system that extracts and structures information from both code and documentation. This system is designed to work in tandem with the memory-bank to provide comprehensive project understanding.

### Key Components

#### 1. Automated Code Analysis

- Extracts classes, functions, and their relationships
- Captures docstrings and parameter information
- Identifies import dependencies

#### 2. Document Processing

- Integrates key documentation files into the knowledge graph
- Maintains relationships between documentation and code
- Supports semantic search across all content

#### 3. Knowledge Graph

- Centralized storage of entities and relationships
- Supports complex queries about the codebase
- Enables discovery of implicit relationships

### Document Mapping

Key documentation is automatically integrated into the knowledge base through the `document_mapping.py` configuration. The default mapping includes:

| Document | Path | Description |
|----------|------|-------------|
| System Architecture | `memory-bank/systemFiles+Flow_AI.md` | System architecture and component relationships |
| Parameter System | `memory-bank/parameter_management_AI.md` | V3 Parameter system design and usage |
| Reporting System | `memory-bank/reporting_system_AI.md` | Reporting system architecture |
| Performance Standards | `docs/v3_performance_reporting_standards_a.md` | Performance reporting standards |

### AI Reference Index

Update the AI Reference Index to include knowledge base queries:

```markdown
# AI Reference Index

## Module Questions
- For module relationships → See [systemFiles+Flow_AI.md]
- For function listings → See [v3_module+functions_list_AI.md]

## Parameter Questions
- For parameter system → See [parameter_management_AI.md]
- For parameter grouping → See [parameterGroups.md]

## Knowledge Base
- For code documentation → Query the knowledge base
- For system architecture → See knowledge base or [systemFiles+Flow_AI.md]
- For API details → Use code analysis in knowledge base

## Project Status
- For current focus → See [activeContext.md]
- For progress tracking → See [progress.md]

## Implementation Plan

1. Create necessary directories in docs folder (guidelines, architecture, migration, reporting)
2. Move reference documentation files to appropriate locations
3. Create AI reference index in memory-bank
4. Add memory creation headers to key files
5. Update cross-references between files
6. Set up knowledge base integration:

   ```bash
   # Initialize knowledge base
   python -m docs.memory.scripts.init_knowledge_base
   # Run code analysis
   python -m docs.memory.scripts.code_analyzer
   # Process documents
   python -m docs.memory.scripts.document_processor
   # Start query interface
   python -m docs.memory.scripts.query_kb
   ```

1. Update CI/CD to regenerate knowledge base on significant changes

## Best Practices for Knowledge Base Maintenance

### Documentation Updates

- Update document mapping when adding new key documents
- Keep docstrings and comments in sync with documentation
- Use consistent naming conventions

### Code Changes

- Run code analysis after significant architectural changes
- Update entity relationships when interfaces change
- Document deprecated functionality

### Knowledge Validation

- Periodically verify knowledge base accuracy
- Update document processing as documentation structure evolves
- Monitor for stale or outdated information

---

## Last Updated

2025-05-14
