@echo off
REM run_report_verification.bat
REM Runs the report verification tests and logs the results

echo Running Report Verification Tests...
echo ===================================

REM Activate the virtual environment
call activate_env.bat

REM Run the verification tests
python tests\run_report_verification.py %*

REM Check exit code
if %ERRORLEVEL% EQU 0 (
    echo.
    echo Report Verification: PASSED
) else (
    echo.
    echo Report Verification: FAILED
)

echo.
echo Verification results have been logged to docs\para_RF\Problem_Changes_Fixes_C_Log.md
echo HTML report is available in tests\report_verification directory
echo.

pause
