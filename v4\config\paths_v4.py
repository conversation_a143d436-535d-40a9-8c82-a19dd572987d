"""
Centralized path configuration for the backtesting framework.
ALL PATHS SHOULD BE DEFINED HERE AND IMPORTED ELSEWHERE.
This is the single source of truth for all file paths in the system.
"""

import os
import sys
from pathlib import Path

# Base directories - PROJECT_ROOT is the anchor for all relative paths
PROJECT_ROOT = Path(__file__).parent.parent.parent.absolute()  # Go up to project root (3 levels up from v4/config/)
DATA_DIR = PROJECT_ROOT / "v4" / "data"  # Data files are actually in v4/data/

# Output directories - all relative to PROJECT_ROOT
OUTPUT_DIR = PROJECT_ROOT / "reporting"
V4_TRACE_OUTPUTS_DIR = PROJECT_ROOT / "v4_trace_outputs"
OPTIMIZATION_VALIDATION_DIR = PROJECT_ROOT / "optimization_validation"
LEGACY_OUTPUT_DIR = PROJECT_ROOT / "output"  # Legacy output directory

# Python code directories
PY_REPORTING_DIR = PROJECT_ROOT / "v4" / "py_reporting"  # Python reporting code location

# External paths (these remain absolute as they're outside project)
VENV_PATH = Path(r"F:\AI_Library\my_quant_env")
CUSTOM_LIB_PATH = Path(r"S:\Dropbox\Scott Only Internal\Quant_Python_24\Custom Function Library").absolute()

# Configuration paths
V4_SETTINGS_DIR = PROJECT_ROOT / "v4" / "settings"
V4_SETTINGS_FILE = V4_SETTINGS_DIR / "settings_parameters_v4.ini"

# Add for backward compatibility with older code
CUSTOM_FUNCTION_LIB_DIR = CUSTOM_LIB_PATH

# Ensure output directory exists
OUTPUT_DIR.mkdir(exist_ok=True)

# Add project root to sys.path (highest priority)
if str(PROJECT_ROOT) not in sys.path:
    sys.path.insert(0, str(PROJECT_ROOT))

# Add Custom Function Library to sys.path
if str(CUSTOM_LIB_PATH) not in sys.path:
    # Use str() directly without escape sequence modifications
    sys.path.insert(1, str(CUSTOM_LIB_PATH))

# Add all subdirectories of the Custom Function Library to sys.path
# This is necessary because the library doesn't have proper package structure
for subdir in CUSTOM_LIB_PATH.iterdir():
    if subdir.is_dir() and subdir.name not in ['__pycache__']:
        if str(subdir) not in sys.path:
            sys.path.append(str(subdir))

# Print Python path for debugging
print("Python version:", sys.version)
print("Python paths:")
for p in sys.path[:10]:  # Show first 10 paths
    print(f"  {p}")

# Derived paths
LOG_DIR = PROJECT_ROOT / "logs"
REPORT_DIR = OUTPUT_DIR
CHART_DIR = OUTPUT_DIR
OUTPUT_PATH = OUTPUT_DIR  # Add this for backward compatibility

# Specific file paths that are commonly used
EQUITY_CURVE_STRATEGY_LATEST = OUTPUT_DIR / "equity_curve_strategy_latest.csv"
EQUITY_CURVE_BENCHMARK_LATEST = OUTPUT_DIR / "equity_curve_benchmark_latest.csv"

# Function to get validation directory with timestamp
def get_validation_dir(timestamp=None):
    """Get validation directory path with optional timestamp."""
    if timestamp:
        return OPTIMIZATION_VALIDATION_DIR / timestamp
    return OPTIMIZATION_VALIDATION_DIR

# Function to get reporting file path
def get_reporting_file_path(filename):
    """Get full path for a file in the reporting directory."""
    return OUTPUT_DIR / filename

# Function to get v4 trace outputs file path
def get_v4_trace_file_path(filename):
    """Get full path for a file in the v4_trace_outputs directory."""
    return V4_TRACE_OUTPUTS_DIR / filename

# Remove legacy subdirectories from output
for subdir in ["reports", "charts", "allocation_reports", "debug", "debug_reports", "ema_calculations", "param_combo_1", "param_combo_2", "param_combo_3", "param_combo_4", "param_combo_5", "param_combo_6", "test_reports", "v2_tests", "v3_test", "v3_test_20250514_193828", "v3_test_reports"]:
    try:
        (OUTPUT_DIR / subdir).rmdir()
    except:
        pass

# Ensure all directories exist
LOG_DIR.mkdir(exist_ok=True)
REPORT_DIR.mkdir(exist_ok=True)
CHART_DIR.mkdir(exist_ok=True)
V4_TRACE_OUTPUTS_DIR.mkdir(exist_ok=True)
OPTIMIZATION_VALIDATION_DIR.mkdir(exist_ok=True)
PY_REPORTING_DIR.mkdir(exist_ok=True)
