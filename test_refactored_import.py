#!/usr/bin/env python
"""
Simple test to verify the refactored PerformanceTableGenerator can be imported and initialized.
"""

try:
    print("Testing refactored PerformanceTableGenerator import...")
    from v4.py_reporting.v4_performance_report import PerformanceTableGenerator
    print("✓ Import successful")
    
    print("Testing PerformanceTableGenerator initialization...")
    ptg = PerformanceTableGenerator(csv_flag_use=True)
    print("✓ Initialization successful")
    
    print("Testing get_optimization_combinations method...")
    combinations = ptg.get_optimization_combinations()
    print(f"✓ Found {len(combinations)} combinations")
    
    print("All tests passed! The refactored system is working.")
    
except Exception as e:
    print(f"✗ Error: {e}")
    import traceback
    traceback.print_exc()