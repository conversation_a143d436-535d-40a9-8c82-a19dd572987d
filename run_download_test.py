"""
Temporary test runner to handle path issues
"""
import os
import sys

# Add project root to path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

# Import and run test
from v4.data.download_test import test_download

if __name__ == "__main__":
    print("[TEST] Starting forced data download (Save mode)...")
    success = test_download()
    if success:
        print("[TEST] Download completed successfully")
    else:
        print("[TEST] Download failed - check reporting/logs/dataloader.log")
    input("Press Enter to continue...")
