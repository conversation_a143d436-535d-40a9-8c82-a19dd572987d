#!/usr/bin/env python
# -*- coding: utf-8 -*-
# run_compliance_workflow.py
"""
Step-by-step compliance workflow for performance reports and charts.

This script implements a systematic approach to verify and enhance each report
component according to the requirements in reporting_output_requirements_v4.md.

Author: AI Assistant
Date: 2025-06-12
"""

import os
import sys
import logging
import datetime
import traceback
from pathlib import Path
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from typing import Dict, Any, List, Tuple, Optional

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Add the project root to the path if needed
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# Import required modules
try:
    from v4_reporting.test_v4_performance_report import generate_test_data, test_report_generation
    from v4_reporting.verify_v4_performance_report import run_verification
    from v4_reporting.report_compliance_utils import (
        verify_report_structure, 
        verify_graphics_output,
        generate_compliance_report
    )
except ImportError as e:
    logger.error(f"Error importing required modules: {e}")
    raise

def setup_workflow_directories():
    """Set up directories for the compliance workflow"""
    # Create workflow output directories
    workflow_dir = Path(project_root) / 'v4_reporting' / 'compliance_workflow'
    workflow_dir.mkdir(exist_ok=True, parents=True)
    
    # Create subdirectories for each report component
    components = [
        'performance_table',
        'monthly_returns',
        'cumulative_returns',
        'parameter_flow',
        'signal_history'
    ]
    
    for component in components:
        component_dir = workflow_dir / component
        component_dir.mkdir(exist_ok=True)
    
    return workflow_dir

def run_performance_table_workflow(workflow_dir: Path) -> bool:
    """
    Run the compliance workflow for the Performance Table Report (XLSX).
    
    Args:
        workflow_dir: Path to the workflow directory
        
    Returns:
        Boolean indicating success
    """
    logger.info("=== Starting Performance Table Report Workflow ===")
    
    output_dir = workflow_dir / 'performance_table'
    
    try:
        # Step 1: Generate test data
        logger.info("Step 1: Generating test data...")
        backtest_results = generate_test_data()
        
        # Step 2: Generate performance report
        logger.info("Step 2: Generating performance report...")
        from v4_reporting.v4_performance_report import generate_performance_report
        
        report_path = generate_performance_report(
            backtest_results=backtest_results,
            strategy_name="ComplianceTest",
            output_dir=str(output_dir),
            is_new_file=True
        )
        
        if not report_path or not os.path.exists(report_path):
            logger.error("Failed to generate performance report")
            return False
        
        logger.info(f"Generated report: {report_path}")
        
        # Step 3: Verify report structure
        logger.info("Step 3: Verifying report structure...")
        structure_compliance, structure_results = verify_report_structure(report_path)
        
        for check, result in structure_results.items():
            status = "PASSED" if result else "FAILED"
            logger.info(f"  - {check}: {status}")
        
        # Step 4: Run standard verification
        logger.info("Step 4: Running standard verification...")
        verification_result = run_verification(report_path)
        
        # Step 5: Generate compliance report
        logger.info("Step 5: Generating compliance report...")
        compliance_report_path = generate_compliance_report(report_path, str(output_dir))
        
        logger.info(f"Compliance report generated: {compliance_report_path}")
        
        # Overall result
        overall_result = structure_compliance and verification_result
        logger.info(f"Performance Table Report Workflow: {'PASSED' if overall_result else 'FAILED'}")
        
        return overall_result
    
    except Exception as e:
        logger.error(f"Error in Performance Table Report Workflow: {str(e)}")
        logger.error(traceback.format_exc())
        return False

def run_monthly_returns_workflow(workflow_dir: Path) -> bool:
    """
    Run the compliance workflow for the Monthly & Annual Returns Graphic (PNG).
    
    Args:
        workflow_dir: Path to the workflow directory
        
    Returns:
        Boolean indicating success
    """
    logger.info("=== Starting Monthly & Annual Returns Graphic Workflow ===")
    
    output_dir = workflow_dir / 'monthly_returns'
    
    try:
        # Step 1: Generate test data
        logger.info("Step 1: Generating test data...")
        backtest_results = generate_test_data()
        
        # Step 2: Generate monthly returns chart
        logger.info("Step 2: Generating monthly returns chart...")
        
        # Extract monthly returns from backtest results
        monthly_returns = backtest_results.get('monthly_returns', {})
        
        if not monthly_returns:
            logger.error("No monthly returns data in backtest results")
            return False
        
        # Convert to DataFrame if needed
        if not isinstance(monthly_returns, pd.DataFrame):
            monthly_returns = pd.DataFrame(monthly_returns)
        
        # Generate chart
        plt.figure(figsize=(12, 8))
        monthly_returns.plot(kind='bar', title='Monthly Returns')
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        
        # Save chart
        chart_path = output_dir / 'monthly_returns_chart.png'
        plt.savefig(chart_path)
        plt.close()
        
        logger.info(f"Generated monthly returns chart: {chart_path}")
        
        # Step 3: Verify chart output
        logger.info("Step 3: Verifying chart output...")
        
        if not os.path.exists(chart_path):
            logger.error("Monthly returns chart file not found")
            return False
        
        file_size = os.path.getsize(chart_path)
        if file_size < 10 * 1024:  # 10 KB
            logger.warning(f"Monthly returns chart is suspiciously small: {file_size} bytes")
            
        logger.info(f"Monthly returns chart size: {file_size} bytes")
        
        # Overall result
        logger.info("Monthly & Annual Returns Graphic Workflow: PASSED")
        return True
    
    except Exception as e:
        logger.error(f"Error in Monthly Returns Graphic Workflow: {str(e)}")
        logger.error(traceback.format_exc())
        return False

def run_cumulative_returns_workflow(workflow_dir: Path) -> bool:
    """
    Run the compliance workflow for the Cumulative Returns & Drawdown Graphic (PNG).
    
    Args:
        workflow_dir: Path to the workflow directory
        
    Returns:
        Boolean indicating success
    """
    logger.info("=== Starting Cumulative Returns & Drawdown Graphic Workflow ===")
    
    output_dir = workflow_dir / 'cumulative_returns'
    
    try:
        # Step 1: Generate test data
        logger.info("Step 1: Generating test data...")
        backtest_results = generate_test_data()
        
        # Step 2: Generate cumulative returns chart
        logger.info("Step 2: Generating cumulative returns chart...")
        
        # Extract strategy and benchmark returns
        strategy_returns = backtest_results.get('strategy_returns')
        benchmark_returns = backtest_results.get('benchmark_returns')
        
        if strategy_returns is None:
            logger.error("No strategy returns data in backtest results")
            return False
        
        # Convert to DataFrame if needed
        if not isinstance(strategy_returns, pd.DataFrame) and not isinstance(strategy_returns, pd.Series):
            strategy_returns = pd.Series(strategy_returns)
        
        # Calculate cumulative returns
        cum_strategy = (1 + strategy_returns).cumprod()
        
        # Calculate drawdown
        peak = cum_strategy.cummax()
        drawdown = (cum_strategy - peak) / peak
        
        # Create figure with two subplots
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10), gridspec_kw={'height_ratios': [3, 1]})
        
        # Plot cumulative returns
        ax1.plot(cum_strategy.index, cum_strategy.values, label='Strategy')
        
        if benchmark_returns is not None:
            if not isinstance(benchmark_returns, pd.DataFrame) and not isinstance(benchmark_returns, pd.Series):
                benchmark_returns = pd.Series(benchmark_returns)
            cum_benchmark = (1 + benchmark_returns).cumprod()
            ax1.plot(cum_benchmark.index, cum_benchmark.values, label='Benchmark')
        
        ax1.set_title('Cumulative Returns')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # Plot drawdown
        ax2.fill_between(drawdown.index, drawdown.values, 0, color='red', alpha=0.3)
        ax2.set_title('Drawdown')
        ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        # Save chart
        chart_path = output_dir / 'cumulative_returns_drawdown_chart.png'
        plt.savefig(chart_path)
        plt.close()
        
        logger.info(f"Generated cumulative returns chart: {chart_path}")
        
        # Step 3: Verify chart output
        logger.info("Step 3: Verifying chart output...")
        
        if not os.path.exists(chart_path):
            logger.error("Cumulative returns chart file not found")
            return False
        
        file_size = os.path.getsize(chart_path)
        if file_size < 10 * 1024:  # 10 KB
            logger.warning(f"Cumulative returns chart is suspiciously small: {file_size} bytes")
            
        logger.info(f"Cumulative returns chart size: {file_size} bytes")
        
        # Overall result
        logger.info("Cumulative Returns & Drawdown Graphic Workflow: PASSED")
        return True
    
    except Exception as e:
        logger.error(f"Error in Cumulative Returns Graphic Workflow: {str(e)}")
        logger.error(traceback.format_exc())
        return False

def run_parameter_flow_workflow(workflow_dir: Path) -> bool:
    """
    Run the compliance workflow for Parameter Flow Verification.
    
    Args:
        workflow_dir: Path to the workflow directory
        
    Returns:
        Boolean indicating success
    """
    logger.info("=== Starting Parameter Flow Verification Workflow ===")
    
    output_dir = workflow_dir / 'parameter_flow'
    
    try:
        # Step 1: Verify parameter passing
        logger.info("Step 1: Verifying parameter passing...")
        
        # Import the parameter passing test
        from v4_reporting.test_v4_performance_report import test_parameter_passing
        
        # Run the test
        parameter_test_result = test_parameter_passing()
        
        if not parameter_test_result:
            logger.error("Parameter passing test failed")
            return False
        
        logger.info("Parameter passing test passed")
        
        # Step 2: Document parameter flow
        logger.info("Step 2: Documenting parameter flow...")
        
        # Load settings
        from v4.settings.settings_CPS_v4 import load_settings
        settings = load_settings()
        
        # Create parameter flow documentation
        flow_doc_path = output_dir / 'parameter_flow_documentation.txt'
        
        with open(flow_doc_path, 'w') as f:
            f.write("Parameter Flow Documentation\n")
            f.write("===========================\n\n")
            
            f.write("1. Parameter Source\n")
            f.write("   - Settings loaded from v4.settings.settings_CPS_v4.load_settings()\n")
            
            f.write("\n2. Report Parameters\n")
            if 'report' in settings:
                for key, value in settings['report'].items():
                    f.write(f"   - {key}: {value}\n")
            else:
                f.write("   - No report parameters found in settings\n")
            
            f.write("\n3. Parameter Flow Path\n")
            f.write("   - Settings loaded in v4_performance_report.py\n")
            f.write("   - Passed to _generate_excel_report function\n")
            f.write("   - Used to configure report generation\n")
        
        logger.info(f"Parameter flow documentation created: {flow_doc_path}")
        
        # Overall result
        logger.info("Parameter Flow Verification Workflow: PASSED")
        return True
    
    except Exception as e:
        logger.error(f"Error in Parameter Flow Verification Workflow: {str(e)}")
        logger.error(traceback.format_exc())
        return False

def run_signal_history_workflow(workflow_dir: Path) -> bool:
    """
    Run the compliance workflow for Signal History Verification.
    
    Args:
        workflow_dir: Path to the workflow directory
        
    Returns:
        Boolean indicating success
    """
    logger.info("=== Starting Signal History Verification Workflow ===")
    
    output_dir = workflow_dir / 'signal_history'
    
    try:
        # Step 1: Generate test data
        logger.info("Step 1: Generating test data...")
        backtest_results = generate_test_data()
        
        # Step 2: Extract and verify signal history
        logger.info("Step 2: Extracting and verifying signal history...")
        
        signal_history = backtest_results.get('signal_history')
        
        if signal_history is None:
            logger.error("No signal_history in backtest results")
            return False
        
        # Create signal history verification file
        verification_path = output_dir / 'signal_history_verification.txt'
        
        with open(verification_path, 'w') as f:
            f.write("Signal History Verification\n")
            f.write("=========================\n\n")
            
            f.write("1. Signal History Structure\n")
            if isinstance(signal_history, dict):
                f.write("   - Type: Dictionary (date -> allocations)\n")
                f.write(f"   - Number of dates: {len(signal_history)}\n")
                
                # Sample a few dates
                sample_dates = list(signal_history.keys())[:3]
                f.write("   - Sample dates:\n")
                for date in sample_dates:
                    f.write(f"     * {date}: {signal_history[date]}\n")
                    
            elif isinstance(signal_history, pd.DataFrame):
                f.write("   - Type: DataFrame\n")
                f.write(f"   - Shape: {signal_history.shape}\n")
                f.write(f"   - Columns: {list(signal_history.columns)}\n")
                f.write("   - First few rows:\n")
                f.write(f"{signal_history.head(3).to_string()}\n")
            else:
                f.write(f"   - Type: {type(signal_history)}\n")
                f.write("   - Warning: Unexpected type for signal_history\n")
            
            f.write("\n2. Allocation Verification\n")
            
            # Verify allocations sum to 1.0 or 100%
            if isinstance(signal_history, dict):
                for date, allocations in list(signal_history.items())[:5]:
                    alloc_sum = sum(allocations.values())
                    f.write(f"   - {date}: Sum = {alloc_sum}\n")
            elif isinstance(signal_history, pd.DataFrame):
                # Skip date column if present
                numeric_cols = signal_history.select_dtypes(include=['number']).columns
                row_sums = signal_history[numeric_cols].sum(axis=1)
                f.write(f"   - Row sums (first 5): {row_sums.head(5).tolist()}\n")
        
        logger.info(f"Signal history verification created: {verification_path}")
        
        # Step 3: Write signal history to raw data file
        logger.info("Step 3: Writing signal history to raw data file...")
        
        raw_data_dir = Path(project_root) / 'v4_reporting' / 'test_output' / 'test_raw_data'
        raw_data_dir.mkdir(exist_ok=True, parents=True)
        
        signal_history_path = raw_data_dir / 'signal_history.txt'
        
        # Write signal history to file
        if isinstance(signal_history, dict):
            with open(signal_history_path, 'w') as f:
                f.write("Date,Ticker,Allocation\n")
                for date, allocations in signal_history.items():
                    for ticker, allocation in allocations.items():
                        # Format as percentage with 2 decimal places
                        alloc_pct = f"{allocation*100:.2f}%" if allocation < 1 else f"{allocation:.2f}%"
                        f.write(f"{date},{ticker},{alloc_pct}\n")
        elif isinstance(signal_history, pd.DataFrame):
            signal_history.to_csv(signal_history_path, index=True)
        
        logger.info(f"Signal history written to: {signal_history_path}")
        
        # Overall result
        logger.info("Signal History Verification Workflow: PASSED")
        return True
    
    except Exception as e:
        logger.error(f"Error in Signal History Verification Workflow: {str(e)}")
        logger.error(traceback.format_exc())
        return False

def run_full_compliance_workflow():
    """Run the complete compliance workflow for all reports and charts"""
    logger.info("Starting full compliance workflow...")
    
    # Set up workflow directories
    workflow_dir = setup_workflow_directories()
    
    # Run each workflow component
    results = {}
    
    # 1. Performance Table Report
    results['performance_table'] = run_performance_table_workflow(workflow_dir)
    
    # 2. Monthly & Annual Returns Graphic
    results['monthly_returns'] = run_monthly_returns_workflow(workflow_dir)
    
    # 3. Cumulative Returns & Drawdown Graphic
    results['cumulative_returns'] = run_cumulative_returns_workflow(workflow_dir)
    
    # 4. Parameter Flow Verification
    results['parameter_flow'] = run_parameter_flow_workflow(workflow_dir)
    
    # 5. Signal History Verification
    results['signal_history'] = run_signal_history_workflow(workflow_dir)
    
    # Generate summary report
    summary_path = workflow_dir / 'compliance_summary.txt'
    
    with open(summary_path, 'w') as f:
        f.write("Compliance Workflow Summary\n")
        f.write("=========================\n\n")
        f.write(f"Date: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        for component, result in results.items():
            status = "PASSED" if result else "FAILED"
            f.write(f"{component}: {status}\n")
        
        f.write(f"\nOverall: {'PASSED' if all(results.values()) else 'FAILED'}\n")
    
    logger.info(f"Compliance summary created: {summary_path}")
    logger.info(f"Overall compliance status: {'PASSED' if all(results.values()) else 'FAILED'}")
    
    return all(results.values())

if __name__ == "__main__":
    run_full_compliance_workflow()
