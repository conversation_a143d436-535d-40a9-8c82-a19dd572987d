"""
Simple optimization parameter detection for CPS v4
Uses section-agnostic parameter search to eliminate section dependency bugs.
"""

import configparser
import itertools
from pathlib import Path

# Import centralized path configuration
from v4.config.paths_v4 import V4_SETTINGS_FILE
# Import section-agnostic configuration helper
from v4.settings.config_helper import ConfigHelper

# Import smart logging system
from v4.utils.smart_logging import create_smart_logger
logger = create_smart_logger(__name__)


# Removed load_config function - using ConfigHelper for section-agnostic access


def has_any_optimization_parameters(config_path=None):
    """Check if ANY parameter is set to optimize (section-agnostic detection).

    Returns:
        bool: True if any parameter has optimize=true, False otherwise
    """
    if config_path is None:
        config_path = str(V4_SETTINGS_FILE)

    # Use section-agnostic ConfigHelper instead of section-based search
    helper = ConfigHelper(config_path)

    # Check all parameters regardless of section
    for param_name in helper._param_lookup:
        section_name, param_value = helper._param_lookup[param_name]

        # Skip Lists section
        if section_name == 'Lists':
            continue

        if param_value.strip().startswith('(') and 'optimize=' in param_value:
            try:
                param_str = param_value.strip()[1:-1]
                param_dict = {}
                for item in param_str.split(','):
                    key, value = item.split('=', 1)
                    key = key.strip()
                    value = value.strip()
                    param_dict[key] = value

                # Check if this parameter should be optimized
                if param_dict.get('optimize', 'False').lower() == 'true':
                    logger.debug(f"Found optimization parameter: {param_name} in section [{section_name}]")
                    return True
            except Exception:
                continue  # Skip malformed parameters

    return False


def get_optimization_combinations(config_path=None):
    """Get all optimization parameter combinations from config using section-agnostic search.

    Returns:
        List of parameter combination dictionaries
    """
    if config_path is None:
        config_path = str(V4_SETTINGS_FILE)

    # Use section-agnostic ConfigHelper instead of section-based search
    helper = ConfigHelper(config_path)

    optimization_params = {}
    fixed_params = {}

    logger.info(f"Loading optimization config from: {config_path}")
    logger.debug(f"Using section-agnostic parameter search")

    # Search for ALL ComplexN parameters across ALL sections
    for param_name in helper._param_lookup:
        section_name, param_value = helper._param_lookup[param_name]

        # Skip Lists section
        if section_name == 'Lists':
            continue

        # Check if this is a ComplexN parameter
        if param_value.strip().startswith('(') and 'optimize=' in param_value:
            logger.debug(f"Found ComplexN parameter {param_name} = {param_value} (in [{section_name}])")

            try:
                # Parse ComplexN format
                param_str = param_value.strip()[1:-1]
                param_dict = {}
                for item in param_str.split(','):
                    key, value = item.split('=', 1)
                    key = key.strip()
                    value = value.strip()
                    param_dict[key] = value

                logger.debug(f"Parsed {param_name}: {param_dict}")

                # Check if this parameter should be optimized
                if param_dict.get('optimize', 'False').lower() == 'true':
                    # Generate optimization range
                    min_val = int(float(param_dict.get('min_value', 0)))
                    max_val = int(float(param_dict.get('max_value', 100)))
                    increment = int(float(param_dict.get('increment', 1)))

                    values = list(range(min_val, max_val + 1, increment))
                    optimization_params[param_name] = values
                    logger.debug(f"Added optimization param {param_name}: {values}")
                else:
                    # Fixed parameter - use default value
                    default_val = param_dict.get('default_value', '0')
                    # Handle special parameter name mapping
                    if param_name == 'system_top_n':
                        fixed_params['top_n'] = int(float(default_val))
                    else:
                        fixed_params[param_name] = int(float(default_val))
                    logger.debug(f"Added fixed param {param_name}: {fixed_params.get(param_name, fixed_params.get('top_n'))}")
            except Exception as e:
                logger.debug(f"Error parsing {param_name}: {e}")
                continue

    # Get simple parameters that are commonly needed
    execution_delay = helper.get('execution_delay')
    if execution_delay:
        try:
            fixed_params['execution_delay'] = int(execution_delay)
            logger.debug(f"Added execution_delay: {fixed_params['execution_delay']}")
        except:
            fixed_params['execution_delay'] = 1

    logger.info(f"Final optimization_params: {optimization_params}")
    logger.info(f"Final fixed_params: {fixed_params}")

    # Check if optimization is active using section-agnostic approach
    optimization_active = helper.getboolean('optimization_active', fallback=True)

    logger.info(f"Global optimization_active: {optimization_active}")
    
    # Generate all combinations if optimization is active, otherwise just return default values
    if optimization_active and optimization_params:
        param_names = list(optimization_params.keys())
        param_values = list(optimization_params.values())
        combinations = list(itertools.product(*param_values))
        
        logger.info(f"Generated {len(combinations)} combinations from {len(optimization_params)} optimizable params")
        
        result = []
        for combo in combinations:
            param_set = fixed_params.copy()
            for i, param_name in enumerate(param_names):
                param_set[param_name] = combo[i]
            result.append(param_set)
        
        # Log first few combinations for verification
        for i, combo in enumerate(result[:3]):
            logger.debug(f"Combination {i}: {combo}")

        logger.info(f"Returning {len(result)} combinations for optimization")
        return result
    else:
        # Single run mode - use default values from all parameters (optimization and fixed)
        single_combination = fixed_params.copy()
        
        # Add default values from optimization parameters 
        for section_name in config.sections():
            if section_name == 'Lists':  # Skip lists section
                continue
            section = config[section_name]
            
            for param_name, param_value in section.items():
                if param_value.strip().startswith('(') and 'optimize=' in param_value:
                    try:
                        param_str = param_value.strip()[1:-1]
                        param_dict = {}
                        for item in param_str.split(','):
                            key, value = item.split('=', 1)
                            key = key.strip()
                            value = value.strip()
                            param_dict[key] = value
                        
                        # Use default value regardless of optimize flag
                        if 'default_value' in param_dict:
                            if param_name == 'system_top_n':
                                single_combination['top_n'] = int(float(param_dict['default_value']))
                            else:
                                single_combination[param_name] = int(float(param_dict['default_value']))
                    except Exception as e:
                        logger.debug(f"Error parsing default for {param_name}: {e}")

        logger.info(f"Single run mode - returning single combination with defaults: {single_combination}")
        return [single_combination]


if __name__ == "__main__":
    # Test the function
    combinations = get_optimization_combinations()
    logger.info(f"Final result: {len(combinations)} combinations")
    for i, combo in enumerate(combinations[:5]):
        logger.info(f"  {i}: {combo}")
