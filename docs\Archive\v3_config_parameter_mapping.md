# V3 Config Parameter Mapping Matrix (`config/config_v3.py`)

This document maps the parameters defined in `config/config_v3.py` to their type, usage, and V3 status.

**Status Legend:**

* **V3 Standard Defined:** Uses the `define_parameter` tuple format or a V3 Parameter Class (`CategoricalParam`). Integrated with V3 optimization/GUI systems.
* **Legacy Definition:** Defined as a simple Python type (str, list, bool, int). Not yet converted to a V3 Parameter Class/structure.
* **Unconverted:** Specifically identified in `Engine_files_v3.md` or inferred as needing conversion to a V3 Parameter Class to work correctly with the V3 flow.

**GUI Shown Legend:**

* **Yes (V3 Std):** Parameter uses V3 structure intended for GUI display.
* **No (V3 Std):** Parameter uses V3 structure but is not intended for GUI display (e.g., `ConfigParameter` type intended).
* **No (Legacy):** Legacy definition, not currently shown in V3 GUI.

## Data Parameters (`data_params`)

| Variable Name       | Type | Where Used           | GUI Shown   | Status            | Default Value                         | Description                                          |
| ------------------- | ---- | -------------------- | ----------- | ----------------- | ------------------------------------- | ---------------------------------------------------- |
| `tickers`           | list | Data Loading, Engine | No (Legacy) | Legacy Definition | `['SPY', 'SHV', 'EFA', 'TLT', 'PFF']` | List of tickers to include in portfolio              |
| `start_date`        | str  | Data Loading, Engine | No (Legacy) | Unconverted       | `'2020-01-01'`                        | Backtest start date                                  |
| `end_date`          | str  | Data Loading, Engine | No (Legacy) | Unconverted       | `'2025-04-23'`                        | Backtest end date                                    |
| `price_field`       | str  | Data Loading, Engine | No (Legacy) | Unconverted       | `'Close'`                             | Price field to use for calculations                  |
| `risk_free_ticker`  | str  | Performance Report   | No (Legacy) | Unconverted       | `'^IRX'`                              | Risk-free rate ticker (used for performance metrics) |
| `data_storage_mode` | str  | Data Loading         | No (Legacy) | Unconverted       | `'Read'`                              | Data storage mode: 'Save', 'Read', or 'New'          |

## Backtesting Parameters (`backtest_params`)

| Variable Name     | Type                 | Where Used            | GUI Shown          | Status              | Default Value | Description                                                      |
| ----------------- | -------------------- | --------------------- | ------------------ | ------------------- | ------------- | ---------------------------------------------------------------- |
| ~~`strategy`~~    | ~~str~~              | ~~Engine, Report Header~~ | ~~No (Legacy)~~    | ~~Legacy Definition~~ | ~~`'ema'`~~   | ~~Strategy name (equal_weight, momentum, ema)~~ **DEPRECATED - Replaced by `signal_algo` in `strategy_params`** |
| `rebalance_freq`  | str                  | Engine                | Yes (V3 Intended)* | Unconverted         | `'weekly'`    | Portfolio rebalancing frequency (V3 Class intended: Categorical) |
| `execution_delay` | V3 Optimizable Tuple | Engine, Report Header | Yes (V3 Std)       | V3 Standard Defined | `1`           | Trade execution delay in days (V3 Class: Numeric)                |
| `commission_rate` | float                | Engine, Report Header | No (V3 Std)*       | Unconverted         | `0.001`       | Transaction cost (V3 Class intended: ConfigParameter)            |
| `slippage_rate`   | float                | Engine, Report Header | No (V3 Std)*       | Unconverted         | `0.001`       | Slippage cost (V3 Class intended: ConfigParameter)               |
| `initial_capital` | int                  | Engine, Report Header | No (V3 Std)*       | Unconverted         | `1000000`     | Initial capital (V3 Class intended: ConfigParameter)             |

*Needs conversion to specified V3 Parameter Class for full integration.*

## Benchmark Parameters (`benchmark_params`)

| Variable Name              | Type | Where Used         | GUI Shown   | Status      | Default Value | Description                                | Role Comment      |
| -------------------------- | ---- | ------------------ | ----------- | ----------- | ------------- | ------------------------------------------ | ----------------- |
| `benchmark_ticker`         | str  | Performance Report | No (Legacy) | Unconverted | `'SPY'`       | Ticker for benchmark comparison            | benchmark_primary |
| `benchmark_rebalance_freq` | str  | Engine (Benchmark) | No (Legacy) | Unconverted | `'yearly'`    | Benchmark rebalancing frequency            | benchmark_primary |
| `debug_benchmark`          | bool | Logging            | No (Legacy) | Unconverted | `True`        | Whether to log detailed benchmark activity | benchmark_primary |

## Strategy Parameters (`strategy_params` - EMA Example)

| Variable Name | Type                 | Where Used          | GUI Shown    | Status              | Default Value | Description                               |
| ------------- | -------------------- | ------------------- | ------------ | ------------------- | ------------- | ----------------------------------------- |
| `top_n`       | V3 Optimizable Tuple | Strategy (Momentum) | Yes (V3 Std) | V3 Standard Defined | `2`           | Number of top assets to select (Momentum) |
| `st_lookback` | V3 Optimizable Tuple | Strategy (EMA)      | Yes (V3 Std) | V3 Standard Defined | `15`          | Short-term EMA period (EMA)               |
| `mt_lookback` | V3 Optimizable Tuple | Strategy (EMA)      | Yes (V3 Std) | V3 Standard Defined | `70`          | Medium-term EMA period (EMA)              |
| `lt_lookback` | V3 Optimizable Tuple | Strategy (EMA)      | Yes (V3 Std) | V3 Standard Defined | `100`         | Long-term EMA period (EMA)                |
| `signal_algo` | V3 CategoricalParam  | Strategy Selection, Engine, Report Header | Yes (V3 Std) | V3 Standard Defined | `'ema'`       | Algorithm for signal generation and strategy selection (replaces `strategy` parameter) |

## StrategyOptimizeParameter Mapping

| Config Key | Parameter Class | Required | Default | Optimizable | Reportable | Description |
|-----------|-----------------|:--------:|---------|:-----------:|:----------:|-------------|
| strategy_params.* | StrategyOptimizeParameter | ✓ | - | ✓ | ✓ | Strategy-specific parameters with reporting control |
| strategy_params.*.type | str | ✓ | - | - | - | 'numeric' or 'categorical' |
| strategy_params.*.default | any | ✓ | - | - | - | Default parameter value |
| strategy_params.*.min | number | cond | - | - | - | Required for numeric parameters |
| strategy_params.*.max | number | cond | - | - | - | Required for numeric parameters |
| strategy_params.*.step | number | cond | - | - | - | Required for numeric parameters |
| strategy_params.*.choices | list | cond | - | - | - | Required for categorical parameters |
| strategy_params.*.group | str | - | strategy name | - | - | Parameter group name |
| strategy_params.*.optimize | bool | - | True | - | - | Include in optimization |
| strategy_params.*.show_in_gui | bool | - | True | - | - | Show in GUI interface |
| strategy_params.*.show_in_report | bool | - | True | - | - | Include in performance reports |
| strategy_params.*.description | str | - | "" | - | - | Parameter description |

## Visualization Parameters (`visualization_params`)

| Variable Name   | Type | Where Used       | GUI Shown   | Status      | Default Value                                                                                       | Description                  |
| --------------- | ---- | ---------------- | ----------- | ----------- | --------------------------------------------------------------------------------------------------- | ---------------------------- |
| `create_charts` | bool | Chart Generation | No (Legacy) | Unconverted | `True`                                                                                              | Whether to create charts     |
| `chart_types`   | list | Chart Generation | No (Legacy) | Unconverted | `['cumulative_returns', 'drawdown', 'return_distribution', 'monthly_returns', 'portfolio_weights']` | Types of charts to create    |
| `chart_format`  | str  | Chart Generation | No (Legacy) | Unconverted | `'png'`                                                                                             | Chart format (png, jpg, svg) |
| `chart_dpi`     | int  | Chart Generation | No (Legacy) | Unconverted | `300`                                                                                               | Chart resolution (DPI)       |

## Reporting Parameters (`reporting_params`)

| Variable Name    | Type | Where Used         | GUI Shown   | Status      | Default Value                                            | Description                       |
| ---------------- | ---- | ------------------ | ----------- | ----------- | -------------------------------------------------------- | --------------------------------- |
| `create_excel`   | bool | Report Generation  | No (Legacy) | Unconverted | `True`                                                   | Whether to create Excel reports   |
| `save_trade_log` | bool | Trade Log Output   | No (Legacy) | Unconverted | `True`                                                   | Whether to save trade logs        |
| `output_dir`     | str  | Report Generation  | No (Legacy) | Unconverted | Value from `config.paths.OUTPUT_DIR`                     | Output directory for reports/logs |
| `metrics`        | list | Performance Report | No (Legacy) | Unconverted | `['total_return', 'annualized_return', ..., 'win_rate']` | Performance metrics to include    |
| `report_benchmark` | str | Performance Reporting | No (Legacy) | Implemented | `'SPY'` | Benchmark ticker for comparison |
| `chart_dpi` | int | Performance Charts | No (Legacy) | Implemented | `300` | DPI for output charts (default: 300) |
| `trade_log_format` | str | Trade Log | No (Legacy) | Implemented | `'csv'` | Format style for trade log output |
