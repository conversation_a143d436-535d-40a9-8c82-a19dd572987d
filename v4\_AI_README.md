# V4 Directory - CPS V4 Active Development

This directory contains all active CPS V4 components organized by function. The V4 system follows the Central Parameter System V4 design principles:

- Radical simplicity in parameter handling
- Direct INI parameter access (no adapters)
- AI-friendly modularity (modules under 300 lines)
- Clear error handling for missing parameters (no fallbacks)
- Consistent naming with `_v4` suffix or `v4_` prefix

## Directory Structure

| Directory | Purpose |
|-----------|---------|
| `settings` | Configuration and parameter settings for V4 |
| `engine` | Core backtest engine modules |
| `models` | Strategy and allocation models |
| `reporting` | Report generation modules |
| `utils` | Utility functions and helpers |
| `scripts` | Scripts for running V4 components |
| `app` | GUI components (if applicable) |
| `config` | Configuration files and specifications |
| `optimization` | Optimization algorithms and tools |
| `tools` | Development and maintenance tools |

## Module and Function Matrix

### Settings Modules

| Module | Key Functions | Purpose |
|--------|--------------|---------|
| `settings_CPS_v4.py` | `load_settings()`, `get_parameter()` | Core parameter loading and access |
| `settings_parameters_v4.ini` | N/A | Primary parameter configuration file |

### Engine Modules

| Module | Key Functions | Purpose |
|--------|--------------|---------|
| `backtest_v4.py` | `run_backtest()`, `initialize_backtest()` | Main backtest execution |
| `portfolio_v4.py` | `Portfolio`, `update_positions()` | Portfolio management |
| `execution_v4.py` | `execute_orders()`, `calculate_slippage()` | Order execution |
| `orders_v4.py` | `Order`, `create_order()` | Order creation and management |

### Model Modules

| Module | Key Functions | Purpose |
|--------|--------------|---------|
| `ema_allocation_model_v4.py` | `calculate_allocation()`, `generate_signals()` | EMA strategy implementation |

### Reporting Modules

| Module | Key Functions | Purpose |
|--------|--------------|---------|
| `allocation_report_v4.py` | `generate_allocation_report()` | Asset allocation reporting |

### Utility Modules

| Module | Key Functions | Purpose |
|--------|--------------|---------|
| `date_utils_v4.py` | `format_date()`, `parse_date()` | Date handling utilities |
| `trade_log_v4.py` | `log_trade()`, `export_trade_log()` | Trade logging functionality |

### Config Modules

| Module | Key Functions | Purpose |
|--------|--------------|---------|
| `paths_v4.py` | `get_data_path()`, `get_output_path()` | Path management |
| `transaction_log_spec_v4.py` | `get_log_format()` | Transaction logging specification |

## Integration Flow

1. **Parameter Loading**: `settings_CPS_v4.py` loads parameters from INI files
2. **Backtest Initialization**: `backtest_v4.py` initializes the backtest environment
3. **Model Execution**: Strategy models generate signals and allocations
4. **Order Generation**: Orders are created based on model signals
5. **Order Execution**: Orders are executed with appropriate slippage
6. **Portfolio Management**: Portfolio positions and values are updated
7. **Reporting**: Performance and allocation reports are generated

## Testing

V4 components should be tested using the verification scripts in the `tests/v4` and `tests/verification` directories. The test flow is:

1. Configure parameters via INI files
2. Run backtest engine
3. Generate full report output suite

## Development Guidelines

1. Keep modules under 300 lines of code
2. Use direct parameter access via `settings_CPS_v4.py`
3. Raise explicit errors for missing parameters (no fallbacks)
4. Maintain consistent naming with `_v4` suffix
5. Document all functions with clear docstrings
6. Add filename in comments at top of all code files

## Migration Status

The core engine components have been converted to V4. Reporting modules are currently being migrated. See `docs/CPS_v4/Task_List_CPS_v4.md` for detailed status.
