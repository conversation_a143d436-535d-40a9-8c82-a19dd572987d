#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to inspect the generated XLSX file and validate annual returns and formatting.
"""

import pandas as pd
import openpyxl
from pathlib import Path
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def inspect_latest_xlsx():
    """Inspect the latest generated XLSX file."""
    try:
        # Find the latest XLSX file
        reporting_dir = Path("../reporting")
        xlsx_files = list(reporting_dir.glob("EMA_V3_1_performance_tables_*.xlsx"))
        
        if not xlsx_files:
            logger.error("No XLSX files found in reporting directory")
            return
        
        latest_file = max(xlsx_files, key=lambda f: f.stat().st_mtime)
        logger.info(f"Inspecting latest file: {latest_file}")
        
        # Load workbook
        workbook = openpyxl.load_workbook(latest_file)
        logger.info(f"Sheets in workbook: {workbook.sheetnames}")
        
        # Inspect Performance sheet
        if 'Performance' in workbook.sheetnames:
            ws = workbook['Performance']
            logger.info(f"Performance sheet dimensions: {ws.max_row} rows × {ws.max_column} columns")
            
            # Read headers (row 1)
            headers = []
            for col in range(1, ws.max_column + 1):
                cell_value = ws.cell(row=1, column=col).value
                headers.append(cell_value)
            
            logger.info(f"Headers: {headers}")
            
            # Check for annual return columns (should start around column L = 12)
            annual_columns = [h for h in headers if h and (str(h).isdigit() or 'YTD' in str(h) or 'part' in str(h))]
            logger.info(f"Annual return columns found: {annual_columns}")
            
            # Inspect first few data rows
            logger.info("\nFirst few rows of data:")
            for row in range(1, min(5, ws.max_row + 1)):
                row_data = []
                for col in range(1, min(12, ws.max_column + 1)):  # First 11 columns
                    cell = ws.cell(row=row, column=col)
                    value = cell.value
                    number_format = cell.number_format
                    row_data.append(f"{value} ({number_format})")
                logger.info(f"Row {row}: {row_data}")
            
            # Check specific formatting for CAGR, Sharpe, etc.
            logger.info("\nChecking metric formatting:")
            if ws.max_row >= 2:  # Benchmark row
                cagr_cell = ws.cell(row=2, column=7)  # CAGR column
                sharpe_cell = ws.cell(row=2, column=8)  # Sharpe column
                max_dd_cell = ws.cell(row=2, column=11)  # Max Drawdown column
                
                logger.info(f"CAGR cell: value={cagr_cell.value}, format={cagr_cell.number_format}")
                logger.info(f"Sharpe cell: value={sharpe_cell.value}, format={sharpe_cell.number_format}")
                logger.info(f"Max DD cell: value={max_dd_cell.value}, format={max_dd_cell.number_format}")
        
        # Also read as DataFrame to see the data
        logger.info("\nReading Performance sheet as DataFrame:")
        df = pd.read_excel(latest_file, sheet_name='Performance')
        logger.info(f"DataFrame shape: {df.shape}")
        logger.info(f"DataFrame columns: {list(df.columns)}")
        
        # Show first few rows
        logger.info("\nFirst 3 rows:")
        print(df.head(3).to_string())
        
    except Exception as e:
        logger.error(f"Error inspecting XLSX file: {e}")

if __name__ == "__main__":
    inspect_latest_xlsx()
