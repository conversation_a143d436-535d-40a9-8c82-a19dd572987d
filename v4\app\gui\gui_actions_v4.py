"""
CPS_v4 Parameter System - GUI Actions

This module handles action handlers for the CPS_v4 GUI system,
such as running backtests. It uses the global CPS_v4 settings object.
"""

import os
import sys
import logging
import time
import datetime
import pandas as pd
import numpy as np
from pathlib import Path
import json
from PySide6.QtWidgets import QMessageBox

# Add project root to path if not already present
project_root_path = Path(__file__).resolve().parents[2]
if str(project_root_path) not in sys.path:
    sys.path.insert(0, str(project_root_path))

# Import CPS_v4 settings system
try:
    from config.settings_CPS_v4 import settings
except ImportError as e:
    logging.critical(f"CRITICAL ERROR: Could not import settings_CPS_v4: {e}. Check path and file existence.")
    sys.exit(f"Failed to import settings_CPS_v4. Application cannot start. {e}")

# Import V4 modules
from data.data_loader_v4 import load_data_for_backtest_v4
from models.ema_allocation_model_v4 import ema_allocation_model_v4, calculate_ema_metrics_v4
from engine.backtest_v4 import BacktestEngineV4 # Assuming BacktestEngineV4 class exists
from utils.date_utils import standardize_dataframe_index
# V4 Reporting modules (ensure these exist and are correctly named)
from reporting.performance_report_v4 import generate_performance_report_v4
from reporting.allocation_report_v4 import generate_allocation_report_v4
# from engine.debug_output_v4 import save_engine_output_v4 # If a V4 version exists

# Set up logging
logger = logging.getLogger(__name__)

def run_backtest_action_v4(parent_window, run_params: dict):
    """
    Run a single backtest execution using the CPS_v4 parameter system.

    This function processes one specific set of parameters as provided in `run_params`.
    It does NOT handle optimization loops internally. If optimization (iterating through
    multiple parameter combinations) is required, the calling component (e.g., MainWindowV4)
    is responsible for generating the parameter sets and calling this function for each set.

    Parameter Hierarchy:
    1. Values in `run_params`: These are typically set by the user via the GUI and
       override any defaults for the current specific run.
    2. Global `settings`: If a parameter is not found in `run_params`, its default
       value is sourced from the global `settings` object (loaded from INI files).

    Args:
        parent_window: Parent window for displaying messages (e.g., for QMessageBox).
        run_params (dict): A dictionary containing all parameters for THIS specific
                           backtest run. This includes any GUI-selected values which
                           override defaults from the `settings` object, as well as
                           control flags like 'debug_mode', 'track_ema_calculations',
                           'strategy_name', etc.
    """
    fh = None  # Initialize file handler
    try:
        # --- Configuration --- 
        project_root = str(project_root_path) # Use resolved project root

        # Get output directory from global settings, default if not found
        base_output_dir_str = settings.get('paths', {}).get('output_dir', os.path.join(project_root, 'output'))
        output_dir = Path(base_output_dir_str)
        os.makedirs(output_dir, exist_ok=True)

        # Specific output subdirectories
        ema_tracking_dir = output_dir / "ema_calculations_v4"
        allocation_dir = output_dir / "allocation_reports_v4"
        debug_dir = output_dir / "debug_v4"
        os.makedirs(ema_tracking_dir, exist_ok=True)
        os.makedirs(allocation_dir, exist_ok=True)
        os.makedirs(debug_dir, exist_ok=True)

        # Configure logging based on run_params
        is_debug_mode = run_params.get('debug_mode', False)
        if is_debug_mode:
            # Ensure logger level is set appropriately (could be done globally too)
            # For this specific run's log file:
            today = datetime.date.today()
            timestamp = f"{today.strftime('%Y-%m-%d')}_{int(time.time()) % 100000}"
            log_file = debug_dir / f"debug_log_cps_v4_run_{timestamp}.txt"
            # Note: Adding handlers repeatedly can cause duplicate logs. 
            # Consider configuring a distinct logger or managing handlers carefully if this action is called multiple times.
            # For simplicity here, we assume this is for a dedicated log file per run if debug is on.
            # A more robust solution might involve a global logger configured once.
            fh = logging.FileHandler(str(log_file))
            fh.setLevel(logging.DEBUG)
            fh.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
            logging.getLogger().addHandler(fh) # Add to root logger or a specific one
            logger.info(f"Debug mode enabled. Detailed logs will be written to: {log_file}")
        
        logger.info(f"Running backtest with CPS_v4. Run parameters: {run_params}")

        # --- Data Loading --- 
        # Data loader parameters can be a mix from global settings and run_params
        # Example: tickers might come from run_params if GUI allows selection, or from settings
        data_loader_params = settings.get('data_loader_config', {})
        data_loader_params.update({k: v for k, v in run_params.items() if k in ['tickers', 'start_date', 'end_date']}) # Example override
        
        price_data = load_data_for_backtest_v4(
            tickers=data_loader_params.get('tickers', ['SPY']),
            start_date=data_loader_params.get('start_date', '2010-01-01'),
            end_date=data_loader_params.get('end_date', '2023-01-01'),
            data_source=settings.get('data_loader_config', {}).get('data_source', 'yahoo_direct'),
            data_path=settings.get('data_loader_config', {}).get('data_path', './data/historical_data.xlsx'),
            settings=settings # Pass full settings if needed by loader_v4
        )
        price_data = standardize_dataframe_index(price_data)

        # --- Model Instantiation --- 
        # Model parameters are a combination of global settings and run_params
        # The ema_allocation_model_v4 should be designed to take settings and optional run_specific overrides
        model_instance = ema_allocation_model_v4(settings=settings, run_specific_params=run_params)

        # --- Backtest Engine --- 
        engine = BacktestEngineV4(
            price_data=price_data, 
            model=model_instance, 
            settings=settings, 
            run_specific_params=run_params
        )
        
        # --- Run Backtest (Single Run based on run_params) --- 
        logger.info(f"Executing backtest with current parameters.")
        results = engine.run_backtest() # run_backtest in V4 engine takes necessary params from init or settings
        
        # --- EMA Tracking --- 
        if run_params.get('track_ema_calculations', False):
            try:
                # calculate_ema_metrics_v4 needs price_data and relevant EMA parameters from settings/run_params
                ema_metrics = calculate_ema_metrics_v4(price_data, settings=settings, run_specific_params=run_params)
                if ema_metrics is not None and not ema_metrics.empty:
                    today = datetime.date.today()
                    timestamp = f"{today.strftime('%Y-%m-%d')}_{int(time.time()) % 100000}"
                    ema_tracking_file = ema_tracking_dir / f"ema_tracking_cps_v4_{timestamp}.xlsx"
                    ema_metrics.to_excel(ema_tracking_file)
                    logger.info(f"EMA tracking data saved to {ema_tracking_file}")
            except Exception as e:
                logger.error(f"Error saving EMA tracking data: {e}", exc_info=True)

        # --- Debug Output & Allocation Report (if debug_parameter_flow or similar flag is on) ---
        if run_params.get('debug_parameter_flow', False): # Or a more generic debug output flag
            try:
                # Save engine output (if a V4 version exists and is imported)
                # if 'save_engine_output_v4' in globals():
                #     save_engine_output_v4(results, debug_dir, f"engine_output_cps_v4", run_params)
                # else:
                #     logger.info("save_engine_output_v4 not available for detailed engine output.")

                signal_df = results.get('signal_history')
                weights_df = results.get('weights_history')
                portfolio_values_df = results.get('portfolio_values')

                if signal_df is None or signal_df.empty:
                    logger.warning("Signal history is empty. Allocation report might be incomplete.")
                if weights_df is None or weights_df.empty:
                    logger.warning("Weights history is empty. Allocation report might be incomplete.")
                
                logger.info(f"Generating allocation report for CPS_v4 strategy.")
                generate_allocation_report_v4(
                    signal_df=signal_df,
                    allocation_df=weights_df,
                    output_dir=str(allocation_dir),
                    strategy_name=run_params.get('strategy_name', 'Strategy_CPS_V4'),
                    portfolio_values=portfolio_values_df,
                    include_cash=run_params.get('report_include_cash', True), # Example: make it configurable
                    parameters=run_params # Pass current run_params for report context
                )
                logger.info(f"Allocation report generated successfully.")
            except Exception as e:
                logger.error(f"Error processing engine output or generating allocation report: {e}", exc_info=True)

        # --- Performance Report --- 
        today = datetime.date.today()
        timestamp = f"{today.strftime('%Y-%m-%d')}_{int(time.time()) % 100000}"
        strategy_name_for_report = run_params.get('strategy_name', 'Strategy_CPS_V4')
        # Ensure output_dir is Path object for '/' operator
        output_path = Path(output_dir) / f"{strategy_name_for_report}_performance_{timestamp}.xlsx"
        
        try:
            generate_performance_report_v4(
                results_data=results, # This should be the direct output from engine.run_backtest()
                output_filepath=str(output_path),
                report_name=strategy_name_for_report, 
                parameters=run_params, 
                settings=settings      
            )
            logger.info(f"Performance report saved to {output_path}")
            formatted_path = str(output_path).replace('\\', '/')
            QMessageBox.information(parent_window, "Backtest Complete", 
                f"Backtest completed successfully. Results saved to {formatted_path}")
        except Exception as e:
            logger.error(f"Error generating performance report: {e}", exc_info=True)
            QMessageBox.critical(parent_window, "Report Error", f"Failed to generate performance report: {str(e)}")
        
    except Exception as e:
        # Log the error and show message box
        logger.exception("Error running backtest")
        QMessageBox.critical(parent_window, "Error", f"Error running backtest: {str(e)}")
    finally:
        if fh is not None:
            fh.close()
            logging.getLogger().removeHandler(fh)
            logger.info("Debug file handler closed and removed.")
