"""
Minimal file creation test script
"""

import os
import sys
from pathlib import Path
from datetime import datetime

# Print to console
print("MINIMAL FILE TEST STARTED")
print(f"Current time: {datetime.now()}")
print(f"Current directory: {os.getcwd()}")
print(f"Python version: {sys.version}")

# Create directory
log_dir = Path(__file__).parent / "minimal_logs"
print(f"Attempting to create directory: {log_dir}")

try:
    log_dir.mkdir(exist_ok=True)
    print(f"Directory created or already exists: {log_dir}")
    
    # Write to file
    log_file = log_dir / f"test_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
    print(f"Attempting to write to file: {log_file}")
    
    with open(log_file, 'w') as f:
        f.write(f"Test log created at {datetime.now()}\n")
        f.write(f"Current directory: {os.getcwd()}\n")
        f.write(f"Python version: {sys.version}\n")
    
    print(f"Successfully wrote to file: {log_file}")
    print(f"File exists: {log_file.exists()}")
    print(f"File size: {log_file.stat().st_size} bytes")
    
except Exception as e:
    print(f"ERROR: {e}")
    
print("MINIMAL FILE TEST COMPLETED")
