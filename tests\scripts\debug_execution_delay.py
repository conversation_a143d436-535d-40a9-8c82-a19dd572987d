"""
Debug script to diagnose execution_delay parameter handling issues.
This script logs detailed information about parameter handling to a CSV file.
"""

import sys
import os
import pandas as pd
from pathlib import Path
import datetime
import logging
import csv
from config.paths import OUTPUT_DIR

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(OUTPUT_DIR / "debug" / f"execution_delay_debug_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Import necessary modules
from config.config_v2 import config_v2, _get_param_value
from config.local_parameter_optimization import define_parameter, validate_parameter, get_parameter_range
from engine.backtest import BacktestEngine

# Define output file paths
output_dir = OUTPUT_DIR / "debug"
output_dir.mkdir(parents=True, exist_ok=True)
csv_path = OUTPUT_DIR / "debug" / f"execution_delay_debug_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
os.makedirs(csv_path.parent, exist_ok=True)

with open(csv_path, 'w', newline='') as csvfile:
    csv_writer = csv.writer(csvfile)
    csv_writer.writerow(['Stage', 'Parameter', 'Type', 'Value', 'Details'])
    
    # Test 1: Check raw parameter in config
    raw_exec_delay = config_v2['backtest_params'].get('execution_delay')
    logger.info(f"Raw execution_delay from config: {raw_exec_delay} (type: {type(raw_exec_delay)})")
    csv_writer.writerow(['Config', 'execution_delay', type(raw_exec_delay).__name__, str(raw_exec_delay), ''])
    
    # Test 2: Check parameter extraction with _get_param_value
    extracted_value = _get_param_value(raw_exec_delay)
    logger.info(f"Extracted value with _get_param_value: {extracted_value} (type: {type(extracted_value)})")
    csv_writer.writerow(['get_param_value', 'execution_delay', type(extracted_value).__name__, str(extracted_value), ''])
    
    # Test 3: Check parameter validation
    try:
        validated_value = validate_parameter(raw_exec_delay)
        logger.info(f"Validated value: {validated_value} (type: {type(validated_value)})")
        csv_writer.writerow(['validate_parameter', 'execution_delay', type(validated_value).__name__, str(validated_value), 'Success'])
    except Exception as e:
        logger.error(f"Error validating parameter: {e}")
        csv_writer.writerow(['validate_parameter', 'execution_delay', 'error', '', str(e)])
    
    # Test 4: Check parameter range generation
    try:
        param_range = get_parameter_range(raw_exec_delay)
        logger.info(f"Parameter range: {param_range}")
        csv_writer.writerow(['get_parameter_range', 'execution_delay', type(param_range).__name__, str(param_range), f'Length: {len(param_range)}'])
    except Exception as e:
        logger.error(f"Error getting parameter range: {e}")
        csv_writer.writerow(['get_parameter_range', 'execution_delay', 'error', '', str(e)])
    
    # Test 5: Create optimizable parameter tuple
    optimizable_param = define_parameter(True, 1, 0, 5, 1)
    logger.info(f"Optimizable parameter: {optimizable_param} (type: {type(optimizable_param)})")
    csv_writer.writerow(['define_parameter', 'execution_delay (optimizable)', type(optimizable_param).__name__, str(optimizable_param), ''])
    
    # Test 6: Try to initialize BacktestEngine with different parameter formats
    try:
        # Test with direct value
        engine1 = BacktestEngine(execution_delay=1)
        logger.info("Successfully created BacktestEngine with direct value")
        csv_writer.writerow(['BacktestEngine', 'execution_delay (direct)', 'int', '1', 'Success'])
    except Exception as e:
        logger.error(f"Error creating BacktestEngine with direct value: {e}")
        csv_writer.writerow(['BacktestEngine', 'execution_delay (direct)', 'error', '', str(e)])
    
    try:
        # Test with parameter tuple
        engine2 = BacktestEngine(execution_delay=optimizable_param)
        logger.info("Successfully created BacktestEngine with parameter tuple")
        csv_writer.writerow(['BacktestEngine', 'execution_delay (tuple)', 'tuple', str(optimizable_param), 'Success'])
    except Exception as e:
        logger.error(f"Error creating BacktestEngine with parameter tuple: {e}")
        csv_writer.writerow(['BacktestEngine', 'execution_delay (tuple)', 'error', '', str(e)])
    
    # Test 7: Try to run backtest with different parameter formats
    # Create a minimal price data DataFrame for testing
    import pandas as pd
    import numpy as np
    dates = pd.date_range(start='2025-01-01', periods=10)
    price_data = pd.DataFrame({
        'SPY': np.linspace(100, 110, 10),
        'TLT': np.linspace(90, 100, 10)
    }, index=dates)
    
    # Define a simple signal generator
    def dummy_signal_generator(price_data, **params):
        return {'SPY': 0.6, 'TLT': 0.4}
    
    try:
        # Test with direct value
        engine3 = BacktestEngine()
        result1 = engine3.run_backtest(
            price_data=price_data,
            signal_generator=dummy_signal_generator,
            execution_delay=1
        )
        logger.info("Successfully ran backtest with direct value")
        csv_writer.writerow(['run_backtest', 'execution_delay (direct)', 'int', '1', 'Success'])
    except Exception as e:
        logger.error(f"Error running backtest with direct value: {e}")
        csv_writer.writerow(['run_backtest', 'execution_delay (direct)', 'error', '', str(e)])
    
    try:
        # Test with parameter tuple
        engine4 = BacktestEngine()
        result2 = engine4.run_backtest(
            price_data=price_data,
            signal_generator=dummy_signal_generator,
            execution_delay=optimizable_param
        )
        logger.info("Successfully ran backtest with parameter tuple")
        csv_writer.writerow(['run_backtest', 'execution_delay (tuple)', 'tuple', str(optimizable_param), 'Success'])
    except Exception as e:
        logger.error(f"Error running backtest with parameter tuple: {e}")
        csv_writer.writerow(['run_backtest', 'execution_delay (tuple)', 'error', '', str(e)])
        # Print full traceback for detailed error information
        import traceback
        logger.error(f"Detailed traceback: {traceback.format_exc()}")

logger.info(f"Debug information saved to: {csv_path}")
print(f"Debug information saved to: {csv_path}")
