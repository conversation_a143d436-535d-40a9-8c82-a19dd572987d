================================================================================
SCRIPT STARTED: simple_console_test.py
================================================================================

STEP 1: Importing basic modules - (Handled by top-level imports now)
[PASS] Basic modules (sys, os, pathlib) assumed loaded due to top-level imports.

STEP 2: Path setup (Handled by AGGRESSIVE PATH PREPEND at top)
[INFO] sys.path aggressively prepended at script start.

STEP 3: Importing data manipulation modules
[PASS] Data modules imported: pandas, numpy, datetime
  - pandas version: 2.2.3
  - numpy version: 2.2.2

STEP 4: Loading settings from configuration files
  - Successfully imported load_settings from settings_CPS_v4
Loaded settings from: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\settings\settings_parameters_v4.ini
Found 11 parameter sections
Found 55 named lists for parameter references
[PASS] Settings loaded successfully
  - Settings keys:
    - lists
    - core
    - strategy
    - ema_model
    - backtest
    - allocation
    - report
    - performance
    - visualization
    - output
    - system
    - data_params

  - Backtest settings:
    - initial_capital: 1000000
    - commission_rate: 0.001
    - slippage_rate: 0.0005
    - rebalance_freq: {'default': "'monthly'", 'picklist': ['Rebalance_Daily', 'Rebalance_Weekly', 'Rebalance_Monthly', 'Rebalance_Quarterly', 'Rebalance_Yearly']}
    - benchmark_rebalance_freq: {'default': "'yearly'", 'picklist': ['Rebalance_Daily', 'Rebalance_Weekly', 'Rebalance_Monthly', 'Rebalance_Quarterly', 'Rebalance_Yearly']}
    - signal_history: False
    - include_cash: True

STEP 5: Loading real price data from settings
Python version: 3.13.1 (tags/v3.13.1:0671451, Dec  3 2024, 19:06:28) [MSC v.1942 64 bit (AMD64)]
Python paths:
  S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4
  S:\Dropbox\Scott Only Internal\Quant_Python_24\Custom Function Library
  S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\tests
  S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template
  C:\Python313\python313.zip
  C:\Python313\DLLs
  C:\Python313\Lib
  C:\Python313
  F:\AI_Library\my_quant_env
  F:\AI_Library\my_quant_env\Lib\site-packages
Loaded settings from: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\settings\settings_parameters_v4.ini
Found 11 parameter sections
Found 55 named lists for parameter references
  - Successfully imported load_price_data
  - Data source: yahoo
  - Tickers: []
  - Date range: 2020-01-01 to 2022-12-31
  - Loading price data...
Loaded settings from: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\settings\settings_parameters_v4.ini
Found 11 parameter sections
Found 55 named lists for parameter references
[TickerData] Mode=Save, file=S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\data\tickerdata_('SPY'_'QQQ'_'IWM'_'GLD'_'TLT')  ; Default ETF list_20200101_20250101.xlsx
[FAIL] ERROR generating price data: cannot import name 'standardize_date' from 'utils.date_utils' (S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\utils\date_utils.py)
Traceback:
Traceback (most recent call last):

  File "S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\tests\simple_console_test.py", line 173, in <module>
    price_data = load_price_data(tickers=tickers, start_date=start_date, end_date=end_date, data_source=data_source)

  File "S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\data\data_loader.py", line 17, in load_price_data
    data = load_data_for_backtest()

  File "S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\engine\data_loader_v4.py", line 137, in load_data_for_backtest
    df = load_ticker_data()

  File "S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\engine\data_loader_v4.py", line 104, in load_ticker_data
    df = get_adjusted_close_data()

  File "S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\engine\data_loader_v4.py", line 62, in get_adjusted_close_data
    from utils.date_utils import (
    ...<2 lines>...
    )

ImportError: cannot import name 'standardize_date' from 'utils.date_utils' (S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\utils\date_utils.py)

Continuing after error.

STEP 6: Importing backtest engine
  Attempting to import BacktestEngine...
Loaded settings from: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\settings\settings_parameters_v4.ini
Found 11 parameter sections
Found 55 named lists for parameter references
Loaded settings from: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\settings\settings_parameters_v4.ini
Found 11 parameter sections
Found 55 named lists for parameter references
Loaded settings from: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\settings\settings_parameters_v4.ini
Found 11 parameter sections
Found 55 named lists for parameter references
Loaded settings from: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\settings\settings_parameters_v4.ini
Found 11 parameter sections
Found 55 named lists for parameter references
[FAIL] ERROR importing BacktestEngine: Missing required CPS v4 parameter in 'ema_model' settings: 'lt_lookback'
Traceback:
Traceback (most recent call last):

  File "S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\models\ema_allocation_model_v4.py", line 47, in <module>
    lt_lookback = int(ema_config.get('long_period', ema_config['lt_lookback']))
                                                    ~~~~~~~~~~^^^^^^^^^^^^^^^

KeyError: 'lt_lookback'


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\tests\simple_console_test.py", line 193, in <module>
    from v4.engine.backtest_v4 import BacktestEngine

  File "S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\engine\backtest_v4.py", line 37, in <module>
    from v4.models.ema_allocation_model_v4 import calculate_ema_metrics

  File "S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\models\ema_allocation_model_v4.py", line 51, in <module>
    raise ValueError(f"Missing required CPS v4 parameter in 'ema_model' settings: {e}")

ValueError: Missing required CPS v4 parameter in 'ema_model' settings: 'lt_lookback'

Continuing after error.

STEP 7: Importing signal generator
  Attempting to import generate_signals...
[PASS] Successfully imported generate_signals

STEP 8: Creating BacktestEngine instance
  Instantiating BacktestEngine...
[FAIL] ERROR creating BacktestEngine instance: name 'BacktestEngine' is not defined
Traceback:
Traceback (most recent call last):

  File "S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\tests\simple_console_test.py", line 217, in <module>
    engine = BacktestEngine()
             ^^^^^^^^^^^^^^

NameError: name 'BacktestEngine' is not defined

Continuing after error.

STEP 9: Testing signal generation
  Generating signals with strategy='equal_weight'...
[FAIL] ERROR generating signals: name 'price_data' is not defined
Traceback:
Traceback (most recent call last):

  File "S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\tests\simple_console_test.py", line 230, in <module>
    signals = generate_signals(price_data, strategy='equal_weight')
                               ^^^^^^^^^^

NameError: name 'price_data' is not defined

Continuing after error.

STEP 10: Running backtest
  Running backtest with parameters:
[FAIL] ERROR running backtest: name 'price_data' is not defined
Traceback:
Traceback (most recent call last):

  File "S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\tests\simple_console_test.py", line 252, in <module>
    flushed_print(f"    - price_data shape: {price_data.shape}")
                                             ^^^^^^^^^^

NameError: name 'price_data' is not defined

Continuing after error.

STEP 11: Checking results structure
  Checking if results is a dictionary...
[FAIL] ERROR checking results: name 'results' is not defined
Traceback:
Traceback (most recent call last):

  File "S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\tests\simple_console_test.py", line 279, in <module>
    if isinstance(results, dict):
                  ^^^^^^^

NameError: name 'results' is not defined

Continuing after error.

STEP 12: Comparing weights_history and signal_history
[FAIL] ERROR comparing histories: name 'results' is not defined
Traceback:
Traceback (most recent call last):

  File "S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\tests\simple_console_test.py", line 309, in <module>
    weights_history = results.get('weights_history')
                      ^^^^^^^

NameError: name 'results' is not defined


================================================================================
TEST COMPLETE

STEP 14: Saving sample CSVs
[FAIL] ERROR saving sample CSVs: name 'results' is not defined

STEP 13: Printing performance metrics
[FAIL] ERROR printing performance metrics: name 'results' is not defined
================================================================================
