@echo off
REM Clean Graphviz installation batch file
SET VENV_PATH=F:\AI_Library\my_quant_env
SET PROJECT_PATH=s:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template

echo Activating virtual environment...
call "%VENV_PATH%\Scripts\activate"

echo Uninstalling any existing graphviz...
pip uninstall graphviz -y

echo Installing graphviz...
pip install graphviz

echo Verifying installation...
python -c "import graphviz; print('Graphviz successfully installed at:', graphviz.__file__)"

echo Installation complete
pause
