# filename: app/gui/parameter_widgets_v4.py
"""
CPS_v4 Parameter System - GUI Parameter Widget Creation

This module provides functions to dynamically create GUI widgets for parameters
defined in the CPS_v4 settings object. It supports various parameter types
and includes options for optimization checkboxes.
"""

import logging
from PySide6.QtWidgets import (
    QWidget,
    QLabel,
    QLineEdit,
    QCheckBox,
    QComboBox,
    QSpinBox,
    QDoubleSpinBox,
    QVBoxLayout,
    QHBoxLayout,
    QGroupBox
)
from PySide6.QtCore import Qt

logger = logging.getLogger(__name__)

# Helper to convert string to appropriate type
def _parse_value(value_str, target_type):
    if target_type == 'int':
        return int(value_str)
    elif target_type == 'float':
        return float(value_str)
    elif target_type == 'bool':
        return value_str.lower() in ['true', '1', 't', 'yes', 'y']
    return value_str # Default to string

def create_parameter_widgets_v4(param_definitions: dict, layout: QVBoxLayout) -> dict:
    """
    Creates GUI widgets for a group of parameters and adds them to the given layout.

    Args:
        param_definitions (dict): A dictionary where keys are parameter names and
                                  values are dictionaries defining the parameter's
                                  attributes (type, default, description, etc.).
                                  Example:
                                  {
                                      'param_name': {
                                          'type': 'numeric' | 'categorical' | 'boolean' | 'string',
                                          'default': 'some_value',
                                          'description': 'Parameter description',
                                          'min_value': 0, (for numeric)
                                          'max_value': 100, (for numeric)
                                          'step': 1, (for numeric)
                                          'choices': ['a', 'b'], (for categorical)
                                          'optimizable': True,
                                          'show_in_gui': True
                                      }, ...
                                  }
        layout (QVBoxLayout): The Qt layout to which the widgets will be added.

    Returns:
        dict: A dictionary where keys are parameter names and values are the
              corresponding Qt input widgets (and optimize checkboxes if applicable).
              This allows easy retrieval of widget values later.
    """
    widgets = {}
    if not param_definitions:
        logger.warning("No parameter definitions provided to create_parameter_widgets_v4.")
        no_params_label = QLabel("No parameters defined for this group.")
        no_params_label.setStyleSheet("font-style: italic; color: gray;")
        layout.addWidget(no_params_label)
        return widgets

    for name, details in param_definitions.items():
        if not details.get('show_in_gui', True):
            logger.debug(f"Parameter '{name}' configured not to show in GUI. Skipping widget creation.")
            continue

        widget_container = QWidget()
        param_layout = QHBoxLayout(widget_container)
        param_layout.setContentsMargins(0, 0, 0, 0)

        description = details.get('description', name)
        label = QLabel(f"{description}:")
        label.setToolTip(details.get('tooltip', description))
        param_layout.addWidget(label)

        param_type = details.get('type', 'string').lower()
        default_value = details.get('default')
        input_widget = None

        if param_type == 'numeric':
            is_float = False
            if isinstance(default_value, float) or \
               isinstance(details.get('step'), float) or \
               isinstance(details.get('min_value'), float) or \
               isinstance(details.get('max_value'), float):
                is_float = True
            
            if is_float:
                input_widget = QDoubleSpinBox()
                if default_value is not None: input_widget.setValue(float(default_value))
                input_widget.setMinimum(float(details.get('min_value', -1e9)))
                input_widget.setMaximum(float(details.get('max_value', 1e9)))
                input_widget.setSingleStep(float(details.get('step', 0.1)))
                input_widget.setDecimals(details.get('decimals', 2))
            else:
                input_widget = QSpinBox()
                if default_value is not None: input_widget.setValue(int(default_value))
                input_widget.setMinimum(int(details.get('min_value', -2147483648)))
                input_widget.setMaximum(int(details.get('max_value', 2147483647)))
                input_widget.setSingleStep(int(details.get('step', 1)))
        elif param_type == 'categorical':
            input_widget = QComboBox()
            choices = details.get('choices', [])
            if isinstance(choices, str): choices = [c.strip() for c in choices.split(',')]
            input_widget.addItems(choices)
            if default_value in choices: input_widget.setCurrentText(str(default_value))
        elif param_type == 'boolean':
            input_widget = QCheckBox()
            if default_value is not None: input_widget.setChecked(bool(_parse_value(str(default_value), 'bool')))
        elif param_type == 'string':
            input_widget = QLineEdit()
            if default_value is not None: input_widget.setText(str(default_value))
        else:
            logger.warning(f"Unsupported parameter type '{param_type}' for '{name}'. Defaulting to QLineEdit.")
            input_widget = QLineEdit()
            if default_value is not None: input_widget.setText(str(default_value))

        if input_widget:
            input_widget.setObjectName(name)
            param_layout.addWidget(input_widget, stretch=1)
            widgets[name] = input_widget
        else:
            logger.error(f"Could not create input widget for parameter '{name}' with type '{param_type}'.")
            continue

        if details.get('optimizable', False):
            optimize_checkbox = QCheckBox("Optimize")
            optimize_checkbox.setObjectName(f"optimize_{name}")
            param_layout.addWidget(optimize_checkbox)
            widgets[f"optimize_{name}"] = optimize_checkbox

        layout.addWidget(widget_container)

    if not widgets and param_definitions:
        all_hidden_label = QLabel("All parameters in this group are configured as hidden from GUI.")
        all_hidden_label.setStyleSheet("font-style: italic; color: gray;")
        layout.addWidget(all_hidden_label)
        
    return widgets

# Note: update_parameters_from_widgets_v4 is intentionally NOT included here.
# Parameter collection from widgets is now the responsibility of the calling GUI component.
