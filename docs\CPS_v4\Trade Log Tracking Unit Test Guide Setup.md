# AI Agent Prompt Guide for Order Execution Testing Phases 1-4

## Overview

This plan guides AI implementation of a unit test framework specifically designed to test portfolio rebalancing execution systems, focusing on mark-to-market calculations, sell-first/buy-second execution logic, allocation accuracy, and trade logging mechanisms.

The allocation history indicates a substantial problem somewhere in the trade accounting process.  

allocation_history_20250622_213451.csv

Trade Accounting:

1 Start with Mark to Market Existing positions value using pre-set prices and shares going into that day, calculating % of total portfolio value by position.  Total must sum to  100% potential for very small, < 1%, cash, to tie to 100%.
2 Compare existing position allocation vs the Signal allocation.
3 If differences greater than 2% over allocated (Example ticker SPY signal 30%, currently holding 50%, then need to sell enough SPY shares to bring it down to 30% of portfolio value.)
4 AFTER all sells are done, update market to market portfolio mix, with value of sells going into cash.
5 All under allocated positons (example ticker PFF should have 20%, currently 0%), the use the cash to buy enough PFF shares to bring it to 20% of portfolio.
6 After all sells and buys are accounted, update and validate that results match to expectations, with variations less than .5% of portfolio value.   



All Trades are simply shares * price, buy or sell.


Start with Mark

Reference:

- currently using run_trading_phase_standalone.bat  to run output

- Output here: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4_trace_outputs
  
  txt contain the terminal output
  csv contain the reporting
  
  System is using pre-stored price and signal files, and just focusing on getting the trade accounting handled properly

Testing Guideline

- Philosophy:** All testing and validation must use the full production code flow. Do not create synthetic test harnesses.
- **Test Location:** Unit tests for `v4/engine/` should be located in `tests/v4/engine/`.
- **Focus:** Tests should verify the data flow and accounting steps between modules, and validate the final CSV outputs.

## Phase 1: Test Environment Setup

### Step 1: Identify and Import Real Modules

1. Locate the actual rebalancing execution module in the codebase

2. Import the portfolio management module containing position tracking

3. Import the market data module for real-time pricing

4. Import the trade execution module handling order placement

5. Import the logging module capturing trade activities

### Step 2: Prepare Test Data Infrastructure

1. Create a test data loader that reads historical market prices

2. Set up sample portfolio positions with real ticker symbols

3. Define target allocation percentages matching production scenarios

4. Prepare historical trade logs for comparison testing

### Step 3: Initialize Test Fixtures

1. Create a base test class inheriting from unittest.TestCase

2. Implement setUp() method to initialize portfolio state

3. Implement tearDown() method to clean up test artifacts

4. Create helper methods for common test operations

## Phase 2: Mark-to-Market Testing

### Step 4: Test Current Value Calculations

1. Load a portfolio with known positions and quantities

2. Apply current market prices to calculate portfolio value

3. Verify individual position values match expected calculations

4. Test total portfolio value aggregation

### Step 5: Test Price Update Mechanisms

1. Verify Using correct price on correct ticker and day

2. Verify portfolio values update correctly

### Step 6: Test Performance Calculations

1. Calculate daily returns based on mark-to-market values

2. Verify return calculations against manual computations

3. Test handling of cash positions in returns

4. Validate benchmark comparison calculations

## Phase 3: Sell-First/Buy-Second Logic Testing

### Step 7: Test Sell Order Generation

1. Create a portfolio requiring rebalancing

2. Verify system identifies overweight positions correctly

3. Test sell order creation for positions above target

4. Validate sell quantities match rebalancing requirements

5. Ensure sells are queued before any buy orders

### Step 8: Test Buy Order Generation

1. Verify buy orders only generate after sells are queued

2. Test identification of underweight positions

3. Validate buy quantities based on available cash
   
   Verify no buys exceed available liquidity

### Step 9: Test Order Sequencing

1. Create scenarios requiring multiple sells and buys

2. Verify strict sell-first ordering in execution queue

3. Validate cash settlement between sells and buys

4. Test error handling if sells fail

## Phase 4: Allocation Accuracy Testing

### Step 10: Test Target Allocation Calculations

1. Define target allocations summing to 100%

2. Test rebalancing calculations for various portfolio sizes

3. Verify handling of allocation constraints (min/max positions)

4. Test rounding logic for share quantities

5. Validate drift tolerance thresholds

### Step 11: Test Post-Trade Allocation Verification

1. Execute a complete rebalancing cycle

2. Calculate actual allocations after all trades

3. Compare actual vs target allocations

4. Test acceptable deviation thresholds

5. 

### Step 12: Test Edge Cases in Allocation

1. Test rebalancing with zero target allocations

2. Handle new position additions

3. Test complete position liquidations

4. 

## Phase 5: Trade Logging Validation

### Step 13: Test Trade Log Generation

1. Execute sample trades through the system

2. Verify all trades appear in logs with correct timestamps

3. Test log entries contain all required fields

4. Validate trade direction (buy/sell) accuracy

5. Ensure quantities and prices match execution

### Step 14: Test Log Data Integrity

1. Compare logged trades against execution confirmations

2. Verify no duplicate entries in logs

3. Validate log format consistency

4. 

### Step 15: Test Allocation Report Generation

1. Generate allocation reports from trade logs

2. Verify report calculations match portfolio state

3. Test historical report regeneration

4. Validate report formatting and completeness

5. Test automated report distribution

## Phase 6: Bug Detection Implementation

### Step 16: Create Anomaly Detection Tests

1. Test for trades exceeding position sizes

2. Identify sells of non-existent positions

3. Detect allocation drift beyond thresholds

4. Flag unusual price movements in executions

5. Identify missing or delayed trade logs

### Step 17: Implement Reconciliation Tests

1. Compare trade logs against broker confirmations

2. Reconcile position quantities with custodian

3. Verify cash balances after trade settlement

4. Test for unmatched or orphaned trades

5. Validate fee and commission calculations

### Step 18: Build Regression Test Suite

1. Capture known bug scenarios as test cases

2. Create tests for previously fixed issues

3. Implement daily automated test runs

4. Generate test coverage reports

5. Set up alerts for test failures

## Phase 7: Integration and Reporting

### Step 19: Create Test Orchestration

1. Build test suite runner for all components

2. Implement parallel test execution

3. Create test dependency management

4. Set up continuous integration hooks

5. Configure test environment isolation

### Step 20: Implement Test Reporting

1. Generate detailed test execution reports

2. Create dashboards for test metrics

3. Track test coverage percentages

4. Document failed test investigations

5. Maintain test case documentation

## Implementation Guidelines

### Data Requirements

* Use production data schemas and formats

* Maintain data privacy and security

* Create realistic test scenarios

* Preserve data relationships

### Error Handling

* Test both success and failure paths

* Validate error messages and codes

* Ensure graceful degradation

* Test recovery mechanisms

### Performance Considerations

* Monitor test execution time

* Optimize slow-running tests

* Use appropriate test data sizes

* Implement test parallelization

### Maintenance Strategy

* Update tests with code changes

* Review and refactor test code

* Maintain test documentation

* Regular test effectiveness reviews

This plan provides a comprehensive approach to building a unit test framework that will effectively validate rebalancing execution systems and help identify bugs in production trade logs and allocation reports.

Come up with a unit test file for somefile.py. Write test stubs ONLY (do not implement code yet until I tell you to do so). List every required test to test every branch of logic in an isolated unit test, in both positive and negative cases, failing cases, edge cases, and error raising. Write enough stubs to achieve 100% coverage on first pass. In each test stub, comment what the test is testing, and how it will be implemented AFTER I tell you to start working. You are only writing test stubs with no logic. Import all logic from my modules. Do not mock anything.