"""
Data download test - forces fresh download with saving
"""
import os
import sys

# Add project root to Python path
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

from v4.engine.data_loader_v4 import load_data_for_backtest
from CPS_v4.settings_CPS_v4 import load_settings

import logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_download():
    """Force download and save of fresh price data"""
    try:
        test_settings = load_settings()
        logger.debug(f"Loaded settings: {test_settings}")
        test_settings['data_params']['data_storage_mode'] = 'Save'
        logger.debug(f"Modified settings: {test_settings}")
        data = load_data_for_backtest(settings_override=test_settings)
        logger.info(f"Successfully downloaded and saved data: {data['price_data'].shape}")
        return True
    except Exception as e:
        logger.error(f"Download failed: {e}", exc_info=True)
        return False

if __name__ == "__main__":
    print("[TEST] Starting forced data download (Save mode)...")
    success = test_download()
    if success:
        print("[TEST] Download completed successfully")
    else:
        print("[TEST] Download failed - check logs")
    input("Press Enter to exit...")
