#!/usr/bin/env python
# verify_report_output.py
"""
Report Output Verification Script
This script verifies that reports meet PRD standards using real market data.
It focuses on actual report output quality without introducing mock data.

This module uses report_validators.py for detailed validation checks.
"""

import os
import sys
import logging
import datetime
import pandas as pd
import json
from pathlib import Path

# Add project root to path
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.insert(0, project_root)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(os.path.join(project_root, 'tests', 'report_output_verification.log')),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('report_output_verification')

# Import project modules (after path setup)
from config import paths
from v3_engine.V3_perf_repadapt_legacybridge import generate_performance_report
from v3_reporting.v3_performance_charts import generate_performance_charts
from v3_reporting.v3_allocation_report import generate_rebalance_report
from tests.report_validators import (
    validate_performance_report,
    validate_allocation_report,
    validate_signal_history_report
)

# Create output directory
OUTPUT_DIR = os.path.join(project_root, 'tests', 'report_verification')
VALIDATION_DIR = os.path.join(OUTPUT_DIR, 'validation_results')
os.makedirs(OUTPUT_DIR, exist_ok=True)
os.makedirs(VALIDATION_DIR, exist_ok=True)

def load_real_market_data():
    """
    Load real market data from project data sources.
    No mock data is used to ensure production readiness.
    """
    logger.info("Loading real market data from project data sources")
    
    # Use the project's data loading functions
    try:
        from data_loader.market_data import load_market_data
        market_data = load_market_data()
        logger.info(f"Successfully loaded market data with {len(market_data)} records")
        return market_data
    except ImportError:
        logger.error("Could not import market data loader module")
        # Fallback to a standard data file in the project
        data_path = os.path.join(project_root, 'data', 'market_data.csv')
        if os.path.exists(data_path):
            logger.info(f"Loading market data from {data_path}")
            return pd.read_csv(data_path, parse_dates=['date'])
        else:
            logger.error(f"No market data found at {data_path}")
            return None

def load_backtest_results():
    """
    Load real backtest results from a previous run.
    This ensures we're testing with real data, not mock data.
    """
    logger.info("Loading real backtest results")
    
    # Try to find recent backtest results
    results_dir = os.path.join(project_root, 'results')
    if not os.path.exists(results_dir):
        logger.error(f"Results directory not found: {results_dir}")
        return None
    
    # Find most recent result file
    result_files = [f for f in os.listdir(results_dir) if f.endswith('.pkl') and 'backtest_results' in f]
    if not result_files:
        logger.error("No backtest result files found")
        return None
    
    # Sort by modification time (most recent first)
    result_files.sort(key=lambda f: os.path.getmtime(os.path.join(results_dir, f)), reverse=True)
    latest_result = os.path.join(results_dir, result_files[0])
    
    logger.info(f"Loading most recent backtest results: {latest_result}")
    try:
        import pickle
        with open(latest_result, 'rb') as f:
            results = pickle.load(f)
        logger.info("Successfully loaded backtest results")
        return results
    except Exception as e:
        logger.error(f"Error loading backtest results: {e}")
        return None

def verify_performance_report():
    """
    Generate and verify performance report using real backtest results.
    Uses detailed validation from report_validators module.
    """
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    logger.info("Verifying performance report generation")
    
    # Load real backtest results
    results = load_backtest_results()
    if not results:
        logger.error("Cannot verify performance report without backtest results")
        return False
    
    # Generate performance report
    try:
        report_path = os.path.join(OUTPUT_DIR, f"performance_report_{timestamp}")
        excel_path = f"{report_path}.xlsx"
        
        # Generate performance report with real data
        generate_performance_report(
            results=results,
            output_path=report_path,
            create_excel=True
        )
        
        # Generate performance charts
        generate_performance_charts(
            results=results,
            output_path=report_path,
            chart_format='png',
            chart_dpi=600
        )
        
        # Verify report was created
        if not os.path.exists(excel_path):
            logger.error(f"Performance report not created: {excel_path}")
            return False
        
        # Collect chart paths for validation
        chart_paths = {
            'equity_curve': f"{report_path}_equity_curve.png",
            'drawdown': f"{report_path}_drawdown.png",
            'monthly_returns': f"{report_path}_monthly_returns.png"
        }
        
        # Perform detailed validation
        logger.info(f"Performing detailed validation on performance report: {excel_path}")
        validation_results = validate_performance_report(excel_path, chart_paths)
        
        # Save validation results
        validation_file = os.path.join(VALIDATION_DIR, f"performance_validation_{timestamp}.json")
        with open(validation_file, 'w') as f:
            json.dump(validation_results, f, indent=2)
        
        logger.info(f"Validation results saved to: {validation_file}")
        
        # Log validation results
        for check, result in validation_results.items():
            if check != 'overall':
                logger.info(f"Validation check '{check}': {'PASSED' if result else 'FAILED'}")
        
        overall_result = validation_results.get('overall', False)
        logger.info(f"Performance report verification: {'PASSED' if overall_result else 'FAILED'}")
        return overall_result
    
    except Exception as e:
        logger.error(f"Error generating performance report: {e}")
        return False

def verify_allocation_report():
    """
    Generate and verify allocation report using real backtest results.
    Uses detailed validation from report_validators module.
    """
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    logger.info("Verifying allocation report generation")
    
    # Load real backtest results
    results = load_backtest_results()
    if not results:
        logger.error("Cannot verify allocation report without backtest results")
        return False
    
    try:
        # Generate allocation report
        report_path = os.path.join(OUTPUT_DIR, f"allocation_report_{timestamp}")
        excel_path = f"{report_path}.xlsx"
        
        # Generate allocation report with real data
        generate_rebalance_report(
            backtest_results=results,
            output_path=report_path,
            create_excel=True,
            create_charts=True,
            chart_format='png',
            chart_dpi=600
        )
        
        # Verify report was created
        if not os.path.exists(excel_path):
            logger.error(f"Allocation report not created: {excel_path}")
            return False
        
        # Collect chart paths for validation
        chart_paths = {
            'allocation_pie': f"{report_path}_allocation_pie.png",
            'allocation_history': f"{report_path}_allocation_history.png"
        }
        
        # Perform detailed validation
        logger.info(f"Performing detailed validation on allocation report: {excel_path}")
        validation_results = validate_allocation_report(excel_path, chart_paths)
        
        # Save validation results
        validation_file = os.path.join(VALIDATION_DIR, f"allocation_validation_{timestamp}.json")
        with open(validation_file, 'w') as f:
            json.dump(validation_results, f, indent=2)
        
        logger.info(f"Validation results saved to: {validation_file}")
        
        # Log validation results
        for check, result in validation_results.items():
            if check != 'overall':
                logger.info(f"Validation check '{check}': {'PASSED' if result else 'FAILED'}")
        
        overall_result = validation_results.get('overall', False)
        logger.info(f"Allocation report verification: {'PASSED' if overall_result else 'FAILED'}")
        return overall_result
    
    except Exception as e:
        logger.error(f"Error generating allocation report: {e}")
        return False

def verify_signal_history_report():
    """
    Generate and verify signal history report using real backtest results.
    Uses detailed validation from report_validators module.
    """
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    logger.info("Verifying signal history report generation")
    
    # Load real backtest results
    results = load_backtest_results()
    if not results or 'signal_history' not in results:
        logger.error("Cannot verify signal history report without signal history")
        return False
    
    # Check if signal history report generator exists
    try:
        from v3_reporting.v3_signal_history_report import generate_signal_history_report
        
        # Generate signal history report
        report_path = os.path.join(OUTPUT_DIR, f"signal_history_report_{timestamp}")
        excel_path = f"{report_path}.xlsx"
        
        # Generate signal history report with real data
        generate_signal_history_report(
            signal_history=results['signal_history'],
            output_path=report_path,
            create_charts=True,
            chart_format='png',
            chart_dpi=600
        )
        
        # Verify report was created
        if not os.path.exists(excel_path):
            logger.error(f"Signal history report not created: {excel_path}")
            return False
        
        # Collect chart paths for validation
        chart_paths = {
            'signal_history': f"{report_path}_signal_history.png",
            'signal_distribution': f"{report_path}_signal_distribution.png"
        }
        
        # Perform detailed validation
        logger.info(f"Performing detailed validation on signal history report: {excel_path}")
        validation_results = validate_signal_history_report(excel_path, chart_paths)
        
        # Save validation results
        validation_file = os.path.join(VALIDATION_DIR, f"signal_history_validation_{timestamp}.json")
        with open(validation_file, 'w') as f:
            json.dump(validation_results, f, indent=2)
        
        logger.info(f"Validation results saved to: {validation_file}")
        
        # Log validation results
        for check, result in validation_results.items():
            if check != 'overall':
                logger.info(f"Validation check '{check}': {'PASSED' if result else 'FAILED'}")
        
        overall_result = validation_results.get('overall', False)
        logger.info(f"Signal history report verification: {'PASSED' if overall_result else 'FAILED'}")
        return overall_result
    
    except ImportError:
        logger.warning("Signal history report generator not found - skipping test")
        return True  # Not a failure if module doesn't exist
    except Exception as e:
        logger.error(f"Error generating signal history report: {e}")
        return False

def run_verification():
    """Run all report verification tests"""
    logger.info("Starting report output verification")
    
    # Create summary report
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    summary_path = os.path.join(OUTPUT_DIR, f"verification_summary_{timestamp}.txt")
    html_summary_path = os.path.join(OUTPUT_DIR, f"verification_summary_{timestamp}.html")
    
    # Run verification tests
    performance_result = verify_performance_report()
    allocation_result = verify_allocation_report()
    signal_result = verify_signal_history_report()
    
    # Collect validation results
    validation_files = {}
    for report_type in ['performance', 'allocation', 'signal_history']:
        files = [f for f in os.listdir(VALIDATION_DIR) if f.startswith(f"{report_type}_validation_") and f.endswith('.json')]
        if files:
            # Get the most recent file
            files.sort(reverse=True)
            validation_files[report_type] = os.path.join(VALIDATION_DIR, files[0])
    
    # Load validation details
    validation_details = {}
    for report_type, file_path in validation_files.items():
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r') as f:
                    validation_details[report_type] = json.load(f)
            except Exception as e:
                logger.error(f"Error loading validation details: {e}")
    
    # Write text summary report
    with open(summary_path, 'w') as f:
        f.write("Report Output Verification Summary\n")
        f.write("="*50 + "\n")
        f.write(f"Timestamp: {timestamp}\n\n")
        
        f.write(f"Performance Report: {'PASSED' if performance_result else 'FAILED'}\n")
        f.write(f"Allocation Report: {'PASSED' if allocation_result else 'FAILED'}\n")
        f.write(f"Signal History Report: {'PASSED' if signal_result else 'FAILED'}\n")
        
        overall_result = performance_result and allocation_result and signal_result
        f.write(f"\nOverall Result: {'PASSED' if overall_result else 'FAILED'}\n\n")
        
        # Add validation details
        f.write("Detailed Validation Results:\n")
        f.write("-"*50 + "\n")
        
        for report_type, details in validation_details.items():
            f.write(f"\n{report_type.upper()} REPORT VALIDATION:\n")
            for check, result in details.items():
                if check != 'overall':
                    f.write(f"  - {check}: {'PASSED' if result else 'FAILED'}\n")
    
    # Write HTML summary report for better visualization
    with open(html_summary_path, 'w') as f:
        f.write("<!DOCTYPE html>\n<html>\n<head>\n")
        f.write("<title>Report Verification Summary</title>\n")
        f.write("<style>\n")
        f.write("body { font-family: Arial, sans-serif; margin: 20px; }\n")
        f.write("h1 { color: #2c3e50; }\n")
        f.write("h2 { color: #3498db; margin-top: 20px; }\n")
        f.write(".passed { color: green; font-weight: bold; }\n")
        f.write(".failed { color: red; font-weight: bold; }\n")
        f.write("table { border-collapse: collapse; width: 100%; margin-top: 20px; }\n")
        f.write("th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }\n")
        f.write("th { background-color: #f2f2f2; }\n")
        f.write("tr:nth-child(even) { background-color: #f9f9f9; }\n")
        f.write("</style>\n</head>\n<body>\n")
        
        f.write(f"<h1>Report Verification Summary</h1>\n")
        f.write(f"<p><strong>Timestamp:</strong> {timestamp}</p>\n")
        
        # Overall results table
        f.write("<h2>Overall Results</h2>\n")
        f.write("<table>\n")
        f.write("<tr><th>Report Type</th><th>Status</th></tr>\n")
        f.write(f"<tr><td>Performance Report</td><td class=\"{'passed' if performance_result else 'failed'}\">{'PASSED' if performance_result else 'FAILED'}</td></tr>\n")
        f.write(f"<tr><td>Allocation Report</td><td class=\"{'passed' if allocation_result else 'failed'}\">{'PASSED' if allocation_result else 'FAILED'}</td></tr>\n")
        f.write(f"<tr><td>Signal History Report</td><td class=\"{'passed' if signal_result else 'failed'}\">{'PASSED' if signal_result else 'FAILED'}</td></tr>\n")
        f.write(f"<tr><th>Overall Result</th><th class=\"{'passed' if overall_result else 'failed'}\">{'PASSED' if overall_result else 'FAILED'}</th></tr>\n")
        f.write("</table>\n")
        
        # Detailed validation results
        f.write("<h2>Detailed Validation Results</h2>\n")
        
        for report_type, details in validation_details.items():
            f.write(f"<h3>{report_type.title()} Report</h3>\n")
            f.write("<table>\n")
            f.write("<tr><th>Check</th><th>Status</th></tr>\n")
            
            for check, result in sorted(details.items()):
                if check != 'overall':
                    f.write(f"<tr><td>{check}</td><td class=\"{'passed' if result else 'failed'}\">{'PASSED' if result else 'FAILED'}</td></tr>\n")
            
            f.write("</table>\n")
        
        f.write("</body>\n</html>")
    
    logger.info(f"Verification summary written to {summary_path}")
    logger.info(f"HTML verification summary written to {html_summary_path}")
    return overall_result

if __name__ == "__main__":
    success = run_verification()
    sys.exit(0 if success else 1)
