@echo off
REM =============================================
REM Install pywin32 in Virtual Environment
REM =============================================

REM Change to project directory
cd /d %~dp0

REM Activate virtual environment
echo Activating Python environment: F:\AI_Library\my_quant_env
call F:\AI_Library\my_quant_env\Scripts\activate.bat

REM Install pywin32
echo Installing pywin32...
pip install pywin32

REM Verify installation
echo Verifying installation...
python -c "import win32api; print('win32api available:', win32api.__file__)"

pause
