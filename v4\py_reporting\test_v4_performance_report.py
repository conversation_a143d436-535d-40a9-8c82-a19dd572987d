#!/usr/bin/env python
# -*- coding: utf-8 -*-
# test_v4_performance_report.py
"""
Test script for v4_performance_report.py

This script provides simple, isolated tests for the v4_performance_report module
following the simplified testing strategy outlined in CPS_V4_Master_Plan.md.

Author: AI Assistant
Date: 2025-06-11
"""

import os
import sys
import datetime
import logging
import traceback
from pathlib import Path
import pandas as pd
import numpy as np

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Add the project root to the path
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# Create test output directory if it doesn't exist
test_output_dir = Path(project_root) / 'v4_reporting' / 'test_output'
test_output_dir.mkdir(exist_ok=True, parents=True)

def generate_test_data():
    """Generate synthetic backtest data for testing"""
    # Create date range
    start_date = datetime.date(2023, 1, 1)
    dates = [start_date + datetime.timedelta(days=i) for i in range(252)]  # One year of trading days
    
    # Generate portfolio values with some randomness
    initial_value = 100000
    daily_returns = np.random.normal(0.0005, 0.01, len(dates))  # Mean 0.05% daily, 1% std
    portfolio_values = [initial_value]
    
    for ret in daily_returns:
        portfolio_values.append(portfolio_values[-1] * (1 + ret))
    
    # Generate benchmark values (slightly worse performance)
    benchmark_returns = np.random.normal(0.0003, 0.009, len(dates))  # Mean 0.03% daily, 0.9% std
    benchmark_values = [initial_value]
    
    for ret in benchmark_returns:
        benchmark_values.append(benchmark_values[-1] * (1 + ret))
    
    # Generate some sample trades
    trades = [
        {
            'date': dates[20].strftime('%Y-%m-%d'),
            'ticker': 'AAPL',
            'action': 'BUY',
            'quantity': 100,
            'price': 150.25,
            'total': 15025.00
        },
        {
            'date': dates[50].strftime('%Y-%m-%d'),
            'ticker': 'MSFT',
            'action': 'BUY',
            'quantity': 50,
            'price': 250.75,
            'total': 12537.50
        },
        {
            'date': dates[100].strftime('%Y-%m-%d'),
            'ticker': 'AAPL',
            'action': 'SELL',
            'quantity': 100,
            'price': 165.50,
            'total': 16550.00
        }
    ]
    
    # Generate signal history (required for report generation)
    # This should be a dictionary with dates as keys and allocation signals as values
    tickers = ['AAPL', 'MSFT', 'GOOG', 'AMZN', 'CASH']
    signal_history = {}
    
    for i, d in enumerate(dates):
        date_str = d.strftime('%Y-%m-%d')
        
        # Create random allocations that sum to 1.0
        if i % 30 == 0:  # Change allocations monthly for test data
            raw_allocations = np.random.random(len(tickers))
            allocations = raw_allocations / raw_allocations.sum()
        
        # Store as dictionary for each date
        signal_dict = {ticker: alloc for ticker, alloc in zip(tickers, allocations)}
        signal_history[date_str] = signal_dict
    
    # Generate allocation history (weights_history) with a 1-day lag from signals
    # This represents actual executed allocations
    weights_history = {}
    
    for i, d in enumerate(dates):
        if i == 0:
            # First day has no allocation yet
            weights_history[d.strftime('%Y-%m-%d')] = {ticker: 0.0 for ticker in tickers}
            weights_history[d.strftime('%Y-%m-%d')]['CASH'] = 1.0  # Start with all cash
        else:
            # Use previous day's signal with slight variation to simulate execution differences
            prev_date = dates[i-1].strftime('%Y-%m-%d')
            prev_signal = signal_history[prev_date]
            
            # Add small random variation (±1%) to simulate execution differences
            weights = {}
            for ticker, alloc in prev_signal.items():
                variation = np.random.normal(0, 0.01)  # ±1% variation
                weights[ticker] = max(0, alloc + variation)  # Ensure non-negative
            
            # Normalize to ensure weights sum to 1.0
            total_weight = sum(weights.values())
            weights = {ticker: weight/total_weight for ticker, weight in weights.items()}
            
            weights_history[d.strftime('%Y-%m-%d')] = weights
    
    # Create backtest results dictionary with all required components
    backtest_results = {
        'dates': [d.strftime('%Y-%m-%d') for d in dates],
        'portfolio_values': portfolio_values[1:],  # Remove initial value
        'benchmark_values': benchmark_values[1:],  # Remove initial value
        'trades': trades,
        'signal_history': signal_history,  # Add signal history
        'weights_history': weights_history,  # Add weights history (allocation history)
        'initial_capital': initial_value,
        'final_value': portfolio_values[-1],
        'total_return': (portfolio_values[-1] / initial_value) - 1,
        'performance': {
            'cagr': 0.12,  # Example values for testing
            'volatility': 0.15,
            'sharpe': 0.8,
            'max_drawdown': 0.12,
            'turnover': 1.5,
            'win_rate': 0.6
        }
    }
    
    # Save raw data files for verification
    test_raw_dir = Path('v4_reporting/test_output/test_raw_data')
    test_raw_dir.mkdir(exist_ok=True, parents=True)
    
    # Save signal_history.txt with proper formatting (no NumPy types)
    with open(test_raw_dir / 'signal_history.txt', 'w') as f:
        f.write(f"Signal History Data\n")
        for date, signals in signal_history.items():
            # Format each ticker's allocation as a percentage
            formatted_signals = {ticker: f"{float(alloc):.2%}" for ticker, alloc in signals.items()}
            f.write(f"{date}: {formatted_signals}\n")
    
    # Save signal_history.csv (tabular format for Excel import)
    import pandas as pd
    # Convert signal_history to DataFrame
    signal_df_data = []
    for date, signals in signal_history.items():
        row = {'Date': date}
        row.update({ticker: float(alloc) for ticker, alloc in signals.items()})
        signal_df_data.append(row)
    
    signal_df = pd.DataFrame(signal_df_data)
    signal_df.to_csv(test_raw_dir / 'signal_history.csv', index=False)
    
    # Save allocation_history.txt with proper formatting
    # This should be weights_history in the backtest_results
    with open(test_raw_dir / 'allocation_history.txt', 'w') as f:
        f.write(f"Allocation History Data\n")
        if 'weights_history' in backtest_results:
            for date, weights in backtest_results['weights_history'].items():
                # Format each ticker's allocation as a percentage
                formatted_weights = {ticker: f"{float(weight):.2%}" for ticker, weight in weights.items()}
                f.write(f"{date}: {formatted_weights}\n")
        else:
            f.write("No allocation_history in backtest_results\n")
    
    # Save performance_metrics.txt
    with open(test_raw_dir / 'performance_metrics.txt', 'w') as f:
        f.write(f"Performance Metrics Data\n")
        if 'performance' in backtest_results:
            for metric, value in backtest_results['performance'].items():
                if metric in ['sharpe', 'win_rate', 'turnover']:
                    f.write(f"{metric}: {value:.2f}\n")
                else:  # Percentage metrics
                    f.write(f"{metric}: {value:.2%}\n")
        else:
            f.write("No performance metrics in backtest_results\n")
    
    # Save trade_log.csv
    import csv
    with open(test_raw_dir / 'trade_log.csv', 'w', newline='') as f:
        writer = csv.writer(f)
        writer.writerow(['date', 'ticker', 'action', 'quantity', 'price', 'total'])
        for trade in trades:
            writer.writerow([trade['date'], trade['ticker'], trade['action'], 
                           trade['quantity'], trade['price'], trade['total']])
    
    return backtest_results

def test_parameter_passing():
    """Test that parameters are correctly passed from settings to the reporting module"""
    logger.info("Running parameter passing test...")
    
    try:
        # Import the settings module
        from v4.settings.settings_CPS_v4 import load_settings
        
        # Load settings
        settings = load_settings()
        
        # Check if report section exists
        if 'report' in settings:
            logger.info("✓ Report section found in settings")
            
            # Check for specific reporting parameters
            reporting_params = settings['report']
            required_params = ['create_excel', 'output_directory', 'risk_free_rate']
            
            for param in required_params:
                if param in reporting_params:
                    logger.info(f"✓ Found parameter: {param} = {reporting_params[param]}")
                else:
                    logger.warning(f"✗ Missing parameter: {param}")
        else:
            logger.warning("✗ Reporting section not found in settings")
            
        return True
        
    except Exception as e:
        logger.error(f"✗ Parameter passing test failed: {e}")
        return False

def test_module_initialization():
    """Test that the v4_performance_report module initializes correctly"""
    logger.info("Running module initialization test...")
    
    try:
        # Import the module
        from v4_reporting import v4_performance_report
        
        logger.info("✓ Successfully imported v4_performance_report module")
        return True
        
    except Exception as e:
        logger.error(f"✗ Module initialization test failed: {e}")
        return False

def test_report_generation():
    """Test the performance report generation functionality"""
    logger.info("Running report generation test...")
    
    try:
        # Import the module
        from v4_reporting import v4_performance_report
        
        # Generate test data
        backtest_results = generate_test_data()
        
        try:
            # Print detailed debug information
            print("\n\n==== DEBUG: REPORT GENERATION TEST ====")
            print(f"Test output directory: {test_output_dir}")
            print(f"Backtest results keys: {list(backtest_results.keys())}")
            
            report_path = v4_performance_report.generate_performance_report(
                backtest_results=backtest_results,
                strategy_name="TestStrategy",
                output_dir=str(test_output_dir),
                is_new_file=True,
                debug_mode=True  # Enable debug output for testing
            )
            
            # Verify debug output was created
            debug_dir = test_output_dir / 'debug_output'
            if debug_dir.exists() and any(debug_dir.iterdir()):
                logger.info(f"✓ Debug output found in: {debug_dir}")
                for f in debug_dir.glob('*.*'):
                    logger.debug(f"  - {f.name} ({f.stat().st_size} bytes)")
            else:
                logger.warning(f"✗ No debug output found in: {debug_dir}")
            
            # Verify report was created
            if report_path and os.path.exists(report_path):
                logger.info(f"✓ Successfully generated report: {report_path}")
                return True
            else:
                logger.warning(f"✗ Report generation failed or file not found")
                return False
        except Exception as e:
            print("\n\n==== ERROR: REPORT GENERATION FUNCTION ERROR ====")
            print(f"Error type: {type(e).__name__}")
            print(f"Error message: {str(e)}")
            print("\nFull traceback:")
            traceback.print_exc()
            print("\n==== END ERROR TRACEBACK ====")
            
            logger.error(f"✗ Report generation function error: {e}")
            return False
            
    except Exception as e:
        print("\n\n==== ERROR: REPORT GENERATION TEST FAILED ====")
        print(f"Error type: {type(e).__name__}")
        print(f"Error message: {str(e)}")
        print("\nFull traceback:")
        traceback.print_exc()
        print("\n==== END ERROR TRACEBACK ====")
        
        logger.error(f"✗ Report generation test failed: {e}")
        return False

def test_allocation_history_flow():
    """Test the allocation history data flow from backtest to reporting"""
    logger.info("\n=== TESTING ALLOCATION HISTORY FLOW ===")
    
    try:
        # Import necessary components from backtest engine
        from v4.engine.backtest_v4 import BacktestEngine
        from v4.models.ema_allocation_model_v4 import ema_allocation_model_updated
        from v4.engine.data_loader_v4 import load_price_data
        from CPS_v4.settings_CPS_v4 import load_settings
        import v4_reporting.v4_performance_report as report
        
        # Create logging directory for allocation history debug info
        debug_dir = test_output_dir / "allocation_debug"
        debug_dir.mkdir(exist_ok=True)
        
        # Log function to record state at different points
        def debug_log(stage, data, filename=None):
            if filename is None:
                filename = f"{stage}.txt"
            file_path = debug_dir / filename
            with open(file_path, "w") as f:
                f.write(f"=== {stage} ===\n\n")
                if isinstance(data, dict):
                    for key, value in data.items():
                        f.write(f"{key}: {value}\n")
                elif hasattr(data, "__dict__"):
                    for key, value in data.__dict__.items():
                        if key.startswith("_"):
                            continue
                        f.write(f"{key}: {value}\n")
                else:
                    f.write(str(data))
            return file_path
            
        logger.info("Loading settings and data...")
        settings = load_settings()
        
        # Load real price data
        price_data = load_price_data()
        if price_data is None or price_data.empty:
            logger.error("Failed to load price data")
            return False
        
        logger.info(f"Loaded price data with shape {price_data.shape}")
        
        # Create backtest engine with real data
        backtest_engine = BacktestEngine()
        
        # Run backtest with actual signal generator
        logger.info("Running backtest with EMA allocation model...")
        backtest_results = backtest_engine.run_backtest(
            price_data=price_data, 
            signal_generator=ema_allocation_model_updated
        )
        
        # Check and log allocation data at different points
        logger.info("Checking allocation history data flow...")
        
        # Step 1: Check if weights_history exists in backtest results
        weights_history = backtest_results.get('weights_history')
        if weights_history is None:
            logger.error("❌ weights_history is missing from backtest results")
            debug_log("backtest_results_keys", backtest_results.keys())
            return False
        
        debug_log("weights_history_content", weights_history)
        logger.info(f"✓ weights_history exists in backtest results with {len(weights_history)} entries")
        
        # Step 2: Check if signal_history exists and its structure
        signal_history = backtest_results.get('signal_history')
        if signal_history is None:
            logger.error("❌ signal_history is missing from backtest results")
            return False
            
        debug_log("signal_history_content", signal_history)
        logger.info(f"✓ signal_history exists with {len(signal_history)} entries")
        
        # Step 3: Check if position_history exists and its structure
        position_history = backtest_results.get('position_history')
        if position_history is None:
            logger.error("❌ position_history is missing from backtest results")
            return False
            
        debug_log("position_history_content", position_history)
        logger.info(f"✓ position_history exists")
        
        # Step 4: Check if the reporting function can process the allocation history
        logger.info("Testing allocation history export...")
        
        validation_dir = test_output_dir / "validation_data"
        validation_dir.mkdir(exist_ok=True, parents=True)
        
        try:
            # Extract only what we need for testing allocation history
            allocation_test_data = {
                'weights_history': weights_history
            }
            
            # Use the internal export function directly to test allocation handling
            if hasattr(report, '_export_allocation_history'):
                allocation_path = report._export_allocation_history(
                    weights_history, validation_dir
                )
                logger.info(f"✓ Successfully exported allocation history to {allocation_path}")
            else:
                logger.warning("❌ _export_allocation_history function not found")
                
                # Fallback to full export function
                report.export_validation_files(
                    backtest_results, str(validation_dir)
                )
                logger.info("✓ Successfully ran full export_validation_files")
                
            # Check if files were created
            allocation_csv = validation_dir / "allocation_history.csv"
            allocation_txt = validation_dir / "allocation_history.txt"
            
            if allocation_csv.exists():
                logger.info(f"✓ Allocation CSV created: {allocation_csv}")
            else:
                logger.error("❌ Allocation CSV not created")
                
            if allocation_txt.exists():
                logger.info(f"✓ Allocation TXT created: {allocation_txt}")
            else:
                logger.error("❌ Allocation TXT not created")
                
            return True
            
        except Exception as e:
            logger.error(f"❌ Error exporting allocation history: {e}")
            traceback.print_exc()
            return False
            
    except Exception as e:
        logger.error(f"❌ Allocation history test failed: {e}")
        traceback.print_exc()
        return False

def run_all_tests():
    """Run all tests and report results"""
    logger.info("Starting v4_performance_report tests...")
    
    # Set up error log file
    error_log_path = test_output_dir / "error_log.txt"
    with open(error_log_path, "w") as error_file:
        error_file.write(f"V4 Performance Report Error Log - {datetime.datetime.now()}\n\n")
    
    # Run tests with detailed error capture
    results = {}
    
    # Add the allocation history flow test as the primary test
    try:
        results["allocation_history_flow"] = test_allocation_history_flow()
    except Exception as e:
        with open(error_log_path, "a") as error_file:
            error_file.write(f"\n\nALLOCATION HISTORY FLOW TEST ERROR:\n{str(e)}\n\n")
            error_file.write(traceback.format_exc())
        results["allocation_history_flow"] = False
    
    try:
        results["parameter_passing"] = test_parameter_passing()
    except Exception as e:
        with open(error_log_path, "a") as error_file:
            error_file.write(f"\n\nPARAMETER PASSING TEST ERROR:\n{str(e)}\n\n")
            error_file.write(traceback.format_exc())
        results["parameter_passing"] = False
    
    try:
        results["module_initialization"] = test_module_initialization()
    except Exception as e:
        with open(error_log_path, "a") as error_file:
            error_file.write(f"\n\nMODULE INITIALIZATION TEST ERROR:\n{str(e)}\n\n")
            error_file.write(traceback.format_exc())
        results["module_initialization"] = False
    
    try:
        results["report_generation"] = test_report_generation()
    except Exception as e:
        with open(error_log_path, "a") as error_file:
            error_file.write(f"\n\nREPORT GENERATION TEST ERROR:\n{str(e)}\n\n")
            error_file.write(traceback.format_exc())
        results["report_generation"] = False
    
    # Print summary
    logger.info("\n--- Test Results Summary ---")
    all_passed = True
    
    for test_name, passed in results.items():
        status = "PASSED" if passed else "FAILED"
        logger.info(f"{test_name}: {status}")
        all_passed = all_passed and passed
    
    logger.info(f"\nOverall status: {'PASSED' if all_passed else 'FAILED'}")
    logger.info(f"Detailed error log: {error_log_path}")
    
    # Write results to file
    with open(test_output_dir / "test_results.txt", "w") as f:
        f.write("V4 Performance Report Test Results\n")
        # Alternative approach: Format the date outside the f-string
        current_time = datetime.datetime.now()
        formatted_date = current_time.strftime('%Y-%m-%d %H:%M:%S')
        f.write(f"Date: {formatted_date}\n\n")
        
        for test_name, passed in results.items():
            status = "PASSED" if passed else "FAILED"
            f.write(f"{test_name}: {status}\n")
        
        f.write(f"\nOverall status: {'PASSED' if all_passed else 'FAILED'}")
    
    return all_passed

if __name__ == "__main__":
    run_all_tests()
