@echo on

REM =====================================================
REM COMMON VARIABLES
REM =====================================================
set "TESTS_DIR=S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\tests"
set "PYTHON_EXEC=python"  REM Uses the python from activated venv

echo =====================================================
echo CONFIG READER TEST - %date% %time%
echo =====================================================

REM Set virtual environment path
set VENV_PATH=F:\AI_Library\my_quant_env

REM Activate virtual environment
echo.
echo Activating virtual environment at %VENV_PATH%...
call %VENV_PATH%\Scripts\activate.bat
if %errorlevel% neq 0 (
    echo ERROR: Failed to activate virtual environment! Error code: %errorlevel%
    goto :error
)
echo Virtual environment activated.

echo.
echo --- Python Environment Diagnostics (within batch) ---
echo Current Directory: %CD%
echo.
echo Checking Python interpreter and version:
where python
python --version
echo.
echo Attempting direct Python print test from batch:
python -u -c "import sys; sys.stdout.write('BATCH_DIRECT_PYTHON_PRINT_SUCCESSFUL\n'); sys.stdout.flush(); print('BATCH_STANDARD_PRINT_SUCCESSFUL')"
echo Exit code from direct Python print test: %errorlevel%
echo --- End Python Environment Diagnostics ---

echo.
echo ------------------------------------------------------
echo Preparing to run simple_file_logger_test.py script...
echo ------------------------------------------------------
echo.

echo.
echo +++++++++++++++++++++++++++++++++++++++++++++++++++++++
echo + STEP A: Running ULTRA MINIMAL FILE WRITE TEST       +
echo +++++++++++++++++++++++++++++++++++++++++++++++++++++++
echo.

"%PYTHON_EXEC%" "%TESTS_DIR%\ultra_minimal_file_write.py"
set ULTRA_MINIMAL_EXIT_CODE=%errorlevel%
echo.
echo ULTRA MINIMAL FILE WRITE TEST exited with code: %ULTRA_MINIMAL_EXIT_CODE%
echo Please check for a file named 'ultra_minimal_write_test_YYYYMMDD_HHMMSS_ffffff.txt'
_OR_ 'ultra_minimal_write_ERROR_YYYYMMDD_HHMMSS_ffffff.txt' in the tests directory.
echo.

if %ULTRA_MINIMAL_EXIT_CODE% neq 0 (
    echo ✗ ULTRA MINIMAL FILE WRITE TEST FAILED. Halting further tests.
    goto :end_of_tests
) else (
    echo ✓ ULTRA MINIMAL FILE WRITE TEST SUCCEEDED (or at least ran without error code).
)

echo.
echo +++++++++++++++++++++++++++++++++++++++++++++++++++++++
echo + STEP B: Preparing for simple_file_logger_test.py    +
echo +++++++++++++++++++++++++++++++++++++++++++++++++++++++
echo.

REM Delete old log file if it exists to ensure a fresh log for this run
set "LOG_FILE_PATH=%TESTS_DIR%\simple_file_logger_test.log"
if exist "%LOG_FILE_PATH%" (
    echo Deleting existing log file: %LOG_FILE_PATH%
    del "%LOG_FILE_PATH%"
)

echo.
echo ------------------------------------------------------
echo Running simple_file_logger_test.py script...
echo (Output will be in %LOG_FILE_PATH%)
echo ------------------------------------------------------
echo.

REM Run the Python script
"%PYTHON_EXEC%" "%TESTS_DIR%\simple_file_logger_test.py"

:end_of_tests

echo.
echo ------------------------------------------------------
echo Python script execution finished. Exit code: %errorlevel%
echo ------------------------------------------------------

goto :end

:error
echo.
echo !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
echo      ERROR: TEST SCRIPT EXECUTION FAILED!
echo !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
echo.

:end
echo.
echo Press any key to close this window...
echo.
 echo Press any key to close this window...
 pause
