@echo on
REM ==============================================================================
REM super_simple_data_download.bat - Minimal test to download and verify price data
REM ==============================================================================

echo Activating virtual environment...
call "F:\AI_Library\my_quant_env\Scripts\activate.bat"
if %errorlevel% neq 0 (
    echo ERROR: Failed to activate virtual environment
    pause
    goto :eof
)

echo.
echo Downloading data and verifying...
python "%~dp0super_simple_data_download.py"
echo Exit code: %errorlevel%

echo.
echo Done. Press any key to view CSV or exit.
pause
