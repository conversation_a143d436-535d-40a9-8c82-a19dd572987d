#!/usr/bin/env python3
"""
Test script to validate PerformanceTableGenerator fixes
"""

import sys
import os
from pathlib import Path

# Add CPS_V4 to path
sys.path.insert(0, str(Path(__file__).parent))

from v4.py_reporting.performance_table_generator import PerformanceTableGenerator
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

print("="*60)
print("Testing PerformanceTableGenerator Fixes")
print("="*60)

try:
    print("1. Testing generator initialization...")
    generator = PerformanceTableGenerator()
    print("✅ Generator initialized successfully")
    
    print("\n2. Testing optimizable parameters extraction...")
    params = generator._get_optimizable_parameters()
    print(f"✅ Found {len(params)} optimizable parameters")
    for key, value in list(params.items())[:5]:  # Show first 5
        print(f"   {key}: {value}")
    
    print("\n3. Testing data file loading...")
    try:
        signals, allocations, trades = generator._load_data_files()
        print(f"✅ Data loaded successfully:")
        print(f"   Signals: {signals.shape}")
        print(f"   Allocations: {allocations.shape}")
        print(f"   Trades: {trades.shape}")
    except Exception as e:
        print(f"❌ Data loading failed: {e}")
        
    print("\n4. Testing full report generation...")
    try:
        filepath = generator.generate_performance_table()
        print(f"✅ Report generated: {filepath}")
    except Exception as e:
        print(f"❌ Report generation failed: {e}")
        
except Exception as e:
    print(f"❌ Generator initialization failed: {e}")
    import traceback
    traceback.print_exc()

print("\n" + "="*60)
print("Test complete")
print("="*60)
