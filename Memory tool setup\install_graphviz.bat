@echo off
:: Check for admin rights
net session >nul 2>&1
if %errorLevel% == 0 (
    echo Running with admin privileges
) else (
    echo This script requires administrator privileges.
    echo Please right-click and select "Run as administrator"
    pause
    exit /b 1
)

echo Downloading Graphviz installer...
powershell -Command "& {Invoke-WebRequest -Uri 'https://gitlab.com/api/v4/projects/42072381/packages/generic/graphviz-releases/10.0.1/windows_10_msbuild_Release_graphviz-10.0.1-win32.exe' -OutFile 'graphviz_installer.exe'}"

if not exist "graphviz_installer.exe" (
    echo Failed to download Graphviz installer
    pause
    exit /b 1
)

echo Installing Graphviz (this may take a few minutes)...
"graphviz_installer.exe" /S /v"/qn /norestart"

:: Wait for installation to complete
timeout /t 30 /nobreak >nul

:: Add Graphviz to system PATH
setx /M PATH "C:\\Program Files\\Graphviz\\bin;%PATH%"

echo Installation complete! Please close and reopen any command prompts.
echo.
echo Verifying installation...
where dot
dot -V

echo.
echo If you see the Graphviz version above, installation was successful!
pause
