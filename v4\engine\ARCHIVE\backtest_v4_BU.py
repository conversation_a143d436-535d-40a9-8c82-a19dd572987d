"""
engine/backtest_v4.py
Main backtest engine module (CPS v4 compliant).
Ties together all components for running backtests.
# CPS v4 compliance verified: 2025-06-08

This module uses the Central Parameter System v4 for configuration.
Parameters are loaded directly from the settings_parameters_v4.ini file.

Required parameters:
- backtest.initial_capital: Initial capital for the backtest
- backtest.benchmark_rebalance_freq: Rebalancing frequency for benchmarks
- backtest.commission_rate: Commission rate as percentage
- backtest.slippage_rate: Slippage rate as percentage
"""

import pandas as pd
import numpy as np
from datetime import date, datetime, timedelta
import logging
import sys
import os
from pathlib import Path

# Set pandas options to avoid warnings
pd.set_option('future.no_silent_downcasting', True)

# Try to import debug loggers
try:
    from utils.debug_logger import get_benchmark_debug_logger
except ImportError:
    # Create a dummy logger if import fails
    def get_benchmark_debug_logger(config=None):
        return None
        
# Import allocation debug utilities
try:
    from utils.allocation_debug import export_allocation_data, compare_allocations
except ImportError:
    # Create dummy functions if import fails
    def export_allocation_data(data_dict, stage_name, output_dir="debug_allocations"):
        pass
    def compare_allocations(signal_history, weights_history, output_dir="debug_allocations"):
        pass

# Import local modules
from v4.engine.portfolio_v4 import Portfolio
from v4.engine.orders import Order, Trade, TradeLog
from v4.engine.execution import (
    ExecutionEngine,
    PercentCommissionModel,
    PercentSlippageModel,
)
from v4.engine.allocation import calculate_rebalance_orders
from v4.models.ema_allocation_model_v4 import calculate_ema_metrics

# Configure logging
lvl = os.getenv("BACKTEST_LOG_LEVEL", "INFO").upper()
numeric = getattr(logging, lvl, logging.INFO)
logging.basicConfig(
    level=numeric,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Debug allocation output function
def export_allocation_data(data_dict, stage_name, output_dir="debug_allocations"):
    """Export allocation data for debugging.
    
    Args:
        data_dict (dict): Dictionary of DataFrames to export
        stage_name (str): Name of the allocation stage (e.g., 'signals', 'weights')
        output_dir (str): Directory to save outputs
    """
    # Skip if debug mode not enabled
    if os.getenv("EXPORT_ALLOCATIONS", "").lower() != "true":
        return
        
    # Create output directory if it doesn't exist
    Path(output_dir).mkdir(exist_ok=True)
    
    # Process and export each DataFrame
    for name, data in data_dict.items():
        if data is None:
            logger.warning(f"Cannot export {name} at stage {stage_name}: Data is None")
            continue
            
        if not hasattr(data, 'to_csv'):
            logger.warning(f"Cannot export {name} at stage {stage_name}: Not a DataFrame")
            continue
        
        # Get timestamps
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Create filenames
        csv_file = Path(output_dir) / f"{stage_name}_{name}_{timestamp}.csv"
        txt_file = Path(output_dir) / f"{stage_name}_{name}_{timestamp}.txt"
        
        # Export as CSV for machine processing
        data.to_csv(csv_file)
        
        # Export as readable text for human/AI review
        with open(txt_file, "w") as f:
            f.write(f"=== {stage_name}: {name} ===\n\n")
            f.write(f"Shape: {data.shape}\n")
            f.write(f"Columns: {list(data.columns)}\n")
            f.write(f"Index: {list(data.index)[:5]}... (truncated)\n\n")
            f.write("First 10 rows:\n")
            f.write(str(data.head(10)))
            f.write("\n\nLast 10 rows:\n")
            f.write(str(data.tail(10)))
            
        logger.info(f"Exported {name} at stage {stage_name} to {csv_file} and {txt_file}")


# --- CPS_v4 Integration ---
from v4.settings.settings_CPS_v4 import load_settings
settings = load_settings()
try:
    backtest_params = settings['backtest']
    initial_capital = backtest_params['initial_capital']
    benchmark_rebalance_freq = backtest_params['benchmark_rebalance_freq']
    commission_rate = backtest_params['commission_rate']
    slippage_rate = backtest_params['slippage_rate']
except KeyError as e:
    logger.error(f"Missing required parameter in CPS_v4 settings: {e}")
    raise ValueError(f"Missing required parameter in CPS_v4 settings") from e
# --- END CPS_v4 Integration ---

class BacktestEngine:
    """
    Main backtest engine that orchestrates the simulation.
    """
    def __init__(self, config=None):
        """
        Initialize backtest engine.
        
        Args:
            config (None): Configuration object (currently unused but kept for potential future extension).
        """
        # CPS_v4 parameters are loaded at module level
        # Store configuration
        self.benchmark_rebalance_freq = benchmark_rebalance_freq
        self.config = config
        
        # Store price data for benchmark calculation
        self.price_data = None
        # Create commission and slippage models
        commission_model = PercentCommissionModel(commission_rate)
        slippage_model = PercentSlippageModel(slippage_rate)
        
        # Create execution engine
        self.execution_engine = ExecutionEngine(
            initial_capital=initial_capital,
            commission_model=commission_model,
            slippage_model=slippage_model
        )
        
        # Track dates for rebalancing
        self.last_rebalance_date = None
        
        # Track pending orders for execution delay
        self.pending_orders = {}  # {execution_date: [orders]}
        
        logger.info(f"Initialized backtest engine with ${initial_capital:,.2f} capital")
        logger.info(f"Commission rate: {commission_rate:.2%}")
        logger.info(f"Slippage rate: {slippage_rate:.2%}")
    
    def run_backtest(self, price_data, signal_generator, **signal_params):
        # Get required parameters from CPS_v4
        try:
            rebalance_freq = backtest_params['rebalance_freq']
            execution_delay = backtest_params.get('execution_delay', 0)
        except KeyError as e:
            logger.error(f"Missing required parameter in CPS_v4 settings: {e}")
            raise ValueError(f"Missing required parameter in CPS_v4 settings") from e
        """
        Run a backtest.
        
        Args:
            price_data (DataFrame): Historical price data
            signal_generator (function): Function that generates allocation signals
            **signal_params: Additional parameters for the signal generator
            
        Returns:
            dict: Backtest results
        """
        # Fetch configuration parameters from CPS v4 settings
        back_cfg = settings.get('backtest', {})
        if 'rebalance_freq' not in back_cfg:
            raise ValueError("Missing required setting: backtest.rebalance_freq")
        rebalance_freq = back_cfg['rebalance_freq']
        strat_cfg = settings.get('strategy', {})
        if 'execution_delay' not in strat_cfg:
            raise ValueError("Missing required setting: strategy.execution_delay")
        execution_delay = strat_cfg['execution_delay']
        logger.info(f"Starting backtest with {rebalance_freq} rebalancing")
        logger.info(f"Execution delay: {execution_delay} days")
        
        # Store price data for benchmark calculation
        self.price_data = price_data
        
        # Get portfolio and trade log
        portfolio = self.execution_engine.get_portfolio_state()
        trade_log = self.execution_engine.get_trade_log()
        
        # Convert rebalance frequency to pandas offset using the utility function
        from utils.date_utils import map_rebalance_frequency
        pd_freq = map_rebalance_frequency(rebalance_freq)
        
        # Precompute EMAs for EMA allocation model
        if signal_generator.__name__ == 'ema_allocation_model':
            pre_emas = calculate_ema_metrics(
                price_data,
                signal_params.get('st_lookback', 10),
                signal_params.get('mt_lookback', 50),
                signal_params.get('lt_lookback', 150)
            )
            signal_params['precomputed_emas'] = pre_emas
        
        # Generate rebalance dates
        if rebalance_freq.lower() == 'daily':
            rebalance_dates = price_data.index
        else:
            # Use pandas date_range with the appropriate frequency
            rebalance_dates = pd.date_range(
                start=price_data.index[0],
                end=price_data.index[-1],
                freq=pd_freq
            )
            # Filter to only include dates in the price data
            rebalance_dates = [d.date() if isinstance(d, pd.Timestamp) else d for d in rebalance_dates]
            rebalance_dates = [d for d in rebalance_dates if pd.Timestamp(d) in price_data.index]
        
        # Track signals and actual allocations for reporting
        signal_history = pd.DataFrame(index=price_data.index, columns=price_data.columns)
        weights_history = pd.DataFrame(index=price_data.index, columns=price_data.columns)
        
        # Initialize with zeros instead of NaN for better compatibility with reporting
        signal_history = signal_history.fillna(0.0)
        weights_history = weights_history.fillna(0.0)
        
        logger.info(f"Initialized signal_history with shape {signal_history.shape}")
        logger.info(f"Initialized weights_history with shape {weights_history.shape}")
        
        # Main backtest loop
        for i, current_date_idx in enumerate(price_data.index):
            # Convert timestamp to date if needed
            if isinstance(current_date_idx, pd.Timestamp):
                current_date = current_date_idx.date()
            else:
                current_date = current_date_idx
                
            # Log the historical trading day (only if debug is enabled)
            if logger.level <= logging.DEBUG:
                logger.debug(f"Processing trading day: {current_date} ({i+1}/{len(price_data.index)})")
            
            # Get prices for the current date
            current_prices = {col: price_data.loc[current_date_idx, col] for col in price_data.columns}
            
            # Check for pending orders to execute on this date
            # Ensure current_date is properly compared (handle both date objects and parameter tuples)
            pending_date_match = False
            if isinstance(current_date, (date, datetime, pd.Timestamp)):
                pending_date_match = current_date in self.pending_orders
            
            if pending_date_match:
                pending_orders = self.pending_orders[current_date]
                
                # Execute the pending orders
                logger.debug(f"Executing {len(pending_orders)} pending orders on {current_date} (Trading day {i+1}/{len(price_data.index)})")
                self.execution_engine.execute_orders(pending_orders, current_date, current_prices)
                
                # Update weights_history with actual portfolio positions after trades
                portfolio_positions = portfolio.get_positions()
                portfolio_total_value = portfolio.get_total_value()
                
                if portfolio_total_value > 0:
                    for symbol in weights_history.columns:
                        if symbol in portfolio_positions:
                            position = portfolio_positions[symbol]
                            weight = position['value'] / portfolio_total_value
                            weights_history.loc[current_date_idx, symbol] = weight
                        else:
                            weights_history.loc[current_date_idx, symbol] = 0.0
                
                # Remove the executed orders
                del self.pending_orders[current_date]
            
            # Always mark to market to update portfolio value
            portfolio.mark_to_market(current_date, current_prices)
            
            # CRITICAL FIX: Update weights_history EVERY DAY based on current positions
            # This ensures continuous allocation history data (not just on rebalance days)
            portfolio_positions = portfolio.get_positions()
            portfolio_total_value = portfolio.get_total_value()
            
            if portfolio_total_value > 0:
                for symbol in weights_history.columns:
                    if symbol in portfolio_positions:
                        position = portfolio_positions[symbol]
                        weight = position['value'] / portfolio_total_value
                        weights_history.loc[current_date_idx, symbol] = weight
                    else:
                        weights_history.loc[current_date_idx, symbol] = 0.0
            
            # Debug: Export weights history periodically
            if current_date_idx == price_data.index[0] or i % 20 == 0:  # First day and every 20th day
                export_allocation_data({
                    'weights_history': weights_history
                }, 'daily_weights_update')
            
            # Store daily portfolio values for later reference
            if not hasattr(self, 'portfolio_history'):
                self.portfolio_history = pd.DataFrame(index=price_data.index, columns=['total_value', 'cash_value'])
            self.portfolio_history.loc[current_date_idx, 'total_value'] = portfolio_total_value
            self.portfolio_history.loc[current_date_idx, 'cash_value'] = portfolio.cash
                
            # Check if this is a rebalance date
            is_rebalance_day = current_date in rebalance_dates
            
            if is_rebalance_day:
                logger.debug(f"Rebalance day: {current_date} (Trading day {i+1}/{len(price_data.index)})")
                
                # Check if rebalance is needed
                # Calculate rebalance date
                if is_rebalance_day:
                    # Generate signals
                    logger.debug(f"Generating signals for {current_date}")
                    signals = signal_generator(price_data.loc[:current_date_idx], **signal_params)
                    
                    # Store signals in history
                    for symbol, weight in signals.items():
                        if symbol in signal_history.columns:
                            signal_history.loc[current_date_idx, symbol] = weight
                        else:
                            logger.warning(f"Symbol {symbol} not found in signal_history columns")
                    
                    # Debug: Export signals at generation stage
                    if current_date_idx == price_data.index[0] or i % 20 == 0:  # First day and every 20th day
                        export_allocation_data({
                            'signal_history': signal_history
                        }, 'signal_generation')
                    
                    # Log signal updates
                    logger.debug(f"Updated signal_history for {current_date_idx}")
                    logger.debug(f"Signal values: {signals}")
                    
                    # Calculate orders
                    logger.debug(f"Calculating rebalance orders for {current_date}")
                    orders = calculate_rebalance_orders(portfolio, signals, current_prices)
                    
                    # Schedule orders for execution based on execution delay
                    if orders:
                        # Calculate execution date
                        # Ensure execution_delay is an integer
                        delay_days = int(execution_delay) if isinstance(execution_delay, (int, float)) else 0
                        
                        if delay_days > 0:
                            # Use calendar days for simplicity
                            execution_date = current_date + timedelta(days=delay_days)
                            logger.debug(f"Scheduling orders for execution on {execution_date} (delay: {delay_days} days)")
                        else:
                            # Same-day execution
                            execution_date = current_date
                            logger.debug(f"Executing orders immediately on {execution_date}")
                        
                        # Execute orders
                        self.execution_engine.execute_orders(orders, current_date, current_prices)
                    
                    # Weights are now updated after every mark-to-market anyway (moved to daily update above)
                    # No need to duplicate the code here
                else:
                    # Calculate execution date with delay
                    future_dates = price_data.index[price_data.index > current_date_idx]
                    if len(future_dates) > execution_delay:
                        execution_date_idx = future_dates[execution_delay]
                        if isinstance(execution_date_idx, pd.Timestamp):
                            execution_date = execution_date_idx.date()
                        else:
                            execution_date = execution_date_idx
                            
                        # Store orders for future execution
                        logger.debug(f"Delaying execution to {execution_date} ({execution_delay} days)")
                        
                        if execution_date not in self.pending_orders:
                            self.pending_orders[execution_date] = []
                        
                        self.pending_orders[execution_date].extend(orders)
                    else:
                        logger.warning(f"Not enough future dates for execution delay, skipping rebalance")
                
                # Update last rebalance date
                self.last_rebalance_date = current_date
        
        # Forward fill the weights_history to ensure continuous allocation data
        weights_history = weights_history.ffill()
        
        # Validate and clean up signal and weights history before returning
        # Replace any remaining NaN values with zeros
        signal_history = signal_history.fillna(0.0)
        weights_history = weights_history.fillna(0.0)
        
        # Log final data shapes and sample values
        logger.info(f"Final signal_history shape: {signal_history.shape}")
        logger.info(f"Final weights_history shape: {weights_history.shape}")
        logger.info(f"Signal history sample (first 5 rows):\n{signal_history.head()}")
        logger.info(f"Weights history sample (first 5 rows):\n{weights_history.head()}")
        
        # Check for empty dataframes
        if signal_history.empty:
            logger.error("Signal history is empty")
        if weights_history.empty:
            logger.error("Weights history is empty")
        
        # Return results
        results = {
            'portfolio_values': self.portfolio_history['total_value'],
            'cash_values': self.portfolio_history['cash_value'],
            'signal_history': signal_history,
            'weights_history': weights_history,
            'trade_log': trade_log
        }
        
        return results
    
    def _calculate_results(self, portfolio, trade_log, signal_history, price_data=None, weights_history=None):
        """
        Calculate backtest results.
        
        Args:
            portfolio (Portfolio): Portfolio state
            trade_log (TradeLog): Trade log
            signal_history (DataFrame): Signal history
            price_data (DataFrame): Price data for benchmark calculation
            
        Returns:
            dict: Backtest results
        """
        # Get portfolio history
        portfolio_history = portfolio.get_history()
        
        # Calculate daily returns
        if len(portfolio_history) > 1:
            portfolio_values = pd.Series({d: v['total_value'] for d, v in portfolio_history.items()})
            portfolio_values = portfolio_values.sort_index()
            
            # Calculate returns
            strategy_returns = portfolio_values.pct_change().dropna()
            
            # Calculate performance metrics
            total_return = (portfolio_values.iloc[-1] / portfolio_values.iloc[0]) - 1
            
            # Calculate annualized metrics
            days = (portfolio_values.index[-1] - portfolio_values.index[0]).days
            years = days / 365.25
            
            if years > 0:
                cagr = (1 + total_return) ** (1 / years) - 1
                
                # Volatility (annualized)
                volatility = strategy_returns.std() * np.sqrt(252)
                
                # Sharpe ratio (assuming risk-free rate of 0)
                sharpe = cagr / volatility if volatility > 0 else 0
                
                # Maximum drawdown
                cumulative_returns = (1 + strategy_returns).cumprod()
                running_max = cumulative_returns.cummax()
                drawdown = (cumulative_returns / running_max) - 1
                max_drawdown = drawdown.min()
            else:
                cagr = 0
                volatility = 0
                sharpe = 0
                max_drawdown = 0
        else:
            strategy_returns = pd.Series()
            total_return = 0
            cagr = 0
            volatility = 0
            sharpe = 0
            max_drawdown = 0
        
        # Extract weights history - preserve passed weights_history if available
        if weights_history is None:
            logger.warning("No weights_history provided, using signal_history as fallback")
            weights_history = signal_history.copy()
        else:
            logger.info(f"Using actual executed weights_history with shape {weights_history.shape}")
            
        # Debug: Compare signal history with weights history
        compare_allocations(signal_history, weights_history)
        
        # Debug: Export final allocation data
        export_allocation_data({
            'signal_history': signal_history,
            'weights_history': weights_history
        }, 'final_results')
        
        # Get trade log as dataframe
        trade_df = trade_log.to_dataframe() if trade_log.trades else pd.DataFrame()
        
        # Calculate position history
        position_history = pd.DataFrame()
        if portfolio_history:
            dates = sorted(portfolio_history.keys())
            for date in dates:
                positions = portfolio_history[date]['positions']
                position_data = {symbol: pos['value'] for symbol, pos in positions.items()}
                position_data['Cash'] = portfolio_history[date]['cash']
                position_history = pd.concat([
                    position_history, 
                    pd.DataFrame(position_data, index=[date])
                ])
        
        # Calculate turnover
        turnover = 0
        if not trade_df.empty and 'amount' in trade_df.columns:
            # Sum of absolute trade values divided by average portfolio value
            total_trade_value = trade_df['amount'].abs().sum()
            avg_portfolio_value = portfolio_values.mean() if len(portfolio_values) > 0 else 0
            if avg_portfolio_value > 0:
                turnover = total_trade_value / avg_portfolio_value
        
        # Calculate win rate
        win_rate = 0
        if not trade_df.empty and 'pnl' in trade_df.columns:
            winning_trades = (trade_df['pnl'] > 0).sum()
            total_trades = len(trade_df)
            if total_trades > 0:
                win_rate = winning_trades / total_trades
        
        # Calculate monthly returns - using simple groupby approach
        monthly_returns = pd.Series()
        if not strategy_returns.empty:
            # Group by year and month
            strategy_returns.index = pd.DatetimeIndex(strategy_returns.index)
            monthly_groups = strategy_returns.groupby([strategy_returns.index.year, strategy_returns.index.month])
            
            # Calculate monthly returns
            monthly_returns = monthly_groups.apply(lambda x: (1 + x).prod() - 1)
            
            # Create proper index for monthly returns
            month_ends = []
            for (year, month), _ in monthly_groups:
                # Get all dates in this month
                month_dates = strategy_returns.index[
                    (strategy_returns.index.year == year) & 
                    (strategy_returns.index.month == month)
                ]
                # Take the last date (end of month for market days)
                month_ends.append(month_dates[-1])
            
            monthly_returns.index = month_ends
        
        # Calculate yearly returns - using simple groupby approach
        yearly_returns = pd.Series()
        if not strategy_returns.empty:
            # Group by year
            yearly_groups = strategy_returns.groupby(strategy_returns.index.year)
            
            # Calculate yearly returns
            yearly_returns = yearly_groups.apply(lambda x: (1 + x).prod() - 1)
            
            # Create proper index for yearly returns
            year_ends = []
            for year, _ in yearly_groups:
                # Get all dates in this year
                year_dates = strategy_returns.index[strategy_returns.index.year == year]
                # Take the last date (end of year for market days)
                year_ends.append(year_dates[-1])
            
            yearly_returns.index = year_ends
        
        # Calculate benchmark returns using equal weight with annual rebalancing
        from .benchmark import calculate_equal_weight_benchmark
        
        # Get debug logger
        debug_logger = get_benchmark_debug_logger(self.config)
        
        benchmark_returns = None
        # Use price_data for benchmark calculation if available, otherwise fall back to self.price_data
        price_data_for_benchmark = price_data if price_data is not None else self.price_data
        
        if price_data_for_benchmark is not None and not price_data_for_benchmark.empty:
            # Use the benchmark rebalancing frequency from initialization
            benchmark_rebalance_freq = self.benchmark_rebalance_freq
            
            # Debug logging for price data
            if debug_logger:
                debug_logger.log("Price data info for benchmark calculation:")
                debug_logger.log_dataframe(price_data_for_benchmark, "price_data")
            
            # Calculate returns from price data
            # We use pct_change on the price data
            # Fix deprecated warnings by specifying fill_method=None and handling NA values separately
            returns_data = price_data_for_benchmark.pct_change(fill_method=None)
            
            # Fill NA values with zeros
            returns_data = returns_data.fillna(0)
            
            # Debug logging for returns data
            if debug_logger:
                debug_logger.log("Returns data for benchmark calculation:")
                debug_logger.log_dataframe(returns_data, "returns_data")
            
            # Calculate equal weight benchmark with specified rebalancing frequency
            benchmark_returns = calculate_equal_weight_benchmark(
                asset_returns=returns_data,
                rebalance_freq=benchmark_rebalance_freq,
                config=self.config
            )
        
        # Ensure weights_history reflects execution delay by using trade dates
        # First get all trade dates from the trade log
        if not trade_df.empty and 'execution_date' in trade_df.columns:
            trade_dates = sorted(trade_df['execution_date'].unique())
            
            # For better visualization, we'll create a weights history that properly reflects
            # when trades were actually executed rather than when signals were generated
            executed_weights = pd.DataFrame(index=weights_history.index, columns=weights_history.columns)
            executed_weights.fillna(0, inplace=True)
            
            # Initialize with the first day's weights
            if not weights_history.empty:
                first_valid_day = weights_history.first_valid_index()
                if first_valid_day is not None:
                    executed_weights.loc[first_valid_day] = weights_history.loc[first_valid_day]
            
            # Update weights only on actual trade dates
            for trade_date in trade_dates:
                # Find the corresponding date in the index
                # Handle different date types safely
                try:
                    # Convert to pandas Timestamp for consistent handling
                    trade_datetime = pd.Timestamp(trade_date)
                    # Find closest date in index
                    if trade_datetime in executed_weights.index:
                        executed_weights.loc[trade_datetime] = weights_history.loc[trade_datetime]
                    # If direct match fails, try date string conversion
                    elif str(trade_date) in [str(idx) for idx in executed_weights.index]:
                        # Find matching date by string representation
                        for idx in executed_weights.index:
                            if str(idx).startswith(str(trade_date)):
                                executed_weights.loc[idx] = weights_history.loc[idx]
                                break
                except (TypeError, ValueError) as e:
                    logger.warning(f"Could not process trade date {trade_date}: {e}")
            
            # Forward fill values to show continuous allocation
            executed_weights = executed_weights.ffill()
            
            # Replace weights_history with the executed weights
            weights_history = executed_weights
        
        # --- ROOT CAUSE ANALYSIS: Allocation History ---
        debug_dir = Path("debug_allocation_history")
        debug_dir.mkdir(exist_ok=True)
        debug_file = debug_dir / "weights_history_debug.txt"
        issue_found = False
        try:
            if weights_history is None:
                issue_found = True
                with open(debug_file, "w") as f:
                    f.write("weights_history is None\n")
            elif hasattr(weights_history, 'empty') and weights_history.empty:
                issue_found = True
                with open(debug_file, "w") as f:
                    f.write("weights_history is EMPTY\n")
                    f.write(f"weights_history columns: {getattr(weights_history, 'columns', None)}\n")
                    f.write(f"weights_history index: {getattr(weights_history, 'index', None)}\n")
            elif hasattr(weights_history, 'shape'):
                # Check if all values are zero or NaN
                if (weights_history.fillna(0).values == 0).all():
                    issue_found = True
                    with open(debug_file, "w") as f:
                        f.write("weights_history is ALL ZEROS or NaN\n")
                        f.write(str(weights_history.head(10)))
            # Log a sample for inspection
            with open(debug_file, "a") as f:
                f.write("\nSample weights_history (first 10 rows):\n")
                f.write(str(weights_history.head(10)))
        except Exception as e:
            with open(debug_file, "a") as f:
                f.write(f"Exception during weights_history root cause analysis: {e}\n")
        if issue_found:
            logger.error(f"ROOT CAUSE ANALYSIS: Problem detected in weights_history. See {debug_file}")
        else:
            logger.info("ROOT CAUSE ANALYSIS: weights_history appears valid.")
        # --- END ROOT CAUSE ANALYSIS ---

        return {
            'initial_capital': portfolio.initial_capital,
            'final_value': portfolio_values.iloc[-1] if len(portfolio_values) > 0 else portfolio.initial_capital,
            'total_return': total_return,
            'strategy_returns': strategy_returns,
            'benchmark_returns': benchmark_returns,  # Added benchmark returns
            'weights_history': weights_history,      # Now contains actual allocation based on executed trades
            'position_history': position_history,
            'signal_history': signal_history,        # Contains signals as generated (before execution delay)
            'trade_log': trade_df,
            'performance': {
                'cagr': cagr,
                'volatility': volatility,
                'sharpe': sharpe,
                'max_drawdown': max_drawdown,
                'turnover': turnover,
                'win_rate': win_rate
            },
            'monthly_returns': monthly_returns,
            'yearly_returns': yearly_returns
        }
