@echo off
setlocal

REM Activate virtual environment
call F:\AI_Library\my_quant_env\Scripts\activate.bat

REM Set environment variables
set PYTHONPATH=%~dp0
set BACKTEST_LOG_LEVEL=INFO
set PYTHONUNBUFFERED=1

echo Running settings_loader_v4.py test...
python "v4\config\settings_loader_v4.py"

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo SCRIPT FAILED with error code %ERRORLEVEL%
) else (
    echo.
    echo Script completed successfully.
)

endlocal
pause
