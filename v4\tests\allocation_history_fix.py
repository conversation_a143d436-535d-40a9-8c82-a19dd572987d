#!/usr/bin/env python
# -*- coding: utf-8 -*-
# allocation_history_fix.py
"""
Fix for allocation history preservation in the backtest engine.
This module provides a patched version of the _calculate_results method
that ensures allocation history is properly preserved.

Author: AI Assistant
Date: 2025-06-14
"""

import pandas as pd
import logging
from pathlib import Path
import sys

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Add parent directory to path to import modules
project_root = Path(__file__).parent.parent.parent
if str(project_root) not in sys.path:
    sys.path.append(str(project_root))

def patch_backtest_engine():
    """
    Apply the patch to the BacktestEngine class to fix allocation history preservation.
    """
    from v4.engine.backtest_v4 import BacktestEngine
    
    # Store the original method for reference
    original_calculate_results = BacktestEngine._calculate_results
    
    def patched_calculate_results(self, portfolio, trade_log, signal_history, price_data=None, weights_history=None):
        """
        Patched version of _calculate_results that ensures allocation history is properly preserved.
        
        The key changes are:
        1. Properly tracking weights_history from portfolio snapshots
        2. Ensuring weights_history is not overwritten by signal_history
        3. Maintaining the distinction between target allocations and actual allocations
        """
        print("\n===== PATCHED _calculate_results STARTED =====")
        
        # Get portfolio history
        portfolio_history = portfolio.get_history()
        
        # Extract dates and portfolio values
        dates = sorted(portfolio_history.keys())
        portfolio_values = pd.Series([snapshot['total_value'] for snapshot in 
                                     [portfolio_history[d] for d in dates]], index=dates)
        
        # Get position history from portfolio
        position_history = portfolio.get_position_history()
        
        # Create weights history from portfolio snapshots if not provided
        if weights_history is None:
            print("Creating weights_history from portfolio snapshots")
            weights_history = pd.DataFrame()
            
            # Extract weights from portfolio history
            for date, snapshot in portfolio_history.items():
                # Get weights from portfolio snapshot
                weights = {}
                for symbol, position in snapshot['positions'].items():
                    if snapshot['total_value'] > 0:
                        weights[symbol] = position['value'] / snapshot['total_value']
                
                # Add cash weight
                if snapshot['total_value'] > 0:
                    weights['Cash'] = snapshot['cash'] / snapshot['total_value']
                
                # Add to weights history
                if weights:
                    weights_history.loc[date] = pd.Series(weights)
            
            print(f"Created weights_history with shape: {weights_history.shape}")
        else:
            print(f"Using provided weights_history with shape: {weights_history.shape}")
        
        # Extract trades for performance calculation
        trades = trade_log.get_trades()
        trade_df = trade_log.to_dataframe()
        
        # Calculate returns
        if len(portfolio_values) > 1:
            strategy_returns = portfolio_values.pct_change().dropna()
        else:
            strategy_returns = pd.Series()
        
        # Calculate benchmark returns if price data is provided
        benchmark_returns = pd.Series()
        if price_data is not None:
            # Use equal-weight benchmark
            symbols = price_data.columns
            benchmark_values = []
            
            # Initialize with equal weights
            initial_value = portfolio.initial_capital
            shares = {}
            for symbol in symbols:
                shares[symbol] = (initial_value / len(symbols)) / price_data.iloc[0][symbol]
            
            # Calculate benchmark values
            for i in range(len(price_data)):
                date = price_data.index[i]
                value = sum(shares[symbol] * price_data.iloc[i][symbol] for symbol in symbols)
                benchmark_values.append(value)
            
            benchmark_series = pd.Series(benchmark_values, index=price_data.index)
            benchmark_returns = benchmark_series.pct_change().dropna()
        
        # Calculate performance metrics
        if len(strategy_returns) > 0:
            # CAGR
            total_days = (strategy_returns.index[-1] - strategy_returns.index[0]).days
            if total_days > 0:
                total_return = (portfolio_values.iloc[-1] / portfolio_values.iloc[0]) - 1
                cagr = (1 + total_return) ** (365 / total_days) - 1
            else:
                cagr = 0
                
            # Volatility (annualized)
            volatility = strategy_returns.std() * (252 ** 0.5)
            
            # Sharpe ratio (simplified)
            sharpe = cagr / volatility if volatility > 0 else 0
        else:
            cagr = 0
            volatility = 0
            sharpe = 0
            total_return = 0
        
        # Drawdowns
        if len(strategy_returns) > 0:
            cum_returns = (1 + strategy_returns).cumprod()
            running_max = cum_returns.cummax()
            drawdowns = (cum_returns / running_max) - 1
            max_drawdown = drawdowns.min()
        else:
            max_drawdown = 0
            
        # Turnover
        if len(trade_df) > 0 and len(portfolio_values) > 0:
            total_traded_value = trade_df['value'].sum()
            avg_portfolio_value = portfolio_values.mean()
            turnover = total_traded_value / (2 * avg_portfolio_value)
        else:
            turnover = 0
            
        # Win rate
        if len(trades) > 0:
            wins = sum(1 for trade in trades if trade.profit > 0)
            win_rate = wins / len(trades)
        else:
            win_rate = 0
            
        # Monthly returns
        if len(strategy_returns) > 0:
            monthly_returns = strategy_returns.resample('M').apply(lambda x: (1 + x).prod() - 1)
        else:
            monthly_returns = pd.Series()
            
        # Yearly returns
        if len(strategy_returns) > 0:
            yearly_returns = strategy_returns.resample('Y').apply(lambda x: (1 + x).prod() - 1)
        else:
            yearly_returns = pd.Series()
        
        # Ensure weights_history has the same dates as trades for proper alignment
        if weights_history is not None and len(trades) > 0:
            print(f"Processing weights_history for {len(trades)} trades")
            
            # Forward fill values to show continuous allocation
            weights_history = weights_history.ffill()
            print("Forward-filled weights_history to show continuous allocation")
            
            # Add current weights if not already in weights_history
            current_date = dates[-1] if dates else None
            if current_date and current_date not in weights_history.index:
                current_weights = portfolio.get_weights()
                weights_history.loc[current_date] = current_weights
                print(f"Added current weights to weights_history for date {current_date}")
        
        print("===== PATCHED _calculate_results COMPLETED =====\n")
        
        return {
            'initial_capital': portfolio.initial_capital,
            'final_value': portfolio_values.iloc[-1] if len(portfolio_values) > 0 else portfolio.initial_capital,
            'total_return': total_return,
            'strategy_returns': strategy_returns,
            'benchmark_returns': benchmark_returns,
            'weights_history': weights_history,      # Now contains actual allocation based on portfolio history
            'position_history': position_history,
            'signal_history': signal_history,        # Contains signals as generated (before execution delay)
            'trade_log': trade_df,
            'performance': {
                'cagr': cagr,
                'volatility': volatility,
                'sharpe': sharpe,
                'max_drawdown': max_drawdown,
                'turnover': turnover,
                'win_rate': win_rate
            },
            'monthly_returns': monthly_returns,
            'yearly_returns': yearly_returns
        }
    
    # Apply the patch
    BacktestEngine._calculate_results = patched_calculate_results
    
    logger.info("Applied patch to BacktestEngine._calculate_results")
    return original_calculate_results

def apply_patch():
    """Apply the patch and run a test to verify it works."""
    original_method = patch_backtest_engine()
    logger.info("Patch applied successfully")
    return original_method

def restore_original(original_method):
    """Restore the original method."""
    from v4.engine.backtest_v4 import BacktestEngine
    BacktestEngine._calculate_results = original_method
    logger.info("Original method restored")

if __name__ == "__main__":
    print("=====================================================")
    print("ALLOCATION HISTORY FIX")
    print("=====================================================\n")
    
    original = apply_patch()
    print("Patch applied successfully. The BacktestEngine._calculate_results method has been replaced.")
    print("Run your tests to verify the fix works correctly.")
    print("\nTo restore the original method, use restore_original(original_method)")
