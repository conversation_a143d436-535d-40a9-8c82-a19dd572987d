#!/usr/bin/env python
"""
Test script to run unified pipeline
"""

import sys
import os

print("Python version:", sys.version)
print("Current working directory:", os.getcwd())

# Add current directory to Python path if not already there
current_dir = os.getcwd()
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)
    print(f"Added {current_dir} to Python path")

print("Python paths:")
for p in sys.path:
    print(f"  {p}")

try:
    from v4.pipeline.config import setup_logger, parse_cli_arguments, determine_pipeline_mode
    print("✓ Pipeline imports successful")
    
    # Try to run the unified pipeline
    from v4.run_unified_pipeline import run_unified_pipeline
    print("✓ Unified pipeline import successful")
    
    # Test with default settings
    print("Testing unified pipeline with default settings...")
    result = run_unified_pipeline()
    print(f"✓ Pipeline execution completed: {result}")
    
except Exception as e:
    print(f"✗ Error: {e}")
    import traceback
    traceback.print_exc()
