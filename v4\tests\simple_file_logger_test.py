#!/usr/bin/env python
# -*- coding: utf-8 -*-
# simple_file_logger_test.py
"""
Script that logs all its operations directly to a file.
It attempts to load settings and logs the outcome.
"""

import sys
import os
import datetime
import traceback

# --- Log File Setup ---
LOG_FILENAME = "simple_file_logger_test.log"
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
LOG_FILE_PATH = os.path.join(SCRIPT_DIR, LOG_FILENAME)

def log_message(message):
    """Appends a message to the log file with a timestamp."""
    now = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")
    try:
        with open(LOG_FILE_PATH, "a") as f:
            f.write(f"[{now}] {message}\n")
    except Exception as e:
        # If logging fails, we're in deep trouble. Try to print as a last resort.
        print(f"CRITICAL LOGGING ERROR: {e} while trying to write: {message}")
        sys.stdout.flush()

# --- Script Execution ---
log_message("==== SCRIPT EXECUTION STARTED: simple_file_logger_test.py ====")

try:
    log_message(f"Python version: {sys.version.replace('\n', ' ')}")
    log_message(f"Python executable: {sys.executable}")
    log_message(f"Current working directory: {os.getcwd()}")
    log_message(f"Script path: {__file__}")
    log_message(f"Log file path: {LOG_FILE_PATH}")

    log_message("STEP 1: Attempting to import pathlib.Path")
    from pathlib import Path
    log_message("✓ Successfully imported pathlib.Path")

    log_message("STEP 2: Setting up sys.path")
    project_root = Path(__file__).resolve().parent.parent.parent
    log_message(f"  Identified project root: {project_root}")
    if str(project_root) not in sys.path:
        sys.path.insert(0, str(project_root))
        log_message(f"  Added project root to sys.path. Current sys.path[0]: {sys.path[0]}")
    else:
        log_message("  Project root already in sys.path.")
    log_message(f"  Full sys.path: {sys.path}")
    log_message("✓ sys.path configured.")

    log_message("STEP 3: Attempting to import load_settings from v4.settings.settings_CPS_v4")
    from v4.settings.settings_CPS_v4 import load_settings
    log_message("✓ Successfully imported load_settings.")

    log_message("STEP 4: Attempting to load settings using load_settings()")
    settings = load_settings()
    log_message("✓ Settings loaded successfully.")

    log_message("STEP 5: Logging loaded settings...")
    if settings:
        for main_key, main_value in settings.items():
            log_message(f"  --- {main_key.upper()} SETTINGS ---")
            if isinstance(main_value, dict):
                for sub_key, sub_value in main_value.items():
                    log_message(f"    {sub_key}: {sub_value}")
            else:
                log_message(f"    {main_key}: {main_value}")
    else:
        log_message("✗ No settings were loaded or settings dictionary is empty.")
    log_message("✓ Finished logging settings.")

except ImportError as e_import:
    log_message(f"✗ IMPORT ERROR: {e_import}")
    log_message("  Traceback:")
    log_message(traceback.format_exc())
except Exception as e_general:
    log_message(f"✗ AN UNEXPECTED ERROR OCCURRED: {e_general}")
    log_message("  Traceback:")
    log_message(traceback.format_exc())
finally:
    log_message("==== SCRIPT EXECUTION FINISHED: simple_file_logger_test.py ====\n")
