# Combo ID Visibility and Tracking Design

## Overview

This design document outlines a comprehensive visibility and tracking system for combo IDs throughout the optimization testing process. The system provides real-time status updates, detailed logging, progress tracking, and historical analysis to ensure complete transparency of combo ID processing.

## Architecture

### Core Components

```mermaid
graph TB
    A[Combo ID Tracker] --> B[Status Dashboard]
    A --> C[Progress Monitor]
    A --> D[Log Manager]
    A --> E[File Tracker]
    
    B --> F[Real-time Display]
    C --> G[Time Estimation]
    D --> H[Structured Logging]
    E --> I[File Correlation]
    
    A --> J[Historical Database]
    J --> K[Performance Analysis]
    
    L[Environment Context] --> A
    M[Validation Framework] --> A
```

### Data Flow

1. **Initialization**: Combo ID tracker initializes with lookup table and historical data
2. **Processing**: Each combo ID flows through tracked phases with status updates
3. **Logging**: All operations log combo ID context with structured data
4. **Monitoring**: Progress monitor tracks timing and estimates completion
5. **Reporting**: Status dashboard provides real-time visibility
6. **Storage**: Historical database captures performance metrics

## Components and Interfaces

### 1. ComboIDTracker Class

**Purpose**: Central coordinator for all combo ID tracking and visibility

```python
class ComboIDTracker:
    """Central tracker for combo ID processing visibility and logging."""
    
    def __init__(self, combinations: List[Dict], output_dir: Path):
        """Initialize tracker with combo list and output directory."""
        
    def start_processing(self, combo_id: str, combination: Dict) -> None:
        """Mark combo ID as starting processing with timestamp."""
        
    def update_phase(self, combo_id: str, phase: str, status: str, details: Dict = None) -> None:
        """Update combo ID processing phase with status and details."""
        
    def complete_processing(self, combo_id: str, success: bool, duration: float, details: Dict = None) -> None:
        """Mark combo ID as completed with success status and timing."""
        
    def get_current_status(self) -> Dict:
        """Get current processing status for all combo IDs."""
        
    def get_progress_summary(self) -> Dict:
        """Get overall progress summary with estimates."""
```

### 2. StatusDashboard Class

**Purpose**: Interactive display of current combo ID processing status

```python
class StatusDashboard:
    """Interactive status dashboard for combo ID processing."""
    
    def __init__(self, tracker: ComboIDTracker):
        """Initialize dashboard with tracker reference."""
        
    def display_startup_summary(self, total_combos: int, estimated_time: float) -> None:
        """Display initial summary when optimization starts."""
        
    def update_current_combo(self, combo_id: str, progress: int, total: int, elapsed: float, estimated_remaining: float) -> None:
        """Update display with current combo ID being processed."""
        
    def display_completion_summary(self, results: Dict) -> None:
        """Display final summary when optimization completes."""
        
    def show_detailed_status(self) -> None:
        """Show detailed status of all combo IDs."""
```

### 3. ComboIDLogger Class

**Purpose**: Structured logging with combo ID context

```python
class ComboIDLogger:
    """Enhanced logger with combo ID context and structured output."""
    
    def __init__(self, base_logger: logging.Logger, output_dir: Path):
        """Initialize with base logger and output directory."""
        
    def log_combo_start(self, combo_id: str, combination: Dict, phase: str) -> None:
        """Log combo ID entering a processing phase."""
        
    def log_combo_progress(self, combo_id: str, phase: str, message: str, details: Dict = None) -> None:
        """Log combo ID progress within a phase."""
        
    def log_combo_complete(self, combo_id: str, phase: str, success: bool, duration: float, details: Dict = None) -> None:
        """Log combo ID completing a phase."""
        
    def log_combo_error(self, combo_id: str, phase: str, error: Exception, context: Dict = None) -> None:
        """Log combo ID error with full context."""
        
    def create_combo_log_file(self, combo_id: str) -> Path:
        """Create dedicated log file for specific combo ID."""
```

### 4. ProgressMonitor Class

**Purpose**: Track timing and estimate completion

```python
class ProgressMonitor:
    """Monitor progress and estimate completion times for combo ID processing."""
    
    def __init__(self, total_combos: int, historical_data: Dict = None):
        """Initialize with total combo count and optional historical data."""
        
    def record_combo_start(self, combo_id: str) -> None:
        """Record when combo ID processing starts."""
        
    def record_combo_complete(self, combo_id: str, success: bool) -> None:
        """Record when combo ID processing completes."""
        
    def get_estimated_remaining_time(self) -> float:
        """Calculate estimated time remaining based on current progress."""
        
    def get_progress_statistics(self) -> Dict:
        """Get detailed progress statistics."""
        
    def get_performance_metrics(self) -> Dict:
        """Get performance metrics for current run."""
```

### 5. FileTracker Class

**Purpose**: Track all files created with combo ID correlation

```python
class FileTracker:
    """Track all files created during combo ID processing for correlation."""
    
    def __init__(self, output_dir: Path):
        """Initialize file tracker with output directory."""
        
    def register_file_creation(self, combo_id: str, file_type: str, file_path: Path, phase: str) -> None:
        """Register a file created for specific combo ID."""
        
    def get_combo_files(self, combo_id: str) -> List[Dict]:
        """Get all files associated with specific combo ID."""
        
    def validate_file_naming(self, combo_id: str) -> List[str]:
        """Validate that all files use consistent combo ID naming."""
        
    def generate_file_correlation_report(self) -> Dict:
        """Generate report correlating files across combo IDs."""
```

## Data Models

### ComboIDStatus

```python
@dataclass
class ComboIDStatus:
    """Status information for a combo ID."""
    combo_id: str
    combination: Dict[str, Any]
    current_phase: str
    status: str  # 'pending', 'processing', 'completed', 'failed'
    start_time: Optional[datetime]
    end_time: Optional[datetime]
    duration: Optional[float]
    phases_completed: List[str]
    files_created: List[Path]
    error_message: Optional[str]
    details: Dict[str, Any]
```

### ProcessingPhase

```python
@dataclass
class ProcessingPhase:
    """Information about a processing phase."""
    name: str
    combo_id: str
    start_time: datetime
    end_time: Optional[datetime]
    status: str  # 'running', 'completed', 'failed'
    details: Dict[str, Any]
    files_created: List[Path]
    log_entries: List[str]
```

### ProgressSummary

```python
@dataclass
class ProgressSummary:
    """Overall progress summary."""
    total_combos: int
    completed_combos: int
    failed_combos: int
    current_combo: Optional[str]
    start_time: datetime
    estimated_completion: Optional[datetime]
    average_combo_time: float
    success_rate: float
    current_phase: str
```

## Error Handling

### Combo ID Validation

```python
def validate_combo_id_format(combo_id: str) -> bool:
    """Validate combo ID follows expected format S{n}_M{n}_L{n}_E{n}_T{n}."""
    pattern = r'^S\d+_M\d+_L\d+_E\d+_T\d+$'
    return bool(re.match(pattern, combo_id))

def validate_combo_id_uniqueness(combo_ids: List[str]) -> List[str]:
    """Validate all combo IDs are unique, return duplicates if any."""
    seen = set()
    duplicates = []
    for combo_id in combo_ids:
        if combo_id in seen:
            duplicates.append(combo_id)
        seen.add(combo_id)
    return duplicates
```

### Error Recovery

```python
def handle_combo_processing_error(combo_id: str, error: Exception, tracker: ComboIDTracker) -> bool:
    """Handle errors during combo ID processing with recovery options."""
    
    # Log error with full context
    tracker.log_combo_error(combo_id, error)
    
    # Attempt recovery based on error type
    if isinstance(error, FileNotFoundError):
        return attempt_file_recovery(combo_id, error)
    elif isinstance(error, subprocess.TimeoutExpired):
        return attempt_timeout_recovery(combo_id, error)
    else:
        return False  # No recovery possible
```

## Testing Strategy

### Unit Tests

```python
class TestComboIDTracker:
    """Test suite for ComboIDTracker functionality."""
    
    def test_combo_id_generation(self):
        """Test combo ID generation produces expected format."""
        
    def test_status_tracking(self):
        """Test status updates are tracked correctly."""
        
    def test_progress_calculation(self):
        """Test progress calculations are accurate."""
        
    def test_error_handling(self):
        """Test error scenarios are handled gracefully."""

class TestStatusDashboard:
    """Test suite for StatusDashboard display functionality."""
    
    def test_display_formatting(self):
        """Test status display formats correctly."""
        
    def test_real_time_updates(self):
        """Test real-time updates work properly."""

class TestFileTracking:
    """Test suite for file tracking and correlation."""
    
    def test_file_registration(self):
        """Test file registration works correctly."""
        
    def test_naming_validation(self):
        """Test file naming validation catches inconsistencies."""
```

### Integration Tests

```python
class TestEndToEndTracking:
    """Integration tests for complete combo ID tracking."""
    
    def test_full_optimization_tracking(self):
        """Test tracking through complete optimization cycle."""
        
    def test_error_recovery_flow(self):
        """Test error recovery maintains tracking integrity."""
        
    def test_historical_data_integration(self):
        """Test historical data integration works correctly."""
```

## Implementation Phases

### Phase 1: Core Tracking Infrastructure
- Implement ComboIDTracker class
- Add basic logging with combo ID context
- Create status data models
- Add combo ID validation

### Phase 2: Visibility and Monitoring
- Implement StatusDashboard class
- Add ProgressMonitor functionality
- Create real-time display updates
- Add file tracking capabilities

### Phase 3: Historical Analysis
- Implement historical database storage
- Add performance trend analysis
- Create comparison reporting
- Add optimization recommendations

### Phase 4: Advanced Features
- Add interactive dashboard features
- Implement advanced error recovery
- Create automated performance alerts
- Add integration with external monitoring tools

## Configuration

### Environment Variables

```bash
# Combo ID tracking configuration
CPS_V4_COMBO_TRACKING_ENABLED=true
CPS_V4_COMBO_LOG_LEVEL=INFO
CPS_V4_COMBO_DASHBOARD_ENABLED=true
CPS_V4_COMBO_HISTORICAL_DB_PATH=./combo_history.db
CPS_V4_COMBO_PROGRESS_UPDATE_INTERVAL=5
```

### Configuration File

```yaml
combo_id_tracking:
  enabled: true
  log_level: INFO
  dashboard:
    enabled: true
    update_interval: 5
    show_detailed_status: true
  file_tracking:
    enabled: true
    validate_naming: true
  historical_analysis:
    enabled: true
    database_path: "./combo_history.db"
    retention_days: 90
  progress_monitoring:
    enabled: true
    estimate_completion: true
    show_performance_metrics: true
```

## Success Criteria

1. **Real-time Visibility**: Users can see current combo ID processing status at all times
2. **Complete Logging**: All combo ID operations are logged with proper context
3. **File Correlation**: All generated files can be correlated back to specific combo IDs
4. **Progress Tracking**: Accurate progress estimates and completion times
5. **Error Transparency**: Clear error reporting with combo ID context
6. **Historical Analysis**: Performance trends and optimization recommendations
7. **Testing Coverage**: Comprehensive test suite validates all functionality
8. **Documentation**: Clear documentation for users and developers

This design provides comprehensive visibility and tracking for combo ID processing while maintaining performance and reliability of the optimization system.