@echo off
REM ============================================
REM Script: test_report_fix_proper.bat
REM Test PerformanceTableGenerator fixes properly
REM ============================================

SETLOCAL
pushd "%~dp0"

REM --- Configuration ---
SET PYTHON_EXE=F:\AI_Library\my_quant_env\Scripts\python.exe
SET SCRIPT_PATH=%~dp0test_report_fix.py
SET OUTPUT_LOG_DIR=output

REM Create timestamp
FOR /f "tokens=2 delims==" %%G IN ('wmic os get localdatetime /value') DO SET TIMESTAMP=%%G
SET TIMESTAMP=%TIMESTAMP:~0,8%_%TIMESTAMP:~8,6%
SET OUTPUT_FILE=%OUTPUT_LOG_DIR%\test_report_fix_%TIMESTAMP%.txt

REM --- Ensure output directory exists ---
if not exist "%OUTPUT_LOG_DIR%" mkdir "%OUTPUT_LOG_DIR%"

echo Testing PerformanceTableGenerator fixes...
echo Logging to: %OUTPUT_FILE%
echo.

REM --- Run the test ---
"%PYTHON_EXE%" "%SCRIPT_PATH%" > "%OUTPUT_FILE%" 2>&1

REM --- Check result ---
if %ERRORLEVEL% EQU 0 (
    echo ✅ Test completed successfully!
) else (
    echo ❌ Test failed with error code %ERRORLEVEL%
)

echo.
echo Results:
type "%OUTPUT_FILE%"
echo.
echo Full log saved to: %OUTPUT_FILE%

popd
pause
