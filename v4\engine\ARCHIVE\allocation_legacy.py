"""v4.engine.allocation
Compatibility shim that exposes the API from :pymod:`v4.engine.allocation_v4`.

Legacy code still imports ``v4.engine.allocation``.  This façade forwards
all public symbols to *allocation_v4.py* so that we can proceed with
incremental refactor without mass-editing existing modules.
"""
from importlib import import_module as _imp
import sys as _sys

_src = _imp("v4.engine.allocation_v4")

__all__ = _src.__all__ if hasattr(_src, "__all__") else [
    n for n in _src.__dict__.keys() if not n.startswith("_")
]

globals().update({n: getattr(_src, n) for n in __all__})

# Ensure submodule resolution works for relative imports
_sys.modules[__name__ + ".allocation_v4"] = _src
