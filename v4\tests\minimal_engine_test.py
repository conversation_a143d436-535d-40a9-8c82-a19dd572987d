#!/usr/bin/env python
# -*- coding: utf-8 -*-
# minimal_engine_test.py
"""
Minimal test script to verify if the backtest engine runs at all.
Prints output directly to console without trying to save files.

Author: AI Assistant
Date: 2025-06-14
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime
import logging
from pathlib import Path

# Configure logging to console
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Add parent directory to path to import modules
project_root = Path(__file__).parent.parent.parent
if str(project_root) not in sys.path:
    sys.path.append(str(project_root))

def generate_test_data(start_date='2022-01-01', end_date='2022-03-31', symbols=None):
    """Generate minimal synthetic price data for testing."""
    if symbols is None:
        symbols = ['AAPL', 'MSFT']  # Just 2 symbols for simplicity
    
    # Create date range (just 3 months for speed)
    dates = pd.date_range(start=start_date, end=end_date, freq='B')
    
    # Create price data with random walks
    np.random.seed(42)  # For reproducibility
    price_data = pd.DataFrame(index=dates)
    
    for symbol in symbols:
        # Start with 100
        prices = [100]
        # Generate random daily returns
        for _ in range(1, len(dates)):
            daily_return = np.random.normal(0.0005, 0.015)
            prices.append(prices[-1] * (1 + daily_return))
        
        price_data[symbol] = prices
    
    return price_data

def run_minimal_test():
    """Run a minimal test of the backtest engine."""
    print("\n===== MINIMAL BACKTEST ENGINE TEST =====\n")
    
    # Step 1: Generate test data
    print("Step 1: Generating test data...")
    price_data = generate_test_data()
    print(f"Generated price data with shape: {price_data.shape}")
    print(f"Price data sample:\n{price_data.head()}")
    
    # Step 2: Import backtest engine
    print("\nStep 2: Importing backtest engine...")
    try:
        from v4.engine.backtest_v4 import BacktestEngine
        print("✅ Successfully imported BacktestEngine")
    except Exception as e:
        print(f"❌ Failed to import BacktestEngine: {e}")
        return
    
    # Step 3: Import signal generator
    print("\nStep 3: Importing signal generator...")
    try:
        from v4.engine.signal_generator_v4 import generate_signals
        print("✅ Successfully imported generate_signals")
    except Exception as e:
        print(f"❌ Failed to import generate_signals: {e}")
        return
    
    # Step 4: Create backtest engine instance
    print("\nStep 4: Creating backtest engine instance...")
    try:
        engine = BacktestEngine()
        print("✅ Successfully created BacktestEngine instance")
    except Exception as e:
        print(f"❌ Failed to create BacktestEngine instance: {e}")
        return
    
    # Step 5: Run backtest
    print("\nStep 5: Running backtest...")
    try:
        results = engine.run_backtest(
            price_data=price_data,
            signal_generator=generate_signals,
            strategy='equal_weight'
        )
        print("✅ Successfully ran backtest")
    except Exception as e:
        print(f"❌ Failed to run backtest: {e}")
        print(f"Exception details: {str(e)}")
        import traceback
        traceback.print_exc()
        return
    
    # Step 6: Check results structure
    print("\nStep 6: Checking results structure...")
    expected_keys = ['initial_capital', 'final_value', 'total_return', 'strategy_returns', 
                    'benchmark_returns', 'weights_history', 'position_history', 
                    'signal_history', 'trade_log', 'performance']
    
    missing_keys = [key for key in expected_keys if key not in results]
    if missing_keys:
        print(f"❌ Missing expected keys in results: {missing_keys}")
    else:
        print("✅ All expected keys present in results")
        
    # Print key results
    print("\nKey results:")
    print(f"Initial capital: {results.get('initial_capital', 'N/A')}")
    print(f"Final value: {results.get('final_value', 'N/A')}")
    print(f"Total return: {results.get('total_return', 'N/A')}")
    
    # Check signal_history
    signal_history = results.get('signal_history')
    if signal_history is not None and not signal_history.empty:
        print(f"\nSignal history exists with shape: {signal_history.shape}")
        print(f"Signal history sample:\n{signal_history.head()}")
    else:
        print("\nSignal history is None or empty")
    
    # Check weights_history
    weights_history = results.get('weights_history')
    if weights_history is not None and not weights_history.empty:
        print(f"\nWeights history exists with shape: {weights_history.shape}")
        print(f"Weights history sample:\n{weights_history.head()}")
    else:
        print("\nWeights history is None or empty")
    
    # Compare weights_history and signal_history
    if weights_history is not None and signal_history is not None:
        if weights_history.equals(signal_history):
            print("\n❌ weights_history equals signal_history - allocation history is not preserved!")
        else:
            print("\n✅ weights_history differs from signal_history - allocation history is preserved!")
    
    print("\n===== TEST COMPLETE =====")

if __name__ == "__main__":
    print("=====================================================")
    print("MINIMAL BACKTEST ENGINE TEST")
    print(f"Run Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=====================================================")
    
    run_minimal_test()
