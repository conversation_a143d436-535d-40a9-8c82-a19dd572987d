Combined Project Documentation
#documentation
#project_structure
#api_reference
Edit
# Combined Project Documentation

## Table of Contents
1. [v3_module+functions_list_AI](#v3_module+functions_list_ai)
2. [reporting_system_AI](#reporting_system_ai)
3. [systemFiles+Flow_AI](#systemfiles+flow_ai)
4. [parameter_management_AI](#parameter_management_ai)
5. [v3_module+functions_list](#v3_module+functions_list)
6. [v3_performance_reporting_standards_a](#v3_performance_reporting_standards_a)
7. [v3_parameter_system_reference](#v3_parameter_system_reference)

## Key Components

### Core Engine Modules
- `v3_engine/parameter_registry.py`: Central parameter registry
- `v3_engine/parameters.py`: Parameter class definitions
- `v3_engine/strategy_parameter_set.py`: Strategy parameter container

### GUI Components
- `app/gui/v3_gui_core.py`: Main GUI window
- `app/gui/v3_parameter_widgets.py`: Parameter widgets
- `v3_engine/gui_parameter_manager.py`: Manages GUI parameters

### Reporting System
- `v3_reporting/v3_performance_report.py`: Performance metrics
- `v3_reporting/v3_allocation_report.py`: Allocation reports
- `v3_engine/performance_reporter_adapter.py`: Adapter for reporting

### Parameter System
- **NumericParameter**: For numeric values with min/max bounds
- **CategoricalParameter**: For selection from fixed options
- **CategoricalListParameter**: For user-defined groups
- **ConfigParameter**: For config-only values