#!/usr/bin/env python
"""
Test script to check pipeline imports
"""

import sys
import os

print("Python version:", sys.version)
print("Current working directory:", os.getcwd())
print("Python paths:")
for p in sys.path:
    print(f"  {p}")

try:
    import v4.pipeline.config
    print("✓ v4.pipeline.config import successful")
except ImportError as e:
    print(f"✗ v4.pipeline.config import failed: {e}")

try:
    import v4.pipeline.modes
    print("✓ v4.pipeline.modes import successful")
except ImportError as e:
    print(f"✗ v4.pipeline.modes import failed: {e}")

try:
    import v4.pipeline.trading
    print("✓ v4.pipeline.trading import successful")
except ImportError as e:
    print(f"✗ v4.pipeline.trading import failed: {e}")

try:
    from v4.pipeline.config import setup_logger, parse_cli_arguments, determine_pipeline_mode
    print("✓ v4.pipeline.config functions import successful")
except ImportError as e:
    print(f"✗ v4.pipeline.config functions import failed: {e}")
