2025-06-18 12:10:47,591 - INFO - Script setup complete. Settings loaded and loggers configured.
2025-06-18 12:10:47,592 - INFO - EMA Production Flow Trace Log - 20250618_121047
2025-06-18 12:10:47,592 - INFO - Period: 2021-01-20 to 2021-02-28
2025-06-18 12:10:48,070 - INFO - Price data loaded. Shape: (1370, 5)
2025-06-18 12:10:48,100 - INFO - --- Effective Settings for Trace ---
2025-06-18 12:10:48,101 - INFO - EMA Lookbacks: ST=15, MT=70, LT=100
2025-06-18 12:10:48,101 - INFO - EMA Weights: Min=0.0, Max=1.0
2025-06-18 12:10:48,102 - INFO - EMA Strategy Top N: 3
2025-06-18 12:10:48,106 - INFO - System Top N (for allocation rules): 2
2025-06-18 12:10:48,106 - INFO - Signal Algorithm for Rules: {'default': "'ema'", 'picklist': ['Strategy_EMA', 'Strategy_SMA', 'Strategy_MACD', 'Strategy_RSI', 'Strategy_BB']}
2025-06-18 12:10:48,106 - INFO - -----------------------------------
2025-06-18 12:10:48,112 - INFO - 
========== Tracing for Date: 2021-01-20 ==========
2025-06-18 12:10:48,113 - INFO - --- Step 1: Calculate EMA Metrics ---
2025-06-18 12:10:48,113 - INFO - Input price_data_slice shape: (265, 5)
2025-06-18 12:10:48,113 - INFO - Setting module-level ema_params in ema_model_module: {'st_lookback': 15, 'mt_lookback': 70, 'lt_lookback': 100, 'min_weight': 0.0, 'max_weight': 1.0, 'top_n_strat_specific': 3}
2025-06-18 12:10:48,128 - INFO - Output short_ema_df tail(1):
                   SPY       SHV        EFA         TLT        PFF
Date                                                              
2021-01-20  354.597918  97.17208  66.021205  134.545233  29.691865
2025-06-18 12:10:48,132 - INFO - Output med_ema_df tail (1):
                   SPY        SHV        EFA         TLT        PFF
Date                                                               
2021-01-20  339.835007  97.173336  62.416445  137.835581  29.151148
2025-06-18 12:10:48,136 - INFO - Output long_ema_df tail (1):
                   SPY        SHV        EFA         TLT        PFF
Date                                                               
2021-01-20  333.307234  97.166897  61.070301  138.596178  28.839638
2025-06-18 12:10:48,136 - INFO - --- Step 2: Calculate EMA Ratios ---
2025-06-18 12:10:48,143 - INFO - Output stmtemax_series (latest):
                 SPY       SHV       EFA       TLT       PFF
Date                                                        
2021-01-20  1.043441  0.999987  1.057753  0.976128  1.018549
2025-06-18 12:10:48,145 - INFO - Output mtltemax_series (latest):
                 SPY       SHV       EFA       TLT       PFF
Date                                                        
2021-01-20  1.019585  1.000066  1.022043  0.994512  1.010801
2025-06-18 12:10:48,147 - INFO - Output emaxavg_series (latest for ranking):
                 SPY       SHV       EFA      TLT       PFF
Date                                                       
2021-01-20  1.031513  1.000027  1.039898  0.98532  1.014675
2025-06-18 12:10:48,148 - INFO - --- Step 3: Determine Allocation Weights ---
2025-06-18 12:10:48,149 - INFO - Calling ema_model_module.ema_allocation_model_updated with price_data up to 2021-01-20
2025-06-18 12:10:48,149 - INFO - Attempting to call ema_model_module.ema_allocation_model_updated...
2025-06-18 12:10:48,149 - INFO -   ema_model_module.st_lookback=15, mt_lookback=70, lt_lookback=100
2025-06-18 12:10:48,149 - INFO -   ema_model_module.min_weight=0.0, ema_model_module.max_weight=1.0
2025-06-18 12:10:48,150 - INFO -   Trace script's signal_algo: {'default': "'ema'", 'picklist': ['Strategy_EMA', 'Strategy_SMA', 'Strategy_MACD', 'Strategy_RSI', 'Strategy_BB']}, system_top_n: 2 (Note: production module uses its own settings for these)
2025-06-18 12:10:48,163 - INFO - Successfully called ema_model_module.ema_allocation_model_updated.
2025-06-18 12:10:48,164 - INFO - Output final_weights for 2021-01-20:
SPY    0.2
SHV    0.2
EFA    0.2
TLT    0.2
PFF    0.2
2025-06-18 12:10:48,165 - INFO - Final normalized weights for 2021-01-20: {'SPY': np.float64(0.2), 'SHV': np.float64(0.2), 'EFA': np.float64(0.2), 'TLT': np.float64(0.2), 'PFF': np.float64(0.2)}
2025-06-18 12:10:48,165 - INFO - 
========== Tracing for Date: 2021-01-21 ==========
2025-06-18 12:10:48,167 - INFO - --- Step 1: Calculate EMA Metrics ---
2025-06-18 12:10:48,167 - INFO - Input price_data_slice shape: (266, 5)
2025-06-18 12:10:48,167 - INFO - Setting module-level ema_params in ema_model_module: {'st_lookback': 15, 'mt_lookback': 70, 'lt_lookback': 100, 'min_weight': 0.0, 'max_weight': 1.0, 'top_n_strat_specific': 3}
2025-06-18 12:10:48,185 - INFO - Output short_ema_df tail(1):
                   SPY        SHV        EFA         TLT        PFF
Date                                                               
2021-01-21  355.498505  97.171578  66.137961  134.332903  29.710302
2025-06-18 12:10:48,187 - INFO - Output med_ema_df tail (1):
                   SPY        SHV        EFA         TLT        PFF
Date                                                               
2021-01-21  340.453813  97.173187  62.544299  137.695046  29.170534
2025-06-18 12:10:48,189 - INFO - Output long_ema_df tail (1):
                   SPY       SHV        EFA         TLT        PFF
Date                                                              
2021-01-21  333.871499  97.16692  61.186835  138.482325  28.859434
2025-06-18 12:10:48,189 - INFO - --- Step 2: Calculate EMA Ratios ---
2025-06-18 12:10:48,194 - INFO - Output stmtemax_series (latest):
                SPY       SHV       EFA       TLT       PFF
Date                                                       
2021-01-21  1.04419  0.999983  1.057458  0.975583  1.018504
2025-06-18 12:10:48,196 - INFO - Output mtltemax_series (latest):
                 SPY       SHV       EFA       TLT      PFF
Date                                                       
2021-01-21  1.019715  1.000064  1.022186  0.994315  1.01078
2025-06-18 12:10:48,199 - INFO - Output emaxavg_series (latest for ranking):
                 SPY       SHV       EFA       TLT       PFF
Date                                                        
2021-01-21  1.031953  1.000024  1.039822  0.984949  1.014642
2025-06-18 12:10:48,200 - INFO - --- Step 3: Determine Allocation Weights ---
2025-06-18 12:10:48,200 - INFO - Calling ema_model_module.ema_allocation_model_updated with price_data up to 2021-01-21
2025-06-18 12:10:48,200 - INFO - Attempting to call ema_model_module.ema_allocation_model_updated...
2025-06-18 12:10:48,200 - INFO -   ema_model_module.st_lookback=15, mt_lookback=70, lt_lookback=100
2025-06-18 12:10:48,201 - INFO -   ema_model_module.min_weight=0.0, ema_model_module.max_weight=1.0
2025-06-18 12:10:48,201 - INFO -   Trace script's signal_algo: {'default': "'ema'", 'picklist': ['Strategy_EMA', 'Strategy_SMA', 'Strategy_MACD', 'Strategy_RSI', 'Strategy_BB']}, system_top_n: 2 (Note: production module uses its own settings for these)
2025-06-18 12:10:48,215 - INFO - Successfully called ema_model_module.ema_allocation_model_updated.
2025-06-18 12:10:48,216 - INFO - Output final_weights for 2021-01-21:
SPY    0.2
SHV    0.2
EFA    0.2
TLT    0.2
PFF    0.2
2025-06-18 12:10:48,216 - INFO - Final normalized weights for 2021-01-21: {'SPY': np.float64(0.2), 'SHV': np.float64(0.2), 'EFA': np.float64(0.2), 'TLT': np.float64(0.2), 'PFF': np.float64(0.2)}
2025-06-18 12:10:48,217 - INFO - 
========== Tracing for Date: 2021-01-22 ==========
2025-06-18 12:10:48,218 - INFO - --- Step 1: Calculate EMA Metrics ---
2025-06-18 12:10:48,218 - INFO - Input price_data_slice shape: (267, 5)
2025-06-18 12:10:48,218 - INFO - Setting module-level ema_params in ema_model_module: {'st_lookback': 15, 'mt_lookback': 70, 'lt_lookback': 100, 'min_weight': 0.0, 'max_weight': 1.0, 'top_n_strat_specific': 3}
2025-06-18 12:10:48,236 - INFO - Output short_ema_df tail(1):
                   SPY        SHV        EFA         TLT        PFF
Date                                                               
2021-01-22  356.126446  97.171138  66.200219  134.200864  29.733249
2025-06-18 12:10:48,238 - INFO - Output med_ema_df tail (1):
                   SPY        SHV        EFA         TLT       PFF
Date                                                              
2021-01-22  341.019115  97.173043  62.659559  137.570582  29.19091
2025-06-18 12:10:48,240 - INFO - Output long_ema_df tail (1):
                   SPY        SHV       EFA         TLT        PFF
Date                                                              
2021-01-22  334.399232  97.166943  61.29474  138.379241  28.879919
2025-06-18 12:10:48,241 - INFO - --- Step 2: Calculate EMA Ratios ---
2025-06-18 12:10:48,246 - INFO - Output stmtemax_series (latest):
                 SPY      SHV       EFA       TLT       PFF
Date                                                       
2021-01-22  1.044301  0.99998  1.056506  0.975506  1.018579
2025-06-18 12:10:48,250 - INFO - Output mtltemax_series (latest):
                 SPY       SHV       EFA       TLT       PFF
Date                                                        
2021-01-22  1.019796  1.000063  1.022266  0.994156  1.010768
2025-06-18 12:10:48,252 - INFO - Output emaxavg_series (latest for ranking):
                 SPY       SHV       EFA       TLT       PFF
Date                                                        
2021-01-22  1.032048  1.000022  1.039386  0.984831  1.014674
2025-06-18 12:10:48,253 - INFO - --- Step 3: Determine Allocation Weights ---
2025-06-18 12:10:48,254 - INFO - Calling ema_model_module.ema_allocation_model_updated with price_data up to 2021-01-22
2025-06-18 12:10:48,254 - INFO - Attempting to call ema_model_module.ema_allocation_model_updated...
2025-06-18 12:10:48,254 - INFO -   ema_model_module.st_lookback=15, mt_lookback=70, lt_lookback=100
2025-06-18 12:10:48,255 - INFO -   ema_model_module.min_weight=0.0, ema_model_module.max_weight=1.0
2025-06-18 12:10:48,255 - INFO -   Trace script's signal_algo: {'default': "'ema'", 'picklist': ['Strategy_EMA', 'Strategy_SMA', 'Strategy_MACD', 'Strategy_RSI', 'Strategy_BB']}, system_top_n: 2 (Note: production module uses its own settings for these)
2025-06-18 12:10:48,263 - INFO - Successfully called ema_model_module.ema_allocation_model_updated.
2025-06-18 12:10:48,265 - INFO - Output final_weights for 2021-01-22:
SPY    0.2
SHV    0.2
EFA    0.2
TLT    0.2
PFF    0.2
2025-06-18 12:10:48,266 - INFO - Final normalized weights for 2021-01-22: {'SPY': np.float64(0.2), 'SHV': np.float64(0.2), 'EFA': np.float64(0.2), 'TLT': np.float64(0.2), 'PFF': np.float64(0.2)}
2025-06-18 12:10:48,266 - INFO - 
========== Tracing for Date: 2021-01-25 ==========
2025-06-18 12:10:48,268 - INFO - --- Step 1: Calculate EMA Metrics ---
2025-06-18 12:10:48,268 - INFO - Input price_data_slice shape: (268, 5)
2025-06-18 12:10:48,268 - INFO - Setting module-level ema_params in ema_model_module: {'st_lookback': 15, 'mt_lookback': 70, 'lt_lookback': 100, 'min_weight': 0.0, 'max_weight': 1.0, 'top_n_strat_specific': 3}
2025-06-18 12:10:48,284 - INFO - Output short_ema_df tail(1):
                   SPY        SHV        EFA         TLT        PFF
Date                                                               
2021-01-25  356.853621  97.170753  66.244718  134.281677  29.743593
2025-06-18 12:10:48,286 - INFO - Output med_ema_df tail (1):
                   SPY        SHV        EFA         TLT        PFF
Date                                                               
2021-01-25  341.608544  97.172902  62.769323  137.493872  29.208518
2025-06-18 12:10:48,291 - INFO - Output long_ema_df tail (1):
                  SPY        SHV        EFA         TLT        PFF
Date                                                              
2021-01-25  334.94467  97.166965  61.398927  138.309303  28.898455
2025-06-18 12:10:48,291 - INFO - --- Step 2: Calculate EMA Ratios ---
2025-06-18 12:10:48,298 - INFO - Output stmtemax_series (latest):
                 SPY       SHV       EFA       TLT       PFF
Date                                                        
2021-01-25  1.044627  0.999978  1.055368  0.976638  1.018319
2025-06-18 12:10:48,300 - INFO - Output mtltemax_series (latest):
                 SPY       SHV      EFA       TLT       PFF
Date                                                       
2021-01-25  1.019895  1.000061  1.02232  0.994104  1.010729
2025-06-18 12:10:48,302 - INFO - Output emaxavg_series (latest for ranking):
                 SPY       SHV       EFA       TLT       PFF
Date                                                        
2021-01-25  1.032261  1.000019  1.038844  0.985371  1.014524
2025-06-18 12:10:48,303 - INFO - --- Step 3: Determine Allocation Weights ---
2025-06-18 12:10:48,303 - INFO - Calling ema_model_module.ema_allocation_model_updated with price_data up to 2021-01-25
2025-06-18 12:10:48,303 - INFO - Attempting to call ema_model_module.ema_allocation_model_updated...
2025-06-18 12:10:48,303 - INFO -   ema_model_module.st_lookback=15, mt_lookback=70, lt_lookback=100
2025-06-18 12:10:48,304 - INFO -   ema_model_module.min_weight=0.0, ema_model_module.max_weight=1.0
2025-06-18 12:10:48,304 - INFO -   Trace script's signal_algo: {'default': "'ema'", 'picklist': ['Strategy_EMA', 'Strategy_SMA', 'Strategy_MACD', 'Strategy_RSI', 'Strategy_BB']}, system_top_n: 2 (Note: production module uses its own settings for these)
2025-06-18 12:10:48,315 - INFO - Successfully called ema_model_module.ema_allocation_model_updated.
2025-06-18 12:10:48,316 - INFO - Output final_weights for 2021-01-25:
SPY    0.2
SHV    0.2
EFA    0.2
TLT    0.2
PFF    0.2
2025-06-18 12:10:48,317 - INFO - Final normalized weights for 2021-01-25: {'SPY': np.float64(0.2), 'SHV': np.float64(0.2), 'EFA': np.float64(0.2), 'TLT': np.float64(0.2), 'PFF': np.float64(0.2)}
2025-06-18 12:10:48,317 - INFO - 
========== Tracing for Date: 2021-01-26 ==========
2025-06-18 12:10:48,318 - INFO - --- Step 1: Calculate EMA Metrics ---
2025-06-18 12:10:48,318 - INFO - Input price_data_slice shape: (269, 5)
2025-06-18 12:10:48,318 - INFO - Setting module-level ema_params in ema_model_module: {'st_lookback': 15, 'mt_lookback': 70, 'lt_lookback': 100, 'min_weight': 0.0, 'max_weight': 1.0, 'top_n_strat_specific': 3}
2025-06-18 12:10:48,329 - INFO - Output short_ema_df tail(1):
                   SPY        SHV        EFA         TLT        PFF
Date                                                               
2021-01-26  357.419286  97.170417  66.295848  134.320578  29.742908
2025-06-18 12:10:48,331 - INFO - Output med_ema_df tail (1):
                   SPY        SHV        EFA         TLT        PFF
Date                                                               
2021-01-26  342.165456  97.172766  62.878744  137.412154  29.223436
2025-06-18 12:10:48,335 - INFO - Output long_ema_df tail (1):
                   SPY        SHV        EFA        TLT        PFF
Date                                                              
2021-01-26  335.468121  97.166986  61.502983  138.23571  28.915082
2025-06-18 12:10:48,336 - INFO - --- Step 2: Calculate EMA Ratios ---
2025-06-18 12:10:48,344 - INFO - Output stmtemax_series (latest):
                SPY       SHV       EFA       TLT       PFF
Date                                                       
2021-01-26  1.04458  0.999976  1.054344  0.977501  1.017776
2025-06-18 12:10:48,346 - INFO - Output mtltemax_series (latest):
                 SPY       SHV       EFA       TLT       PFF
Date                                                        
2021-01-26  1.019964  1.000059  1.022369  0.994042  1.010664
2025-06-18 12:10:48,348 - INFO - Output emaxavg_series (latest for ranking):
                 SPY       SHV       EFA       TLT      PFF
Date                                                       
2021-01-26  1.032272  1.000018  1.038357  0.985772  1.01422
2025-06-18 12:10:48,349 - INFO - --- Step 3: Determine Allocation Weights ---
2025-06-18 12:10:48,349 - INFO - Calling ema_model_module.ema_allocation_model_updated with price_data up to 2021-01-26
2025-06-18 12:10:48,350 - INFO - Attempting to call ema_model_module.ema_allocation_model_updated...
2025-06-18 12:10:48,350 - INFO -   ema_model_module.st_lookback=15, mt_lookback=70, lt_lookback=100
2025-06-18 12:10:48,350 - INFO -   ema_model_module.min_weight=0.0, ema_model_module.max_weight=1.0
2025-06-18 12:10:48,350 - INFO -   Trace script's signal_algo: {'default': "'ema'", 'picklist': ['Strategy_EMA', 'Strategy_SMA', 'Strategy_MACD', 'Strategy_RSI', 'Strategy_BB']}, system_top_n: 2 (Note: production module uses its own settings for these)
2025-06-18 12:10:48,362 - INFO - Successfully called ema_model_module.ema_allocation_model_updated.
2025-06-18 12:10:48,364 - INFO - Output final_weights for 2021-01-26:
SPY    0.2
SHV    0.2
EFA    0.2
TLT    0.2
PFF    0.2
2025-06-18 12:10:48,364 - INFO - Final normalized weights for 2021-01-26: {'SPY': np.float64(0.2), 'SHV': np.float64(0.2), 'EFA': np.float64(0.2), 'TLT': np.float64(0.2), 'PFF': np.float64(0.2)}
2025-06-18 12:10:48,364 - INFO - 
========== Tracing for Date: 2021-01-27 ==========
2025-06-18 12:10:48,365 - INFO - --- Step 1: Calculate EMA Metrics ---
2025-06-18 12:10:48,366 - INFO - Input price_data_slice shape: (270, 5)
2025-06-18 12:10:48,366 - INFO - Setting module-level ema_params in ema_model_module: {'st_lookback': 15, 'mt_lookback': 70, 'lt_lookback': 100, 'min_weight': 0.0, 'max_weight': 1.0, 'top_n_strat_specific': 3}
2025-06-18 12:10:48,377 - INFO - Output short_ema_df tail(1):
                 SPY        SHV        EFA         TLT        PFF
Date                                                             
2021-01-27  356.8102  97.171222  66.149945  134.397388  29.690708
2025-06-18 12:10:48,380 - INFO - Output med_ema_df tail (1):
                   SPY        SHV        EFA         TLT        PFF
Date                                                               
2021-01-27  342.457883  97.172881  62.942121  137.342377  29.226306
2025-06-18 12:10:48,382 - INFO - Output long_ema_df tail (1):
                   SPY        SHV        EFA         TLT        PFF
Date                                                               
2021-01-27  335.806309  97.167182  61.574778  138.170351  28.923205
2025-06-18 12:10:48,382 - INFO - --- Step 2: Calculate EMA Ratios ---
2025-06-18 12:10:48,387 - INFO - Output stmtemax_series (latest):
                SPY       SHV       EFA       TLT      PFF
Date                                                      
2021-01-27  1.04191  0.999983  1.050965  0.978557  1.01589
2025-06-18 12:10:48,390 - INFO - Output mtltemax_series (latest):
                 SPY       SHV       EFA       TLT      PFF
Date                                                       
2021-01-27  1.019808  1.000059  1.022206  0.994008  1.01048
2025-06-18 12:10:48,392 - INFO - Output emaxavg_series (latest for ranking):
                 SPY       SHV       EFA       TLT       PFF
Date                                                        
2021-01-27  1.030859  1.000021  1.036585  0.986282  1.013185
2025-06-18 12:10:48,392 - INFO - --- Step 3: Determine Allocation Weights ---
2025-06-18 12:10:48,393 - INFO - Calling ema_model_module.ema_allocation_model_updated with price_data up to 2021-01-27
2025-06-18 12:10:48,393 - INFO - Attempting to call ema_model_module.ema_allocation_model_updated...
2025-06-18 12:10:48,393 - INFO -   ema_model_module.st_lookback=15, mt_lookback=70, lt_lookback=100
2025-06-18 12:10:48,394 - INFO -   ema_model_module.min_weight=0.0, ema_model_module.max_weight=1.0
2025-06-18 12:10:48,394 - INFO -   Trace script's signal_algo: {'default': "'ema'", 'picklist': ['Strategy_EMA', 'Strategy_SMA', 'Strategy_MACD', 'Strategy_RSI', 'Strategy_BB']}, system_top_n: 2 (Note: production module uses its own settings for these)
2025-06-18 12:10:48,407 - INFO - Successfully called ema_model_module.ema_allocation_model_updated.
2025-06-18 12:10:48,409 - INFO - Output final_weights for 2021-01-27:
SPY    0.2
SHV    0.2
EFA    0.2
TLT    0.2
PFF    0.2
2025-06-18 12:10:48,409 - INFO - Final normalized weights for 2021-01-27: {'SPY': np.float64(0.2), 'SHV': np.float64(0.2), 'EFA': np.float64(0.2), 'TLT': np.float64(0.2), 'PFF': np.float64(0.2)}
2025-06-18 12:10:48,410 - INFO - 
========== Tracing for Date: 2021-01-28 ==========
2025-06-18 12:10:48,411 - INFO - --- Step 1: Calculate EMA Metrics ---
2025-06-18 12:10:48,411 - INFO - Input price_data_slice shape: (271, 5)
2025-06-18 12:10:48,411 - INFO - Setting module-level ema_params in ema_model_module: {'st_lookback': 15, 'mt_lookback': 70, 'lt_lookback': 100, 'min_weight': 0.0, 'max_weight': 1.0, 'top_n_strat_specific': 3}
2025-06-18 12:10:48,427 - INFO - Output short_ema_df tail(1):
                   SPY        SHV        EFA         TLT        PFF
Date                                                               
2021-01-28  356.656252  97.170826  66.074374  134.371367  29.679108
2025-06-18 12:10:48,430 - INFO - Output med_ema_df tail (1):
                   SPY        SHV        EFA         TLT        PFF
Date                                                               
2021-01-28  342.827481  97.172745  63.015452  137.253555  29.236774
2025-06-18 12:10:48,433 - INFO - Output long_ema_df tail (1):
                  SPY        SHV        EFA         TLT        PFF
Date                                                              
2021-01-28  336.19784  97.167199  61.653404  138.091517  28.936565
2025-06-18 12:10:48,434 - INFO - --- Step 2: Calculate EMA Ratios ---
2025-06-18 12:10:48,441 - INFO - Output stmtemax_series (latest):
                 SPY      SHV       EFA       TLT       PFF
Date                                                       
2021-01-28  1.040337  0.99998  1.048542  0.979001  1.015129
2025-06-18 12:10:48,446 - INFO - Output mtltemax_series (latest):
                 SPY       SHV       EFA       TLT       PFF
Date                                                        
2021-01-28  1.019719  1.000057  1.022092  0.993932  1.010375
2025-06-18 12:10:48,449 - INFO - Output emaxavg_series (latest for ranking):
                 SPY       SHV       EFA       TLT       PFF
Date                                                        
2021-01-28  1.030028  1.000019  1.035317  0.986466  1.012752
2025-06-18 12:10:48,450 - INFO - --- Step 3: Determine Allocation Weights ---
2025-06-18 12:10:48,450 - INFO - Calling ema_model_module.ema_allocation_model_updated with price_data up to 2021-01-28
2025-06-18 12:10:48,450 - INFO - Attempting to call ema_model_module.ema_allocation_model_updated...
2025-06-18 12:10:48,451 - INFO -   ema_model_module.st_lookback=15, mt_lookback=70, lt_lookback=100
2025-06-18 12:10:48,451 - INFO -   ema_model_module.min_weight=0.0, ema_model_module.max_weight=1.0
2025-06-18 12:10:48,451 - INFO -   Trace script's signal_algo: {'default': "'ema'", 'picklist': ['Strategy_EMA', 'Strategy_SMA', 'Strategy_MACD', 'Strategy_RSI', 'Strategy_BB']}, system_top_n: 2 (Note: production module uses its own settings for these)
2025-06-18 12:10:48,460 - INFO - Successfully called ema_model_module.ema_allocation_model_updated.
2025-06-18 12:10:48,462 - INFO - Output final_weights for 2021-01-28:
SPY    0.2
SHV    0.2
EFA    0.2
TLT    0.2
PFF    0.2
2025-06-18 12:10:48,462 - INFO - Final normalized weights for 2021-01-28: {'SPY': np.float64(0.2), 'SHV': np.float64(0.2), 'EFA': np.float64(0.2), 'TLT': np.float64(0.2), 'PFF': np.float64(0.2)}
2025-06-18 12:10:48,462 - INFO - 
========== Tracing for Date: 2021-01-29 ==========
2025-06-18 12:10:48,464 - INFO - --- Step 1: Calculate EMA Metrics ---
2025-06-18 12:10:48,464 - INFO - Input price_data_slice shape: (272, 5)
2025-06-18 12:10:48,464 - INFO - Setting module-level ema_params in ema_model_module: {'st_lookback': 15, 'mt_lookback': 70, 'lt_lookback': 100, 'min_weight': 0.0, 'max_weight': 1.0, 'top_n_strat_specific': 3}
2025-06-18 12:10:48,479 - INFO - Output short_ema_df tail(1):
                   SPY       SHV        EFA         TLT        PFF
Date                                                              
2021-01-29  355.631727  97.17158  65.838668  134.247682  29.654355
2025-06-18 12:10:48,482 - INFO - Output med_ema_df tail (1):
                   SPY        SHV        EFA         TLT        PFF
Date                                                               
2021-01-29  342.986145  97.172861  63.048502  137.144494  29.243656
2025-06-18 12:10:48,485 - INFO - Output long_ema_df tail (1):
                   SPY       SHV        EFA         TLT        PFF
Date                                                              
2021-01-29  336.440656  97.16739  61.703608  137.998257  28.947348
2025-06-18 12:10:48,511 - INFO - --- Step 2: Calculate EMA Ratios ---
2025-06-18 12:10:48,521 - INFO - Output stmtemax_series (latest):
                 SPY       SHV       EFA       TLT       PFF
Date                                                        
2021-01-29  1.036869  0.999987  1.044254  0.978878  1.014044
2025-06-18 12:10:48,524 - INFO - Output mtltemax_series (latest):
                 SPY       SHV       EFA       TLT       PFF
Date                                                        
2021-01-29  1.019455  1.000056  1.021796  0.993813  1.010236
2025-06-18 12:10:48,530 - INFO - Output emaxavg_series (latest for ranking):
                 SPY       SHV       EFA       TLT      PFF
Date                                                       
2021-01-29  1.028162  1.000022  1.033025  0.986345  1.01214
2025-06-18 12:10:48,531 - INFO - --- Step 3: Determine Allocation Weights ---
2025-06-18 12:10:48,531 - INFO - Calling ema_model_module.ema_allocation_model_updated with price_data up to 2021-01-29
2025-06-18 12:10:48,531 - INFO - Attempting to call ema_model_module.ema_allocation_model_updated...
2025-06-18 12:10:48,531 - INFO -   ema_model_module.st_lookback=15, mt_lookback=70, lt_lookback=100
2025-06-18 12:10:48,531 - INFO -   ema_model_module.min_weight=0.0, ema_model_module.max_weight=1.0
2025-06-18 12:10:48,532 - INFO -   Trace script's signal_algo: {'default': "'ema'", 'picklist': ['Strategy_EMA', 'Strategy_SMA', 'Strategy_MACD', 'Strategy_RSI', 'Strategy_BB']}, system_top_n: 2 (Note: production module uses its own settings for these)
2025-06-18 12:10:48,540 - INFO - Successfully called ema_model_module.ema_allocation_model_updated.
2025-06-18 12:10:48,541 - INFO - Output final_weights for 2021-01-29:
SPY    0.2
SHV    0.2
EFA    0.2
TLT    0.2
PFF    0.2
2025-06-18 12:10:48,541 - INFO - Final normalized weights for 2021-01-29: {'SPY': np.float64(0.2), 'SHV': np.float64(0.2), 'EFA': np.float64(0.2), 'TLT': np.float64(0.2), 'PFF': np.float64(0.2)}
2025-06-18 12:10:48,542 - INFO - 
========== Tracing for Date: 2021-02-01 ==========
2025-06-18 12:10:48,542 - INFO - --- Step 1: Calculate EMA Metrics ---
2025-06-18 12:10:48,543 - INFO - Input price_data_slice shape: (273, 5)
2025-06-18 12:10:48,543 - INFO - Setting module-level ema_params in ema_model_module: {'st_lookback': 15, 'mt_lookback': 70, 'lt_lookback': 100, 'min_weight': 0.0, 'max_weight': 1.0, 'top_n_strat_specific': 3}
2025-06-18 12:10:48,554 - INFO - Output short_ema_df tail(1):
                 SPY       SHV        EFA         TLT        PFF
Date                                                            
2021-02-01  355.4603  97.17114  65.727746  134.158786  29.653223
2025-06-18 12:10:48,557 - INFO - Output med_ema_df tail (1):
                   SPY        SHV        EFA         TLT        PFF
Date                                                               
2021-02-01  343.303727  97.172726  63.102102  137.042861  29.254969
2025-06-18 12:10:48,560 - INFO - Output long_ema_df tail (1):
                  SPY        SHV        EFA         TLT        PFF
Date                                                              
2021-02-01  336.79352  97.167404  61.767919  137.909905  28.961169
2025-06-18 12:10:48,560 - INFO - --- Step 2: Calculate EMA Ratios ---
2025-06-18 12:10:48,565 - INFO - Output stmtemax_series (latest):
                 SPY       SHV       EFA       TLT       PFF
Date                                                        
2021-02-01  1.035411  0.999984  1.041609  0.978955  1.013613
2025-06-18 12:10:48,567 - INFO - Output mtltemax_series (latest):
                SPY       SHV     EFA       TLT       PFF
Date                                                     
2021-02-01  1.01933  1.000055  1.0216  0.993713  1.010145
2025-06-18 12:10:48,569 - INFO - Output emaxavg_series (latest for ranking):
                SPY       SHV       EFA       TLT       PFF
Date                                                       
2021-02-01  1.02737  1.000019  1.031605  0.986334  1.011879
2025-06-18 12:10:48,569 - INFO - --- Step 3: Determine Allocation Weights ---
2025-06-18 12:10:48,570 - INFO - Calling ema_model_module.ema_allocation_model_updated with price_data up to 2021-02-01
2025-06-18 12:10:48,570 - INFO - Attempting to call ema_model_module.ema_allocation_model_updated...
2025-06-18 12:10:48,570 - INFO -   ema_model_module.st_lookback=15, mt_lookback=70, lt_lookback=100
2025-06-18 12:10:48,571 - INFO -   ema_model_module.min_weight=0.0, ema_model_module.max_weight=1.0
2025-06-18 12:10:48,571 - INFO -   Trace script's signal_algo: {'default': "'ema'", 'picklist': ['Strategy_EMA', 'Strategy_SMA', 'Strategy_MACD', 'Strategy_RSI', 'Strategy_BB']}, system_top_n: 2 (Note: production module uses its own settings for these)
2025-06-18 12:10:48,586 - INFO - Successfully called ema_model_module.ema_allocation_model_updated.
2025-06-18 12:10:48,587 - INFO - Output final_weights for 2021-02-01:
SPY    0.2
SHV    0.2
EFA    0.2
TLT    0.2
PFF    0.2
2025-06-18 12:10:48,587 - INFO - Final normalized weights for 2021-02-01: {'SPY': np.float64(0.2), 'SHV': np.float64(0.2), 'EFA': np.float64(0.2), 'TLT': np.float64(0.2), 'PFF': np.float64(0.2)}
2025-06-18 12:10:48,588 - INFO - 
========== Tracing for Date: 2021-02-02 ==========
2025-06-18 12:10:48,589 - INFO - --- Step 1: Calculate EMA Metrics ---
2025-06-18 12:10:48,589 - INFO - Input price_data_slice shape: (274, 5)
2025-06-18 12:10:48,590 - INFO - Setting module-level ema_params in ema_model_module: {'st_lookback': 15, 'mt_lookback': 70, 'lt_lookback': 100, 'min_weight': 0.0, 'max_weight': 1.0, 'top_n_strat_specific': 3}
2025-06-18 12:10:48,605 - INFO - Output short_ema_df tail(1):
                   SPY        SHV        EFA         TLT        PFF
Date                                                               
2021-02-02  355.936477  97.170755  65.712708  133.973383  29.665917
2025-06-18 12:10:48,608 - INFO - Output med_ema_df tail (1):
                   SPY        SHV        EFA         TLT        PFF
Date                                                               
2021-02-02  343.753473  97.172594  63.172675  136.919839  29.269048
2025-06-18 12:10:48,610 - INFO - Output long_ema_df tail (1):
                   SPY        SHV        EFA         TLT        PFF
Date                                                               
2021-02-02  337.238593  97.167417  61.843949  137.806255  28.976884
2025-06-18 12:10:48,610 - INFO - --- Step 2: Calculate EMA Ratios ---
2025-06-18 12:10:48,615 - INFO - Output stmtemax_series (latest):
                 SPY       SHV       EFA      TLT       PFF
Date                                                       
2021-02-02  1.035441  0.999981  1.040208  0.97848  1.013559
2025-06-18 12:10:48,617 - INFO - Output mtltemax_series (latest):
                 SPY       SHV       EFA       TLT       PFF
Date                                                        
2021-02-02  1.019318  1.000053  1.021485  0.993568  1.010083
2025-06-18 12:10:48,620 - INFO - Output emaxavg_series (latest for ranking):
                SPY       SHV       EFA       TLT       PFF
Date                                                       
2021-02-02  1.02738  1.000017  1.030846  0.986024  1.011821
2025-06-18 12:10:48,621 - INFO - --- Step 3: Determine Allocation Weights ---
2025-06-18 12:10:48,622 - INFO - Calling ema_model_module.ema_allocation_model_updated with price_data up to 2021-02-02
2025-06-18 12:10:48,622 - INFO - Attempting to call ema_model_module.ema_allocation_model_updated...
2025-06-18 12:10:48,622 - INFO -   ema_model_module.st_lookback=15, mt_lookback=70, lt_lookback=100
2025-06-18 12:10:48,623 - INFO -   ema_model_module.min_weight=0.0, ema_model_module.max_weight=1.0
2025-06-18 12:10:48,623 - INFO -   Trace script's signal_algo: {'default': "'ema'", 'picklist': ['Strategy_EMA', 'Strategy_SMA', 'Strategy_MACD', 'Strategy_RSI', 'Strategy_BB']}, system_top_n: 2 (Note: production module uses its own settings for these)
2025-06-18 12:10:48,637 - INFO - Successfully called ema_model_module.ema_allocation_model_updated.
2025-06-18 12:10:48,639 - INFO - Output final_weights for 2021-02-02:
SPY    0.2
SHV    0.2
EFA    0.2
TLT    0.2
PFF    0.2
2025-06-18 12:10:48,640 - INFO - Final normalized weights for 2021-02-02: {'SPY': np.float64(0.2), 'SHV': np.float64(0.2), 'EFA': np.float64(0.2), 'TLT': np.float64(0.2), 'PFF': np.float64(0.2)}
2025-06-18 12:10:48,640 - INFO - 
========== Tracing for Date: 2021-02-03 ==========
2025-06-18 12:10:48,641 - INFO - --- Step 1: Calculate EMA Metrics ---
2025-06-18 12:10:48,641 - INFO - Input price_data_slice shape: (275, 5)
2025-06-18 12:10:48,642 - INFO - Setting module-level ema_params in ema_model_module: {'st_lookback': 15, 'mt_lookback': 70, 'lt_lookback': 100, 'min_weight': 0.0, 'max_weight': 1.0, 'top_n_strat_specific': 3}
2025-06-18 12:10:48,656 - INFO - Output short_ema_df tail(1):
                   SPY        SHV        EFA         TLT        PFF
Date                                                               
2021-02-03  356.388433  97.172616  65.719501  133.664003  29.663339
2025-06-18 12:10:48,659 - INFO - Output med_ema_df tail (1):
                   SPY        SHV        EFA        TLT        PFF
Date                                                              
2021-02-03  344.198506  97.172962  63.245756  136.76712  29.279647
2025-06-18 12:10:48,661 - INFO - Output long_ema_df tail (1):
                   SPY        SHV        EFA         TLT       PFF
Date                                                              
2021-02-03  337.680446  97.167778  61.921634  137.681346  28.99012
2025-06-18 12:10:48,661 - INFO - --- Step 2: Calculate EMA Ratios ---
2025-06-18 12:10:48,666 - INFO - Output stmtemax_series (latest):
                 SPY       SHV       EFA       TLT       PFF
Date                                                        
2021-02-03  1.035415  0.999996  1.039113  0.977311  1.013104
2025-06-18 12:10:48,671 - INFO - Output mtltemax_series (latest):
                 SPY       SHV       EFA      TLT       PFF
Date                                                       
2021-02-03  1.019302  1.000053  1.021384  0.99336  1.009987
2025-06-18 12:10:48,674 - INFO - Output emaxavg_series (latest for ranking):
                 SPY       SHV       EFA       TLT       PFF
Date                                                        
2021-02-03  1.027359  1.000025  1.030249  0.985335  1.011546
2025-06-18 12:10:48,675 - INFO - --- Step 3: Determine Allocation Weights ---
2025-06-18 12:10:48,675 - INFO - Calling ema_model_module.ema_allocation_model_updated with price_data up to 2021-02-03
2025-06-18 12:10:48,675 - INFO - Attempting to call ema_model_module.ema_allocation_model_updated...
2025-06-18 12:10:48,676 - INFO -   ema_model_module.st_lookback=15, mt_lookback=70, lt_lookback=100
2025-06-18 12:10:48,676 - INFO -   ema_model_module.min_weight=0.0, ema_model_module.max_weight=1.0
2025-06-18 12:10:48,676 - INFO -   Trace script's signal_algo: {'default': "'ema'", 'picklist': ['Strategy_EMA', 'Strategy_SMA', 'Strategy_MACD', 'Strategy_RSI', 'Strategy_BB']}, system_top_n: 2 (Note: production module uses its own settings for these)
2025-06-18 12:10:48,686 - INFO - Successfully called ema_model_module.ema_allocation_model_updated.
2025-06-18 12:10:48,688 - INFO - Output final_weights for 2021-02-03:
SPY    0.2
SHV    0.2
EFA    0.2
TLT    0.2
PFF    0.2
2025-06-18 12:10:48,688 - INFO - Final normalized weights for 2021-02-03: {'SPY': np.float64(0.2), 'SHV': np.float64(0.2), 'EFA': np.float64(0.2), 'TLT': np.float64(0.2), 'PFF': np.float64(0.2)}
2025-06-18 12:10:48,688 - INFO - 
========== Tracing for Date: 2021-02-04 ==========
2025-06-18 12:10:48,689 - INFO - --- Step 1: Calculate EMA Metrics ---
2025-06-18 12:10:48,690 - INFO - Input price_data_slice shape: (276, 5)
2025-06-18 12:10:48,690 - INFO - Setting module-level ema_params in ema_model_module: {'st_lookback': 15, 'mt_lookback': 70, 'lt_lookback': 100, 'min_weight': 0.0, 'max_weight': 1.0, 'top_n_strat_specific': 3}
2025-06-18 12:10:48,739 - INFO - Output short_ema_df tail(1):
                   SPY        SHV        EFA         TLT        PFF
Date                                                               
2021-02-04  357.294724  97.173146  65.734312  133.349374  29.670859
2025-06-18 12:10:48,743 - INFO - Output med_ema_df tail (1):
                   SPY        SHV        EFA         TLT       PFF
Date                                                              
2021-02-04  344.746119  97.173072  63.318777  136.608806  29.29215
2025-06-18 12:10:48,747 - INFO - Output long_ema_df tail (1):
                   SPY        SHV        EFA         TLT        PFF
Date                                                               
2021-02-04  338.194472  97.167957  61.999186  137.551952  29.004642
2025-06-18 12:10:48,747 - INFO - --- Step 2: Calculate EMA Ratios ---
2025-06-18 12:10:48,765 - INFO - Output stmtemax_series (latest):
               SPY       SHV       EFA      TLT       PFF
Date                                                     
2021-02-04  1.0364  1.000001  1.038149  0.97614  1.012929
2025-06-18 12:10:48,769 - INFO - Output mtltemax_series (latest):
                 SPY       SHV       EFA       TLT       PFF
Date                                                        
2021-02-04  1.019372  1.000053  1.021284  0.993143  1.009912
2025-06-18 12:10:48,772 - INFO - Output emaxavg_series (latest for ranking):
                 SPY       SHV       EFA       TLT       PFF
Date                                                        
2021-02-04  1.027886  1.000027  1.029716  0.984642  1.011421
2025-06-18 12:10:48,773 - INFO - --- Step 3: Determine Allocation Weights ---
2025-06-18 12:10:48,774 - INFO - Calling ema_model_module.ema_allocation_model_updated with price_data up to 2021-02-04
2025-06-18 12:10:48,774 - INFO - Attempting to call ema_model_module.ema_allocation_model_updated...
2025-06-18 12:10:48,774 - INFO -   ema_model_module.st_lookback=15, mt_lookback=70, lt_lookback=100
2025-06-18 12:10:48,775 - INFO -   ema_model_module.min_weight=0.0, ema_model_module.max_weight=1.0
2025-06-18 12:10:48,775 - INFO -   Trace script's signal_algo: {'default': "'ema'", 'picklist': ['Strategy_EMA', 'Strategy_SMA', 'Strategy_MACD', 'Strategy_RSI', 'Strategy_BB']}, system_top_n: 2 (Note: production module uses its own settings for these)
2025-06-18 12:10:48,792 - INFO - Successfully called ema_model_module.ema_allocation_model_updated.
2025-06-18 12:10:48,794 - INFO - Output final_weights for 2021-02-04:
SPY    0.2
SHV    0.2
EFA    0.2
TLT    0.2
PFF    0.2
2025-06-18 12:10:48,794 - INFO - Final normalized weights for 2021-02-04: {'SPY': np.float64(0.2), 'SHV': np.float64(0.2), 'EFA': np.float64(0.2), 'TLT': np.float64(0.2), 'PFF': np.float64(0.2)}
2025-06-18 12:10:48,794 - INFO - 
========== Tracing for Date: 2021-02-05 ==========
2025-06-18 12:10:48,795 - INFO - --- Step 1: Calculate EMA Metrics ---
2025-06-18 12:10:48,795 - INFO - Input price_data_slice shape: (277, 5)
2025-06-18 12:10:48,795 - INFO - Setting module-level ema_params in ema_model_module: {'st_lookback': 15, 'mt_lookback': 70, 'lt_lookback': 100, 'min_weight': 0.0, 'max_weight': 1.0, 'top_n_strat_specific': 3}
2025-06-18 12:10:48,806 - INFO - Output short_ema_df tail(1):
                   SPY        SHV        EFA       TLT        PFF
Date                                                             
2021-02-05  358.266631  97.174707  65.798257  132.9368  29.686235
2025-06-18 12:10:48,813 - INFO - Output med_ema_df tail (1):
                   SPY        SHV       EFA         TLT        PFF
Date                                                              
2021-02-05  345.318622  97.173426  63.40123  136.424017  29.306283
2025-06-18 12:10:48,818 - INFO - Output long_ema_df tail (1):
                  SPY        SHV        EFA         TLT       PFF
Date                                                             
2021-02-05  338.72666  97.168307  62.083279  137.403375  29.02027
2025-06-18 12:10:48,819 - INFO - --- Step 2: Calculate EMA Ratios ---
2025-06-18 12:10:48,827 - INFO - Output stmtemax_series (latest):
                 SPY       SHV       EFA       TLT       PFF
Date                                                        
2021-02-05  1.037496  1.000013  1.037807  0.974438  1.012965
2025-06-18 12:10:48,831 - INFO - Output mtltemax_series (latest):
                 SPY       SHV       EFA       TLT       PFF
Date                                                        
2021-02-05  1.019461  1.000053  1.021229  0.992872  1.009856
2025-06-18 12:10:48,834 - INFO - Output emaxavg_series (latest for ranking):
                 SPY       SHV       EFA       TLT      PFF
Date                                                       
2021-02-05  1.028478  1.000033  1.029518  0.983655  1.01141
2025-06-18 12:10:48,834 - INFO - --- Step 3: Determine Allocation Weights ---
2025-06-18 12:10:48,835 - INFO - Calling ema_model_module.ema_allocation_model_updated with price_data up to 2021-02-05
2025-06-18 12:10:48,835 - INFO - Attempting to call ema_model_module.ema_allocation_model_updated...
2025-06-18 12:10:48,835 - INFO -   ema_model_module.st_lookback=15, mt_lookback=70, lt_lookback=100
2025-06-18 12:10:48,835 - INFO -   ema_model_module.min_weight=0.0, ema_model_module.max_weight=1.0
2025-06-18 12:10:48,836 - INFO -   Trace script's signal_algo: {'default': "'ema'", 'picklist': ['Strategy_EMA', 'Strategy_SMA', 'Strategy_MACD', 'Strategy_RSI', 'Strategy_BB']}, system_top_n: 2 (Note: production module uses its own settings for these)
2025-06-18 12:10:48,848 - INFO - Successfully called ema_model_module.ema_allocation_model_updated.
2025-06-18 12:10:48,850 - INFO - Output final_weights for 2021-02-05:
SPY    0.2
SHV    0.2
EFA    0.2
TLT    0.2
PFF    0.2
2025-06-18 12:10:48,850 - INFO - Final normalized weights for 2021-02-05: {'SPY': np.float64(0.2), 'SHV': np.float64(0.2), 'EFA': np.float64(0.2), 'TLT': np.float64(0.2), 'PFF': np.float64(0.2)}
2025-06-18 12:10:48,850 - INFO - 
========== Tracing for Date: 2021-02-08 ==========
2025-06-18 12:10:48,851 - INFO - --- Step 1: Calculate EMA Metrics ---
2025-06-18 12:10:48,851 - INFO - Input price_data_slice shape: (278, 5)
2025-06-18 12:10:48,852 - INFO - Setting module-level ema_params in ema_model_module: {'st_lookback': 15, 'mt_lookback': 70, 'lt_lookback': 100, 'min_weight': 0.0, 'max_weight': 1.0, 'top_n_strat_specific': 3}
2025-06-18 12:10:48,864 - INFO - Output short_ema_df tail(1):
                   SPY        SHV        EFA         TLT      PFF
Date                                                             
2021-02-08  359.446616  97.174976  65.911846  132.647175  29.7036
2025-06-18 12:10:48,867 - INFO - Output med_ema_df tail (1):
                   SPY        SHV        EFA         TLT        PFF
Date                                                               
2021-02-08  345.949266  97.173522  63.494349  136.260518  29.320899
2025-06-18 12:10:48,870 - INFO - Output long_ema_df tail (1):
                   SPY        SHV        EFA         TLT        PFF
Date                                                               
2021-02-08  339.300518  97.168477  62.174837  137.269046  29.036209
2025-06-18 12:10:48,870 - INFO - --- Step 2: Calculate EMA Ratios ---
2025-06-18 12:10:48,875 - INFO - Output stmtemax_series (latest):
                 SPY       SHV       EFA       TLT       PFF
Date                                                        
2021-02-08  1.039015  1.000015  1.038074  0.973482  1.013052
2025-06-18 12:10:48,877 - INFO - Output mtltemax_series (latest):
                 SPY       SHV       EFA       TLT       PFF
Date                                                        
2021-02-08  1.019595  1.000052  1.021223  0.992653  1.009805
2025-06-18 12:10:48,879 - INFO - Output emaxavg_series (latest for ranking):
                 SPY       SHV       EFA       TLT       PFF
Date                                                        
2021-02-08  1.029305  1.000033  1.029648  0.983068  1.011428
2025-06-18 12:10:48,880 - INFO - --- Step 3: Determine Allocation Weights ---
2025-06-18 12:10:48,880 - INFO - Calling ema_model_module.ema_allocation_model_updated with price_data up to 2021-02-08
2025-06-18 12:10:48,881 - INFO - Attempting to call ema_model_module.ema_allocation_model_updated...
2025-06-18 12:10:48,881 - INFO -   ema_model_module.st_lookback=15, mt_lookback=70, lt_lookback=100
2025-06-18 12:10:48,881 - INFO -   ema_model_module.min_weight=0.0, ema_model_module.max_weight=1.0
2025-06-18 12:10:48,881 - INFO -   Trace script's signal_algo: {'default': "'ema'", 'picklist': ['Strategy_EMA', 'Strategy_SMA', 'Strategy_MACD', 'Strategy_RSI', 'Strategy_BB']}, system_top_n: 2 (Note: production module uses its own settings for these)
2025-06-18 12:10:48,891 - INFO - Successfully called ema_model_module.ema_allocation_model_updated.
2025-06-18 12:10:48,892 - INFO - Output final_weights for 2021-02-08:
SPY    0.2
SHV    0.2
EFA    0.2
TLT    0.2
PFF    0.2
2025-06-18 12:10:48,893 - INFO - Final normalized weights for 2021-02-08: {'SPY': np.float64(0.2), 'SHV': np.float64(0.2), 'EFA': np.float64(0.2), 'TLT': np.float64(0.2), 'PFF': np.float64(0.2)}
2025-06-18 12:10:48,893 - INFO - 
========== Tracing for Date: 2021-02-09 ==========
2025-06-18 12:10:48,894 - INFO - --- Step 1: Calculate EMA Metrics ---
2025-06-18 12:10:48,894 - INFO - Input price_data_slice shape: (279, 5)
2025-06-18 12:10:48,894 - INFO - Setting module-level ema_params in ema_model_module: {'st_lookback': 15, 'mt_lookback': 70, 'lt_lookback': 100, 'min_weight': 0.0, 'max_weight': 1.0, 'top_n_strat_specific': 3}
2025-06-18 12:10:48,905 - INFO - Output short_ema_df tail(1):
                   SPY        SHV        EFA        TLT        PFF
Date                                                              
2021-02-09  360.448494  97.176309  66.056679  132.40583  29.721726
2025-06-18 12:10:48,909 - INFO - Output med_ema_df tail (1):
                   SPY        SHV        EFA         TLT        PFF
Date                                                               
2021-02-09  346.555249  97.173864  63.595086  136.104346  29.335764
2025-06-18 12:10:48,912 - INFO - Output long_ema_df tail (1):
                   SPY        SHV        EFA         TLT        PFF
Date                                                               
2021-02-09  339.858164  97.168817  62.271781  137.139291  29.052296
2025-06-18 12:10:48,913 - INFO - --- Step 2: Calculate EMA Ratios ---
2025-06-18 12:10:48,917 - INFO - Output stmtemax_series (latest):
                SPY       SHV       EFA       TLT       PFF
Date                                                       
2021-02-09  1.04009  1.000025  1.038707  0.972826  1.013157
2025-06-18 12:10:48,920 - INFO - Output mtltemax_series (latest):
                 SPY       SHV      EFA       TLT       PFF
Date                                                       
2021-02-09  1.019706  1.000052  1.02125  0.992453  1.009757
2025-06-18 12:10:48,922 - INFO - Output emaxavg_series (latest for ranking):
                 SPY       SHV       EFA      TLT       PFF
Date                                                       
2021-02-09  1.029898  1.000039  1.029979  0.98264  1.011457
2025-06-18 12:10:48,922 - INFO - --- Step 3: Determine Allocation Weights ---
2025-06-18 12:10:48,923 - INFO - Calling ema_model_module.ema_allocation_model_updated with price_data up to 2021-02-09
2025-06-18 12:10:48,923 - INFO - Attempting to call ema_model_module.ema_allocation_model_updated...
2025-06-18 12:10:48,923 - INFO -   ema_model_module.st_lookback=15, mt_lookback=70, lt_lookback=100
2025-06-18 12:10:48,924 - INFO -   ema_model_module.min_weight=0.0, ema_model_module.max_weight=1.0
2025-06-18 12:10:48,924 - INFO -   Trace script's signal_algo: {'default': "'ema'", 'picklist': ['Strategy_EMA', 'Strategy_SMA', 'Strategy_MACD', 'Strategy_RSI', 'Strategy_BB']}, system_top_n: 2 (Note: production module uses its own settings for these)
2025-06-18 12:10:48,937 - INFO - Successfully called ema_model_module.ema_allocation_model_updated.
2025-06-18 12:10:48,938 - INFO - Output final_weights for 2021-02-09:
SPY    0.2
SHV    0.2
EFA    0.2
TLT    0.2
PFF    0.2
2025-06-18 12:10:48,938 - INFO - Final normalized weights for 2021-02-09: {'SPY': np.float64(0.2), 'SHV': np.float64(0.2), 'EFA': np.float64(0.2), 'TLT': np.float64(0.2), 'PFF': np.float64(0.2)}
2025-06-18 12:10:48,939 - INFO - 
========== Tracing for Date: 2021-02-10 ==========
2025-06-18 12:10:48,939 - INFO - --- Step 1: Calculate EMA Metrics ---
2025-06-18 12:10:48,940 - INFO - Input price_data_slice shape: (280, 5)
2025-06-18 12:10:48,940 - INFO - Setting module-level ema_params in ema_model_module: {'st_lookback': 15, 'mt_lookback': 70, 'lt_lookback': 100, 'min_weight': 0.0, 'max_weight': 1.0, 'top_n_strat_specific': 3}
2025-06-18 12:10:48,955 - INFO - Output short_ema_df tail(1):
                   SPY        SHV       EFA         TLT        PFF
Date                                                              
2021-02-10  361.305133  97.177475  66.15459  132.303381  29.735633
2025-06-18 12:10:48,957 - INFO - Output med_ema_df tail (1):
                   SPY        SHV        EFA         TLT       PFF
Date                                                              
2021-02-10  347.139653  97.174195  63.686491  135.977075  29.34977
2025-06-18 12:10:48,960 - INFO - Output long_ema_df tail (1):
                   SPY       SHV       EFA        TLT        PFF
Date                                                            
2021-02-10  340.401599  97.16915  62.36224  137.02933  29.067755
2025-06-18 12:10:48,960 - INFO - --- Step 2: Calculate EMA Ratios ---
2025-06-18 12:10:48,965 - INFO - Output stmtemax_series (latest):
                 SPY       SHV       EFA       TLT       PFF
Date                                                        
2021-02-10  1.040806  1.000034  1.038754  0.972983  1.013147
2025-06-18 12:10:48,969 - INFO - Output mtltemax_series (latest):
                 SPY       SHV       EFA       TLT       PFF
Date                                                        
2021-02-10  1.019794  1.000052  1.021235  0.992321  1.009702
2025-06-18 12:10:48,973 - INFO - Output emaxavg_series (latest for ranking):
               SPY       SHV       EFA       TLT       PFF
Date                                                      
2021-02-10  1.0303  1.000043  1.029994  0.982652  1.011425
2025-06-18 12:10:48,974 - INFO - --- Step 3: Determine Allocation Weights ---
2025-06-18 12:10:48,974 - INFO - Calling ema_model_module.ema_allocation_model_updated with price_data up to 2021-02-10
2025-06-18 12:10:48,975 - INFO - Attempting to call ema_model_module.ema_allocation_model_updated...
2025-06-18 12:10:48,975 - INFO -   ema_model_module.st_lookback=15, mt_lookback=70, lt_lookback=100
2025-06-18 12:10:48,975 - INFO -   ema_model_module.min_weight=0.0, ema_model_module.max_weight=1.0
2025-06-18 12:10:48,976 - INFO -   Trace script's signal_algo: {'default': "'ema'", 'picklist': ['Strategy_EMA', 'Strategy_SMA', 'Strategy_MACD', 'Strategy_RSI', 'Strategy_BB']}, system_top_n: 2 (Note: production module uses its own settings for these)
2025-06-18 12:10:48,985 - INFO - Successfully called ema_model_module.ema_allocation_model_updated.
2025-06-18 12:10:48,986 - INFO - Output final_weights for 2021-02-10:
SPY    0.2
SHV    0.2
EFA    0.2
TLT    0.2
PFF    0.2
2025-06-18 12:10:48,986 - INFO - Final normalized weights for 2021-02-10: {'SPY': np.float64(0.2), 'SHV': np.float64(0.2), 'EFA': np.float64(0.2), 'TLT': np.float64(0.2), 'PFF': np.float64(0.2)}
2025-06-18 12:10:48,987 - INFO - 
========== Tracing for Date: 2021-02-11 ==========
2025-06-18 12:10:48,987 - INFO - --- Step 1: Calculate EMA Metrics ---
2025-06-18 12:10:48,988 - INFO - Input price_data_slice shape: (281, 5)
2025-06-18 12:10:48,988 - INFO - Setting module-level ema_params in ema_model_module: {'st_lookback': 15, 'mt_lookback': 70, 'lt_lookback': 100, 'min_weight': 0.0, 'max_weight': 1.0, 'top_n_strat_specific': 3}
2025-06-18 12:10:49,002 - INFO - Output short_ema_df tail(1):
                   SPY        SHV        EFA         TLT        PFF
Date                                                               
2021-02-11  362.128834  97.177398  66.296789  132.123687  29.739003
2025-06-18 12:10:49,004 - INFO - Output med_ema_df tail (1):
                   SPY       SHV       EFA         TLT        PFF
Date                                                             
2021-02-11  347.724304  97.17427  63.78806  135.833097  29.361399
2025-06-18 12:10:49,006 - INFO - Output long_ema_df tail (1):
                   SPY        SHV        EFA        TLT        PFF
Date                                                              
2021-02-11  340.946017  97.169302  62.459863  136.90728  29.081514
2025-06-18 12:10:49,006 - INFO - --- Step 2: Calculate EMA Ratios ---
2025-06-18 12:10:49,011 - INFO - Output stmtemax_series (latest):
                 SPY       SHV       EFA       TLT       PFF
Date                                                        
2021-02-11  1.041425  1.000032  1.039329  0.972691  1.012861
2025-06-18 12:10:49,015 - INFO - Output mtltemax_series (latest):
                 SPY       SHV       EFA       TLT       PFF
Date                                                        
2021-02-11  1.019881  1.000051  1.021265  0.992154  1.009624
2025-06-18 12:10:49,018 - INFO - Output emaxavg_series (latest for ranking):
                 SPY       SHV       EFA       TLT       PFF
Date                                                        
2021-02-11  1.030653  1.000042  1.030297  0.982423  1.011242
2025-06-18 12:10:49,019 - INFO - --- Step 3: Determine Allocation Weights ---
2025-06-18 12:10:49,020 - INFO - Calling ema_model_module.ema_allocation_model_updated with price_data up to 2021-02-11
2025-06-18 12:10:49,020 - INFO - Attempting to call ema_model_module.ema_allocation_model_updated...
2025-06-18 12:10:49,020 - INFO -   ema_model_module.st_lookback=15, mt_lookback=70, lt_lookback=100
2025-06-18 12:10:49,020 - INFO -   ema_model_module.min_weight=0.0, ema_model_module.max_weight=1.0
2025-06-18 12:10:49,021 - INFO -   Trace script's signal_algo: {'default': "'ema'", 'picklist': ['Strategy_EMA', 'Strategy_SMA', 'Strategy_MACD', 'Strategy_RSI', 'Strategy_BB']}, system_top_n: 2 (Note: production module uses its own settings for these)
2025-06-18 12:10:49,033 - INFO - Successfully called ema_model_module.ema_allocation_model_updated.
2025-06-18 12:10:49,034 - INFO - Output final_weights for 2021-02-11:
SPY    0.2
SHV    0.2
EFA    0.2
TLT    0.2
PFF    0.2
2025-06-18 12:10:49,034 - INFO - Final normalized weights for 2021-02-11: {'SPY': np.float64(0.2), 'SHV': np.float64(0.2), 'EFA': np.float64(0.2), 'TLT': np.float64(0.2), 'PFF': np.float64(0.2)}
2025-06-18 12:10:49,035 - INFO - 
========== Tracing for Date: 2021-02-12 ==========
2025-06-18 12:10:49,035 - INFO - --- Step 1: Calculate EMA Metrics ---
2025-06-18 12:10:49,036 - INFO - Input price_data_slice shape: (282, 5)
2025-06-18 12:10:49,036 - INFO - Setting module-level ema_params in ema_model_module: {'st_lookback': 15, 'mt_lookback': 70, 'lt_lookback': 100, 'min_weight': 0.0, 'max_weight': 1.0, 'top_n_strat_specific': 3}
2025-06-18 12:10:49,049 - INFO - Output short_ema_df tail(1):
                   SPY       SHV        EFA         TLT        PFF
Date                                                              
2021-02-12  363.076742  97.17733  66.467766  131.763288  29.731199
2025-06-18 12:10:49,051 - INFO - Output med_ema_df tail (1):
                   SPY        SHV        EFA         TLT        PFF
Date                                                               
2021-02-12  348.343678  97.174343  63.897259  135.647389  29.370277
2025-06-18 12:10:49,053 - INFO - Output long_ema_df tail (1):
                   SPY        SHV        EFA         TLT        PFF
Date                                                               
2021-02-12  341.515643  97.169452  62.562927  136.755463  29.093297
2025-06-18 12:10:49,053 - INFO - --- Step 2: Calculate EMA Ratios ---
2025-06-18 12:10:49,059 - INFO - Output stmtemax_series (latest):
                 SPY       SHV       EFA       TLT       PFF
Date                                                        
2021-02-12  1.042295  1.000031  1.040229  0.971366  1.012289
2025-06-18 12:10:49,063 - INFO - Output mtltemax_series (latest):
                 SPY      SHV       EFA       TLT      PFF
Date                                                      
2021-02-12  1.019993  1.00005  1.021328  0.991897  1.00952
2025-06-18 12:10:49,065 - INFO - Output emaxavg_series (latest for ranking):
                 SPY       SHV       EFA       TLT       PFF
Date                                                        
2021-02-12  1.031144  1.000041  1.030778  0.981632  1.010905
2025-06-18 12:10:49,066 - INFO - --- Step 3: Determine Allocation Weights ---
2025-06-18 12:10:49,066 - INFO - Calling ema_model_module.ema_allocation_model_updated with price_data up to 2021-02-12
2025-06-18 12:10:49,067 - INFO - Attempting to call ema_model_module.ema_allocation_model_updated...
2025-06-18 12:10:49,067 - INFO -   ema_model_module.st_lookback=15, mt_lookback=70, lt_lookback=100
2025-06-18 12:10:49,067 - INFO -   ema_model_module.min_weight=0.0, ema_model_module.max_weight=1.0
2025-06-18 12:10:49,067 - INFO -   Trace script's signal_algo: {'default': "'ema'", 'picklist': ['Strategy_EMA', 'Strategy_SMA', 'Strategy_MACD', 'Strategy_RSI', 'Strategy_BB']}, system_top_n: 2 (Note: production module uses its own settings for these)
2025-06-18 12:10:49,076 - INFO - Successfully called ema_model_module.ema_allocation_model_updated.
2025-06-18 12:10:49,077 - INFO - Output final_weights for 2021-02-12:
SPY    0.2
SHV    0.2
EFA    0.2
TLT    0.2
PFF    0.2
2025-06-18 12:10:49,078 - INFO - Final normalized weights for 2021-02-12: {'SPY': np.float64(0.2), 'SHV': np.float64(0.2), 'EFA': np.float64(0.2), 'TLT': np.float64(0.2), 'PFF': np.float64(0.2)}
2025-06-18 12:10:49,078 - INFO - 
========== Tracing for Date: 2021-02-16 ==========
2025-06-18 12:10:49,079 - INFO - --- Step 1: Calculate EMA Metrics ---
2025-06-18 12:10:49,079 - INFO - Input price_data_slice shape: (283, 5)
2025-06-18 12:10:49,079 - INFO - Setting module-level ema_params in ema_model_module: {'st_lookback': 15, 'mt_lookback': 70, 'lt_lookback': 100, 'min_weight': 0.0, 'max_weight': 1.0, 'top_n_strat_specific': 3}
2025-06-18 12:10:49,094 - INFO - Output short_ema_df tail(1):
                  SPY        SHV        EFA         TLT      PFF
Date                                                            
2021-02-16  363.86613  97.177271  66.678333  131.201951  29.7058
2025-06-18 12:10:49,097 - INFO - Output med_ema_df tail (1):
                   SPY        SHV        EFA        TLT       PFF
Date                                                             
2021-02-16  348.936584  97.174414  64.017119  135.41148  29.37472
2025-06-18 12:10:49,099 - INFO - Output long_ema_df tail (1):
                   SPY        SHV        EFA         TLT        PFF
Date                                                               
2021-02-16  342.067647  97.169599  62.673607  136.567683  29.101905
2025-06-18 12:10:49,099 - INFO - --- Step 2: Calculate EMA Ratios ---
2025-06-18 12:10:49,104 - INFO - Output stmtemax_series (latest):
                 SPY       SHV      EFA       TLT       PFF
Date                                                       
2021-02-16  1.042786  1.000029  1.04157  0.968913  1.011271
2025-06-18 12:10:49,109 - INFO - Output mtltemax_series (latest):
                 SPY      SHV       EFA       TLT       PFF
Date                                                       
2021-02-16  1.020081  1.00005  1.021437  0.991534  1.009374
2025-06-18 12:10:49,111 - INFO - Output emaxavg_series (latest for ranking):
                 SPY       SHV       EFA       TLT       PFF
Date                                                        
2021-02-16  1.031433  1.000039  1.031503  0.980223  1.010323
2025-06-18 12:10:49,112 - INFO - --- Step 3: Determine Allocation Weights ---
2025-06-18 12:10:49,112 - INFO - Calling ema_model_module.ema_allocation_model_updated with price_data up to 2021-02-16
2025-06-18 12:10:49,112 - INFO - Attempting to call ema_model_module.ema_allocation_model_updated...
2025-06-18 12:10:49,112 - INFO -   ema_model_module.st_lookback=15, mt_lookback=70, lt_lookback=100
2025-06-18 12:10:49,113 - INFO -   ema_model_module.min_weight=0.0, ema_model_module.max_weight=1.0
2025-06-18 12:10:49,113 - INFO -   Trace script's signal_algo: {'default': "'ema'", 'picklist': ['Strategy_EMA', 'Strategy_SMA', 'Strategy_MACD', 'Strategy_RSI', 'Strategy_BB']}, system_top_n: 2 (Note: production module uses its own settings for these)
2025-06-18 12:10:49,121 - INFO - Successfully called ema_model_module.ema_allocation_model_updated.
2025-06-18 12:10:49,124 - INFO - Output final_weights for 2021-02-16:
SPY    0.2
SHV    0.2
EFA    0.2
TLT    0.2
PFF    0.2
2025-06-18 12:10:49,125 - INFO - Final normalized weights for 2021-02-16: {'SPY': np.float64(0.2), 'SHV': np.float64(0.2), 'EFA': np.float64(0.2), 'TLT': np.float64(0.2), 'PFF': np.float64(0.2)}
2025-06-18 12:10:49,125 - INFO - 
========== Tracing for Date: 2021-02-17 ==========
2025-06-18 12:10:49,126 - INFO - --- Step 1: Calculate EMA Metrics ---
2025-06-18 12:10:49,127 - INFO - Input price_data_slice shape: (284, 5)
2025-06-18 12:10:49,127 - INFO - Setting module-level ema_params in ema_model_module: {'st_lookback': 15, 'mt_lookback': 70, 'lt_lookback': 100, 'min_weight': 0.0, 'max_weight': 1.0, 'top_n_strat_specific': 3}
2025-06-18 12:10:49,139 - INFO - Output short_ema_df tail(1):
                   SPY        SHV        EFA         TLT        PFF
Date                                                               
2021-02-17  364.567449  97.177219  66.812702  130.811816  29.681619
2025-06-18 12:10:49,142 - INFO - Output med_ema_df tail (1):
                   SPY        SHV        EFA         TLT        PFF
Date                                                               
2021-02-17  349.515179  97.174483  64.122363  135.204984  29.378597
2025-06-18 12:10:49,145 - INFO - Output long_ema_df tail (1):
                 SPY        SHV        EFA         TLT        PFF
Date                                                             
2021-02-17  342.6104  97.169742  62.774195  136.399627  29.110033
2025-06-18 12:10:49,146 - INFO - --- Step 2: Calculate EMA Ratios ---
2025-06-18 12:10:49,152 - INFO - Output stmtemax_series (latest):
                 SPY       SHV       EFA       TLT       PFF
Date                                                        
2021-02-17  1.043066  1.000028  1.041956  0.967507  1.010314
2025-06-18 12:10:49,154 - INFO - Output mtltemax_series (latest):
                 SPY       SHV       EFA       TLT       PFF
Date                                                        
2021-02-17  1.020153  1.000049  1.021476  0.991242  1.009226
2025-06-18 12:10:49,156 - INFO - Output emaxavg_series (latest for ranking):
                SPY       SHV       EFA       TLT      PFF
Date                                                      
2021-02-17  1.03161  1.000038  1.031716  0.979374  1.00977
2025-06-18 12:10:49,157 - INFO - --- Step 3: Determine Allocation Weights ---
2025-06-18 12:10:49,157 - INFO - Calling ema_model_module.ema_allocation_model_updated with price_data up to 2021-02-17
2025-06-18 12:10:49,157 - INFO - Attempting to call ema_model_module.ema_allocation_model_updated...
2025-06-18 12:10:49,157 - INFO -   ema_model_module.st_lookback=15, mt_lookback=70, lt_lookback=100
2025-06-18 12:10:49,157 - INFO -   ema_model_module.min_weight=0.0, ema_model_module.max_weight=1.0
2025-06-18 12:10:49,158 - INFO -   Trace script's signal_algo: {'default': "'ema'", 'picklist': ['Strategy_EMA', 'Strategy_SMA', 'Strategy_MACD', 'Strategy_RSI', 'Strategy_BB']}, system_top_n: 2 (Note: production module uses its own settings for these)
2025-06-18 12:10:49,167 - INFO - Successfully called ema_model_module.ema_allocation_model_updated.
2025-06-18 12:10:49,169 - INFO - Output final_weights for 2021-02-17:
SPY    0.2
SHV    0.2
EFA    0.2
TLT    0.2
PFF    0.2
2025-06-18 12:10:49,170 - INFO - Final normalized weights for 2021-02-17: {'SPY': np.float64(0.2), 'SHV': np.float64(0.2), 'EFA': np.float64(0.2), 'TLT': np.float64(0.2), 'PFF': np.float64(0.2)}
2025-06-18 12:10:49,170 - INFO - 
========== Tracing for Date: 2021-02-18 ==========
2025-06-18 12:10:49,171 - INFO - --- Step 1: Calculate EMA Metrics ---
2025-06-18 12:10:49,172 - INFO - Input price_data_slice shape: (285, 5)
2025-06-18 12:10:49,172 - INFO - Setting module-level ema_params in ema_model_module: {'st_lookback': 15, 'mt_lookback': 70, 'lt_lookback': 100, 'min_weight': 0.0, 'max_weight': 1.0, 'top_n_strat_specific': 3}
2025-06-18 12:10:49,183 - INFO - Output short_ema_df tail(1):
                   SPY        SHV        EFA         TLT        PFF
Date                                                               
2021-02-18  364.984544  97.177174  66.888156  130.405658  29.660461
2025-06-18 12:10:49,185 - INFO - Output med_ema_df tail (1):
                   SPY       SHV        EFA         TLT        PFF
Date                                                              
2021-02-18  350.033179  97.17455  64.215151  134.989704  29.382365
2025-06-18 12:10:49,190 - INFO - Output long_ema_df tail (1):
                   SPY        SHV        EFA         TLT     PFF
Date                                                            
2021-02-18  343.111268  97.169883  62.866119  136.224636  29.118
2025-06-18 12:10:49,191 - INFO - --- Step 2: Calculate EMA Ratios ---
2025-06-18 12:10:49,196 - INFO - Output stmtemax_series (latest):
                 SPY       SHV       EFA       TLT       PFF
Date                                                        
2021-02-18  1.042714  1.000027  1.041626  0.966042  1.009465
2025-06-18 12:10:49,198 - INFO - Output mtltemax_series (latest):
                 SPY       SHV       EFA       TLT       PFF
Date                                                        
2021-02-18  1.020174  1.000048  1.021459  0.990935  1.009079
2025-06-18 12:10:49,200 - INFO - Output emaxavg_series (latest for ranking):
                 SPY       SHV       EFA       TLT       PFF
Date                                                        
2021-02-18  1.031444  1.000038  1.031542  0.978488  1.009272
2025-06-18 12:10:49,201 - INFO - --- Step 3: Determine Allocation Weights ---
2025-06-18 12:10:49,201 - INFO - Calling ema_model_module.ema_allocation_model_updated with price_data up to 2021-02-18
2025-06-18 12:10:49,201 - INFO - Attempting to call ema_model_module.ema_allocation_model_updated...
2025-06-18 12:10:49,202 - INFO -   ema_model_module.st_lookback=15, mt_lookback=70, lt_lookback=100
2025-06-18 12:10:49,202 - INFO -   ema_model_module.min_weight=0.0, ema_model_module.max_weight=1.0
2025-06-18 12:10:49,202 - INFO -   Trace script's signal_algo: {'default': "'ema'", 'picklist': ['Strategy_EMA', 'Strategy_SMA', 'Strategy_MACD', 'Strategy_RSI', 'Strategy_BB']}, system_top_n: 2 (Note: production module uses its own settings for these)
2025-06-18 12:10:49,212 - INFO - Successfully called ema_model_module.ema_allocation_model_updated.
2025-06-18 12:10:49,214 - INFO - Output final_weights for 2021-02-18:
SPY    0.2
SHV    0.2
EFA    0.2
TLT    0.2
PFF    0.2
2025-06-18 12:10:49,215 - INFO - Final normalized weights for 2021-02-18: {'SPY': np.float64(0.2), 'SHV': np.float64(0.2), 'EFA': np.float64(0.2), 'TLT': np.float64(0.2), 'PFF': np.float64(0.2)}
2025-06-18 12:10:49,216 - INFO - 
========== Tracing for Date: 2021-02-19 ==========
2025-06-18 12:10:49,217 - INFO - --- Step 1: Calculate EMA Metrics ---
2025-06-18 12:10:49,217 - INFO - Input price_data_slice shape: (286, 5)
2025-06-18 12:10:49,217 - INFO - Setting module-level ema_params in ema_model_module: {'st_lookback': 15, 'mt_lookback': 70, 'lt_lookback': 100, 'min_weight': 0.0, 'max_weight': 1.0, 'top_n_strat_specific': 3}
2025-06-18 12:10:49,228 - INFO - Output short_ema_df tail(1):
                   SPY        SHV        EFA        TLT       PFF
Date                                                             
2021-02-19  365.268287  97.177134  66.978562  129.83832  29.63706
2025-06-18 12:10:49,230 - INFO - Output med_ema_df tail (1):
                   SPY        SHV       EFA         TLT        PFF
Date                                                              
2021-02-19  350.518287  97.174615  64.31082  134.732725  29.384925
2025-06-18 12:10:49,232 - INFO - Output long_ema_df tail (1):
                   SPY        SHV        EFA         TLT        PFF
Date                                                               
2021-02-19  343.589351  97.170021  62.960085  136.019533  29.125034
2025-06-18 12:10:49,232 - INFO - --- Step 2: Calculate EMA Ratios ---
2025-06-18 12:10:49,238 - INFO - Output stmtemax_series (latest):
                 SPY       SHV       EFA       TLT      PFF
Date                                                       
2021-02-19  1.042081  1.000026  1.041482  0.963673  1.00858
2025-06-18 12:10:49,241 - INFO - Output mtltemax_series (latest):
                 SPY       SHV       EFA      TLT       PFF
Date                                                       
2021-02-19  1.020166  1.000047  1.021454  0.99054  1.008923
2025-06-18 12:10:49,244 - INFO - Output emaxavg_series (latest for ranking):
                 SPY       SHV       EFA       TLT       PFF
Date                                                        
2021-02-19  1.031123  1.000037  1.031468  0.977106  1.008752
2025-06-18 12:10:49,244 - INFO - --- Step 3: Determine Allocation Weights ---
2025-06-18 12:10:49,245 - INFO - Calling ema_model_module.ema_allocation_model_updated with price_data up to 2021-02-19
2025-06-18 12:10:49,245 - INFO - Attempting to call ema_model_module.ema_allocation_model_updated...
2025-06-18 12:10:49,245 - INFO -   ema_model_module.st_lookback=15, mt_lookback=70, lt_lookback=100
2025-06-18 12:10:49,245 - INFO -   ema_model_module.min_weight=0.0, ema_model_module.max_weight=1.0
2025-06-18 12:10:49,246 - INFO -   Trace script's signal_algo: {'default': "'ema'", 'picklist': ['Strategy_EMA', 'Strategy_SMA', 'Strategy_MACD', 'Strategy_RSI', 'Strategy_BB']}, system_top_n: 2 (Note: production module uses its own settings for these)
2025-06-18 12:10:49,254 - INFO - Successfully called ema_model_module.ema_allocation_model_updated.
2025-06-18 12:10:49,255 - INFO - Output final_weights for 2021-02-19:
SPY    0.2
SHV    0.2
EFA    0.2
TLT    0.2
PFF    0.2
2025-06-18 12:10:49,255 - INFO - Final normalized weights for 2021-02-19: {'SPY': np.float64(0.2), 'SHV': np.float64(0.2), 'EFA': np.float64(0.2), 'TLT': np.float64(0.2), 'PFF': np.float64(0.2)}
2025-06-18 12:10:49,256 - INFO - 
========== Tracing for Date: 2021-02-22 ==========
2025-06-18 12:10:49,257 - INFO - --- Step 1: Calculate EMA Metrics ---
2025-06-18 12:10:49,258 - INFO - Input price_data_slice shape: (287, 5)
2025-06-18 12:10:49,258 - INFO - Setting module-level ema_params in ema_model_module: {'st_lookback': 15, 'mt_lookback': 70, 'lt_lookback': 100, 'min_weight': 0.0, 'max_weight': 1.0, 'top_n_strat_specific': 3}
2025-06-18 12:10:49,272 - INFO - Output short_ema_df tail(1):
                   SPY      SHV        EFA         TLT        PFF
Date                                                             
2021-02-22  365.163462  97.1771  67.036608  129.222205  29.600944
2025-06-18 12:10:49,275 - INFO - Output med_ema_df tail (1):
                   SPY        SHV        EFA         TLT        PFF
Date                                                               
2021-02-22  350.910157  97.174678  64.399048  134.456012  29.383889
2025-06-18 12:10:49,278 - INFO - Output long_ema_df tail (1):
                   SPY        SHV        EFA         TLT        PFF
Date                                                               
2021-02-22  344.002031  97.170157  63.048854  135.799531  29.129452
2025-06-18 12:10:49,278 - INFO - --- Step 2: Calculate EMA Ratios ---
2025-06-18 12:10:49,284 - INFO - Output stmtemax_series (latest):
                 SPY       SHV       EFA       TLT       PFF
Date                                                        
2021-02-22  1.040618  1.000025  1.040956  0.961074  1.007387
2025-06-18 12:10:49,287 - INFO - Output mtltemax_series (latest):
                 SPY       SHV       EFA       TLT       PFF
Date                                                        
2021-02-22  1.020082  1.000047  1.021415  0.990107  1.008735
2025-06-18 12:10:49,291 - INFO - Output emaxavg_series (latest for ranking):
                SPY       SHV       EFA      TLT       PFF
Date                                                      
2021-02-22  1.03035  1.000036  1.031186  0.97559  1.008061
2025-06-18 12:10:49,292 - INFO - --- Step 3: Determine Allocation Weights ---
2025-06-18 12:10:49,292 - INFO - Calling ema_model_module.ema_allocation_model_updated with price_data up to 2021-02-22
2025-06-18 12:10:49,292 - INFO - Attempting to call ema_model_module.ema_allocation_model_updated...
2025-06-18 12:10:49,293 - INFO -   ema_model_module.st_lookback=15, mt_lookback=70, lt_lookback=100
2025-06-18 12:10:49,293 - INFO -   ema_model_module.min_weight=0.0, ema_model_module.max_weight=1.0
2025-06-18 12:10:49,293 - INFO -   Trace script's signal_algo: {'default': "'ema'", 'picklist': ['Strategy_EMA', 'Strategy_SMA', 'Strategy_MACD', 'Strategy_RSI', 'Strategy_BB']}, system_top_n: 2 (Note: production module uses its own settings for these)
2025-06-18 12:10:49,303 - INFO - Successfully called ema_model_module.ema_allocation_model_updated.
2025-06-18 12:10:49,304 - INFO - Output final_weights for 2021-02-22:
SPY    0.2
SHV    0.2
EFA    0.2
TLT    0.2
PFF    0.2
2025-06-18 12:10:49,304 - INFO - Final normalized weights for 2021-02-22: {'SPY': np.float64(0.2), 'SHV': np.float64(0.2), 'EFA': np.float64(0.2), 'TLT': np.float64(0.2), 'PFF': np.float64(0.2)}
2025-06-18 12:10:49,305 - INFO - 
========== Tracing for Date: 2021-02-23 ==========
2025-06-18 12:10:49,306 - INFO - --- Step 1: Calculate EMA Metrics ---
2025-06-18 12:10:49,306 - INFO - Input price_data_slice shape: (288, 5)
2025-06-18 12:10:49,307 - INFO - Setting module-level ema_params in ema_model_module: {'st_lookback': 15, 'mt_lookback': 70, 'lt_lookback': 100, 'min_weight': 0.0, 'max_weight': 1.0, 'top_n_strat_specific': 3}
2025-06-18 12:10:49,323 - INFO - Output short_ema_df tail(1):
                   SPY        SHV        EFA         TLT        PFF
Date                                                               
2021-02-23  365.127057  97.177069  67.095157  128.638076  29.566411
2025-06-18 12:10:49,326 - INFO - Output med_ema_df tail (1):
                   SPY        SHV       EFA         TLT        PFF
Date                                                              
2021-02-23  351.303455  97.174739  64.48654  134.176946  29.382221
2025-06-18 12:10:49,330 - INFO - Output long_ema_df tail (1):
                   SPY        SHV        EFA         TLT        PFF
Date                                                               
2021-02-23  344.415302  97.170289  63.137094  135.576751  29.133318
2025-06-18 12:10:49,331 - INFO - --- Step 2: Calculate EMA Ratios ---
2025-06-18 12:10:49,337 - INFO - Output stmtemax_series (latest):
                 SPY       SHV       EFA      TLT       PFF
Date                                                       
2021-02-23  1.039349  1.000024  1.040452  0.95872  1.006269
2025-06-18 12:10:49,339 - INFO - Output mtltemax_series (latest):
             SPY       SHV       EFA       TLT       PFF
Date                                                    
2021-02-23  1.02  1.000046  1.021373  0.989675  1.008544
2025-06-18 12:10:49,341 - INFO - Output emaxavg_series (latest for ranking):
                 SPY       SHV       EFA       TLT       PFF
Date                                                        
2021-02-23  1.029675  1.000035  1.030913  0.974197  1.007406
2025-06-18 12:10:49,342 - INFO - --- Step 3: Determine Allocation Weights ---
2025-06-18 12:10:49,342 - INFO - Calling ema_model_module.ema_allocation_model_updated with price_data up to 2021-02-23
2025-06-18 12:10:49,342 - INFO - Attempting to call ema_model_module.ema_allocation_model_updated...
2025-06-18 12:10:49,342 - INFO -   ema_model_module.st_lookback=15, mt_lookback=70, lt_lookback=100
2025-06-18 12:10:49,343 - INFO -   ema_model_module.min_weight=0.0, ema_model_module.max_weight=1.0
2025-06-18 12:10:49,343 - INFO -   Trace script's signal_algo: {'default': "'ema'", 'picklist': ['Strategy_EMA', 'Strategy_SMA', 'Strategy_MACD', 'Strategy_RSI', 'Strategy_BB']}, system_top_n: 2 (Note: production module uses its own settings for these)
2025-06-18 12:10:49,351 - INFO - Successfully called ema_model_module.ema_allocation_model_updated.
2025-06-18 12:10:49,353 - INFO - Output final_weights for 2021-02-23:
SPY    0.2
SHV    0.2
EFA    0.2
TLT    0.2
PFF    0.2
2025-06-18 12:10:49,353 - INFO - Final normalized weights for 2021-02-23: {'SPY': np.float64(0.2), 'SHV': np.float64(0.2), 'EFA': np.float64(0.2), 'TLT': np.float64(0.2), 'PFF': np.float64(0.2)}
2025-06-18 12:10:49,354 - INFO - 
========== Tracing for Date: 2021-02-24 ==========
2025-06-18 12:10:49,355 - INFO - --- Step 1: Calculate EMA Metrics ---
2025-06-18 12:10:49,355 - INFO - Input price_data_slice shape: (289, 5)
2025-06-18 12:10:49,355 - INFO - Setting module-level ema_params in ema_model_module: {'st_lookback': 15, 'mt_lookback': 70, 'lt_lookback': 100, 'min_weight': 0.0, 'max_weight': 1.0, 'top_n_strat_specific': 3}
2025-06-18 12:10:49,365 - INFO - Output short_ema_df tail(1):
                   SPY        SHV       EFA         TLT        PFF
Date                                                              
2021-02-24  365.597785  97.177043  67.17299  128.024836  29.531308
2025-06-18 12:10:49,370 - INFO - Output med_ema_df tail (1):
                   SPY        SHV        EFA         TLT        PFF
Date                                                               
2021-02-24  351.798932  97.174799  64.577562  133.882726  29.379499
2025-06-18 12:10:49,373 - INFO - Output long_ema_df tail (1):
                   SPY        SHV        EFA         TLT        PFF
Date                                                               
2021-02-24  344.900007  97.170419  63.227802  135.342205  29.136333
2025-06-18 12:10:49,373 - INFO - --- Step 2: Calculate EMA Ratios ---
2025-06-18 12:10:49,378 - INFO - Output stmtemax_series (latest):
                 SPY       SHV       EFA       TLT       PFF
Date                                                        
2021-02-24  1.039224  1.000023  1.040191  0.956246  1.005167
2025-06-18 12:10:49,381 - INFO - Output mtltemax_series (latest):
                 SPY       SHV       EFA       TLT       PFF
Date                                                        
2021-02-24  1.020003  1.000045  1.021348  0.989216  1.008346
2025-06-18 12:10:49,383 - INFO - Output emaxavg_series (latest for ranking):
                 SPY       SHV       EFA       TLT       PFF
Date                                                        
2021-02-24  1.029613  1.000034  1.030769  0.972731  1.006756
2025-06-18 12:10:49,383 - INFO - --- Step 3: Determine Allocation Weights ---
2025-06-18 12:10:49,384 - INFO - Calling ema_model_module.ema_allocation_model_updated with price_data up to 2021-02-24
2025-06-18 12:10:49,384 - INFO - Attempting to call ema_model_module.ema_allocation_model_updated...
2025-06-18 12:10:49,384 - INFO -   ema_model_module.st_lookback=15, mt_lookback=70, lt_lookback=100
2025-06-18 12:10:49,384 - INFO -   ema_model_module.min_weight=0.0, ema_model_module.max_weight=1.0
2025-06-18 12:10:49,384 - INFO -   Trace script's signal_algo: {'default': "'ema'", 'picklist': ['Strategy_EMA', 'Strategy_SMA', 'Strategy_MACD', 'Strategy_RSI', 'Strategy_BB']}, system_top_n: 2 (Note: production module uses its own settings for these)
2025-06-18 12:10:49,399 - INFO - Successfully called ema_model_module.ema_allocation_model_updated.
2025-06-18 12:10:49,400 - INFO - Output final_weights for 2021-02-24:
SPY    0.2
SHV    0.2
EFA    0.2
TLT    0.2
PFF    0.2
2025-06-18 12:10:49,401 - INFO - Final normalized weights for 2021-02-24: {'SPY': np.float64(0.2), 'SHV': np.float64(0.2), 'EFA': np.float64(0.2), 'TLT': np.float64(0.2), 'PFF': np.float64(0.2)}
2025-06-18 12:10:49,401 - INFO - 
========== Tracing for Date: 2021-02-25 ==========
2025-06-18 12:10:49,402 - INFO - --- Step 1: Calculate EMA Metrics ---
2025-06-18 12:10:49,402 - INFO - Input price_data_slice shape: (290, 5)
2025-06-18 12:10:49,402 - INFO - Setting module-level ema_params in ema_model_module: {'st_lookback': 15, 'mt_lookback': 70, 'lt_lookback': 100, 'min_weight': 0.0, 'max_weight': 1.0, 'top_n_strat_specific': 3}
2025-06-18 12:10:49,413 - INFO - Output short_ema_df tail(1):
                   SPY       SHV        EFA         TLT        PFF
Date                                                              
2021-02-25  364.898581  97.17592  67.097003  127.235668  29.471267
2025-06-18 12:10:49,417 - INFO - Output med_ema_df tail (1):
                   SPY        SHV        EFA         TLT        PFF
Date                                                               
2021-02-25  352.030064  97.174609  64.633549  133.539875  29.370245
2025-06-18 12:10:49,420 - INFO - Output long_ema_df tail (1):
                   SPY        SHV        EFA        TLT        PFF
Date                                                              
2021-02-25  345.199099  97.170373  63.293887  135.07229  29.134643
2025-06-18 12:10:49,420 - INFO - --- Step 2: Calculate EMA Ratios ---
2025-06-18 12:10:49,425 - INFO - Output stmtemax_series (latest):
                 SPY       SHV       EFA       TLT      PFF
Date                                                       
2021-02-25  1.036555  1.000013  1.038114  0.952792  1.00344
2025-06-18 12:10:49,427 - INFO - Output mtltemax_series (latest):
                 SPY       SHV       EFA       TLT       PFF
Date                                                        
2021-02-25  1.019788  1.000044  1.021166  0.988655  1.008087
2025-06-18 12:10:49,431 - INFO - Output emaxavg_series (latest for ranking):
                 SPY       SHV      EFA       TLT       PFF
Date                                                       
2021-02-25  1.028172  1.000029  1.02964  0.970723  1.005763
2025-06-18 12:10:49,432 - INFO - --- Step 3: Determine Allocation Weights ---
2025-06-18 12:10:49,432 - INFO - Calling ema_model_module.ema_allocation_model_updated with price_data up to 2021-02-25
2025-06-18 12:10:49,433 - INFO - Attempting to call ema_model_module.ema_allocation_model_updated...
2025-06-18 12:10:49,433 - INFO -   ema_model_module.st_lookback=15, mt_lookback=70, lt_lookback=100
2025-06-18 12:10:49,433 - INFO -   ema_model_module.min_weight=0.0, ema_model_module.max_weight=1.0
2025-06-18 12:10:49,434 - INFO -   Trace script's signal_algo: {'default': "'ema'", 'picklist': ['Strategy_EMA', 'Strategy_SMA', 'Strategy_MACD', 'Strategy_RSI', 'Strategy_BB']}, system_top_n: 2 (Note: production module uses its own settings for these)
2025-06-18 12:10:49,445 - INFO - Successfully called ema_model_module.ema_allocation_model_updated.
2025-06-18 12:10:49,446 - INFO - Output final_weights for 2021-02-25:
SPY    0.2
SHV    0.2
EFA    0.2
TLT    0.2
PFF    0.2
2025-06-18 12:10:49,447 - INFO - Final normalized weights for 2021-02-25: {'SPY': np.float64(0.2), 'SHV': np.float64(0.2), 'EFA': np.float64(0.2), 'TLT': np.float64(0.2), 'PFF': np.float64(0.2)}
2025-06-18 12:10:49,447 - INFO - 
========== Tracing for Date: 2021-02-26 ==========
2025-06-18 12:10:49,448 - INFO - --- Step 1: Calculate EMA Metrics ---
2025-06-18 12:10:49,448 - INFO - Input price_data_slice shape: (291, 5)
2025-06-18 12:10:49,448 - INFO - Setting module-level ema_params in ema_model_module: {'st_lookback': 15, 'mt_lookback': 70, 'lt_lookback': 100, 'min_weight': 0.0, 'max_weight': 1.0, 'top_n_strat_specific': 3}
2025-06-18 12:10:49,460 - INFO - Output short_ema_df tail(1):
                   SPY        SHV        EFA         TLT        PFF
Date                                                               
2021-02-26  364.054908  97.176037  66.913026  127.048107  29.440235
2025-06-18 12:10:49,462 - INFO - Output med_ema_df tail (1):
                   SPY        SHV        EFA         TLT        PFF
Date                                                               
2021-02-26  352.202435  97.174672  64.661482  133.320024  29.366097
2025-06-18 12:10:49,464 - INFO - Output long_ema_df tail (1):
                   SPY        SHV        EFA         TLT        PFF
Date                                                               
2021-02-26  345.455536  97.170501  63.340051  134.887397  29.136393
2025-06-18 12:10:49,465 - INFO - --- Step 2: Calculate EMA Ratios ---
2025-06-18 12:10:49,469 - INFO - Output stmtemax_series (latest):
                 SPY       SHV      EFA       TLT       PFF
Date                                                       
2021-02-26  1.033652  1.000014  1.03482  0.952956  1.002525
2025-06-18 12:10:49,472 - INFO - Output mtltemax_series (latest):
                SPY       SHV       EFA      TLT       PFF
Date                                                      
2021-02-26  1.01953  1.000043  1.020862  0.98838  1.007884
2025-06-18 12:10:49,477 - INFO - Output emaxavg_series (latest for ranking):
                 SPY       SHV       EFA       TLT       PFF
Date                                                        
2021-02-26  1.026591  1.000028  1.027841  0.970668  1.005204
2025-06-18 12:10:49,478 - INFO - --- Step 3: Determine Allocation Weights ---
2025-06-18 12:10:49,479 - INFO - Calling ema_model_module.ema_allocation_model_updated with price_data up to 2021-02-26
2025-06-18 12:10:49,479 - INFO - Attempting to call ema_model_module.ema_allocation_model_updated...
2025-06-18 12:10:49,479 - INFO -   ema_model_module.st_lookback=15, mt_lookback=70, lt_lookback=100
2025-06-18 12:10:49,479 - INFO -   ema_model_module.min_weight=0.0, ema_model_module.max_weight=1.0
2025-06-18 12:10:49,480 - INFO -   Trace script's signal_algo: {'default': "'ema'", 'picklist': ['Strategy_EMA', 'Strategy_SMA', 'Strategy_MACD', 'Strategy_RSI', 'Strategy_BB']}, system_top_n: 2 (Note: production module uses its own settings for these)
2025-06-18 12:10:49,488 - INFO - Successfully called ema_model_module.ema_allocation_model_updated.
2025-06-18 12:10:49,489 - INFO - Output final_weights for 2021-02-26:
SPY    0.2
SHV    0.2
EFA    0.2
TLT    0.2
PFF    0.2
2025-06-18 12:10:49,490 - INFO - Final normalized weights for 2021-02-26: {'SPY': np.float64(0.2), 'SHV': np.float64(0.2), 'EFA': np.float64(0.2), 'TLT': np.float64(0.2), 'PFF': np.float64(0.2)}
2025-06-18 12:10:49,492 - INFO - Attempting to save final weights to S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\test_logs\production_flow_weights_20210120_20210228_20250618_121047.csv...
2025-06-18 12:10:49,510 - INFO - Final weights CSV saved to: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\test_logs\production_flow_weights_20210120_20210228_20250618_121047.csv
2025-06-18 12:10:49,516 - INFO - Trace script finished (end of try block).
2025-06-18 12:10:49,516 - INFO - EMA production flow trace finished (finally block).
