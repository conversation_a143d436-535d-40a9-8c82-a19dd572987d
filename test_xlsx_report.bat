@echo off
REM ============================================
REM Test XLSX Report Generation with Unified Pipeline
REM ============================================

SETLOCAL

echo [%TIME%] Testing XLSX Report Generation...
echo.

REM --- Setup environment using master config ---
CALL "%~dp0master_config.bat"
IF ERRORLEVEL 1 (
    echo ERROR: Failed to set up environment
    exit /b 1
)

echo [%TIME%] Environment setup complete
echo [%TIME%] Python version:
python --version
echo.

REM --- Run unified pipeline with XLSX report generation ---
echo [%TIME%] Running unified pipeline with XLSX report generation...
echo ============================================

python -m v4.run_unified_pipeline

SET "EXIT_CODE=%ERRORLEVEL%"

echo.
echo ============================================
echo [%TIME%] Pipeline completed with exit code %EXIT_CODE%

IF %EXIT_CODE% EQU 0 (
    echo.
    echo SUCCESS: Pipeline completed successfully!
    echo.
    echo Checking for generated files...
    echo.
    
    REM Check for XLSX report
    echo Looking for XLSX reports in reporting directory:
    IF EXIST "reporting\*.xlsx" (
        dir "reporting\*.xlsx" /O:D
        echo.
        echo ✅ XLSX report(s) found!
    ) ELSE (
        echo ❌ No XLSX reports found in reporting directory
    )
    
    echo.
    echo Looking for latest CSV outputs:
    IF EXIST "v4_trace_outputs\unified_*.csv" (
        echo Latest unified CSV files:
        dir "v4_trace_outputs\unified_*.csv" /O:D | findstr /V "Directory"
    )
    
) ELSE (
    echo.
    echo ❌ Pipeline failed with exit code %EXIT_CODE%
    echo Check the logs for details.
)

echo.
echo [%TIME%] Test completed.

ENDLOCAL
pause
exit /b %EXIT_CODE%
