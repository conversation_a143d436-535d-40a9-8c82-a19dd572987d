#!/usr/bin/env python
"""
Test EMA model parameter loading with custom settings file
"""

import sys
from pathlib import Path

# Add project root to path
_project_root = Path(__file__).resolve().parent
if str(_project_root) not in sys.path:
    sys.path.insert(0, str(_project_root))

from v4.settings.settings_CPS_v4 import load_settings
from v4.models.ema_allocation_model_v4 import _load_fresh_parameters

def test_ema_params(settings_file=None):
    print(f"Testing EMA parameter loading...")
    print(f"Settings file: {settings_file or 'default'}")
    print("=" * 50)
    
    # Test load_settings function
    if settings_file:
        settings = load_settings(custom_file=settings_file)
    else:
        settings = load_settings()
    
    # Extract strategy section
    strategy_section = settings.get('strategy', {})
    print("Strategy section parameters:")
    for key in ['st_lookback', 'mt_lookback', 'lt_lookback']:
        if key in strategy_section:
            value = strategy_section[key]
            print(f"  {key}: {value}")
    
    print()
    
    # Test fresh parameter loading
    try:
        fresh_params = _load_fresh_parameters()
        print("Fresh parameters loaded:")
        for key, value in fresh_params.items():
            print(f"  {key}: {value}")
        
        return fresh_params
        
    except Exception as e:
        print(f"Error loading fresh parameters: {e}")
        return None

if __name__ == "__main__":
    import sys
    settings_file = sys.argv[1] if len(sys.argv) > 1 else None
    test_ema_params(settings_file)
