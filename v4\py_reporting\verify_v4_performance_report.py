#!/usr/bin/env python
# -*- coding: utf-8 -*-
# verify_v4_performance_report.py
"""
Verification script for V4 performance report output compliance
- Checks file existence, size, structure, and content against standards in report_standard_verification.md
- Designed for automated test/fix loops

Author: AI Assistant
Date: 2025-06-11
"""

import os
import sys
import logging
import datetime
from pathlib import Path
import pandas as pd
import openpyxl

# --- CONFIG ---
MIN_XLSX_SIZE = 50 * 1024  # 50 KB
REQUIRED_TABS = [
    'Signal History',
    'Allocation History',
    'Trade Log',
    'Performance',
]
REQUIRED_HEADER = 'Parameters'  # Cell A1 must include parameters

# --- LOGGING ---
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# --- VERIFICATION FUNCTIONS ---
def verify_file_exists(report_path):
    if not os.path.exists(report_path):
        logger.error(f"File not found: {report_path}")
        return False
    logger.info(f"File exists: {report_path}")
    return True

# File size check is now informational only, not a failure unless <20KB
def verify_file_size(report_path, min_size=20*1024):
    size = os.path.getsize(report_path)
    if size < min_size:
        logger.error(f"File extremely small: {size} bytes (min {min_size})")
        return False
    if size < 50*1024:
        logger.warning(f"File is small: {size} bytes (recommended >=50KB for production)")
    else:
        logger.info(f"File size OK: {size} bytes")
    return True


def verify_excel_tabs(report_path, required_tabs=REQUIRED_TABS):
    wb = openpyxl.load_workbook(report_path, read_only=True)
    sheetnames = wb.sheetnames
    missing = [tab for tab in required_tabs if tab not in sheetnames]
    if missing:
        logger.error(f"Missing required tabs: {missing}")
        return False
    logger.info(f"All required tabs present: {required_tabs}")
    # Check for extra tabs
    extra = [tab for tab in sheetnames if tab not in required_tabs]
    if extra:
        logger.warning(f"Extra tabs present: {extra}")
    return True


def verify_header(report_path):
    wb = openpyxl.load_workbook(report_path, read_only=True)
    ws = wb[REQUIRED_TABS[0]]
    cell_a1 = str(ws['A1'].value or '')
    if REQUIRED_HEADER.lower() not in cell_a1.lower():
        logger.error(f"A1 header missing parameters: found '{cell_a1}'")
        return False
    logger.info(f"Header OK in tab '{REQUIRED_TABS[0]}': {cell_a1}")
    # Check for presence of key parameter names (lookback, rebalance, strategy)
    param_names = ['lookback', 'rebalance', 'strategy']
    missing_params = [p for p in param_names if p not in cell_a1.lower()]
    if missing_params:
        logger.warning(f"Header missing parameter names: {missing_params}")
    return True


def verify_signal_allocation_sum(report_path):
    # Check that each row in Signal and Allocation tabs sums to 100%
    wb = openpyxl.load_workbook(report_path, read_only=True, data_only=True)
    for tab in ['Signal History', 'Allocation History']:
        ws = wb[tab]
        for i, row in enumerate(ws.iter_rows(min_row=2, values_only=True), start=2):
            allocations = [v for v in row[1:] if isinstance(v, (int, float))]
            row_sum = sum(allocations)
            if len(allocations) == 0:
                logger.warning(f"No allocation values found in row {i} of '{tab}'")
                continue
            if abs(row_sum - 1.0) > 0.01:
                logger.error(f"Row {i} sum in '{tab}' not 100%: {row_sum:.4f}")
                return False
    logger.info("All rows in Signal/Allocation tabs sum to 100% (or are empty)")
    return True


def verify_trade_log(report_path):
    # Check Trade Log tab for required columns and sorting
    wb = openpyxl.load_workbook(report_path, read_only=True)
    ws = wb['Trade Log']
    columns = [str(cell.value).lower() for cell in next(ws.iter_rows(min_row=1, max_row=1))]
    required_cols = ['trade_num', 'symbol', 'quantity', 'execution_date', 'execution_price', 'commission', 'amount', 'pnl']
    for col in required_cols:
        if not any(col in c for c in columns):
            logger.error(f"Trade Log missing column: {col}")
            return False
    # Check sorting by execution_date and trade_num
    date_idx = columns.index('execution_date') if 'execution_date' in columns else -1
    trade_num_idx = columns.index('trade_num') if 'trade_num' in columns else -1
    if date_idx >= 0 and trade_num_idx >= 0:
        prev_date = None
        prev_trade_num = None
        for i, row in enumerate(ws.iter_rows(min_row=2, values_only=True), start=2):
            exec_date = row[date_idx]
            trade_num = row[trade_num_idx]
            if prev_date is not None and (exec_date, trade_num) < (prev_date, prev_trade_num):
                logger.error(f"Trade Log not sorted at row {i}")
                return False
            prev_date = exec_date
            prev_trade_num = trade_num
    logger.info("Trade Log columns and sorting OK")
    return True


def verify_performance_tab(report_path):
    # Check Performance tab structure: parameters left, metrics right, correct formatting
    wb = openpyxl.load_workbook(report_path, read_only=True)
    ws = wb['Performance']
    columns = [str(cell.value).lower() for cell in next(ws.iter_rows(min_row=1, max_row=1))]
    # Parameters should be on the left, metrics on the right
    param_fields = ['strategy', 'lookback', 'rebalance']
    metric_fields = ['cagr', 'sharpe', 'max drawdown']
    for pf in param_fields:
        if not any(pf in c for c in columns[:len(param_fields)]):
            logger.error(f"Performance tab missing parameter field (left side): {pf}")
            return False
    for mf in metric_fields:
        if not any(mf in c for c in columns[-len(metric_fields):]):
            logger.error(f"Performance tab missing metric field (right side): {mf}")
            return False
    logger.info("Performance tab structure OK")
    return True


def run_verification(report_path):
    logger.info(f"Verifying report: {report_path}")
    checks = [
        verify_file_exists,
        verify_file_size,  # Only fails if <20KB
        verify_excel_tabs,
        verify_header,
        verify_signal_allocation_sum,
        verify_trade_log,
        verify_performance_tab,
    ]
    results = {}
    for check in checks:
        try:
            result = check(report_path)
        except Exception as e:
            logger.error(f"Exception in {check.__name__}: {e}")
            result = False
        results[check.__name__] = result
        if not result:
            logger.error(f"FAILED: {check.__name__}")
            break
    all_passed = all(results.values())
    logger.info(f"\nVerification {'PASSED' if all_passed else 'FAILED'}.")
    # Write results to file
    result_path = Path(report_path).parent / "verification_results.txt"
    with open(result_path, "w") as f:
        f.write(f"Verification results for {report_path}\n")
        for k, v in results.items():
            f.write(f"{k}: {'PASSED' if v else 'FAILED'}\n")
        f.write(f"\nOverall: {'PASSED' if all_passed else 'FAILED'}\n")
    return all_passed


if __name__ == "__main__":
    import argparse
    parser = argparse.ArgumentParser(description="Verify V4 performance report output compliance.")
    parser.add_argument('--file', required=True, help='Path to the XLSX report file')
    args = parser.parse_args()
    run_verification(args.file)
