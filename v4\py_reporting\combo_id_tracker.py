#!/usr/bin/env python
"""
Combo ID Tracking and Visibility System

This module provides comprehensive tracking and visibility for combo ID processing
throughout the optimization pipeline. It includes real-time status updates, 
detailed logging, progress monitoring, and file correlation.

Author: AI Assistant
Date: 2025-07-28
"""

import os
import re
import json
import logging
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from collections import defaultdict
import time

logger = logging.getLogger(__name__)


@dataclass
class ComboIDStatus:
    """Status information for a combo ID."""
    combo_id: str
    combination: Dict[str, Any]
    current_phase: str
    status: str  # 'pending', 'processing', 'completed', 'failed'
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    duration: Optional[float] = None
    phases_completed: List[str] = None
    files_created: List[str] = None
    error_message: Optional[str] = None
    details: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.phases_completed is None:
            self.phases_completed = []
        if self.files_created is None:
            self.files_created = []
        if self.details is None:
            self.details = {}


@dataclass
class ProcessingPhase:
    """Information about a processing phase."""
    name: str
    combo_id: str
    start_time: datetime
    end_time: Optional[datetime] = None
    status: str = 'running'  # 'running', 'completed', 'failed'
    details: Dict[str, Any] = None
    files_created: List[str] = None
    log_entries: List[str] = None
    
    def __post_init__(self):
        if self.details is None:
            self.details = {}
        if self.files_created is None:
            self.files_created = []
        if self.log_entries is None:
            self.log_entries = []


@dataclass
class ProgressSummary:
    """Overall progress summary."""
    total_combos: int
    completed_combos: int
    failed_combos: int
    current_combo: Optional[str]
    start_time: datetime
    estimated_completion: Optional[datetime]
    average_combo_time: float
    success_rate: float
    current_phase: str
    processing_combos: int = 0
    pending_combos: int = 0


class ComboIDTracker:
    """Central tracker for combo ID processing visibility and logging."""
    
    def __init__(self, combinations: List[Dict], output_dir: Path, lookup_data: List[Dict] = None, enable_file_logging: bool = True):
        """Initialize tracker with combo list and output directory.
        
        Args:
            combinations: List of parameter combination dictionaries
            output_dir: Directory for output files and logs
            lookup_data: Optional lookup table data for combo IDs
            enable_file_logging: Whether to enable file logging (disable for testing)
        """
        self.combinations = combinations
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # Create lookup table if not provided
        if lookup_data is None:
            self.lookup_data = self._create_lookup_data(combinations)
        else:
            self.lookup_data = lookup_data
        
        # Initialize tracking data
        self.combo_statuses: Dict[str, ComboIDStatus] = {}
        self.processing_phases: Dict[str, List[ProcessingPhase]] = defaultdict(list)
        self.start_time = datetime.now()
        self.current_combo = None
        
        # Initialize all combo IDs as pending
        for i, combination in enumerate(combinations):
            combo_id = self._generate_combo_id(combination)
            self.combo_statuses[combo_id] = ComboIDStatus(
                combo_id=combo_id,
                combination=combination,
                current_phase='pending',
                status='pending'
            )
        
        # Create tracking log file
        self.enable_file_logging = enable_file_logging
        if enable_file_logging:
            self.tracking_log_file = self.output_dir / f"combo_tracking_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
            self._setup_tracking_logger()
        else:
            self.tracking_log_file = None
            self._setup_console_logger()
        
        logger.info(f"ComboIDTracker initialized with {len(combinations)} combinations")
        self._log_tracking_event("TRACKER_INITIALIZED", {
            "total_combos": len(combinations),
            "output_dir": str(output_dir),
            "tracking_log": str(self.tracking_log_file)
        })
    
    def _generate_combo_id(self, combination: Dict[str, Any]) -> str:
        """Generate combo ID from parameter combination."""
        return f"S{combination['st_lookback']}_M{combination['mt_lookback']}_L{combination['lt_lookback']}_E{combination['execution_delay']}_T{combination['top_n']}"
    
    def _create_lookup_data(self, combinations: List[Dict]) -> List[Dict]:
        """Create lookup data for combinations."""
        lookup_data = []
        for i, combination in enumerate(combinations):
            combo_id = self._generate_combo_id(combination)
            lookup_entry = {
                "combo_id": combo_id,
                "combination_number": i + 1,
                "st_lookback": combination['st_lookback'],
                "mt_lookback": combination['mt_lookback'], 
                "lt_lookback": combination['lt_lookback'],
                "execution_delay": combination['execution_delay'],
                "system_lookback": combination['system_lookback'],
                "top_n": combination['top_n'],
                "description": f"Short={combination['st_lookback']}, Medium={combination['mt_lookback']}, Long={combination['lt_lookback']}, Delay={combination['execution_delay']}, TopN={combination['top_n']}"
            }
            lookup_data.append(lookup_entry)
        return lookup_data
    
    def _setup_tracking_logger(self):
        """Setup dedicated logger for tracking events with file output."""
        self.tracking_logger = logging.getLogger(f"combo_tracker_{id(self)}")
        self.tracking_logger.setLevel(logging.INFO)
        
        # Create file handler for tracking log
        self.log_handler = logging.FileHandler(self.tracking_log_file)
        formatter = logging.Formatter(
            '%(asctime)s | %(levelname)s | %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        self.log_handler.setFormatter(formatter)
        self.tracking_logger.addHandler(self.log_handler)
    
    def _setup_console_logger(self):
        """Setup dedicated logger for tracking events with console output only."""
        self.tracking_logger = logging.getLogger(f"combo_tracker_{id(self)}")
        self.tracking_logger.setLevel(logging.INFO)
        
        # Create console handler for tracking log
        self.log_handler = logging.StreamHandler()
        formatter = logging.Formatter(
            '%(asctime)s | %(levelname)s | %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        self.log_handler.setFormatter(formatter)
        self.tracking_logger.addHandler(self.log_handler)
    
    def cleanup(self):
        """Clean up resources, especially log handlers."""
        try:
            if hasattr(self, 'log_handler') and self.log_handler:
                # Remove handler from logger
                self.tracking_logger.removeHandler(self.log_handler)
                # Close the handler to release file lock
                self.log_handler.close()
                # Clear the reference
                self.log_handler = None
                
            # Also remove all handlers from the logger
            for handler in self.tracking_logger.handlers[:]:
                handler.close()
                self.tracking_logger.removeHandler(handler)
                
        except Exception as e:
            # Ignore cleanup errors but log them
            logger.warning(f"Error during ComboIDTracker cleanup: {e}")
    
    def __del__(self):
        """Destructor to ensure cleanup happens."""
        try:
            self.cleanup()
        except:
            pass
    
    def __enter__(self):
        """Context manager entry."""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit with cleanup."""
        self.cleanup()
        return False
    
    def _log_tracking_event(self, event_type: str, details: Dict[str, Any], combo_id: str = None):
        """Log tracking event with structured format."""
        log_entry = {
            "timestamp": datetime.now().isoformat(),
            "event_type": event_type,
            "combo_id": combo_id,
            "details": details
        }
        
        # Log to tracking logger
        log_message = f"{event_type}"
        if combo_id:
            log_message += f" | {combo_id}"
        log_message += f" | {json.dumps(details, default=str)}"
        
        self.tracking_logger.info(log_message)
        
        # Also log to main logger for visibility
        if combo_id:
            logger.info(f"[COMBO_TRACK] {event_type} for {combo_id}: {details}")
        else:
            logger.info(f"[COMBO_TRACK] {event_type}: {details}")
    
    def validate_combo_id_format(self, combo_id: str) -> bool:
        """Validate combo ID follows expected format S{n}_M{n}_L{n}_E{n}_T{n}."""
        pattern = r'^S\d+_M\d+_L\d+_E\d+_T\d+$'
        return bool(re.match(pattern, combo_id))
    
    def validate_combo_id_uniqueness(self, combo_ids: List[str]) -> List[str]:
        """Validate all combo IDs are unique, return duplicates if any."""
        seen = set()
        duplicates = []
        for combo_id in combo_ids:
            if combo_id in seen:
                duplicates.append(combo_id)
            seen.add(combo_id)
        return duplicates
    
    def start_processing(self, combo_id: str, combination: Dict) -> None:
        """Mark combo ID as starting processing with timestamp."""
        if not self.validate_combo_id_format(combo_id):
            raise ValueError(f"Invalid combo ID format: {combo_id}")
        
        if combo_id not in self.combo_statuses:
            # Create status if it doesn't exist
            self.combo_statuses[combo_id] = ComboIDStatus(
                combo_id=combo_id,
                combination=combination,
                current_phase='starting',
                status='processing'
            )
        
        status = self.combo_statuses[combo_id]
        status.status = 'processing'
        status.current_phase = 'starting'
        status.start_time = datetime.now()
        self.current_combo = combo_id
        
        # Log tracking event
        self._log_tracking_event("COMBO_STARTED", {
            "combination": combination,
            "start_time": status.start_time.isoformat()
        }, combo_id)
        
        # Print status update
        progress = self.get_progress_summary()
        completed = progress.completed_combos + progress.failed_combos
        print(f"\n[COMBO {completed + 1}/{progress.total_combos}] Starting {combo_id}")
        print(f"   Parameters: {self._get_combo_description(combo_id)}")
        print(f"   Progress: {completed}/{progress.total_combos} completed ({progress.success_rate:.1f}% success rate)")
    
    def update_phase(self, combo_id: str, phase: str, status: str, details: Dict = None) -> None:
        """Update combo ID processing phase with status and details."""
        if combo_id not in self.combo_statuses:
            logger.warning(f"Combo ID {combo_id} not found in statuses")
            return
        
        combo_status = self.combo_statuses[combo_id]
        combo_status.current_phase = phase
        
        # Create phase tracking entry
        phase_entry = ProcessingPhase(
            name=phase,
            combo_id=combo_id,
            start_time=datetime.now(),
            status=status,
            details=details or {}
        )
        self.processing_phases[combo_id].append(phase_entry)
        
        # Log tracking event
        self._log_tracking_event("PHASE_UPDATE", {
            "phase": phase,
            "status": status,
            "details": details or {}
        }, combo_id)
        
        # Print phase update
        print(f"   Phase: {phase} ({status})")
        if details:
            for key, value in details.items():
                print(f"      {key}: {value}")
    
    def complete_processing(self, combo_id: str, success: bool, duration: float, details: Dict = None) -> None:
        """Mark combo ID as completed with success status and timing."""
        if combo_id not in self.combo_statuses:
            logger.warning(f"Combo ID {combo_id} not found in statuses")
            return
        
        status = self.combo_statuses[combo_id]
        status.status = 'completed' if success else 'failed'
        status.end_time = datetime.now()
        status.duration = duration
        status.current_phase = 'completed' if success else 'failed'
        
        if details:
            status.details.update(details)
        
        # Complete current phase
        if self.processing_phases[combo_id]:
            current_phase = self.processing_phases[combo_id][-1]
            current_phase.end_time = datetime.now()
            current_phase.status = 'completed' if success else 'failed'
        
        # Log tracking event
        self._log_tracking_event("COMBO_COMPLETED", {
            "success": success,
            "duration": duration,
            "details": details or {}
        }, combo_id)
        
        # Print completion status
        status_icon = "[OK]" if success else "[FAIL]"
        status_text = "COMPLETED" if success else "FAILED"
        print(f"   {status_icon} {status_text} in {duration:.1f}s")
        
        if not success and details and 'error' in details:
            print(f"      Error: {details['error']}")
        
        # Update current combo
        if self.current_combo == combo_id:
            self.current_combo = None
    
    def register_file_creation(self, combo_id: str, file_type: str, file_path: Path, phase: str = None) -> None:
        """Register a file created for specific combo ID."""
        if combo_id not in self.combo_statuses:
            logger.warning(f"Combo ID {combo_id} not found in statuses")
            return
        
        # Add to combo status
        self.combo_statuses[combo_id].files_created.append(str(file_path))
        
        # Add to current phase if exists
        if self.processing_phases[combo_id]:
            current_phase = self.processing_phases[combo_id][-1]
            current_phase.files_created.append(str(file_path))
        
        # Log tracking event
        self._log_tracking_event("FILE_CREATED", {
            "file_type": file_type,
            "file_path": str(file_path),
            "phase": phase
        }, combo_id)
        
        print(f"      Created {file_type}: {file_path.name}")
    
    def get_current_status(self) -> Dict:
        """Get current processing status for all combo IDs."""
        status_summary = {
            "total_combos": len(self.combo_statuses),
            "current_combo": self.current_combo,
            "statuses": {}
        }
        
        for combo_id, status in self.combo_statuses.items():
            status_summary["statuses"][combo_id] = {
                "status": status.status,
                "current_phase": status.current_phase,
                "start_time": status.start_time.isoformat() if status.start_time else None,
                "duration": status.duration,
                "files_created": len(status.files_created),
                "phases_completed": len(status.phases_completed)
            }
        
        return status_summary
    
    def get_progress_summary(self) -> ProgressSummary:
        """Get overall progress summary with estimates."""
        total_combos = len(self.combo_statuses)
        completed_combos = sum(1 for s in self.combo_statuses.values() if s.status == 'completed')
        failed_combos = sum(1 for s in self.combo_statuses.values() if s.status == 'failed')
        processing_combos = sum(1 for s in self.combo_statuses.values() if s.status == 'processing')
        pending_combos = sum(1 for s in self.combo_statuses.values() if s.status == 'pending')
        
        # Calculate average time
        completed_durations = [s.duration for s in self.combo_statuses.values() if s.duration is not None]
        average_combo_time = sum(completed_durations) / len(completed_durations) if completed_durations else 0
        
        # Calculate success rate
        total_finished = completed_combos + failed_combos
        success_rate = (completed_combos / total_finished * 100) if total_finished > 0 else 0
        
        # Estimate completion time
        remaining_combos = total_combos - total_finished
        estimated_completion = None
        if average_combo_time > 0 and remaining_combos > 0:
            estimated_seconds = remaining_combos * average_combo_time
            estimated_completion = datetime.now() + timedelta(seconds=estimated_seconds)
        
        return ProgressSummary(
            total_combos=total_combos,
            completed_combos=completed_combos,
            failed_combos=failed_combos,
            processing_combos=processing_combos,
            pending_combos=pending_combos,
            current_combo=self.current_combo,
            start_time=self.start_time,
            estimated_completion=estimated_completion,
            average_combo_time=average_combo_time,
            success_rate=success_rate,
            current_phase=self.combo_statuses[self.current_combo].current_phase if self.current_combo else 'none'
        )
    
    def _get_combo_description(self, combo_id: str) -> str:
        """Get human-readable description for combo ID."""
        for entry in self.lookup_data:
            if entry['combo_id'] == combo_id:
                return entry['description']
        return f"Unknown combo: {combo_id}"
    
    def display_startup_summary(self) -> None:
        """Display initial summary when optimization starts."""
        print("\n" + "=" * 80)
        print("COMBO ID OPTIMIZATION TRACKING STARTED")
        print("=" * 80)
        print(f"Total Combinations: {len(self.combo_statuses)}")
        print(f"Output Directory: {self.output_dir}")
        print(f"Tracking Log: {self.tracking_log_file}")
        print(f"Start Time: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        # Show first few combo IDs as preview
        print(f"\nCombo ID Preview:")
        for i, (combo_id, status) in enumerate(list(self.combo_statuses.items())[:5]):
            description = self._get_combo_description(combo_id)
            print(f"  {i+1:2d}. {combo_id} - {description}")
        
        if len(self.combo_statuses) > 5:
            print(f"  ... and {len(self.combo_statuses) - 5} more combinations")
        
        print("\n" + "=" * 80)
    
    def display_completion_summary(self) -> None:
        """Display final summary when optimization completes."""
        progress = self.get_progress_summary()
        end_time = datetime.now()
        total_duration = (end_time - self.start_time).total_seconds()
        
        print("\n" + "=" * 80)
        print("COMBO ID OPTIMIZATION COMPLETED")
        print("=" * 80)
        print(f"Total Combinations: {progress.total_combos}")
        print(f"Completed: {progress.completed_combos}")
        print(f"Failed: {progress.failed_combos}")
        print(f"Success Rate: {progress.success_rate:.1f}%")
        print(f"Total Duration: {total_duration:.1f}s ({total_duration/60:.1f} minutes)")
        print(f"Average per Combo: {progress.average_combo_time:.1f}s")
        print(f"Tracking Log: {self.tracking_log_file}")
        
        # Show failed combos if any
        if progress.failed_combos > 0:
            print(f"\nFailed Combinations:")
            for combo_id, status in self.combo_statuses.items():
                if status.status == 'failed':
                    description = self._get_combo_description(combo_id)
                    error_msg = status.error_message or "Unknown error"
                    print(f"   {combo_id} - {description}")
                    print(f"      Error: {error_msg}")
        
        print("=" * 80)
    
    def save_tracking_data(self) -> Path:
        """Save complete tracking data to JSON file."""
        tracking_data = {
            "summary": asdict(self.get_progress_summary()),
            "combo_statuses": {combo_id: asdict(status) for combo_id, status in self.combo_statuses.items()},
            "processing_phases": {combo_id: [asdict(phase) for phase in phases] for combo_id, phases in self.processing_phases.items()},
            "lookup_data": self.lookup_data,
            "start_time": self.start_time.isoformat(),
            "end_time": datetime.now().isoformat()
        }
        
        tracking_file = self.output_dir / f"combo_tracking_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        with open(tracking_file, 'w') as f:
            json.dump(tracking_data, f, indent=2, default=str)
        
        logger.info(f"Saved tracking data to {tracking_file}")
        return tracking_file