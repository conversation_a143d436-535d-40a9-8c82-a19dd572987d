#!/usr/bin/env python
"""
Test script for CPS V4 Optimization Validation with Step-by-Step Framework
"""

import os
import sys
import json
from datetime import datetime
from pathlib import Path

# Import centralized path configuration FIRST
from v4.config.paths_v4 import PROJECT_ROOT, OPTIMIZATION_VALIDATION_DIR, get_validation_dir

from v4.py_reporting.v4_performance_report import PerformanceTableGenerator
import pandas as pd
from v4.engine.data_loader_v4 import load_data_for_backtest
from v4.settings.settings_CPS_v4 import load_settings

def test_optimization_validation():
    print("Testing CPS V4 Optimization Validation...")
    print("=" * 60)

    try:
        # GATEWAY SETUP: Check for optimization parameters and set environment variable
        print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] EARLY GATEWAY: Checking optimization parameters...")

        try:
            from v4.optimization_detector import has_any_optimization_parameters
            has_optimization = has_any_optimization_parameters()

            if has_optimization:
                os.environ['CPS_V4_OPTIMIZATION_ACTIVE'] = 'true'
                print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] EARLY GATEWAY: Set CPS_V4_OPTIMIZATION_ACTIVE=true (optimization parameters detected)")
            else:
                # Ensure it's not set if no optimization parameters
                if 'CPS_V4_OPTIMIZATION_ACTIVE' in os.environ:
                    del os.environ['CPS_V4_OPTIMIZATION_ACTIVE']
                print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] EARLY GATEWAY: CPS_V4_OPTIMIZATION_ACTIVE not set (no optimization parameters)")

        except Exception as e:
            print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] WARNING: EARLY GATEWAY WARNING: Could not check optimization parameters: {e}")

        # Set up validation directory using centralized paths (flat structure)
        print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] Setting up validation directory...")
        validation_timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        validation_dir = get_validation_dir(validation_timestamp)
        print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] Validation directory: {validation_dir}")

        if not validation_dir.exists():
            os.makedirs(validation_dir, exist_ok=True)
            print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] Created validation directory")

        # Initialize validation log (flat structure with prefix)
        with open(validation_dir / 'validation__main.log', 'w') as f:
            f.write(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] Validation started\n')
        print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] Initialized validation log")
        
        # Load base settings and data
        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] Loading settings...')
        try:
            settings = load_settings()
            print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] Settings loaded successfully')
        except Exception as e:
            print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] HARD FAILURE: Failed to load settings: {e}')
            raise RuntimeError(f"Settings loading failed: {e}") from e
        
        # Initialize PerformanceTableGenerator with validation mode enabled and CSV generation
        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] Initializing PerformanceTableGenerator...')
        try:
            ptg = PerformanceTableGenerator(csv_flag_use=True)  # Enable CSV generation for validation
            print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] PerformanceTableGenerator created')
            
            ptg.validation_mode = True  # Enable validation tracking
            ptg.validation_dir = validation_dir  # Set validation directory
            print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] Validation mode enabled')
            
            ptg._setup_validation_directories()  # Setup validation directories
            print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] Validation directories set up')
        except Exception as e:
            print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] HARD FAILURE: Failed to initialize PerformanceTableGenerator: {e}')
            raise RuntimeError(f"PerformanceTableGenerator initialization failed: {e}") from e
        
        # Log validation initialization (flat structure)
        with open(validation_dir / 'validation__main.log', 'a') as f:
            f.write(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] Validation framework initialized\n')
            f.write(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] Generator initialized with validation\n')
    
        # STEP 1: Parameter Validation
        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] STEP 1: Getting optimization combinations...')
        try:
            combinations = ptg.get_optimization_combinations()[:12]  # Test all 12 combinations for 2 variables
            print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] STEP 1: Got {len(combinations)} combinations')
            
            if len(combinations) == 0:
                raise RuntimeError("STEP 1 HARD FAILURE: No optimization combinations found")
            
            print(f'Testing {len(combinations)} combinations:')
            for i, combo in enumerate(combinations):
                print(f'  {i}: {combo}')
            
            # Log combinations to validation directory (flat structure)
            with open(validation_dir / 'combinations.json', 'w') as f:
                json.dump([{k: str(v) for k, v in combo.items()} for combo in combinations], f, indent=2)
            print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] STEP 1: Combinations saved to file')
            
        except Exception as e:
            print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] STEP 1 HARD FAILURE: {e}')
            raise RuntimeError(f"STEP 1 failed: {e}") from e
        
        # Load data - real backtest will generate allocation
        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] Loading price data...')
        try:
            data_result = load_data_for_backtest(settings)
            price_data = data_result['price_data']
            print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] Price data loaded: {price_data.shape}')
        except Exception as e:
            print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] HARD FAILURE: Failed to load price data: {e}')
            raise RuntimeError(f"Price data loading failed: {e}") from e
        
        # STEP 2: Settings Validation (happens during matrix optimization)
        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] STEP 2-10: Running complete validation process...')
        
        # STEP 3: Single Combination Test
        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] STEP 3: Testing single combination...')
        try:
            if len(combinations) > 0:
                print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] STEP 3: Testing combination: {combinations[0]}')
                single_success = ptg._validate_single_combination(combinations[0])
                print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] STEP 3: Single combination result: {single_success}')
                
                if not single_success:
                    raise RuntimeError("STEP 3 HARD FAILURE: Single combination test returned False")
                    
                print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] STEP 3: Single combination test PASSED')
            else:
                raise RuntimeError("STEP 3 HARD FAILURE: No combinations available for testing")
        except Exception as e:
            print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] STEP 3 HARD FAILURE: {e}')
            raise RuntimeError(f"STEP 3 failed: {e}") from e
        
        # STEP 4-10: Full Matrix Optimization with all validation steps
        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] STEP 4-10: Running matrix optimization with full validation...')
        try:
            equity_matrix, metadata = ptg._run_matrix_optimization(combinations)
            print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] STEP 4-10: Matrix optimization completed')
        except Exception as e:
            print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] STEP 4-10 HARD FAILURE: {e}')
            raise RuntimeError(f"STEP 4-10 failed: {e}") from e
        
        # STEP 10: Final Validation Summary
        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] STEP 10: Generating final validation summary...')
    
        # Save validation metadata
        validation_metadata = {
            'timestamp': datetime.now().strftime('%Y-%m-%dT%H:%M:%SZ'),
            'combinations_count': len(combinations),
            'result_shape': equity_matrix.shape[0] if equity_matrix is not None else 0,
            'validation_directory': str(validation_dir),
            'status': 'SUCCESS' if equity_matrix is not None else 'FAILURE',
            'final_step': 'STEP_10_COMPLETED'
        }
        with open(validation_dir / 'validation__metadata.json', 'w') as f:
            json.dump(validation_metadata, f, indent=2)

        # Create final summary (flat structure)
        summary_file = validation_dir / 'step10__validation_summary.json'
        with open(summary_file, 'w') as f:
            json.dump(validation_metadata, f, indent=2)

        # Update final status (flat structure)
        with open(validation_dir / 'status__current_step.txt', 'w') as f:
            f.write('STEP_10_OF_10_COMPLETED')
        with open(validation_dir / 'step10__checkpoint.txt', 'w') as f:
            f.write('STEP_10_OF_10_COMPLETED\n')
            f.write('Step Name: Final Validation Summary\n')
            f.write(f'Timestamp: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}\n')
        
        print(f'\nResult matrix shape: {equity_matrix.shape}')
        print('Final values per combination:')
        for col in equity_matrix.columns:
            final_value = equity_matrix[col].iloc[-1]
            print(f'  {col}: ${final_value:,.2f}')
        
        # Check if values are different
        final_values = [equity_matrix[col].iloc[-1] for col in equity_matrix.columns]
        unique_values = set(final_values)
        if len(unique_values) > 1:
            validation_result = 'SUCCESS'
            message = 'SUCCESS: Different final values detected - parameters are working!'
            print('\n✓ ' + message)
        else:
            validation_result = 'FAILURE'
            message = 'FAILURE: All final values are identical - parameters not working'
            print('\n✗ ' + message)

        # Write final status to status file (flat structure) - use ASCII-safe characters
        with open(validation_dir / 'validation__complete.txt', 'w', encoding='utf-8') as f:
            f.write(f'{validation_result}\n{message}\n')
            f.write(f'Completed at: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}\n')
            f.write(f'Validation directory: {validation_dir}\n')
        
        # Log completion
        with open(validation_dir / 'validation__main.log', 'a') as f:
            f.write(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] Validation completed: {validation_result}\n')
        
        print(f'\nValidation artifacts saved to: {validation_dir}')
        return validation_result == 'SUCCESS'

    except Exception as e:
        print(f"\n{'='*60}")
        print(f"VALIDATION TEST FAILED WITH EXCEPTION")
        print(f"{'='*60}")
        print(f"Error: {e}")
        print(f"Error type: {type(e).__name__}")
        import traceback
        print(f"Full traceback:")
        traceback.print_exc()
        print(f"{'='*60}")
        return False


if __name__ == "__main__":
    try:
        success = test_optimization_validation()
        if success:
            print(f"\n{'='*60}")
            print("VALIDATION TEST COMPLETED SUCCESSFULLY")
            print(f"{'='*60}")
            exit(0)
        else:
            print(f"\n{'='*60}")
            print("VALIDATION TEST FAILED")
            print(f"{'='*60}")
            exit(1)
    except Exception as e:
        print(f"\n{'='*60}")
        print(f"VALIDATION TEST CRASHED WITH UNHANDLED EXCEPTION")
        print(f"{'='*60}")
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
        print(f"{'='*60}")
        exit(1)
