@echo OFF
REM This batch file runs a simple test script to verify the execution environment.

REM Define the path to the virtual environment
SET VENV_PATH=F:\AI_Library\my_quant_env

REM Activate the virtual environment
call "%VENV_PATH%\Scripts\activate.bat"

echo "Running Test Execution Script..."

REM Run the python script and redirect all output for debugging
python "%~dp0v4\engine\test_execution.py" > "%~dp0test_log.txt" 2>&1

echo "Test finished. Check test_output.txt and test_log.txt for results."
