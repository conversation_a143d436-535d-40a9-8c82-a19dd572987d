#!/usr/bin/env python
# verify_critical_issues.py
"""
Targeted verification script for critical issues identified in previous work.
This script specifically tests the fixes for known problems without affecting production.
"""

import os
import sys
import logging
import pandas as pd
import datetime
from datetime import date
import matplotlib
matplotlib.use('Agg')  # Non-interactive backend

# Import official benchmark calculation for compliance (see docs/benchmark_setup.md)
# (Local imports also used in test function for test isolation)

# Add project root to path
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.insert(0, project_root)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(os.path.join(project_root, 'tests', 'critical_issues.log')),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('critical_issues')

# Import verification helpers
from tests.verification_helpers import (
    generate_mock_backtest_results,
    generate_verification_summary,
    verify_excel_report
)

# Create isolated test directories
TEST_OUTPUT_DIR = os.path.join(project_root, 'tests', 'critical_verification')
TEST_REPORT_DIR = os.path.join(TEST_OUTPUT_DIR, 'reports')
TEST_CHART_DIR = os.path.join(TEST_OUTPUT_DIR, 'charts')
TEST_ALLOCATION_DIR = os.path.join(TEST_OUTPUT_DIR, 'allocation')

def setup_test_environment():
    """Create isolated test directories"""
    # Create fresh test directories
    for directory in [TEST_OUTPUT_DIR, TEST_REPORT_DIR, TEST_CHART_DIR, TEST_ALLOCATION_DIR]:
        if not os.path.exists(directory):
            os.makedirs(directory)
    
    logger.info(f"Created test environment at {TEST_OUTPUT_DIR}")
    return True

def test_signal_history_population():
    """
    Test if signal_history is properly populated and preserved.
    
    This addresses the critical issue where signal_history was not being
    properly populated or preserved in the backtest engine.
    """
    logger.info("Testing signal history population...")
    
    try:
        # Import necessary modules
        from engine.backtest import BacktestEngine
        from models.ema_allocation_model import ema_allocation_model, ema_allocation_model_updated
        
        # Create a minimal test dataset
        dates = pd.date_range(start='2024-01-01', end='2024-01-31', freq='B')
        tickers = ['SPY', 'AGG', 'GLD']
        
        # Create price data
        price_data = pd.DataFrame(index=dates)
        for ticker in tickers:
            price_data[ticker] = [100 * (1 + 0.001 * i) for i in range(len(dates))]
        
        # Create a simple model function (not a class)
        model = lambda price_data, returns_data=None: ema_allocation_model(
            price_data=price_data,
            returns_data=returns_data,
            st_lookback=5,
            mt_lookback=10,
            lt_lookback=20
        )
        
        # Create a backtest engine with minimal parameters
        engine = BacktestEngine(
            initial_capital=100000,
            commission_rate=0.001,
            slippage_rate=0.0005,
            benchmark_rebalance_freq='yearly'
        )
        
        # Run the backtest
        results = engine.run_backtest(
            price_data=price_data,
            signal_generator=model,
            rebalance_freq='W-FRI',  # Weekly on Friday
            execution_delay=1
        )
        
        # Check if signal_history exists and is populated
        if 'signal_history' not in results:
            logger.error("signal_history missing from backtest results")
            return False
        
        if results['signal_history'] is None or len(results['signal_history']) == 0:
            logger.error("signal_history is empty")
            return False
        
        # Check if signal_history has the expected structure
        for date, allocations in results['signal_history'].items():
            if not isinstance(date, pd.Timestamp):
                logger.error(f"Invalid date type in signal_history: {type(date)}")
                return False
            
            if not isinstance(allocations, dict):
                logger.error(f"Invalid allocations type in signal_history: {type(allocations)}")
                return False
            
            # Check if allocations contain the tickers
            for ticker in tickers:
                if ticker not in allocations and allocations:
                    logger.warning(f"Ticker {ticker} missing from allocations on {date}")
        
        # Verify signal_history dates align with rebalance frequency
        rebalance_dates = pd.date_range(start=dates[0], end=dates[-1], freq='W-FRI')
        signal_dates = list(results['signal_history'].keys())
        
        # Allow for execution delay
        expected_dates = rebalance_dates[:-1]  # Exclude last date which might not have signal due to delay
        
        if not all(date in signal_dates for date in expected_dates):
            logger.error("signal_history dates don't match expected rebalance dates")
            return False
        
        logger.info(f"Signal history test passed with {len(results['signal_history'])} signals")
        return True
    
    except Exception as e:
        logger.error(f"Error testing signal history: {e}")
        return False

def test_benchmark_calculation():
    """
    Test if benchmark calculation uses price_data (returns) and the official benchmark function.
    
    This addresses the critical issue where benchmark calculation was returning
    all zeros because it was incorrectly using signal_history instead of price_data.
    See docs/benchmark_setup.md for compliance.
    """
    logger.info("Testing benchmark calculation...")
    
    try:
        # Import the official benchmark function
        from engine.benchmark import calculate_equal_weight_benchmark
        
        # Create a test dataset
        dates = pd.date_range(start='2024-01-01', end='2024-01-31', freq='B')
        
        # Create price data with known growth
        price_data = pd.DataFrame(index=dates)
        price_data['SPY'] = [100 * (1 + 0.001 * i) for i in range(len(dates))]
        
        # Calculate daily returns for benchmark function
        returns = price_data.pct_change().fillna(0)
        
        # Use the official benchmark calculation function
        benchmark_returns = calculate_equal_weight_benchmark(returns, rebalance_freq='yearly')
        
        # Check if benchmark returns are calculated correctly
        if benchmark_returns is None:
            logger.error("benchmark_returns is None")
            return False
        
        if len(benchmark_returns) != len(dates):  # Should match dates
            logger.error(f"Wrong number of benchmark returns: {len(benchmark_returns)}")
            return False
        
        # Check if returns are non-zero (the critical issue was all zeros)
        if all(return_val == 0 for return_val in benchmark_returns):
            logger.error("All benchmark returns are zero")
            return False
        
        # Calculate expected returns for a single asset (should match pct_change for equal weight)
        expected_returns = returns['SPY']  # Since only one asset, equal weight
        
        # Compare calculated returns with expected returns
        if not benchmark_returns.equals(expected_returns):
            logger.error("Benchmark returns don't match expected values")
            return False
        
        logger.info("Benchmark calculation test passed")
        return True
    
    except Exception as e:
        logger.error(f"Error testing benchmark calculation: {e}")
        return False

def test_logging_levels():
    """
    Test if trade/portfolio update messages use DEBUG level instead of INFO.
    
    This addresses the issue where excessive logging was occurring at INFO level
    despite changing to DEBUG level.
    """
    logger.info("Testing logging levels...")
    
    try:
        # Import necessary modules
        from engine.portfolio import Portfolio
        from engine.orders import Order
        import logging
        
        # Create a test logger that captures log messages
        test_logger = logging.getLogger('test_portfolio')
        log_capture = []
        
        class TestHandler(logging.Handler):
            def emit(self, record):
                log_capture.append((record.levelname, record.getMessage()))
        
        test_handler = TestHandler()
        test_logger.addHandler(test_handler)
        test_logger.setLevel(logging.DEBUG)
        
        # Create a portfolio with logging
        try:
            # Portfolio class accepts initial_capital parameter
            portfolio = Portfolio(initial_capital=100000)
            
            # Set the logger manually if needed
            if hasattr(portfolio, 'logger'):
                portfolio.logger = test_logger
        except Exception as e:
            logger.error(f"Could not create Portfolio object: {e}")
            return False
        
        # Create a test order
        order = Order(
            symbol='SPY',
            quantity=10,
            price=400.0,
            order_type='MARKET'
        )
        
        # Check if the Order class has a to_trade method
        if hasattr(order, 'to_trade'):
            # Execute a trade to trigger logging
            portfolio.update_from_trade(order.to_trade(execution_price=400.0))
        else:
            # Create a trade manually if to_trade method doesn't exist
            from engine.orders import Trade
            trade = Trade(
                order=order,
                execution_date=date.today(),
                execution_price=400.0,
                commission=1.0,
                amount=4000.0
            )
            portfolio.update_from_trade(trade)
        
        # Check log messages
        trade_update_messages = [
            msg for level, msg in log_capture 
            if "Updated portfolio from trade" in msg or "New cash balance" in msg
        ]
        
        if not trade_update_messages:
            logger.error("No trade update messages found in logs")
            return False
        
        # Check if all trade update messages are at DEBUG level
        info_level_messages = [
            msg for level, msg in log_capture 
            if "Updated portfolio from trade" in msg or "New cash balance" in msg
            and level == 'INFO'
        ]
        
        if info_level_messages:
            logger.error(f"Found {len(info_level_messages)} trade messages at INFO level")
            return False
        
        logger.info("Logging levels test passed")
        return True
    
    except Exception as e:
        logger.error(f"Error testing logging levels: {e}")
        return False

def test_execution_delay_parameter():
    """
    Test if execution_delay parameter optimization flows to performance tab output.
    
    This addresses the issue where execution delay parameter optimization
    was not flowing to performance tab output.
    """
    logger.info("Testing execution delay parameter optimization...")
    
    try:
        # Import necessary modules
        from v3_engine.V3_perf_repadapt_legacybridge import generate_performance_report
        
        # Create mock results with execution_delay as optimization parameter
        mock_results = generate_mock_backtest_results(TEST_OUTPUT_DIR)
        
        # Convert any Timestamp objects to strings in the mock results
        # This prevents issues with Excel serialization
        for key in mock_results:
            if isinstance(mock_results[key], pd.Timestamp):
                mock_results[key] = str(mock_results[key])
            elif isinstance(mock_results[key], dict):
                for subkey in mock_results[key]:
                    if isinstance(mock_results[key][subkey], pd.Timestamp):
                        mock_results[key][subkey] = str(mock_results[key][subkey])
        
        # Set execution_delay as optimization parameter tuple
        # Format: ('Y', current_value, min_val, max_val, step)
        mock_results['parameters']['execution_delay'] = ('Y', 1, 0, 5, 1)
        
        # Generate performance report
        report_path = os.path.join(TEST_REPORT_DIR, "execution_delay_test.xlsx")
        
        # Call performance report generation
        generate_performance_report(
            results=mock_results,
            output_path=report_path,
            create_excel=True
        )
        
        # Verify Excel file was created
        if not os.path.exists(report_path):
            logger.error(f"Performance report not created at {report_path}")
            return False
        
        # Load the Excel file to check if execution_delay is properly handled
        try:
            excel = pd.ExcelFile(report_path)
            
            # Check if performance tab exists
            if 'Performance' not in excel.sheet_names:
                logger.error("Performance sheet missing from report")
                return False
            
            # Load the performance sheet
            performance_df = pd.read_excel(report_path, sheet_name='Performance')
            
            # Check if execution_delay parameter is included
            param_found = False
            for col in performance_df.columns:
                if 'execution_delay' in col.lower():
                    param_found = True
                    break
            
            if not param_found:
                logger.error("execution_delay parameter not found in Performance sheet")
                return False
            
            logger.info("Execution delay parameter test passed")
            return True
            
        except Exception as e:
            logger.error(f"Error checking Excel report: {e}")
            return False
    
    except Exception as e:
        logger.error(f"Error testing execution delay parameter: {e}")
        return False

def test_parameter_registration():
    """
    Test if parameters are registered correctly using StrategyOptimizeParameter.
    
    This addresses the issue where parameters needed to be registered correctly
    to appear in the GUI and use the correct parameter class.
    """
    logger.info("Testing parameter registration...")
    
    try:
        # Import necessary modules
        from v3_engine.parameter_registry import get_registry, register_parameter
        
        # Import the parameter type class
        try:
            from v3_engine.strategy_optimize_parameter import StrategyOptimizeParameter
        except ImportError:
            logger.error("Could not import StrategyOptimizeParameter class")
            return False
            
        # Reset the parameter registry to avoid conflicts
        registry = get_registry()
        registry.reset()
        
        # Register required test parameters
        try:
            # Register test parameters
            required_params = [
                StrategyOptimizeParameter(
                    name='create_excel',
                    param_type='categorical',
                    default=True,
                    choices=[True, False],
                    description='Create Excel report',
                    group='reporting'
                ),
                StrategyOptimizeParameter(
                    name='save_trade_log',
                    param_type='categorical',
                    default=True,
                    choices=[True, False],
                    description='Save trade log',
                    group='reporting'
                ),
                StrategyOptimizeParameter(
                    name='create_charts',
                    param_type='categorical',
                    default=True,
                    choices=[True, False],
                    description='Create charts',
                    group='reporting'
                ),
                StrategyOptimizeParameter(
                    name='chart_format',
                    param_type='categorical',
                    default='png',
                    choices=['png', 'pdf', 'svg'],
                    description='Chart format',
                    group='reporting'
                ),
                StrategyOptimizeParameter(
                    name='chart_dpi',
                    param_type='numeric',
                    default=300,
                    min_val=72,
                    max_val=600,
                    step=1,
                    description='Chart DPI',
                    group='reporting'
                ),
            ]
            
            # Register each parameter
            for param in required_params:
                register_parameter(param)
                
            # Get all parameters from registry
            reporting_params = registry.get_all_parameters()
            
        except Exception as e:
            logger.error(f"Error registering test parameters: {e}")
            return False
        
        # Check if parameters exist
        if not reporting_params:
            logger.error("No reporting parameters found")
            return False
        
        # Check if all parameters use StrategyOptimizeParameter
        non_strategy_params = []
        for name, param in reporting_params.items():
            if not isinstance(param, StrategyOptimizeParameter):
                non_strategy_params.append(name)
        
        if non_strategy_params:
            logger.error(f"Parameters not using StrategyOptimizeParameter: {non_strategy_params}")
            return False
        
        # Check for required parameters
        required_params = ['create_excel', 'save_trade_log', 'create_charts', 'chart_format', 'chart_dpi']
        missing_params = [param for param in required_params if param not in reporting_params]
        
        if missing_params:
            logger.error(f"Missing required parameters: {missing_params}")
            return False
        
        logger.info("Parameter registration test passed")
        return True
    
    except Exception as e:
        logger.error(f"Error testing parameter registration: {e}")
        return False

def run_critical_verification():
    """Run verification for critical issues"""
    logger.info("Starting critical issues verification")
    
    # Set up test environment
    setup_test_environment()
    
    # Initialize results
    timestamp = datetime.datetime.now().strftime("%Y-%m-%d_%H%M%S")
    results = {
        "signal_history": False,
        "benchmark_calculation": False,
        "logging_levels": False,
        "execution_delay": False,
        "parameter_registration": False
    }
    
    # Run tests
    results["signal_history"] = test_signal_history_population()
    results["benchmark_calculation"] = test_benchmark_calculation()
    results["logging_levels"] = test_logging_levels()
    results["execution_delay"] = test_execution_delay_parameter()
    results["parameter_registration"] = test_parameter_registration()
    
    # Generate summary
    summary_path = generate_verification_summary(results, timestamp, TEST_OUTPUT_DIR)
    
    # Return success status
    return all(results.values()), summary_path

if __name__ == "__main__":
    success, summary_path = run_critical_verification()
    logger.info(f"Verification {'passed' if success else 'failed'}")
    logger.info(f"Summary available at: {summary_path}")
    sys.exit(0 if success else 1)
