#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
v4/pipeline/trading.py

Trading Operations and Signal Handling for CPS v4 Pipeline

This module contains trading-related functions extracted from run_unified_pipeline.py:
- modify_run_trading_to_accept_dataframe(): Modified version of run_trading_phase to accept DataFrame directly

This function extends the existing run_trading_phase functionality to accept pre-computed signals
as a DataFrame, enabling direct handoff from signal generation.

All functions preserve their original functionality and imports exactly as they were.

Author: AI Assistant
Date: 2025-07-26
"""

import os
import logging
from datetime import datetime
from pathlib import Path
from typing import Optional, Dict, Any
import pandas as pd

# Import centralized path configuration
from v4.config.paths_v4 import V4_TRACE_OUTPUTS_DIR


def modify_run_trading_to_accept_dataframe(signals_df: Optional[pd.DataFrame] = None, 
                                         signals_file: Optional[str] = None,
                                         custom_settings_file: Optional[str] = None) -> Dict[str, Any]:
    """Modified version of run_trading_phase to accept DataFrame directly.
    
    This function extends the existing run_trading_phase functionality to accept
    pre-computed signals as a DataFrame, enabling direct handoff from signal generation.
    
    Args:
        signals_df: Pre-computed signals DataFrame (takes precedence over signals_file)
        signals_file: Path to signals file (legacy path)
        
    Returns:
        Results dictionary from backtest execution
    """
    from v4.settings.settings_CPS_v4 import load_settings
    from v4.engine.data_loader_v4 import load_data_for_backtest
    from v4.engine.backtest_v4 import BacktestEngine
    from v4.run_trading_phase import validate_signals
    
    logger = logging.getLogger('unified_pipeline.trading')
    logger.info("[MILESTONE] Starting Trading Phase")
    
    # Load settings and data (use custom file if provided)
    if custom_settings_file:
        settings = load_settings(custom_file=custom_settings_file)
    else:
        settings = load_settings()
    data_result = load_data_for_backtest(settings)
    price_data = data_result['price_data']
    
    # Log data information
    logger.info(f"Loaded price data with shape {price_data.shape}")
    logger.info(f"Date range: {price_data.index.min()} to {price_data.index.max()}")
    logger.info(f"Tickers: {', '.join(price_data.columns)}")
    
    # Handle signals input - DataFrame takes precedence
    if signals_df is not None:
        logger.info("Using pre-computed signals DataFrame from signal generation phase")
        signals = signals_df.copy()
        logger.info(f"Signals DataFrame shape: {signals.shape}")
        logger.info(f"Signals date range: {signals.index.min()} to {signals.index.max()}")
    else:
        # Fallback to file-based loading (legacy path) - CSV only
        # Note: Uses CSV format only (Parquet eliminated for performance)
        if signals_file is None:
            # Find the most recent timestamped signal file
            import glob
            signal_pattern = str(V4_TRACE_OUTPUTS_DIR / "signals_output_*.csv")
            signal_files = glob.glob(signal_pattern)
            if signal_files:
                # Sort by filename to get most recent timestamp
                signals_file = max(signal_files)
                logger.info(f"No signal source provided, using most recent timestamped file: {signals_file}")
            else:
                raise FileNotFoundError(f"No timestamped signal files found in {V4_TRACE_OUTPUTS_DIR}")
        else:
            logger.info(f"Using provided signal file: {signals_file}")

        signals_path = Path(signals_file)
        if not signals_path.exists():
            logger.error(f"Signals file {signals_file} not found!")
            raise FileNotFoundError(f"Signals file {signals_file} not found!")

        try:
            # Load CSV file and set Date column as index
            signals = pd.read_csv(signals_file, index_col='Date', parse_dates=True)
            logger.info(f"Loaded signals from CSV with shape {signals.shape}")
            logger.info(f"Signals date range: {signals.index.min()} to {signals.index.max()}")

        except Exception as e:
            logger.error(f"Error loading signals file: {e}")
            raise

    # Validate signals
    if not validate_signals(signals, price_data):
        logger.error("Signal validation failed")
        raise ValueError("Signal validation failed")
    
    # Validate that signals sum to 100% (within tolerance)
    signal_sums = signals.sum(axis=1)
    max_deviation = abs(signal_sums - 1.0).max()
    if max_deviation > 0.01:  # 1% tolerance
        logger.warning(f"Signal validation warning! Max deviation from 100%: {max_deviation:.3%}")
    else:
        logger.info(f"Signal validation passed. Max deviation from 100%: {max_deviation:.3%}")
    
    # Run backtest
    logger.info("Initializing backtest engine")
    engine = BacktestEngine(settings)
    
    # Validate that BacktestEngine has required methods
    if not hasattr(engine, 'run_backtest_with_signals'):
        logger.error("BacktestEngine does not have run_backtest_with_signals method")
        raise AttributeError("BacktestEngine does not have run_backtest_with_signals method")
    
    logger.info("Running backtest with pre-computed signals")
    results = engine.run_backtest_with_signals(signals, price_data)
    
    # Save results - Always save essential result files, but log appropriately
    optimization_active = os.environ.get('CPS_V4_OPTIMIZATION_ACTIVE', '').lower() == 'true'
    
    output_dir = V4_TRACE_OUTPUTS_DIR
    os.makedirs(output_dir, exist_ok=True)
    
    # Add timestamp to filenames
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    allocation_history = results.get('allocation_history')
    trade_log = results.get('trade_log')
    
    # Load CSV control flags from settings
    from v4.settings.settings_CPS_v4 import load_settings
    settings = load_settings()
    csv_valid_det = settings.get('system', {}).get('csv_valid_det', False)
    if isinstance(csv_valid_det, str):
        csv_valid_det = csv_valid_det.lower() == 'true'

    # ALLOCATION HISTORY - Only save if csv_valid_det = True during optimization
    if allocation_history is not None:
        if not optimization_active or csv_valid_det:
            allocation_file = output_dir / f"allocation_history_{timestamp}.csv"
            allocation_history.to_csv(str(allocation_file))
            logger.info(f"Saved allocation history to: {allocation_file}")
        else:
            logger.info(f"[OPTIMIZATION] Skipped allocation history CSV (csv_valid_det = False)")
    else:
        logger.warning("No allocation history in results")

    # TRADE LOG - Only save if csv_valid_det = True during optimization
    if trade_log is not None:
        if not optimization_active or csv_valid_det:
            trades_file = output_dir / f"trade_log_{timestamp}.csv"
            trade_log.to_csv(str(trades_file))
            logger.info(f"Saved trade log to: {trades_file}")
        else:
            logger.info(f"[OPTIMIZATION] Skipped trade log CSV (csv_valid_det = False)")
    else:
        logger.warning("No trade log in results")
    
    logger.info("[MILESTONE] Trading phase complete")
    return results
