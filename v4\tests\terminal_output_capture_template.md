# Terminal Output Capture Template

This template outlines how to capture terminal output from a batch script to a text file:

1. Activate the Python virtual environment:
   ```batch
   call "%VENV_PATH%\Scripts\activate.bat"
   ```
2. Ensure the output directory exists:
   ```batch
   if not exist "%~dp0output" mkdir "%~dp0output"
   ```
3. Run your Python script and redirect both stdout and stderr to a file:
   ```batch
   python "%~dp0your_script.py" > "%~dp0output\your_output.txt" 2>&1
   ```
4. Check exit code and display output:
   ```batch
   if %ERRORLEVEL% EQU 0 (
       echo Script completed successfully.
       type "%~dp0output\your_output.txt"
   ) else (
       echo <PERSON>ript failed.
       type "%~dp0output\your_output.txt"
   )
   ```
5. (Optional) Pause to review the output:
   ```batch
   pause
   ```
