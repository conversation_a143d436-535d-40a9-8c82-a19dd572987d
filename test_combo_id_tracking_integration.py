#!/usr/bin/env python
"""
Test script for Combo ID Tracking Integration

This script tests the integration of ComboIDTracker with the optimization pipeline.

Author: AI Assistant
Date: 2025-07-28
"""

import sys
import os
from pathlib import Path
import tempfile

# Add the project root to Python path
sys.path.insert(0, str(Path(__file__).parent))

# Import the tracking system
from v4.py_reporting.combo_id_tracker import ComboIDTracker

def test_combo_id_tracker_basic():
    """Test basic ComboIDTracker functionality."""
    print("=" * 60)
    print("TESTING: ComboIDTracker Basic Functionality")
    print("=" * 60)
    
    # Create test combinations
    combinations = [
        {
            'st_lookback': 5,
            'mt_lookback': 30,
            'lt_lookback': 100,
            'execution_delay': 1,
            'system_lookback': 60,
            'top_n': 2
        },
        {
            'st_lookback': 15,
            'mt_lookback': 70,
            'lt_lookback': 100,
            'execution_delay': 1,
            'system_lookback': 60,
            'top_n': 2
        }
    ]
    
    try:
        # Create temporary directory for testing
        with tempfile.TemporaryDirectory() as temp_dir:
            output_dir = Path(temp_dir)
            
            # Initialize tracker with file logging disabled for testing
            print("1. Initializing ComboIDTracker...")
            tracker = ComboIDTracker(combinations, output_dir, enable_file_logging=False)
            
            # Test startup summary
            print("\n2. Testing startup summary...")
            tracker.display_startup_summary()
            
            # Test combo processing simulation
            print("\n3. Testing combo processing simulation...")
            
            # Process first combo successfully
            combo_id_1 = "S5_M30_L100_E1_T2"
            tracker.start_processing(combo_id_1, combinations[0])
            tracker.update_phase(combo_id_1, "settings_creation", "running", {"file": "temp_settings.ini"})
            tracker.update_phase(combo_id_1, "pipeline_execution", "running")
            tracker.register_file_creation(combo_id_1, "settings", Path("temp_settings_S5_M30_L100_E1_T2.ini"))
            tracker.register_file_creation(combo_id_1, "portfolio", Path("unified_portfolio_combo_S5_M30_L100_E1_T2.csv"))
            tracker.complete_processing(combo_id_1, True, 45.2, {"final_value": 125000.0})
            
            # Process second combo with failure
            combo_id_2 = "S15_M70_L100_E1_T2"
            tracker.start_processing(combo_id_2, combinations[1])
            tracker.update_phase(combo_id_2, "settings_creation", "running")
            tracker.update_phase(combo_id_2, "pipeline_execution", "failed", {"error": "Timeout after 600s"})
            tracker.complete_processing(combo_id_2, False, 600.0, {"error": "Pipeline timeout"})
            
            # Test status retrieval
            print("\n4. Testing status retrieval...")
            current_status = tracker.get_current_status()
            print(f"Current status: {len(current_status['statuses'])} combos tracked")
            
            progress = tracker.get_progress_summary()
            print(f"Progress: {progress.completed_combos} completed, {progress.failed_combos} failed")
            print(f"Success rate: {progress.success_rate:.1f}%")
            
            # Test completion summary
            print("\n5. Testing completion summary...")
            tracker.display_completion_summary()
            
            # Test data saving
            print("\n6. Testing data saving...")
            tracking_file = tracker.save_tracking_data()
            print(f"Tracking data saved to: {tracking_file}")
            
            # Verify files exist
            files_created = list(output_dir.glob("*"))
            print(f"\nFiles created: {len(files_created)}")
            for file in files_created:
                print(f"  - {file.name}")
            
            # Clean up tracker resources
            tracker.cleanup()
            
            print("\n✅ All basic functionality tests passed!")
            return True
            
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_combo_id_validation():
    """Test combo ID validation functions."""
    print("\n" + "=" * 60)
    print("TESTING: Combo ID Validation")
    print("=" * 60)
    
    try:
        # Create a simple tracker for validation testing
        combinations = [{'st_lookback': 5, 'mt_lookback': 30, 'lt_lookback': 100, 'execution_delay': 1, 'system_lookback': 60, 'top_n': 2}]
        
        with tempfile.TemporaryDirectory() as temp_dir:
            tracker = ComboIDTracker(combinations, Path(temp_dir), enable_file_logging=False)
            
            # Test valid combo ID formats
            valid_ids = ["S5_M30_L100_E1_T2", "S15_M70_L100_E1_T2", "S25_M90_L100_E1_T2"]
            invalid_ids = ["S5_M30_L100", "invalid_format", "S5-M30-L100-E1-T2", ""]
            
            print("Testing valid combo ID formats:")
            for combo_id in valid_ids:
                is_valid = tracker.validate_combo_id_format(combo_id)
                status = "✅ VALID" if is_valid else "❌ INVALID"
                print(f"  {combo_id}: {status}")
                if not is_valid:
                    return False
            
            print("\nTesting invalid combo ID formats:")
            for combo_id in invalid_ids:
                is_valid = tracker.validate_combo_id_format(combo_id)
                status = "❌ INVALID" if not is_valid else "✅ VALID (unexpected)"
                print(f"  {combo_id}: {status}")
                if is_valid:
                    return False
            
            # Test uniqueness validation
            print("\nTesting combo ID uniqueness:")
            unique_ids = ["S5_M30_L100_E1_T2", "S15_M70_L100_E1_T2"]
            duplicate_ids = ["S5_M30_L100_E1_T2", "S15_M70_L100_E1_T2", "S5_M30_L100_E1_T2"]
            
            unique_duplicates = tracker.validate_combo_id_uniqueness(unique_ids)
            duplicate_duplicates = tracker.validate_combo_id_uniqueness(duplicate_ids)
            
            print(f"  Unique list duplicates: {unique_duplicates} (should be empty)")
            print(f"  Duplicate list duplicates: {duplicate_duplicates} (should contain S5_M30_L100_E1_T2)")
            
            if len(unique_duplicates) != 0 or "S5_M30_L100_E1_T2" not in duplicate_duplicates:
                return False
            
            # Clean up tracker resources
            tracker.cleanup()
            
            print("\n✅ All validation tests passed!")
            return True
            
    except Exception as e:
        print(f"❌ Validation test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tracking integration tests."""
    print("🚀 COMBO ID TRACKING INTEGRATION TESTING")
    print("Testing combo ID tracking system integration...")
    
    results = []
    
    # Test 1: Basic tracker functionality
    results.append(test_combo_id_tracker_basic())
    
    # Test 2: Validation functions
    results.append(test_combo_id_validation())
    
    # Summary
    print("\n" + "=" * 60)
    print("TRACKING INTEGRATION TEST SUMMARY")
    print("=" * 60)
    
    test_names = [
        "ComboIDTracker Basic Functionality",
        "Combo ID Validation Functions"
    ]
    
    for i, (name, result) in enumerate(zip(test_names, results)):
        status = '✅ PASS' if result else '❌ FAIL'
        print(f"{i+1}. {name}: {status}")
    
    all_passed = all(results)
    overall_status = '✅ ALL TESTS PASSED' if all_passed else '❌ SOME TESTS FAILED'
    
    print(f"\nOverall Result: {overall_status}")
    
    if all_passed:
        print("\n🎉 Combo ID tracking integration is working correctly!")
        print("✅ ComboIDTracker initialization works")
        print("✅ Real-time status updates work")
        print("✅ Progress tracking works")
        print("✅ File registration works")
        print("✅ Validation functions work")
        print("✅ Data persistence works")
        print("\nThe tracking system is ready for integration with the optimization pipeline!")
    else:
        print("\n⚠️  Some tests failed. Please check the implementation.")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)