"""
Direct test to find the execution delay parameter error.
This script will identify exactly where the error occurs and log it to a text file.
"""

import sys
import os
from pathlib import Path
import logging
import traceback

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Import necessary modules
from config.paths import OUTPUT_DIR
from config.config_v2 import config_v2, _get_param_value
from config.local_parameter_optimization import define_parameter
from engine.backtest import BacktestEngine

# Define output file
output_file = OUTPUT_DIR / "debug" / "execution_delay_error.txt"
os.makedirs(output_file.parent, exist_ok=True)

with open(output_file, 'w') as f:
    f.write("EXECUTION DELAY PARAMETER ERROR DIAGNOSIS\n")
    f.write("========================================\n\n")
    
    # Import necessary modules
    try:
        f.write("Step 1: Importing modules\n")
        from config.config_v2 import config_v2, _get_param_value
        from config.local_parameter_optimization import define_parameter
        from engine.backtest import BacktestEngine
        f.write("SUCCESS: All modules imported\n\n")
    except Exception as e:
        f.write(f"ERROR: Failed to import modules: {e}\n")
        f.write(traceback.format_exc())
        f.write("\n")
        sys.exit(1)
    
    # Create optimizable parameter
    try:
        f.write("Step 2: Creating optimizable parameter\n")
        # This is how the GUI would set up an optimizable execution_delay parameter
        opt_param = ('Y', 1, 0, 5, 1)  # optimize=True, default=1, min=0, max=5, step=1
        f.write(f"Created parameter: {opt_param} (type: {type(opt_param)})\n\n")
    except Exception as e:
        f.write(f"ERROR: Failed to create parameter: {e}\n")
        f.write(traceback.format_exc())
        f.write("\n")
    
    # Test BacktestEngine initialization
    try:
        f.write("Step 3: Testing BacktestEngine initialization\n")
        engine = BacktestEngine()
        f.write("SUCCESS: Created BacktestEngine\n\n")
    except Exception as e:
        f.write(f"ERROR: Failed to create BacktestEngine: {e}\n")
        f.write(traceback.format_exc())
        f.write("\n")
    
    # Create test data
    try:
        f.write("Step 4: Creating test data\n")
        import pandas as pd
        import numpy as np
        dates = pd.date_range(start='2025-01-01', periods=10)
        price_data = pd.DataFrame({
            'SPY': np.linspace(100, 110, 10),
            'TLT': np.linspace(90, 100, 10)
        }, index=dates)
        
        def dummy_signal(price_data, **params):
            return {'SPY': 0.6, 'TLT': 0.4}
        
        f.write("SUCCESS: Created test data and signal function\n\n")
    except Exception as e:
        f.write(f"ERROR: Failed to create test data: {e}\n")
        f.write(traceback.format_exc())
        f.write("\n")
    
    # Test run_backtest with direct value
    try:
        f.write("Step 5: Testing run_backtest with direct value (execution_delay=1)\n")
        result1 = engine.run_backtest(
            price_data=price_data,
            signal_generator=dummy_signal,
            execution_delay=1
        )
        f.write("SUCCESS: Ran backtest with direct value\n\n")
    except Exception as e:
        f.write(f"ERROR: Failed to run backtest with direct value: {e}\n")
        f.write(traceback.format_exc())
        f.write("\n")
    
    # Test run_backtest with parameter tuple - this is where the error occurs
    try:
        f.write("Step 6: Testing run_backtest with parameter tuple (execution_delay=('Y', 1, 0, 5, 1))\n")
        # This is the exact scenario that fails in the GUI
        result2 = engine.run_backtest(
            price_data=price_data,
            signal_generator=dummy_signal,
            execution_delay=opt_param
        )
        f.write("SUCCESS: Ran backtest with parameter tuple\n\n")
    except Exception as e:
        f.write(f"ERROR: Failed to run backtest with parameter tuple: {e}\n")
        f.write(traceback.format_exc())
        f.write("\n")
        
        # Analyze the error in detail
        f.write("ERROR ANALYSIS:\n")
        error_str = str(e)
        if "isinstance()" in error_str and "arg 2" in error_str:
            f.write("This is the 'isinstance() arg 2' error you're seeing in the GUI.\n")
            f.write("The error occurs when trying to check if a parameter is an instance of a type.\n\n")
            
            # Examine the BacktestEngine.run_backtest method
            import inspect
            run_backtest_source = inspect.getsource(engine.run_backtest)
            f.write("BacktestEngine.run_backtest method (first 500 chars):\n")
            f.write(run_backtest_source[:500])
            f.write("\n...\n\n")
            
            # Check for get_param_value calls before docstring
            docstring_pos = run_backtest_source.find('"""')
            pre_docstring = run_backtest_source[:docstring_pos]
            if "get_param_value" in pre_docstring:
                f.write("FOUND ISSUE: get_param_value is called BEFORE the docstring.\n")
                f.write("This causes Python to interpret the code incorrectly.\n")
                f.write("The fix is to move these calls AFTER the docstring.\n\n")
            
            # Look for isinstance calls in the method
            from datetime import date, datetime
            f.write("Testing isinstance calls with our parameter:\n")
            
            # Test various isinstance scenarios
            tests = [
                ("isinstance(opt_param, int)", lambda: isinstance(opt_param, int)),
                ("isinstance(opt_param, (int, float))", lambda: isinstance(opt_param, (int, float))),
                ("isinstance(opt_param, date)", lambda: isinstance(opt_param, date)),
                ("isinstance(opt_param, (date, datetime))", lambda: isinstance(opt_param, (date, datetime))),
                ("isinstance(opt_param, tuple)", lambda: isinstance(opt_param, tuple)),
                ("isinstance(opt_param, (tuple, list))", lambda: isinstance(opt_param, (tuple, list)))
            ]
            
            for desc, test_func in tests:
                try:
                    result = test_func()
                    f.write(f"{desc} => {result}\n")
                except Exception as test_error:
                    f.write(f"{desc} => ERROR: {test_error}\n")
            
            f.write("\nCONCLUSION: The error occurs because:\n")
            f.write("1. get_param_value is called before the docstring in run_backtest\n")
            f.write("2. This causes the parameter value to be a tuple when it should be an int\n")
            f.write("3. Later in the code, there's an isinstance() check that fails with this tuple\n")
    
    f.write("\nDiagnosis complete. Check the file for detailed results.\n")

print(f"Error diagnosis saved to: {output_file}")
