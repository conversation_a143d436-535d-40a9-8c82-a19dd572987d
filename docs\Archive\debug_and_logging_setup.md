# Debug and Logging Setup Overview

This document provides a quick recap of the current logging and debug infrastructure in the backtest engine.

## 1. Python Logging Configuration

- Configured at module import with environment variable support:

```python
lvl = os.getenv("BACKTEST_LOG_LEVEL", "INFO").upper()
numeric = getattr(logging, lvl, logging.INFO)
logging.basicConfig(
    level=numeric,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)
```

- **Behavior:**
  - Log level is controlled by the `BACKTEST_LOG_LEVEL` environment variable
  - Default level is INFO if environment variable is not set
  - Valid values: DEBUG, INFO, WARNING, ERROR, CRITICAL

## 2. Custom Debug Logger

- Attempted import:

```python
try:
    from utils.debug_logger import get_benchmark_debug_logger
except ImportError:
    def get_benchmark_debug_logger(config=None):
        return None
```

- Usage in `_calculate_results`:

```python
debug_logger = get_benchmark_debug_logger(self.config)
if debug_logger:
    debug_logger.log("Price data info for benchmark calculation:")
    debug_logger.log_dataframe(price_data, "price_data")
    # ... other debug_logger.log() calls
```

- **Current State:**
  - No `utils/debug_logger.py` file present; fallback always returns `None`.
  - Debug logging code paths are never executed.

## 3. Logging Usage in `BacktestEngine`

- **Initialization (`__init__`):**

```python
logger.info(f"Initialized backtest engine with ${initial_capital:,.2f} capital")
logger.info(f"Commission rate: {commission_rate:.2%}")
logger.info(f"Slippage rate: {slippage_rate:.2%}")
```

- **Run Method (`run_backtest`):**

```python
logger.info(f"Starting backtest with {rebalance_freq} rebalancing")
logger.info(f"Execution delay: {execution_delay} days")
```

## 4. Current Logging Issues

- **Excessive INFO Logging:** Many operational details are logged at INFO level:
  - Trade execution details
  - Portfolio updates
  - Cash balance changes
  - Order adjustments
  
- **Inconsistent Log Levels:** Some modules use DEBUG appropriately while others use INFO for all messages

## 5. Best Practices

- **Critical Information** (INFO level):
  - Backtest start/end
  - Final performance metrics
  - Major configuration settings
  - Errors that affect results

- **Operational Details** (DEBUG level):
  - Individual trades
  - Portfolio updates
  - Order adjustments
  - Signal generation details
  - Cash balance changes

## 6. Usage

- **Normal Operation:** Run with default settings (INFO level)

```bash
python run_backtest_v2_with_metrics.py
```

- **Detailed Debugging:** Set environment variable for verbose output

```bash
set BACKTEST_LOG_LEVEL=DEBUG
python run_backtest_v2_with_metrics.py
```

## 7. Next Steps

- **Standardize Logging:** Update all modules to use consistent log levels
  - Change operational details in portfolio.py and execution.py to DEBUG level
  - Ensure model logging uses DEBUG for calculation details
  
- **Enhance Signal History:** Fix signal_history tracking to ensure allocation reports generate properly
- **Standardization:** Audit other modules for consistent logging patterns.

## 5. File-level Instructions

- At the top of `engine/backtest.py`, update logging initialization to use the `BACKTEST_LOG_LEVEL` environment variable:

```python
import os, logging
lvl = os.getenv("BACKTEST_LOG_LEVEL", "INFO").upper()
numeric = getattr(logging, lvl, logging.INFO)
logging.basicConfig(
    level=numeric,
    format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)
```

- Set `BACKTEST_LOG_LEVEL` in your environment to adjust verbosity without code changes:
  - **Windows PowerShell:**
    ```powershell
    $env:BACKTEST_LOG_LEVEL="WARNING"; python run_backtest.py ...
    ```
  - **Unix-like shell:**
    ```bash
    export BACKTEST_LOG_LEVEL="DEBUG"
    python run_backtest.py ...
    ```
