# Parameter Optimization Module

This module provides tools for defining, validating, and generating parameter combinations for strategy optimization and backtesting.

## Key Functions

### `define_parameter(optimize, default_value, min_value, max_value, increment)`

Define a parameter for optimization.

**Parameters:**

- `optimize`: Whether to optimize this parameter (True) or use default value (False)
- `default_value`: Default value for the parameter
- `min_value`: Minimum value for optimization range
- `max_value`: Maximum value for optimization range
- `increment`: Step size for optimization range

**Returns:**

- Parameter tuple in format ('Y'/'N', default_value, min_value, max_value, increment)

**Example:**

```python
momentum_period = define_parameter(True, 30, 20, 40, 5)
print(momentum_period)  # ('Y', 30, 20, 40, 5)
```

### `validate_parameter(param, param_name)`

Validate a parameter tuple.

**Parameters:**

- `param`: Parameter tuple to validate
- `param_name`: Name of the parameter (for error messages)

**Returns:**

- True if parameter is valid, raises ValueError otherwise

### `get_parameter_range(param, param_name)`

Generate a range of values for a parameter based on its tuple definition.

**Parameters:**

- `param`: Parameter tuple in format ('Y'/'N', default_value, min_value, max_value, increment)
- `param_name`: Name of the parameter (for error messages)

**Returns:**

- List of parameter values to test

### `get_parameter_combinations(parameters, optimize_flag=True)`

Generate all combinations of parameters to optimize based on the tuple configuration.

**Parameters:**

- `parameters`: Dictionary of parameter names and their tuple definitions
- `optimize_flag`: Global flag to enable/disable optimization

**Returns:**

- List of parameter dictionaries, each representing a combination to test

**Example:**

```python
parameters = {
    'mom_period': ('Y', 30, 20, 40, 10),
    'vol_period': ('Y', 20, 10, 30, 10),
    'pos_qty': ('N', 5, 3, 7, 1)
}
combinations = get_parameter_combinations(parameters)
print(len(combinations))  # 6 (3 mom_period values × 2 vol_period values × 1 pos_qty value)
```

### `filter_parameter_combinations(combinations, filter_func)`

Filter parameter combinations based on a custom filter function.

**Parameters:**

- `combinations`: List of parameter dictionaries
- `filter_func`: Function that takes a parameter dictionary and returns True/False

**Returns:**

- Filtered list of parameter dictionaries

**Example:**

```python
# Filter combinations where mom_period > vol_period
def custom_filter(params):
    return params['mom_period'] > params['vol_period']

filtered = filter_parameter_combinations(combinations, custom_filter)
```

### `add_derived_parameters(combinations, derived_funcs)`

Add derived parameters to each combination based on custom functions.

**Parameters:**

- `combinations`: List of parameter dictionaries
- `derived_funcs`: Dictionary of parameter names and functions to calculate them

**Returns:**

- List of parameter dictionaries with derived parameters added

**Example:**

```python
# Add a derived parameter that is the ratio of two other parameters
def calc_ratio(params):
    return params['mom_period'] / params['vol_period']

enhanced = add_derived_parameters(combinations, {'ratio': calc_ratio})
```

### `optimize_parameters(parameters, evaluation_func, optimize_flag=True, filter_func=None, derived_funcs=None, max_combinations=None)`

Complete parameter optimization workflow.

**Parameters:**

- `parameters`: Dictionary of parameter names and their tuple definitions
- `evaluation_func`: Function that takes a parameter dictionary and returns a score (higher is better)
- `optimize_flag`: Global flag to enable/disable optimization
- `filter_func`: Optional function to filter parameter combinations
- `derived_funcs`: Optional dictionary of functions to calculate derived parameters
- `max_combinations`: Optional maximum number of combinations to test

**Returns:**

- Tuple of (best_parameters, all_results)

**Example:**

```python
def evaluate_strategy(params):
    # Run backtest with params and return Sharpe ratio
    return backtest_sharpe_ratio

best_params, all_results = optimize_parameters(
    parameters=parameters,
    evaluation_func=evaluate_strategy
)
```

## Usage Example

```python
from config.parameter_optimization import define_parameter, optimize_parameters

# Define parameters
parameters = {
    'momentum_period': define_parameter(True, 30, 20, 60, 10),
    'volatility_period': define_parameter(True, 20, 10, 40, 10),
    'position_count': define_parameter(False, 5, 3, 10, 1)  # Not optimized
}

# Define evaluation function
def evaluate_strategy(params):
    # Run backtest with params
    strategy_returns = run_backtest(
        mom_period=params['momentum_period'],
        vol_period=params['volatility_period'],
        pos_count=params['position_count']
    )
    
    # Calculate Sharpe ratio
    sharpe = calculate_sharpe_ratio(strategy_returns)
    return sharpe

# Run optimization
best_params, all_results = optimize_parameters(
    parameters=parameters,
    evaluation_func=evaluate_strategy
)

print("Best parameters:", best_params)
