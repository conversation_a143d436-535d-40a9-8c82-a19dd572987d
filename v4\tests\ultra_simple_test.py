#!/usr/bin/env python
# -*- coding: utf-8 -*-
# ultra_simple_test.py
"""
Ultra simple test script that forces console output at every step.
Uses real data and settings from configuration files.

Author: AI Assistant
Date: 2025-06-14
"""

# Force unbuffered output
import os
os.environ['PYTHONUNBUFFERED'] = '1'

import sys
from pathlib import Path

# Immediately print startup message
sys.stdout.write("=" * 80 + "\n")
sys.stdout.write("SCRIPT STARTED: ultra_simple_test.py\n")
sys.stdout.write("=" * 80 + "\n")
sys.stdout.flush()

# Step 1: Add project root to path
sys.stdout.write("\nSTEP 1: Setting up project path\n")
sys.stdout.flush()

try:
    project_root = Path(__file__).parent.parent.parent
    sys.stdout.write(f"Project root: {project_root}\n")
    sys.stdout.flush()
    
    if str(project_root) not in sys.path:
        sys.path.append(str(project_root))
        sys.stdout.write(f"Added {project_root} to sys.path\n")
    sys.stdout.flush()
except Exception as e:
    sys.stdout.write(f"ERROR in Step 1: {e}\n")
    sys.stdout.flush()
    sys.exit(1)

# Step 2: Import data modules
sys.stdout.write("\nSTEP 2: Importing data modules\n")
sys.stdout.flush()

try:
    import pandas as pd
    import numpy as np
    from datetime import datetime
    sys.stdout.write(f"Pandas version: {pd.__version__}\n")
    sys.stdout.write(f"Numpy version: {np.__version__}\n")
    sys.stdout.flush()
except Exception as e:
    sys.stdout.write(f"ERROR in Step 2: {e}\n")
    sys.stdout.flush()
    sys.exit(1)

# Step 3: Load settings
sys.stdout.write("\nSTEP 3: Loading settings\n")
sys.stdout.flush()

try:
    from v4.settings.settings_CPS_v4 import load_settings
    sys.stdout.write("Imported load_settings\n")
    sys.stdout.flush()
    
    settings = load_settings()
    sys.stdout.write("Settings loaded successfully\n")
    sys.stdout.write("Settings keys:\n")
    for key in settings.keys():
        sys.stdout.write(f"  - {key}\n")
    sys.stdout.flush()
    
    # Print backtest settings explicitly
    sys.stdout.write("\nBACKTEST SETTINGS (DETAILED):\n")
    sys.stdout.write("----------------------------------------\n")
    
    backtest_settings = settings.get('backtest', {})
    sys.stdout.write(f"Strategy: {backtest_settings.get('strategy', 'N/A')}\n")
    sys.stdout.write(f"Rebalance Frequency: {backtest_settings.get('rebalance_freq', 'N/A')}\n")
    sys.stdout.write(f"Execution Delay: {backtest_settings.get('execution_delay', 'N/A')}\n")
    sys.stdout.write(f"Initial Capital: {backtest_settings.get('initial_capital', 'N/A')}\n")
    sys.stdout.write(f"Commission: {backtest_settings.get('commission', 'N/A')}\n")
    sys.stdout.write(f"Slippage: {backtest_settings.get('slippage', 'N/A')}\n")
    sys.stdout.write(f"Benchmark: {backtest_settings.get('benchmark', 'N/A')}\n")
    sys.stdout.write(f"Risk-Free Rate: {backtest_settings.get('risk_free_rate', 'N/A')}\n")
    sys.stdout.write(f"Position Limits: {backtest_settings.get('position_limits', 'N/A')}\n")
    sys.stdout.write(f"Stop Loss: {backtest_settings.get('stop_loss', 'N/A')}\n")
    sys.stdout.write(f"Take Profit: {backtest_settings.get('take_profit', 'N/A')}\n")
    sys.stdout.flush()
    
    # Print data settings explicitly
    sys.stdout.write("\nDATA SETTINGS (DETAILED):\n")
    sys.stdout.write("----------------------------------------\n")
    
    data_settings = settings.get('data', {})
    sys.stdout.write(f"Data Source: {data_settings.get('data_source', 'N/A')}\n")
    sys.stdout.write(f"Tickers: {data_settings.get('tickers', 'N/A')}\n")
    sys.stdout.write(f"Start Date: {data_settings.get('start_date', 'N/A')}\n")
    sys.stdout.write(f"End Date: {data_settings.get('end_date', 'N/A')}\n")
    sys.stdout.write(f"Frequency: {data_settings.get('frequency', 'N/A')}\n")
    sys.stdout.write(f"Adjust Prices: {data_settings.get('adjust_prices', 'N/A')}\n")
    sys.stdout.write(f"Fill Method: {data_settings.get('fill_method', 'N/A')}\n")
    sys.stdout.write(f"Cache Directory: {data_settings.get('cache_dir', 'N/A')}\n")
    sys.stdout.flush()
    
    # Print any other important settings
    if 'reporting' in settings:
        sys.stdout.write("\nREPORTING SETTINGS (DETAILED):\n")
        sys.stdout.write("----------------------------------------\n")
        reporting_settings = settings.get('reporting', {})
        sys.stdout.write(f"Output Directory: {reporting_settings.get('output_dir', 'N/A')}\n")
        sys.stdout.write(f"Report Format: {reporting_settings.get('report_format', 'N/A')}\n")
        sys.stdout.write(f"Plot Results: {reporting_settings.get('plot_results', 'N/A')}\n")
        sys.stdout.flush()
        
    # Print signal generator settings if present
    if 'signal_generator' in settings:
        sys.stdout.write("\nSIGNAL GENERATOR SETTINGS (DETAILED):\n")
        sys.stdout.write("----------------------------------------\n")
        signal_settings = settings.get('signal_generator', {})
        sys.stdout.write(f"Signal Type: {signal_settings.get('signal_type', 'N/A')}\n")
        sys.stdout.write(f"Parameters: {signal_settings.get('parameters', 'N/A')}\n")
        sys.stdout.flush()
except Exception as e:
    sys.stdout.write(f"ERROR in Step 3: {e}\n")
    import traceback
    sys.stdout.write(traceback.format_exc())
    sys.stdout.flush()
    sys.exit(1)

# Step 4: Load price data
sys.stdout.write("\nSTEP 4: Loading price data\n")
sys.stdout.flush()

try:
    from v4.data.data_loader import load_price_data
    sys.stdout.write("Imported load_price_data\n")
    sys.stdout.flush()
    
    # Get data parameters from settings
    data_settings = settings.get('data', {})
    data_source = data_settings.get('data_source', 'yahoo')
    tickers = data_settings.get('tickers', [])
    start_date = data_settings.get('start_date', '2020-01-01')
    end_date = data_settings.get('end_date', '2022-12-31')
    
    sys.stdout.write(f"Data source: {data_source}\n")
    sys.stdout.write(f"Tickers: {tickers}\n")
    sys.stdout.write(f"Date range: {start_date} to {end_date}\n")
    sys.stdout.flush()
    
    # Load the price data
    sys.stdout.write("Loading price data...\n")
    sys.stdout.flush()
    price_data = load_price_data(tickers=tickers, start_date=start_date, end_date=end_date, data_source=data_source)
    
    sys.stdout.write("Price data loaded successfully\n")
    sys.stdout.write(f"Price data shape: {price_data.shape}\n")
    sys.stdout.write("Price data sample:\n")
    sys.stdout.write(str(price_data.head()) + "\n")
    sys.stdout.flush()
except Exception as e:
    sys.stdout.write(f"ERROR in Step 4: {e}\n")
    import traceback
    sys.stdout.write(traceback.format_exc())
    sys.stdout.flush()
    sys.exit(1)

# Step 5: Import backtest engine
sys.stdout.write("\nSTEP 5: Importing backtest engine\n")
sys.stdout.flush()

try:
    from v4.engine.backtest_v4 import BacktestEngine
    sys.stdout.write("BacktestEngine imported successfully\n")
    sys.stdout.flush()
except Exception as e:
    sys.stdout.write(f"ERROR in Step 5: {e}\n")
    import traceback
    sys.stdout.write(traceback.format_exc())
    sys.stdout.flush()
    sys.exit(1)

# Step 6: Import signal generator
sys.stdout.write("\nSTEP 6: Importing signal generator\n")
sys.stdout.flush()

try:
    from v4.engine.signal_generator_v4 import generate_signals
    sys.stdout.write("Signal generator imported successfully\n")
    sys.stdout.flush()
except Exception as e:
    sys.stdout.write(f"ERROR in Step 6: {e}\n")
    import traceback
    sys.stdout.write(traceback.format_exc())
    sys.stdout.flush()
    sys.exit(1)

# Step 7: Create BacktestEngine instance
sys.stdout.write("\nSTEP 7: Creating BacktestEngine instance\n")
sys.stdout.flush()

try:
    engine = BacktestEngine()
    sys.stdout.write("BacktestEngine instance created successfully\n")
    sys.stdout.write(f"Engine object: {engine}\n")
    sys.stdout.flush()
except Exception as e:
    sys.stdout.write(f"ERROR in Step 7: {e}\n")
    import traceback
    sys.stdout.write(traceback.format_exc())
    sys.stdout.flush()
    sys.exit(1)

# Step 8: Generate signals
sys.stdout.write("\nSTEP 8: Generating signals\n")
sys.stdout.flush()

try:
    # Get strategy from settings
    strategy = settings.get('backtest', {}).get('strategy', 'equal_weight')
    sys.stdout.write(f"Using strategy: {strategy}\n")
    sys.stdout.flush()
    
    signals = generate_signals(price_data, strategy=strategy)
    sys.stdout.write("Signals generated successfully\n")
    sys.stdout.write(f"Signals shape: {signals.shape}\n")
    sys.stdout.write("Signals sample:\n")
    sys.stdout.write(str(signals.head()) + "\n")
    sys.stdout.flush()
except Exception as e:
    sys.stdout.write(f"ERROR in Step 8: {e}\n")
    import traceback
    sys.stdout.write(traceback.format_exc())
    sys.stdout.flush()
    sys.exit(1)

# Step 9: Run backtest
sys.stdout.write("\nSTEP 9: Running backtest\n")
sys.stdout.flush()

try:
    # Get backtest parameters from settings
    backtest_settings = settings.get('backtest', {})
    strategy = backtest_settings.get('strategy', 'equal_weight')
    rebalance_freq = backtest_settings.get('rebalance_freq', 'M')
    execution_delay = backtest_settings.get('execution_delay', 1)
    initial_capital = backtest_settings.get('initial_capital', 10000)
    
    sys.stdout.write("Backtest parameters:\n")
    sys.stdout.write(f"  - strategy: {strategy}\n")
    sys.stdout.write(f"  - rebalance_freq: {rebalance_freq}\n")
    sys.stdout.write(f"  - execution_delay: {execution_delay}\n")
    sys.stdout.write(f"  - initial_capital: {initial_capital}\n")
    sys.stdout.write(f"  - price_data shape: {price_data.shape}\n")
    sys.stdout.flush()
    
    sys.stdout.write("Calling engine.run_backtest()...\n")
    sys.stdout.flush()
    
    results = engine.run_backtest(
        price_data=price_data,
        signal_generator=generate_signals,
        strategy=strategy,
        rebalance_freq=rebalance_freq,
        execution_delay=execution_delay,
        initial_capital=initial_capital
    )
    
    sys.stdout.write("Backtest completed successfully\n")
    sys.stdout.flush()
except Exception as e:
    sys.stdout.write(f"ERROR in Step 9: {e}\n")
    import traceback
    sys.stdout.write(traceback.format_exc())
    sys.stdout.flush()
    sys.exit(1)

# Step 10: Check results
sys.stdout.write("\nSTEP 10: Checking results\n")
sys.stdout.flush()

try:
    if not isinstance(results, dict):
        sys.stdout.write(f"ERROR: Results is not a dictionary, but {type(results)}\n")
        sys.stdout.flush()
        sys.exit(1)
    
    sys.stdout.write(f"Results is a dictionary with {len(results)} keys\n")
    sys.stdout.write("Results keys:\n")
    for key in results:
        sys.stdout.write(f"  - {key}: {type(results[key])}\n")
    sys.stdout.flush()
    
    # Check weights_history and signal_history
    weights_history = results.get('weights_history')
    signal_history = results.get('signal_history')
    
    if weights_history is None:
        sys.stdout.write("ERROR: weights_history is None!\n")
    elif signal_history is None:
        sys.stdout.write("ERROR: signal_history is None!\n")
    else:
        sys.stdout.write(f"weights_history shape: {weights_history.shape}\n")
        sys.stdout.write(f"signal_history shape: {signal_history.shape}\n")
        
        # Check if they're equal
        if weights_history.equals(signal_history):
            sys.stdout.write("ERROR: weights_history EQUALS signal_history - allocation history is NOT preserved!\n")
            
            # Show a sample comparison
            if not weights_history.empty:
                sample_date = weights_history.index[0]
                sys.stdout.write(f"\nSample comparison for date {sample_date}:\n")
                sys.stdout.write(f"weights: {weights_history.loc[sample_date].to_dict()}\n")
                sys.stdout.write(f"signals: {signal_history.loc[sample_date].to_dict()}\n")
        else:
            sys.stdout.write("SUCCESS: weights_history DIFFERS from signal_history - allocation history is preserved!\n")
            
            # Show a sample comparison
            if not weights_history.empty and not signal_history.empty:
                sample_date = weights_history.index[0]
                sys.stdout.write(f"\nSample comparison for date {sample_date}:\n")
                sys.stdout.write(f"weights: {weights_history.loc[sample_date].to_dict()}\n")
                sys.stdout.write(f"signals: {signal_history.loc[sample_date].to_dict()}\n")
    sys.stdout.flush()
except Exception as e:
    sys.stdout.write(f"ERROR in Step 10: {e}\n")
    import traceback
    sys.stdout.write(traceback.format_exc())
    sys.stdout.flush()
    sys.exit(1)

# Final output
sys.stdout.write("\n" + "=" * 80 + "\n")
sys.stdout.write("TEST COMPLETE\n")
sys.stdout.write("=" * 80 + "\n")
sys.stdout.flush()
